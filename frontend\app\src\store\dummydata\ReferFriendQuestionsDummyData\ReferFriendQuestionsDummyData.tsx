export interface ReferFriendQuestionsType {
  id: number;
  question: string;
  answer: string;
}

const ReferFriendQuestionsDummyData: ReferFriendQuestionsType[] = [
  {
    id: 1,
    question: 'How does “Refer a Friend” work?',
    answer:
      'It’s easy, just use the unique link, and both you and your friend will receive a commision free month using our service as a thank you! ',
  },
  {
    id: 2,
    question: 'Are there any rules?',
    answer:
      'Yes, unfortunately, you cannot refer existing members, but there is NO limit to how many new friends you can refer and as such no limit to how many free months you get.',
  },
  {
    id: 3,
    question: 'Where can I find my referral link or code?',
    answer:
      'Your unique referral link can be found through our referral program dashboard.',
  },
  {
    id: 4,
    question: 'I have more questions. How can I get in touch?',
    answer:
    "Feel free to reach out to our customer support team at info@surelysecurity, and we'll be happy to assist you with any further queries you may have about our refer a friend program!",
  },
];
export default ReferFriendQuestionsDummyData;
