// @ts-nocheck
import _, { set } from 'lodash';
import React, { createContext, useEffect, useState, useMemo, useRef, useContext } from 'react';
import Operatives, { addOperatorsFavorite, removeOperatorsFavorite } from 'src/services/operatives';
import { AuthContext } from './AuthContext';
import { useModalAction } from 'src/context/ModalContext';

export const OperativesContext = createContext<any>(null);

export const initialOperativeFilters = {
  postal_code: '',
  job_location_range: '',
  sia_licence: [],
  industry_sectors: [],
  surely_pro_badge: [],
  rating: '',
  languages: [],
  instant_book: false,
  is_inclusivity_pledge: false,
  is_favorite: false,
};

const OperativesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isClient, user } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [allOperatives, setAllOperatives] = useState<any>([]);
  const [selectedOperatives, setSelectedOperatives] = useState();
  const [favourites, setFavourites] = useState([]); //TODO: duhet te zevendesohet me allOperatives dhe te behet filter
  const [totalCount, setTotalCount] = useState(0);
  const [favouritesCount, setFavouritesCount] = useState(0);
  const [page, setPage] = useState(1);
  const [isLastPage, setIsLastPage] = useState(false);
  const [from, setFrom] = useState(0);
  const [search, setSearch] = useState('');
  const [isLoadingOperatives, setIsLoadingOperatives] = useState(false);
  const [isLoadingMoreOperatives, setIsLoadingMoreOperatives] = useState(false);
  const [filters, setFilters] = useState<any>(initialOperativeFilters);

  const defaultPerPage = isAuthenticated ? '&per_page=20' : '&per_page=20';
  const canLoadMore = isAuthenticated;

  const debouncedSearchRef = useRef(
    _.debounce((searchKey: string) => {
      setPage(1);
      setSearch(searchKey);
    }, 500),
  );

  const reload = () => {
    const allFilters = `&page=${page}${memoizedFilters}`;
    setIsLoadingOperatives(true);
    
    // Use topProfiles endpoint for unauthenticated users
    const endpoint = !isAuthenticated ? 'top-profiles' : 'list';
    
    Operatives[endpoint](allFilters).then((res: any) => {
      setAllOperatives(res?.data || []);
      setTotalCount(res?.meta?.total);
      setIsLastPage(res?.meta?.last_page == res?.meta?.current_page || !isAuthenticated);
      setIsLoadingOperatives(false);
    });
  };

  const handleSearch = (searchKey: string) => {
    if (!isAuthenticated) {
      openModal('GUEST_VIEW');
      return;
    }

    const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
    const isGuest = accountType === '5';

    if (isGuest) {
      openModal('GUEST_UPGRADE');
      return;
    }
    
    if (isAuthenticated) {
      debouncedSearchRef.current(searchKey);
    } else {
      setIsLoadingOperatives(true);
      Operatives.topProfiles(`&qs=${searchKey}`).then((res: any) => {
        setAllOperatives(res?.data || []);
        setTotalCount(res?.meta?.total);
        setIsLastPage(true);
        setIsLoadingOperatives(false);
      });
    }
  };

  const toggleInstantBook = () => {
    setPage(1);
    setFilters((prevFilters: any) => {
      const currentInstantBook = !prevFilters.instant_book;

      return {
        ...prevFilters,
        instant_book: currentInstantBook,
      };
    });
  };
  const toggleBS7858 = () => {
    setPage(1);
    setFilters((prevFilters: any) => {
      const currentBS7858 = !prevFilters.bs7858;

      return {
        ...prevFilters,
        bs7858: currentBS7858,
      };
    });
  };
  const toggleInclusivityPledge = () => {
    setPage(1);
    setFilters((prevFilters: any) => {
      const currentInclusivityPledge = !prevFilters.is_inclusivity_pledge;

      return {
        ...prevFilters,
        is_inclusivity_pledge: currentInclusivityPledge,
      };
    });
  };

  const handleFilters = (filters: any) => {
    const { postCode, locationRange, siaLicense, industrySectors, surelyProBadge, ratings, isInstantBook, isInclusivityPledge } = filters;
    setFilters((prevFilters: any) => {
      return {
        ...prevFilters,
        postal_code: postCode,
        job_location_range: locationRange,
        sia_licence: siaLicense,
        industry_sectors: industrySectors,
        surely_pro_badge: surelyProBadge,
        ratings,
        instant_book: isInstantBook,
        is_inclusivity_pledge: isInclusivityPledge,
      };
    });
    setPage(1);
  };

  const resetFilters = () => {
    setFilters({
      postal_code: '',
      job_location_range: '',
      hourly_rate_min: '',
      hourly_rate_max: '',
      sia_licence: [],
      industry_sectors: [],
      surely_pro_badge: [],
      is_emergency_hire: false,
      is_inclusivity_pledge: false,
      rating: '',
      languages: [],
      instant_book: false,
      is_favorite: filters?.is_favorite,
    });
    setPage(1);
  };

  const resetOuterFilters = () => {
    setSearch('');
    resetFilters();
  };

  const handleSelectedOperatives = (id: any) => {
    setSelectedOperatives(allOperatives.find((op: any) => op.id === +id));
  };

  const memoizedFilters = useMemo(() => {
    const allFilters = { qs: search, ...filters };

    return Object.keys(allFilters)?.reduce((acc: any, key: any) => {
      if (allFilters[key]) {
        if (allFilters[key]?.length !== 0) {
          acc += `&${key}=${allFilters[key]}`;
        }
      }
      return acc;
    }, defaultPerPage);
  }, [search, filters, page]);

  // This useEffect was used to fetch all operatives and to filter the favourites from them from the front, this should be done on the server side with the is_favorite query string

  // useEffect(() => {
  //   if (!isAuthenticated) {
  //     return;
  //   }
  //   if (isClient) {
  //   setIsLoadingOperatives(true)
  //   Operatives.list('').then((res) => {
  //     const favOperatives = res.data?.filter((op: any) => op.is_favorite);
  //     setFavourites(favOperatives);
  //     setIsLoadingOperatives(false)
  //   });
  // }
  // }, []);

  const isUserAllowed = () => {
    const accountType = user?.profile?.account_type;
    return isClient || accountType === '5'; // Check for both client and guest
  };

  useEffect(() => {
    const fetchOperatives = async () => {
      const allFilters = `&page=${page}${memoizedFilters}`;
      setIsLoadingMoreOperatives(page > 1);
      setIsLoadingOperatives(page === 1);
      
      try {
        const res = await Operatives.list(allFilters);
        
        if (allFilters?.includes('is_favorite=true')) {
          setFavourites(res?.data || []);
          setFavouritesCount(res?.meta?.total);
        } else {
          // Append new operators to existing ones if loading more
          setAllOperatives(prevOperatives => 
            page === 1 ? res?.data : [...prevOperatives, ...(res?.data || [])]
          );
          setTotalCount(res?.meta?.total);
        }
        
        setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
        setIsLoadingOperatives(false);
        setIsLoadingMoreOperatives(false);
      } catch (error) {
        console.error('Error fetching operatives:', error);
        setIsLoadingOperatives(false);
        setIsLoadingMoreOperatives(false);
      }
    };
  
    fetchOperatives();
  }, [page, memoizedFilters]);

  // useEffect(() => {
  //   if (!isAuthenticated) {
  //     return;
  //   }
  //   if (isClient) {
  //   const allFilters = `&page=${page}${memoizedFilters}`;
  //   setIsLoadingOperatives(true)
  //   Operatives.list(allFilters).then((res: any) => {
  //     setAllOperatives(res?.data || []);
  //     setTotalCount(res?.meta?.total);
  //     setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
  //     setIsLoadingOperatives(false)
  //   });
  // } else {
  //   setAllOperatives([]);
  //     setTotalCount(0);
  //     setIsLastPage(true);
  //     setIsLoadingOperatives(false)
  // }
  // }, [memoizedFilters, isAuthenticated]);

  const loadMore = () => {
    if (!canLoadMore || isLastPage) return;
    
    setIsLoadingMoreOperatives(true);
    setPage(prev => prev + 1);
  };

  const addFavorite = async (id: number) => {
    try {
      const response = await addOperatorsFavorite(id);
      if (!response.error) {
      }
    } catch (error) {
      console.error('error', error);
    }
  };

  // //TODO: emri i ketij funksioni duhet te jete fetchAllFavourites
  const fetchAllOperative = () => {
    Operatives.list('').then((res) => {
      const favOperatives = res?.data?.filter((op: any) => op.is_favorite);
      setFavourites(favOperatives);
    });
  };
  //
  // useEffect(fetchAllOperative, [isAuthenticated]);

  const removeFavorite = async (id: number) => {
    try {
      const response = await removeOperatorsFavorite(id);
      if (!response.error) {
      }
    } catch (error) {
      console.error('error', error);
    }
  };

  const contextValue = {
    fetchAllOperative,
    allOperatives,
    setAllOperatives,
    selectedOperatives,
    setSelectedOperatives,
    handleSelectedOperatives,
    handleLoadMore: loadMore,
    filters,
    setFilters,
    toggleInstantBook,
    toggleInclusivityPledge,
    handleFilters,
    resetFilters,
    totalCount,
    addFavorite,
    removeFavorite,
    favourites,
    setFavourites,
    favouritesCount,
    resetOuterFilters,
    search,
    handleSearch,
    setPage,
    page,
    isLastPage,
    setIsLastPage,
    from,
    reload,
    isLoadingOperatives,
    isLoadingMoreOperatives,
    toggleBS7858,
    canLoadMore,
    loadMore,
  };

  return <OperativesContext.Provider value={contextValue}>{children}</OperativesContext.Provider>;
};

export default OperativesProvider;
