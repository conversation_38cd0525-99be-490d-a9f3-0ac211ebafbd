<?php

namespace App\Charts;

use ArielMejiaDev\LarapexCharts\LarapexChart;

class JobPostingsChart
{
    protected $chart;

    public function __construct(LarapexChart $chart)
    {
        $this->chart = $chart;
    }

    public function build($active_jobs, $completed_jobs)
    {
        return $this->chart->donutChart()
            ->setTitle('Jobs on Surely')
            ->setColors(['#323C58', 'rgb(0, 143, 251)'])
            ->addData([$active_jobs, $completed_jobs,])
            ->setLabels(['Active', 'Completed',])
            ->setHeight(300);
    }
}
