// @ts-nocheck
import { useContext, useState } from 'react'; 
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import dummyOperator from 'src/store/dummydata/OperatorHomePageDummy';
import { IdVerified } from 'src/assets/icons';
import { Button, Text, Image, View, Divider, Card, Icon } from 'reshaped';

const OperatorHomePageCarousel = () => {
  const { openModal } = useModalAction();
  const { isAuthenticated } = useContext(AuthContext);
  const [selectedOperatorIndex, setSelectedOperatorIndex] = useState(0); 

  return (
    <>
      <Carousel
        showArrows={false}
        showStatus={false}
        showThumbs={false}
        infiniteLoop={true}
        showIndicators={false}
        className='p-4'
        onChange={(index) => setSelectedOperatorIndex(index)} 
      >
        {dummyOperator.map((operator, index) => (
          <div
            key={operator?.firstName + operator?.lastName + operator?.operatorId}
            onClick={() => {
              if (!isAuthenticated) {
                openModal('REGISTER');
              }
            }}
          >
            <Card className='mx-auto flex h-full min-h-[421px] cursor-pointer flex-col justify-between shadow-md'>
              <View className='mx-[14px] '>
                <View className='mt-[10px] flex items-center justify-between'>
                  <View className='gap-2  '>
                    <View className='mr-[5px] max-w-xs overflow-hidden  rounded-[4px] !bg-[#CCFFE8]  p-1 '>
                      <Text className='rubik flex items-center text-xs leading-[16px]  text-[#05751F]'>SIA CERTIFIED</Text>
                    </View>
                  </View>

                  {operator.isFavorite ? (
                    <svg xmlns='http://www.w3.org/2000/svg' width='22' height='19' viewBox='0 0 22 19' fill='none'>
                      <path
                        d='M9.87576 17.1178L9.87504 17.1172C7.27067 14.7555 5.1495 12.8312 3.6734 11.0265C2.20305 9.22886 1.42505 7.61102 1.42505 5.87108C1.42505 3.04413 3.63934 0.829834 6.4663 0.829834C8.06796 0.829834 9.613 1.57788 10.6194 2.75968L11 3.20671L11.3807 2.75968C12.3871 1.57788 13.9321 0.829834 15.5338 0.829834C18.3608 0.829834 20.5751 3.04413 20.5751 5.87108C20.5751 7.61102 19.7971 9.22889 18.3266 11.0279C16.8528 12.8309 14.7362 14.7544 12.1377 17.1157L12.1257 17.1266L12.1247 17.1276L11.0013 18.1425L9.87576 17.1178Z'
                        fill='#323C58'
                        stroke='#323C58'
                      />
                    </svg>
                  ) : (
                    <svg xmlns='http://www.w3.org/2000/svg' width='22' height='19' viewBox='0 0 22 19' fill='none'>
                      <path
                        d='M9.87576 17.1178L9.87504 17.1172C7.27067 14.7555 5.1495 12.8312 3.6734 11.0265C2.20305 9.22886 1.42505 7.61102 1.42505 5.87108C1.42505 3.04413 3.63934 0.829834 6.4663 0.829834C8.06796 0.829834 9.613 1.57788 10.6194 2.75968L11 3.20671L11.3807 2.75968C12.3871 1.57788 13.9321 0.829834 15.5338 0.829834C18.3608 0.829834 20.5751 3.04413 20.5751 5.87108C20.5751 7.61102 19.7971 9.22889 18.3266 11.0279C16.8528 12.8309 14.7362 14.7544 12.1377 17.1157L12.1257 17.1266L12.1247 17.1276L11.0013 18.1425L9.87576 17.1178Z'
                        fill='#C7CDDB'
                        stroke='#BBC1D3'
                      />
                    </svg>
                  )}
                </View>
                <View className='mt-[20px] flex flex-col gap-4'>
                  <View className='flex flex-col items-center'>
                    <div className='relative'>
                      <Image className='h-[127px] w-[127px]' src={operator.photo} />
                      <Icon className='absolute bottom-0 right-0 h-[50px] w-[50px]' svg={IdVerified} />
                    </div>
                  </View>

                  <View className='ml-[0px] flex flex-col justify-start gap-1'>
                    <View className='flex items-center gap-2'>
                      <svg xmlns='http://www.w3.org/2000/svg' width='18' height='17' viewBox='0 0 18 17' fill='none'>
                        <path
                          d='M8.99991 13.4327L14.2041 16.5737L12.8231 10.6537L17.421 6.67057L11.3662 6.15689L8.99991 0.57373L6.63359 6.15689L0.578857 6.67057L5.17675 10.6537L3.7957 16.5737L8.99991 13.4327Z'
                          fill='#F4BF00'
                        />
                      </svg>
                      <Text className='rubik text-left text-[15px] font-medium text-[#323c58]'>
                        {operator.rate.toFixed(1)} ({operator.jobsComplete})
                      </Text>
                    </View>
                    <View>
                      <Text className='rubik text-[16px] font-medium text-[#1A1A1A]'>
                        {operator.firstName} {operator.lastName}
                      </Text>
                    </View>
                    <View className='flex items-center'>
                      <Text className='rubik text-[15px] text-[#0B80E7]'>{operator.city}</Text>
                      <span className='material-icons-outlined icon-line-height mt-[5px] p-0 pb-1 text-[18px] !text-[#0B80E7] text-blue-400'>
                        place
                      </span>
                    </View>
                  </View>
                </View>
                <View className='flex flex-col items-start'>
                  <Divider className='mt-[20px] h-[1px] w-full' />
                </View>
                <View className='mt-[20px] flex flex-col gap-2'>
                  {operator.firstgrades.map((firstgrade: any, gradeIndex: any) => (
                    <Button
                      key={firstgrade + gradeIndex}
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] w-fit max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1 '>
                        <span className='material-icons text-[16px]'>star</span>
                        {firstgrade}
                      </Text>
                    </Button>
                  ))}
                </View>
                <View className='my-2 flex flex-col gap-2 '>
                  {operator.secondgrades.map((secondgrade: any, gradeIndex: any) => (
                    <Button
                      key={secondgrade + gradeIndex}
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] w-fit max-w-xs overflow-hidden truncate border  border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1 '>
                        <span className='material-icons text-[16px]'>star</span>
                        {secondgrade}
                      </Text>
                    </Button>
                  ))}
                </View>
              </View>
            </Card>
          </div>
        ))}
      </Carousel>
      <View className=' flex justify-center'>
        {dummyOperator.map((_, index) => (
          <div
            key={index}
            className={`mx-1 h-[12px] cursor-pointer ${
              selectedOperatorIndex === index ? 'w-[22px] rounded-[8px] bg-[#0B80E7]' : 'w-[12px] rounded-[100%] bg-[#C4BCBD]'
            }`}
          />
        ))}
      </View>
    </>
  );
};

export default OperatorHomePageCarousel;
