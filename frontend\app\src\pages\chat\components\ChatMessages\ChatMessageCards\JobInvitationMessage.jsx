import { Button, useToggle } from 'reshaped';

import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useAuthContext } from 'src/context/AuthContext';
import { useChatContext } from 'src/context/ChatContext';
import Job from 'src/services/jobs';
import { applyForJob } from 'src/services/operatives';
import ApplyNowModal from 'src/components/JobDescription/ApplyNowModal/ApplyNowModal';
import { useNavigate } from 'react-router-dom';

const invitation = {
  start: '18-05-2024-08:00',
  end: '22-05-2024-18:00',
  opName: '12Andrea',
  clientName: '<PERSON>',
  reason: 'I found someone else',
  payment: '$100',
  jobName: 'Goodwood Festival',
};

const JobInvitationMessage = ({ message, hasApplied }) => {
  const navigate = useNavigate()
  const { active, activate, deactivate } = useToggle(false);
  const { user, isClient } = useAuthContext();
  const { currentChat, contract } = useChatContext();

  const job = currentChat?.job;
  const jobName = currentChat?.job?.post_name;
  const isReceiver = user?.profile?.id === message?.receiver_id;
  const invitationMessage = isReceiver ? 'has invited you to job' : 'was invited to job';
  const nameShown = isReceiver ? message.sender?.name : message.receiver?.name;
  const jobId = message?.job_id

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#F4F5F7] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Job invitation</h2>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          {isClient ? '' : 'Congratulations!'} <span className='font-medium'>{nameShown}</span> {invitationMessage}{' '}
          <span onClick={() => navigate(`${isClient ? `/manage-jobs/${jobId}` : `/operator-job/${jobId}`}`)} className='font-medium text-[#323C58] cursor-pointer'>{jobName}</span>
        </p>
      </div>
      {isReceiver && !hasApplied && (
        <>
          <Button
            color='black'
            className='-mr-[54px] min-w-[60px] shrink-0 rounded !bg-[#323c58] px-2 py-1 text-[10px] text-sm font-medium leading-5 text-[#FFF] lg:text-[14px]'
            onClick={activate}
          >
            Apply
          </Button>
          <ApplyNowModal active={active} deactivate={deactivate} selectedJob={job} chatId={currentChat?.id} />
        </>
      )}
    </div>
  );
};

export default JobInvitationMessage;
