// @ts-nocheck
import React, { useState } from 'react';
import { Text, View, Button, Modal, Divider,Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import conflictmanagementfristpage from '../../../assets/images/documentsfirstpage/conflictmanagementfirstpage.png';
import costumerservicefirstpage from '../../../assets/images/documentsfirstpage/costumerservicefirstpage.png';
import disabilityfocusfirstpage from '../../../assets/images/documentsfirstpage/disabilityfocusfirstpage.png';
import substanceawarenessfirstpage from '../../../assets/images/documentsfirstpage/substanceawarenessfirstpage.png';
import useofequipmentfirstpage from '../../../assets/images/documentsfirstpage/useofequipmentfirstpage.png';
import vulnerablepeoplefirstpage from '../../../assets/images/documentsfirstpage/vulnerablepeoplefirstpage.png';
import inclusivitypledgefirstpage from '../../../assets/images/documentsfirstpage/inclusivitypledgefirstpage.png';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';

interface DocumentModalProps {
  active: boolean;
  deactivate: () => void;
  text: string;
  document: string;
  downloadDocument: string;
  type: string;
}

const DocumentModal: React.FC<DocumentModalProps> = ({
  active,
  deactivate,
  text,
  document,
  downloadDocument,
  type,
}) => {
  const handleDownload = () => {
    window.open(downloadDocument, '_blank');
  };

  const handlePrint = () => {
    window.open(document, '_blank');
  };

  const getFirstPageDocument = (type: any) => {
    switch (type) {
      case 'CustomerService':
        return costumerservicefirstpage;
      case 'UseOfEquipment':
        return useofequipmentfirstpage;
      case 'DisabilityFocus':
        return disabilityfocusfirstpage;
      case 'SubstanceAwareness':
        return substanceawarenessfirstpage;
      case 'VulnerablePeople':
        return vulnerablepeoplefirstpage;
      case 'ConflictManagament':
        return conflictmanagementfristpage;
        case 'InclusivityPledge':
          return inclusivitypledgefirstpage;
      default:
        return conflictmanagementfristpage;
    }
  };
  const documentFirstPageSrc = getFirstPageDocument(type);

  return (
    <Modal active={active} onClose={deactivate} className='!w-[100%] !h-[auto]'>
      <View className='flex flex-col'>
        <View className='flex items-center p-0 mt-[24px] ml-[24px]'>
          <Text className='text-[#323C58] text-base xl:text-4xl rubik font-normal xl:font-normal leading-4 xl:leading-10'>
            {text}
          </Text>
          <Button
            onClick={deactivate}
            icon={() => (
              <span className='material-icons align-middle text-500 '>
                close
              </span>
            )}
            className='flex items-center justify-end btn-no-hover !bg-transparent ml-auto'
          ></Button>
        </View>
        <Divider className='w-full h-[1px] mt-[16px]'></Divider>

        <Image src={documentFirstPageSrc} className=' w-[521.743px] h-[593.57px] mx-auto mt-[20px]' />

        <Divider className='w-full h-[1px] mt-[16px]'></Divider>

        <View className='flex flex-row justify-center mt-[20px]'>
          <Button
            onClick={handleDownload}
            variant='outline'
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base w-[281.5px] h-[48px] mr-[10px]'
          >
           <Text className='rubik font-medium leading-[24px] text-[#1A1A1A]'> Download</Text>
          </Button>
          <Button
            onClick={handlePrint}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base !bg-[#0B80E7] w-[281.5px] h-[48px]'
          >
            <Text className='rubik font-medium leading-[24px] text-[#FFFFFF]'> Print</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default DocumentModal;
