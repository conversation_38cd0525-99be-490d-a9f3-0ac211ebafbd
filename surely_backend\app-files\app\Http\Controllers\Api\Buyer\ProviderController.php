<?php

namespace App\Http\Controllers\Api\Buyer;

use App\Http\Controllers\Controller;
use App\Models\BuyerSearch;
use App\Models\FormApplicant;
use App\Models\MobileUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProviderController extends Controller
{

    function providerData($providers)
    {
        $data = array();
        foreach ($providers as $provider) {

            $obj = [
                'id' => $provider->id,
                'name' => $provider->name ?? '',
                'expertise' => $provider->expertise->name ?? '',
                'pay_rate' => $provider->pay_rate,
            ];

            array_push($data, $obj);
        }
        return $data;
    }


    public function service_providers()
    {
        $providers = MobileUser::whereIn('account_type', [MobileUser::freelancer, MobileUser::business])->get();

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $this->providerData($providers)
        ]);
    }


    public function location_filter(Request $request)
    {
        //address, city_id  or lat lng

        $providers = MobileUser::whereIn('account_type', [MobileUser::freelancer, MobileUser::business])
            ->where('city_id', $request->city_id)->get();


        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $this->providerData($providers)
        ]);

    }

    public function freelancer_info($id)
    {
        $provider = MobileUser::find($id);

        $experiences = $provider->experiences();

        $data = array();
        foreach ($experiences as $experience) {
            $obj = [
                'name' => $experience->expertise->name,
                'years' => $experience->years,
                'employer_name' => $experience->employer_name,
                'gig' => $experience->gig ? $experience->gig->name : '',
                'start_date' => date('Y-m-d', strtotime($experience->start_date)),
                'end_date' => $experience->end_date ? date('Y-m-d', strtotime($experience->end_date)) : 'Present'
            ];
            array_push($data, $obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => [
                'id' => $provider->id,
                'name' => $provider->name,
                'gender' => $provider->gender_name,
                'age' => $provider->age,
                'height' => $provider->height,
                'years_of_experience' => $provider->years_of_experience,
                'expertise' => $provider->expertise->name,
                'pay_rate' => $provider->pay_rate,
                'experiences' => $experiences,
            ]
        ]);

    }


}
