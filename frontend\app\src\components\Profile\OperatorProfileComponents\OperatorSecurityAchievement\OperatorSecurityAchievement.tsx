// @ts-nocheck
import React, { useState, useContext } from 'react';
import { Card, Text, View, Button, Actionable, Tooltip, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import surelyiconprofile from '../../../../assets/icons/surleyicon/surelyiconprofile.png';
import { AppContext } from '../../../../context/AppContext';

interface SecurityCheck {
  name: string;
  value: boolean;
  additionalData: number[];
}

const OperatorSecurityAchievement: React.FC = () => {
  const navigate = useNavigate();
  const { siaLicence, idCheck, proofOfAddress, employmentHistory, creditCheck, noCriminalRecord } =
    useContext(AppContext);

  const securityChecks: SecurityCheck[] = [
    { name: 'SIA licence', value: siaLicence, additionalData: [] },
    { name: 'ID check', value: idCheck, additionalData: [] },
    {
      name: 'Proof of address',
      value: proofOfAddress,
      additionalData: [],
    },
    {
      name: 'Employment history',
      value: employmentHistory,
      additionalData: [],
    },
    { name: 'Credit check', value: creditCheck, additionalData: [] },
    {
      name: 'No criminal record',
      value: noCriminalRecord,
      additionalData: [],
    },
  ];

  return (
    <Card className='xl:w-full h-[auto] xl:mx-auto p-6 xl:max-w-[200px]'>
      <View className='flex items-center justify-between'>
        <View className='flex gap-2 items-center'>
          <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>BS7858</Text>

          <Tooltip text='BS7858 it is a rigorous screening standard that ensures the highest level of professionalism and integrity in the security industry. This standard covers comprehensive background checks, including identity verification, employment history and criminal record checks. We conduct internal verification to ensure the validity of the SIA license on some details, while other information are self-verified by security operatives.'>
            {(attributes) => (
              <Actionable attributes={attributes} as='div'>
                <div className='w-[20px] h-[20px] rounded-full border bg-[#C7CDDB]  flex items-center justify-center'>
                  <span className='material-icons-outlined text-[12px]'>question_mark</span>
                </div>
              </Actionable>
            )}
          </Tooltip>
        </View>
        <Button
          icon={() => (
            <span className='material-icons-outlined text-white text-base flex items-center justify-center'>edit</span>
          )}
          onClick={() =>
            navigate('/operator-settings-sia-certificate', {
              state: {
                activeTab: '1',
              },
            })
          }
          className='!w-[32px] !h-[32px] !rounded-full border border-[#323C58] !bg-[#323C58] '
        />
      </View>
      <View className='mt-[20px] flex flex-col gap-3'>
        {securityChecks.length === 0 ? (
          <View className='flex flex-col justify-center items-center w-[140px] mx-auto mt-[40px] mb-[40px]'>
            <Image src={surelyiconprofile} className='w-[40px] h-[40px] flex-shrink-0' />
            <Text className='text-[#444B5F] text-center font-normal leading-[24px] rubik !text-[#383838] mt-[10px]'>
              You can be sure that all details are important
            </Text>
          </View>
        ) : (
          securityChecks.map((check, index) => (
            <View key={index} className='flex items-center gap-2'>
              <span
                className={`material-icons-outlined text-[14px] ${check.value ? 'text-[#05751F]' : 'text-[#BABABC]'}`}
              >
                {check.value ? 'check_circle' : 'cancel'}
              </span>
              <Text
                className={`text-neutral rubik text-xs font-medium leading-5 ${
                  check.value ? 'text-[#05751F]' : 'text-[#BABABC]'
                }`}
              >
                {check.name}
              </Text>
            </View>
          ))
        )}
      </View>
    </Card>
  );
};

export default OperatorSecurityAchievement;
