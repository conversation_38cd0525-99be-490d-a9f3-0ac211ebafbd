# Use PHP 8.1 Apache base image
FROM php:8.1-apache

# Set the working directory
WORKDIR /var/www/html

# Set the owner of /var/www/html to www-data:www-data
RUN chown -R www-data:www-data /var/www/html

# Install required packages
RUN apt-get update && apt-get install -y \
    libpng-dev \
    zlib1g-dev \
    libxml2-dev \
    libzip-dev \
    libonig-dev \
    zip \
    curl \
    unzip \
    nano

# Configure and install PHP extensions
RUN docker-php-ext-configure gd && \
    docker-php-ext-install -j$(nproc) gd && \
    docker-php-ext-install pdo_mysql && \
    docker-php-ext-install mysqli && \
    docker-php-ext-install zip

# Copy your virtual host configuration
COPY .docker/vhost.conf /etc/apache2/sites-available/000-default.conf

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# RUN ['composer', 'install']

# Enable Apache mod_rewrite and mod_headers
RUN a2enmod rewrite headers

