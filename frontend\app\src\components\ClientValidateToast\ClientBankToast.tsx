// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { View, Image, Text, Button, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { getValidateOperatorProfile } from 'src/services/user';
import { AppContext } from '../../context/AppContext';

const ClientBankToast: React.FC = ({onClose}: {onClose: any;}) => {
  const navigate = useNavigate();
  const [showToast, setShowToast] = useState(true);

  //   const {
  //     addressVerificationDocument,
  //     documentType,
  //     idBackDocument,
  //     idFrontDocument,
  //     selfieVerificationDocument,
  //     siaLicenceCardPhoto,
  //     siaLicenceExpiryDate,
  //     siaLicenceNumber,
  //   } = useContext(AppContext);

  //   const isValidateUnavailable =
  //     addressVerificationDocument === null ||
  //     documentType === null ||
  //     idBackDocument === null ||
  //     idFrontDocument === null ||
  //     selfieVerificationDocument === null ||
  //     siaLicenceCardPhoto === null ||
  //     siaLicenceExpiryDate === null ||
  //     siaLicenceNumber === null ||
  //     addressVerificationDocument === undefined ||
  //     documentType === undefined ||
  //     idBackDocument === undefined ||
  //     idFrontDocument === undefined ||
  //     selfieVerificationDocument === undefined ||
  //     siaLicenceCardPhoto === undefined ||
  //     siaLicenceExpiryDate === undefined ||
  //     siaLicenceNumber === undefined;

  return (
    <View>
      {showToast && (
        <View className='fixed bottom-1 right-[15.3%] z-10 mt-[26px] w-[320px] translate-y-0 transform sm:bottom-7 sm:right-5 sm:w-[420px]'>
          <View className='w-[370px] rounded-[8px] bg-[#1C212BF7] text-white sm:w-[420px] '>
            <button
              className='ml-auto flex items-center justify-end border border-transparent bg-transparent hover:border-transparent'
              onClick={() => {setShowToast(false); onClose()}}
            >
              <span className='material-icons text-500 align-middle text-[#fff]'>close</span>
            </button>
            <View className='ml-5 mr-4 mt-[-12px] flex items-start gap-4'>
              <Image src={surleyicon} alt='Surely Icon' className='h-[64px] w-[64px]' />
              <View className='mb-[20px] w-[308px] text-left'>
                <Text className='rubik text-[14px] font-bold leading-[20px] text-[#EFF0F1]'>Welcome to our platform!</Text>
                <Text className='rubik text-[14px] font-normal leading-[20px] text-[#EFF0F1]'>
                  In order to start hiring, you will need to fill in your card details in the payment settings section.
                </Text>
                <Button
                  className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                  onClick={() => navigate('/client-settings-payment', { state: { activeTab: '2' } })}
                >
                  Proceed to settings
                </Button>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default ClientBankToast;
