import React, { useContext } from 'react';
import { Card, Text, Button, View } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import { AppContext } from '../../../../context/AppContext';

const OperatorIndustrySelectors: React.FC = () => {
  const navigate = useNavigate();
  const { industrySectors } = useContext(AppContext);

  return (
    <Card className='xl:w-[424px] h-[auto] lg:mx-auto p-6'>
      <View className='flex flex-col gap-5'>
        <View className='flex items-center justify-between'>
          <Text className='text-center text-[#1A1A1A] rubik text-[16px] xl:font-medium xl:leading-5'>
            Industry sectors
          </Text>
          <Button
            icon={() => <span className='material-icons-outlined text-white text-base'>edit</span>}
            onClick={() =>
              navigate('/operator-settings-profile-details', {
                state: {
                  activeTab: '0',
                },
              })
            }
            className='!w-[12px] !h-[12px] !rounded-full border border-[#323C58] !bg-[#323C58] p-0 pb-2'
          ></Button>
        </View>

        <View className='flex gap-2'>
          {industrySectors.length === 0 ? (
            <NoDataProfile />
          ) : (
            <div className=' flex flex-wrap gap-2'>
              {industrySectors.map((industrySector: any, index: any) => (
                <Button
                  key={index}
                  size='small'
                  rounded={true}
                  elevated={false}
                  className='px-[8px] py-[4px] border !bg-[#323C58] max-w-xs overflow-hidden truncate '
                >
                  <Text className='text-[#FFFFFF] rubik font-normal text-[12px]  leading-[20px]'>{industrySector}</Text>
                </Button>
              ))}
            </div>
          )}
        </View>
      </View>
    </Card>
  );
};

export default OperatorIndustrySelectors;
