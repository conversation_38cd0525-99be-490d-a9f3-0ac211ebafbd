export type QuestionDataUseOfEquipments = {
  id: number;
  question: string;
  answer_1: string;
  answer_2: string;
  answer_3: string;
  answer_4: string;
  correct_answer: number;
};

export const useOfEquipmentsQuestions: QuestionDataUseOfEquipments[] = [
  {
    id: 1,
    question:
      'Which of the following is NOT a word used within the phonetic alphabet?',
    answer_1: 'Sugar',
    answer_2: 'Quebec',
    answer_3: 'Zulu',
    answer_4: 'Lima',
    correct_answer: 1,
  },
  {
    id: 2,
    question:
      'Which of the below reasons is one of the primary purposes of using a body worn camera?',
    answer_1: 'To film a local busker',
    answer_2: 'To create social media content',
    answer_3: 'To monitor the behaviour of security operatives',
    answer_4: 'To fit in with the team',
    correct_answer: 3,
  },
  {
    id: 3,
    question:
      'Which of the following items is primarily used for searching a customer and their belongings?',
    answer_1: 'Search wand',
    answer_2: 'Personal alarm',
    answer_3: 'Eye protection',
    answer_4: 'Safety footwear',
    correct_answer: 1,
  },
  {
    id: 4,
    question: 'Which of the following items is not a communication device?',
    answer_1: 'Earpiece',
    answer_2: 'Radio',
    answer_3: 'Mobile phone',
    answer_4: 'Personal alarm',
    correct_answer: 4,
  },
  {
    id: 5,
    question: 'When using a radio, what must you always do first of all?',
    answer_1: 'Interrupt another transmission',
    answer_2: 'Hold the radio as close to your mouth as possible',
    answer_3: 'Think about what you want to say',
    answer_4: 'Speak rapidly and loudly',
    correct_answer: 3,
  },
];
