// @ts-nocheck
import React from 'react';
import { Text, View, Button, Modal } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import moment from 'moment';

interface ConfirmContractModalProps {
  active: boolean;
  deactivate: () => void;
  contract: any;
  handleAcceptContract: () => void;
}

const ConfirmContractModal: React.FC<ConfirmContractModalProps> = ({ active, deactivate, contract, handleAcceptContract }) => {
  const navigate = useNavigate();

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px]'>
      <View className='flex flex-col'>
        <svg xmlns='http://www.w3.org/2000/svg' width='47' height='47' viewBox='0 0 47 47' fill='none'>
          <path
            d='M23.5195 0.450195C10.8235 0.450195 0.519531 10.7542 0.519531 23.4502C0.519531 36.1462 10.8235 46.4502 23.5195 46.4502C36.2155 46.4502 46.5195 36.1462 46.5195 23.4502C46.5195 10.7542 36.2155 0.450195 23.5195 0.450195ZM23.5195 41.8502C13.3765 41.8502 5.11953 33.5932 5.11953 23.4502C5.11953 13.3072 13.3765 5.0502 23.5195 5.0502C33.6625 5.0502 41.9195 13.3072 41.9195 23.4502C41.9195 33.5932 33.6625 41.8502 23.5195 41.8502ZM34.0765 13.2842L18.9195 28.4412L12.9625 22.5072L9.71953 25.7502L18.9195 34.9502L37.3195 16.5502L34.0765 13.2842Z'
            fill='#05751F'
          />
        </svg>
        <Text className='rubik mt-[10px] text-[20px] font-normal text-[#1A1A1A]'>
          Do you want to confirm &nbsp;
          <span className='rubik text-[20px] font-[500] text-[#323C58] '>{contract?.job?.post_name || contract?.id} &nbsp;contract?</span>
        </Text>
        <Text className='rubik mt-[3px] text-[15px] font-normal leading-5 text-[#323C58]'>If yes, we will book the date range in your calendar:</Text>

        <View className='mt-[10px] flex flex-row gap-[8px]'>
          <svg xmlns='http://www.w3.org/2000/svg' width='14' height='16' viewBox='0 0 14 16' fill='none'>
            <path
              d='M12.4682 1.82187H11.7872V0.459961H10.4253V1.82187H3.61572V0.459961H2.25381V1.82187H1.57285C0.823798 1.82187 0.210938 2.43473 0.210938 3.18379V14.0791C0.210938 14.8281 0.823798 15.441 1.57285 15.441H12.4682C13.2172 15.441 13.8301 14.8281 13.8301 14.0791V3.18379C13.8301 2.43473 13.2172 1.82187 12.4682 1.82187ZM12.4682 14.0791H1.57285V6.58857H12.4682V14.0791ZM12.4682 5.22665H1.57285V3.18379H12.4682V5.22665Z'
              fill='#323C58'
            />
          </svg>
          <View className='flex flex-col'>
            <Text className='rubik leadig-[20px] text-[14px] font-normal text-[#383838]'>
              From{' '}
              <span className='rubik leadig-[20px] text-[14px] font-[500] text-[#3C455D]'>
                {moment(contract?.starts?.split(' - ')?.[0]).format('d MMM YYYY')}
              </span>{' '}
              to{' '}
              <span className='rubik leadig-[20px] text-[14px] font-[500] text-[#3C455D]'>
                {moment(contract?.ends?.split(' - ')?.[0]).format('d MMM YYYY')}
              </span>
            </Text>
            <Text className='rubik leadig-[20px] text-[14px] font-normal text-[#383838]'>
              <span className='rubik leadig-[20px] text-[14px] font-[500] text-[#3C455D]'>{contract?.starts?.split(' - ')?.[1]}</span> to{' '}
              <span className='rubik leadig-[20px] text-[14px] font-[500] text-[#3C455D]'>{contract?.ends?.split(' - ')?.[1]}</span>
            </Text>
          </View>
        </View>

        <Text className='rubik mt-[15px] text-[14px] font-normal leading-[20px] text-[#1A1A1A]'>
          Job and payment
          <a href='/terms-of-use' className='ml-1 font-[400] text-blue-500' target='_blank' rel='noreferrer'>
            {' '}
            Terms & Conditions{' '}
          </a>
        </Text>

        <View className='mt-[20px] flex flex-row justify-between'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='border-neutral bg-background-base mr-[10px] flex h-[48px] w-[173px] items-center justify-center gap-2 rounded-[8px] border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#323C58]'>No, go back</Text>
          </Button>
          <Button
            onClick={() => {
              handleAcceptContract();
              deactivate();
            }}
            className='border-neutral bg-background-base flex h-[48px] w-[173px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2 !text-white'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Confirm</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmContractModal;
