// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Button, Text, View, Select, Image, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { headerLogo } from '../../../assets/images';

import { useRegistrationContext } from 'src/context/RegistrationContext';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorSecondStepFlow: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [isFrontCameraOpen, setIsFrontCameraOpen] = useState(false);
  const [isBackCameraOpen, setIsBackCameraOpen] = useState(false);

  const [frontStream, setFrontStream] = useState<MediaStream | null>(null);
  const [backStream, setBackStream] = useState<MediaStream | null>(null);
  const [uploadedPhotoFront, setUploadedPhotoFront] = useState<string | null>(null);
  const [uploadedPhotoBack, setUploadedPhotoBack] = useState<string | null>(null);

  const [frontImage, setFrontImage] = useState<string | string[] | null>(null);
  const [backImage, setBackImage] = useState<string | string[] | null>(null);

  const [frontImageUpload, setFrontImageUpload] = useState<string | string[] | null>(null);
  const [backImageUpload, setBackImageUpload] = useState<string | string[] | null>(null);
  const [documentType, setDocumentType] = useState<string | null>(null);
  const { active, activate, deactivate } = useToggle(false);

  const { operatorRegisterData, setOperatorRegisterData } = useRegistrationContext();

  const [formValid, setFormValid] = useState({
    documentType: true,
    frontImage: true,
    backImage: true,
  });

  const openFrontCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
      });
      setFrontStream(cameraStream);
      setIsFrontCameraOpen(true);
    } catch (error) {}
  };

  const openBackCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
      });
      setBackStream(cameraStream);
      setIsBackCameraOpen(true);
    } catch (error) {}
  };

  const closeFrontCamera = () => {
    if (frontStream) {
      frontStream.getTracks().forEach((track) => track.stop());
    }
    setFrontStream(null);
    setIsFrontCameraOpen(false);
  };

  const closeBackCamera = () => {
    if (backStream) {
      backStream.getTracks().forEach((track) => track.stop());
    }
    setBackStream(null);
    setIsBackCameraOpen(false);
  };

  const takeFrontPicture = () => {
    if (frontStream) {
      const videoElement = document.getElementById('front-camera-feed') as HTMLVideoElement;
      const canvas = document.createElement('canvas');
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const capturedDataURL = canvas.toDataURL('image/jpeg');
        setFrontImageUpload(null);
        setFrontImage(capturedDataURL);
        const id1 = toast.show({
          title: 'Front photo uploaded successfully:',
          text: "Now, let's capture the back of your document for verification.",
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: (
            <Button variant='ghost' className='mb-[70px] h-[21px] w-[21px]' onClick={() => toast.hide(id1)}>
              <Text className='text-[12px]'>X</Text>
            </Button>
          ),
        });
      }
    }
  };

  const takeBackPicture = () => {
    if (backStream) {
      const videoElement = document.getElementById('back-camera-feed') as HTMLVideoElement;
      const canvas = document.createElement('canvas');
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const capturedDataURL = canvas.toDataURL('image/jpeg');
        setBackImageUpload(null);
        setBackImage(capturedDataURL);
        const id2 = toast.show({
          title: 'Back  photo uploaded successfully:',
          text: " Back photo uploaded successfully. We're now processing both front and back images for document verification.",
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: (
            <Button variant='ghost' className='mb-[70px] h-[21px] w-[21px]' onClick={() => toast.hide(id2)}>
              <Text className='text-[12px]'>X</Text>
            </Button>
          ),
        });
      }
    }
  };

  useEffect(() => {
    if (isFrontCameraOpen && frontStream) {
      const videoElement = document.getElementById('front-camera-feed') as HTMLVideoElement;
      videoElement.srcObject = frontStream;
    }
    if (isBackCameraOpen && backStream) {
      const videoElement = document.getElementById('back-camera-feed') as HTMLVideoElement;
      videoElement.srcObject = backStream;
    }
  }, [isFrontCameraOpen, isBackCameraOpen, frontStream, backStream]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleNextStep = () => {
    if (!documentType) {
      const id3 = toast.show({
        title: 'Enter the document type',
        text: 'This field is required.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id3)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
      return setFormValid((formValid) => ({
        ...formValid,
        documentType: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, documentType: true }));

    if (!frontImage && !uploadedPhotoFront) {
      const id4 = toast.show({
        title: 'Upload the front photo.',
        text: 'Please upload the front image for document verification.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id4)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
      return setFormValid((formValid) => ({
        ...formValid,
        frontImage: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, frontImage: true }));

    if (documentType !== 'passport' && (!backImage && !uploadedPhotoBack)) {
      const id5 = toast.show({
        title: 'Upload the back photo.',
        text: 'Please upload the back image for document verification.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id5)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
      return setFormValid((formValid) => ({
        ...formValid,
        backImage: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, backImage: true }));

    setOperatorRegisterData((prevState: any) => ({
      ...prevState,
      secondStep: {
        documentType: documentType,
        frontImage: frontImage ? frontImage : uploadedPhotoFront,
        // backImage: backImage ? backImage : uploadedPhotoBack,
        backImage: documentType === 'passport' ? '' : backImage ? backImage : uploadedPhotoBack,
      },
    }));
    closeFrontCamera();
    closeBackCamera();

    navigate('/third-step-validation-operator');
  };

  const handleFirstButtonClick = () => {
    const fileInput = document.getElementById('filesUploadInput');
    if (fileInput) {
      fileInput.click();
      fileInput.onchange = (e) => {
        const files = (e.target as HTMLInputElement).files;
        if (files && files.length > 0) {
          const fileReader = new FileReader();
          fileReader.onload = () => {
            const base64Data = fileReader.result as string;
            if (!uploadedPhotoFront) {
              setUploadedPhotoFront(base64Data);
            } else if (!uploadedPhotoBack) {
              // setUploadedPhotoBack(base64Data);
              if (documentType !== 'passport') {
                setUploadedPhotoBack(base64Data);
              } else {
                setUploadedPhotoBack('');
              }
            } else {
            }
          };
          fileReader.readAsDataURL(files[0]);
        }
      };
    }
  };

  const handleRemoveImageFirst = () => {
    setFrontImage(null);
  };

  const handleRemoveImageSecond = () => {
    setBackImage(null);
  };

  const handleRemoveUploadedImageFirst = () => {
    setFrontImageUpload(null);
  };

  const handleRemoveUploadedImageSecond = () => {
    setBackImageUpload(null);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const readers = Array.from({ length: files.length }, () => new FileReader());
      const loadedImages: string[] = [];

      const handleLoadEnd = (index: number) => () => {
        loadedImages[index] = readers[index].result as string;

        if (loadedImages.filter(Boolean).length === files.length) {
          if (documentType === 'passport') {
            setFrontImage(null);
            setBackImage(null);
            closeFrontCamera();
            closeBackCamera();
            setFrontImageUpload([loadedImages[0]]);
            setBackImageUpload('');
          } else {
            if (files.length === 1) {
              if (frontImageUpload) {
                closeBackCamera();
                setBackImage(null);
                setBackImageUpload([loadedImages[0]]);
              } else {
                closeFrontCamera();
                setFrontImage(null);
                setFrontImageUpload([loadedImages[0]]);
              }
            } else if (files.length === 2) {
              setFrontImage(null);
              setBackImage(null);
              closeFrontCamera();
              closeBackCamera();
              setFrontImageUpload([loadedImages[0]]);
              setBackImageUpload([loadedImages[1]]);
            }
          }
        }
      };

      readers.forEach((reader, index) => {
        reader.onloadend = handleLoadEnd(index);
        reader.readAsDataURL(files[index]);
      });
    }
  };

  const handleFileChange = (files: FileList | null) => {
    if (files && files.length > 0) {
      const fileReader = new FileReader();
      fileReader.onload = () => {
        const base64Data = fileReader.result as string;
        if (!uploadedPhotoFront) {
          setUploadedPhotoFront(base64Data);
        } else if (!uploadedPhotoBack && documentType !== 'passport') {
          setUploadedPhotoBack(base64Data);
        }
      };
      fileReader.readAsDataURL(files[0]);
    }
  };

  const handleRemoveUploadedImage = (imageType: 'front' | 'back') => {
    if (imageType === 'front') {
      setUploadedPhotoFront(null);
    } else {
      setUploadedPhotoBack(null);
    }
  };

  return (
    <View className='fflex mt-[20px] flex-col overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} className='btn-no-hover ' icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <CloseAccountCreatorModal active={active} deactivate={deactivate} />
      <View className='mx-auto flex  flex-col sm:w-[536px]'>
        <Text className='font-rufina-stencil leading-40 text-[32px] font-normal text-[#1A1A1A] lg:text-center'>Identity verification</Text>
        <Text className='rubik mt-[16px] text-base font-normal leading-6 text-[#323C58] lg:text-center'>
          In order to maintain a secure and professional platform, we kindly request the verification of your ID card. It is essential that the name
          on your ID card matches the name on your SIA licence.
        </Text>
        <Text className='rubik mt-[16px] text-[15px] font-medium leading-5 text-[#1A1A1A] '>Document type</Text>
        <Select
          name='documentType'
          placeholder='Choose a document type'
          className=' mt-[5px] h-[48px] w-[full]'
          options={[
            { label: 'ID card', value: 'id_card' },
            { label: 'Passport', value: 'passport' },
            { label: 'Driving licence', value: 'driving_licence' },
          ]}
          // onChange={(event) => setDocumentType(event.value)}
          onChange={(event) => {
            setDocumentType(event.value);
            if (event.value !== '') {
              setFormValid((prevState) => ({ ...prevState, documentType: true }));
            }
          }}
        />
        {!formValid.documentType && (
          <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Document type is required.</Text>
        )}
        <Text className='rubik mt-[16px] mt-[16px] text-[15px] font-medium leading-5 text-[#1A1A1A]'>
          Upload a photo of your ID document, such as a passport or driving licence.
        </Text>

        <View>
          <Button
            variant='outline'
            icon={() => (
              <>
                {isFrontCameraOpen ? (
                  <span className='material-icons-outlined -mt-0.5'>close</span>
                ) : (
                  <span className='material-icons-outlined -mt-1'>add_a_photo</span>
                )}
              </>
            )}
            onClick={isFrontCameraOpen ? closeFrontCamera : openFrontCamera}
            className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#323C58]'
          >
            {isFrontCameraOpen ? 'Close Camera' : 'Front of your document'}
          </Button>
          {isFrontCameraOpen && (
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined -mt-0.5'>photo_camera</span>}
              onClick={takeFrontPicture}
              className='border-neutral bg-background-base mt-[16px] !flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#323C58]'
            >
              Take Picture
            </Button>
          )}
          <View className='flex flex-row items-center justify-between'>
            {isFrontCameraOpen && (
              <div className='flex items-center justify-center'>
                <video id='front-camera-feed' className='h-[230px] w-[230px]' autoPlay playsInline></video>
              </div>
            )}
            {frontImage && (
              <div className='relative my-2 ml-auto flex h-full w-[230px] items-center justify-center'>
                <img src={frontImage} alt='Front Captured' />
                <button
                  className='absolute right-4 top-4 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                  onClick={handleRemoveImageFirst}
                >
                  <span className='material-icons text-[16px]'>close</span>
                </button>
              </div>
            )}
          </View>
          {documentType !== 'passport' && (
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined -mt-1'>{isBackCameraOpen ? 'close' : 'add_a_photo'}</span>}
              onClick={isBackCameraOpen ? closeBackCamera : openBackCamera}
              className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#323C58]'
            >
              {isBackCameraOpen ? 'Close Camera' : 'Back of your document'}
            </Button>
          )}
          {isBackCameraOpen && (
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined -mt-0.5'>photo_camera</span>}
              onClick={takeBackPicture}
              className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#323C58]'
            >
              Take Picture
            </Button>
          )}
          <View className='flex w-full flex-row items-center justify-between'>
            {isBackCameraOpen && (
              <div className='flex items-center justify-center'>
                <video id='back-camera-feed' className='h-[230px] w-[230px]' autoPlay playsInline></video>
              </div>
            )}
            {backImage && (
              <div className='relative my-2 ml-auto flex h-full w-[230px] items-center justify-center'>
                <img src={backImage} alt='Back Captured' />
                <button
                  className='absolute right-4 top-4 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                  onClick={handleRemoveImageSecond}
                >
                  <span className='material-icons text-[16px]'>close</span>
                </button>
              </div>
            )}
          </View>
        </View>

        <View className='my-[12px] mb-3 flex items-center'>
          <hr className='bg-neutral-faded mr-2 flex-grow' />
          <span className='rubik text-black-white-black my-0.5 w-[37px] text-center text-sm'>OR</span>
          <hr className='bg-neutral-faded ml-2 flex-grow' />
        </View>
        <View className='flex items-center justify-between gap-4'>
          {frontImageUpload && (
            <div className='relative mb-4 flex h-full max-w-[230px] items-center justify-center justify-self-start'>
              <img src={frontImageUpload} alt='Front Captured' />
              <button
                className='absolute right-4 top-4 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                onClick={handleRemoveUploadedImageFirst}
              >
                <span className='material-icons text-[16px]'>close</span>
              </button>
            </div>
          )}
          {backImageUpload && (
            <div className='relative mb-4 ml-auto flex h-full max-w-[230px] items-center justify-center justify-self-end'>
              <img src={backImageUpload} alt='Front Captured' />
              <button
                className='absolute right-4 top-4 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                onClick={handleRemoveUploadedImageSecond}
              >
                <span className='material-icons text-[16px]'>close</span>
              </button>
            </div>
          )}
        </View>
        <View className='flex flex-col'>
          <View className='flex flex-row items-center gap-3'>
            {/* <Button
              variant='outline'
              onClick={handleFirstButtonClick}
              className='w-full max-w-[536px] flex items-center justify-center rounded-lg gap-2 h-[60px] rubik text-[15px] !text-[#323C58] font-medium'
            >
              <span className='w-5 material-icons-outlined align-middle mr-2'>
                upload_2
              </span>
              <span className='align-middle font-medium'>
                Drop new photo or
                <span className='text-[#0B80E7]'> browse</span>
              </span>
            </Button> */}
            <div className='w-full'>
              <input type='file' id='filesUploadInput' style={{ display: 'none' }} onChange={(e) => handleFileChange(e.target.files)} />
              <button
                onClick={handleFirstButtonClick}
                onDragOver={(event) => event.preventDefault()}
                onDrop={(event) => {
                  event.preventDefault();
                  handleFileChange(event.dataTransfer.files);
                }}
                className='rubik flex h-[60px] w-full items-center justify-center gap-2 rounded-lg border border-[#BBC1D3] bg-[#ffff] text-[15px] font-medium !text-[#323C58] lg:w-[536px]'
              >
                <span className='material-icons-outlined mr-2 w-5 align-middle'>upload_2</span>
                <span className='align-middle font-medium'>
                  Drop new photo or
                  <span className='text-[#0B80E7]'> browse</span>
                </span>
              </button>
              <div className='flex items-center justify-between gap-4 mt-5'>
                {uploadedPhotoFront && (
                  <div className='relative mb-4 flex h-full max-w-[230px] items-center justify-center justify-self-start'>
                    <img src={uploadedPhotoFront} alt='Front Captured' />
                    <button
                      className='absolute right-4 top-4 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                      onClick={() => handleRemoveUploadedImage('front')}
                    >
                      <span className='material-icons text-[16px]'>close</span>
                    </button>
                  </div>
                )}
                {uploadedPhotoBack && (
                  <div className='relative mb-4 flex h-full max-w-[230px] items-center justify-center justify-self-start'>
                    <img src={uploadedPhotoBack} alt='Front Captured' />
                    <button
                      className='absolute right-4 top-4 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                      onClick={() => handleRemoveUploadedImage('back')}
                    >
                      <span className='material-icons text-[16px]'>close</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </View>
          <input type='file' accept='image/*' onChange={handleImageUpload} style={{ display: 'none' }} id='filesUploadInput' />
        </View>

        {!formValid.frontImage ||
          (!formValid.backImage && (
            <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-[#CB101D]'>Front and back document picture are required.</Text>
          ))}
        <Text className='rubik mt-[10px] text-[15px] font-normal leading-6 text-[#323C58]'>· Please make sure it’s bright and clear</Text>
        <Text className='rubik text-[15px] font-normal leading-6 text-[#323C58] '>· All corners of the document should be visible</Text>
      </View>

      <View className='mt-[20px] flex  flex-col sm:mt-[123px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full bg-[#0B80E7] lg:w-[660px]' />
          <div className='h-full w-full bg-[#D7D4D4]' />
        </div>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            icon={() => <span className='material-icons-outlined text-[19px] text-[#14171F]'>arrow_back_ios</span>}
            onClick={handleGoBack}
            className='bg-background-base flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[white]  px-4  py-2 sm:w-[103px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#14171F]'>Back</Text>
          </Button>

          <Button
            endIcon={() => <span className='material-icons-outlined text-[18px] text-[#FFF]'>arrow_forward_ios</span>}
            onClick={handleNextStep}
            className='border-neutral bg-background-base flex h-[48px] items-center justify-center self-stretch self-stretch rounded-[8px] border  !bg-[#0B80E7] sm:w-[135px] '
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#FFF]'> Next Step</Text>
          </Button>
        </View>
        <div className='mt-[30px] flex items-center justify-center sm:mt-[0px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default OperatorSecondStepFlow;
