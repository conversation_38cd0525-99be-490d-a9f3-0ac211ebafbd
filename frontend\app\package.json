{"name": "surely_front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:themes": "reshaped theming --output src/themes", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build:css": "tailwindcss -i src/index.css -o public/output.css -w", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.1.1", "@reduxjs/toolkit": "^1.9.5", "@stripe/react-stripe-js": "^3.0.0", "@stripe/stripe-js": "^5.2.0", "axios": "^1.4.0", "camelcase-keys": "^8.0.2", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "google-maps-react": "^2.0.6", "js-cookie": "^3.0.5", "laravel-echo": "^1.15.3", "lodash": "^4.17.21", "moment": "^2.29.4", "plop": "^3.1.2", "pusher-js": "^8.4.0-rc2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.45.0", "react-linkedin-login-oauth2": "^2.0.1", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.13.0", "react-select": "^5.8.0", "react-switch": "^7.0.0", "react-to-print": "^2.15.1", "redux-persist": "^6.0.0", "reshaped": "file:vendor/reshaped.tgz", "yup": "^1.2.0"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^22.10.1", "@types/react": "^18.0.37", "@types/react-beautiful-dnd": "^13.1.5", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react-swc": "^3.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.24", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.3.2", "typescript": "^5.0.2", "vite": "^4.3.9"}}