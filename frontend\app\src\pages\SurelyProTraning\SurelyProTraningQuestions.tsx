// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Button, Text, View, Image, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { headerLogo } from '../../assets/images';
import CancelTestModal from './CancelTestModal/CancelTestModal';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

const SurelyProTraningQuestions: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [correctAnswers, setCorrectAnswers] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(false);
  
  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const { active: active2, activate: activate2, deactivate: deactivate2 } = useToggle(false);
  const { state } = useLocation();

  const { type, text, questionSet, document, downloadDocument, video, minutes } = state || {
    type: '',
    text: '',
    questionSet: [],
    document: '',
    downloadDocument: '',
    video: '',
    minutes: '',
  };

  const [remainingTime, setRemainingTime] = useState(minutes * 60);
  const [timerActive, setTimerActive] = useState(false);
  useEffect(() => {
    if (minutes > 0) {
      setRemainingTime(minutes * 60);
      setTimerActive(true);
      const timerInterval = setInterval(() => {
        setRemainingTime(prevTime => {
          if (prevTime <= 1) {
            clearInterval(timerInterval);
            setTimeElapsed(true);
            setTimerActive(false);
            toast.show({
              title: 'Time’s Up!',
              text: 'The allocated time has passed. Please try again later.',
              startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
            });
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
      return () => clearInterval(timerInterval);
    }
  }, [minutes]);

  const formattedTime = () => {
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };


  const currentQuestion = questionSet[currentQuestionIndex];

  const { id, question, answer_1, answer_2, answer_3, answer_4, answer_5, answer_6, answer_7, answer_8, answer_9, answer_10, correct_answer } =
    currentQuestion || {};
  const handleCheckboxChange = (value: number) => {
    setSelectedAnswer(value === selectedAnswer ? null : value);
  };

  const isCheckboxSelected = (value: number) => selectedAnswer === value;

  const handleNextQuestion = () => {
    if (selectedAnswer !== null) {
      if (selectedAnswer === correct_answer) {
        setCorrectAnswers((prevCorrectAnswers) => prevCorrectAnswers + 1);
      }

      if (currentQuestionIndex < questionSet.length - 1) {
        setCurrentQuestionIndex((prevIndex) => prevIndex + 1);
        setSelectedAnswer(null);
      } else {
        if (currentQuestionIndex === questionSet.length - 1) {
          navigate('/surelypro-traning-points', {
            state: {
              totalQuestions: questionSet.length,
              correctAnswers: correctAnswers + (selectedAnswer === correct_answer ? 1 : 0),
              text,
              type,
              document,
              downloadDocument,
              video,
              questionSet,
            },
          });
        }
      }
    }
  };

  useEffect(() => {
    if (minutes > 0) {
      const timer = setTimeout(() => {
        setTimeElapsed(true);
        toast.show({
          title: 'Time’s Up!',
          text: 'The allocated time has passed. Please try again later.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }, minutes * 60000);
      return () => clearTimeout(timer);
    }
  }, [minutes]);

  return (
    <View className='flex  min-h-screen flex-col items-center justify-center gap-4 p-5 xl:w-[1320px]'>
      <View className='flex space-between w-full items-end justify-between  bg-[C85E1D]'>
      <div>
      {timerActive ? (
        <Text className='rubik mt-[16px] text-center text-[16px] font-normal leading-[24px] text-[#323C58]'>Time Remaining: {formattedTime()}</Text>
      ) : (
        <Text className='rubik mt-[16px] text-center text-[16px] font-normal leading-[24px] text-[#323C58]'>No timer is active.</Text>
      )}
    </div>
        <Button
          className='btn-no-hover !bg-transparent'
          onClick={activate1}
          icon={() => <span className='material-icons mt-[-3px] '>close</span>}
        ></Button>
      </View>

      <CancelTestModal active={active1} deactivate={deactivate1} />


      <View className='mx-auto flex w-full flex-col justify-center sm:w-[536px]'>
        <Text className='font-rufina-stencil text-center text-[32px] font-normal leading-[40px] text-[#1A1A1A]'>Question {id}</Text>
        <Text className='rubik mt-[16px] text-center text-[16px] font-normal leading-[24px] text-[#323C58]'>{question}</Text>
        <View className='mx-auto mt-4 flex w-full max-w-[488px] flex-col gap-4'>
          <View
            className={`flex h-[auto] w-full flex-row items-center justify-between gap-[12px] rounded-md border-[1px] p-4 ${
              isCheckboxSelected(1) ? 'border-[#0B80E7] bg-[#DBDFEA]' : 'border-[#DFE2EA] bg-[#F4F5F7]'
            }`}
          >
            <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323C58]'>
              A. <span className='text-[15px] font-medium leading-[20px] text-[#1A1A1A]'>{answer_1}</span>
            </Text>
            <label className='relative flex cursor-pointer items-center'>
              <input
                type='checkbox'
                id={`answer_${id}_${1}`}
                name={`answer_${id}`}
                checked={isCheckboxSelected(1)}
                onChange={() => handleCheckboxChange(1)}
                className='sr-only'
              />
              <span
                className={`border-neutral flex h-5 w-5 items-center justify-center rounded border-2 ${
                  isCheckboxSelected(1) ? 'bg-[#0B80E7]' : 'bg-[#FFFF]'
                }`}
              >
                {isCheckboxSelected(1) && <span className='material-icons-outlined text-[#FFFF]'>done</span>}
              </span>
            </label>
          </View>

          <View
            className={`flex h-[auto] w-full flex-row items-center justify-between gap-[12px] rounded-md border-[1px] p-4 ${
              isCheckboxSelected(2) ? 'border-[#0B80E7] bg-[#DBDFEA]' : 'border-[#DFE2EA] bg-[#F4F5F7]'
            }`}
          >
            <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323C58]'>
              B. <span className='text-[15px] font-medium leading-[20px] text-[#1A1A1A]'>{answer_2}</span>
            </Text>
            <label className='relative flex cursor-pointer items-center'>
              <input
                type='checkbox'
                id={`answer_${id}_${2}`}
                name={`answer_${id}`}
                checked={isCheckboxSelected(2)}
                onChange={() => handleCheckboxChange(2)}
                className='sr-only'
              />
              <span
                className={`border-neutral flex h-5 w-5 items-center justify-center rounded border-2 ${
                  isCheckboxSelected(2) ? 'bg-[#0B80E7]' : 'bg-[#FFFF]'
                }`}
              >
                {isCheckboxSelected(2) && <span className='material-icons-outlined text-[#FFFF]'>done</span>}
              </span>
            </label>
          </View>

          <View
            className={`flex h-[auto] w-full flex-row items-center justify-between gap-[12px] rounded-md border-[1px] p-4 ${
              isCheckboxSelected(3) ? 'border-[#0B80E7] bg-[#DBDFEA]' : 'border-[#DFE2EA] bg-[#F4F5F7]'
            }`}
          >
            <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323C58] '>
              C. <span className='text-[15px] font-medium leading-[20px] text-[#1A1A1A]'>{answer_3}</span>
            </Text>
            <label className='relative flex cursor-pointer items-center'>
              <input
                type='checkbox'
                id={`answer_${id}_${3}`}
                name={`answer_${id}`}
                checked={isCheckboxSelected(3)}
                onChange={() => handleCheckboxChange(3)}
                className='sr-only'
              />
              <span
                className={`border-neutral flex h-5 w-5 items-center justify-center rounded border-2 ${
                  isCheckboxSelected(3) ? 'bg-[#0B80E7]' : 'bg-[#FFFF]'
                }`}
              >
                {isCheckboxSelected(3) && <span className='material-icons-outlined text-[#FFFF]'>done</span>}
              </span>
            </label>
          </View>

          <View
            className={`flex h-[auto] w-full flex-row items-center justify-between gap-[12px] rounded-md border-[1px] p-4 ${
              isCheckboxSelected(4) ? 'border-[#0B80E7] bg-[#DBDFEA]' : 'border-[#DFE2EA] bg-[#F4F5F7]'
            }`}
          >
            <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323C58]'>
              D. <span className='text-[15px] font-medium leading-[20px] text-[#1A1A1A]'>{answer_4}</span>
            </Text>
            <label className='relative flex cursor-pointer items-center'>
              <input
                type='checkbox'
                id={`answer_${id}_${4}`}
                name={`answer_${id}`}
                checked={isCheckboxSelected(4)}
                onChange={() => handleCheckboxChange(4)}
                className='sr-only'
              />
              <span
                className={`border-neutral flex h-5 w-5 items-center justify-center rounded border-2 ${
                  isCheckboxSelected(4) ? 'bg-[#0B80E7]' : 'bg-[#FFFF]'
                }`}
              >
                {isCheckboxSelected(4) && <span className='material-icons-outlined text-[#FFFF]'>done</span>}
              </span>
            </label>
          </View>
        </View>
      </View>

      <View className='flex w-full flex-col gap-[20px] xl:mt-[118px] xl:w-[1320px] xl:gap-0'>
        <Text className='rubik text-center text-[16px] font-bold leading-[24px] text-[#444B5F]'>
          {id} of {questionSet.length}
        </Text>
        <div className='mt-[27px] flex h-[6px] w-full'>
          <div
            style={{
              width: `${(id / questionSet.length) * 100}%`,
            }}
            className='h-full bg-[#0B80E7]'
          />
          <div
            style={{
              width: `${100 - (id / questionSet.length) * 100}%`,
            }}
            className='h-full bg-[#D7D4D4]'
          />
        </div>
        <View className='mt-[16px] flex flex-row justify-between gap-[20px]  lg:gap-0'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons-outlined p-0 pb-1 text-[18px]'>close</span>}
            onClick={activate2}
            className='rubik border-neutral  bg-background-base flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[white] px-4 py-2 text-[15px] !text-[#000000] sm:w-[180px]'
          >
            Close
          </Button>
          <CancelTestModal active={active2} deactivate={deactivate2} />

          {!timeElapsed && (
            <Button
              endIcon={() => <span className='material-icons-outlined p-0 pb-1 text-[20px]'>arrow_forward_ios</span>}
              onClick={handleNextQuestion}
              className='rubik border-neutral bg-background-base flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[#0B80E7] px-4 py-2 text-[15px] !text-white sm:w-[180px]'
            >
              {currentQuestionIndex < questionSet.length - 1 ? 'Next question' : 'Submit'}
            </Button>
          )}
        </View>
        <div className='flex items-center justify-center'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default SurelyProTraningQuestions;
