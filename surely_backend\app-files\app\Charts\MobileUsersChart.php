<?php

namespace App\Charts;

use ArielMejiaDev\LarapexCharts\LarapexChart;

class MobileUsersChart
{
    protected $chart;

    public function __construct(LarapexChart $chart)
    {
        $this->chart = $chart;
    }

    public function build($clients, $operatives): \ArielMejiaDev\LarapexCharts\PieChart
    {
        return $this->chart->pieChart()
            ->setTitle('Users of Surely')
            ->setColors(['#323C58', 'rgb(0, 143, 251)'])
            ->addData([$operatives, $clients])
            ->setLabels(['Operatives', 'Clients'])
            ->setHeight(310);
    }
}
