// @ts-nocheck
import React, { useContext, useEffect } from 'react';
import { Container, useToast, Image, Button } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import Header from '../components/Header/Header';
import ClientSearchBarTwo from 'src/components/ClientSearchBar/ClientSearchBarTwo';
import { AppContext } from 'src/context/AppContext';
import surleyicon from '../assets/icons/surleyicon/surleyicon.png';
import { EmailVerificationBanner } from 'src/components/EmailVerificationBanner';

interface Props {
  children?: React.ReactNode;
}

export const DashboardLayout = ({ children }: Props): JSX.Element => {
  const { clientRepresentBusinessAs, companyRegisteredNumber, hasCreditCard } = useContext(AppContext);
  const toast = useToast();
  const navigate = useNavigate();
  const isCreditCardUnavailable = hasCreditCard === null || hasCreditCard === undefined;
  const isDataUnavailable = clientRepresentBusinessAs === null || clientRepresentBusinessAs === undefined;

  useEffect(() => {
    if (isCreditCardUnavailable) {
      const id = toast.show({
        title: 'Welcome to our platform!',
        text: (
          <div className='h-max space-y-4 py-4'>
            <div className='flex gap-3'>
              <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
              <h2 className=''>In order to start hiring, you will need to fill in your card details in the payment settings section.</h2>
            </div>
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => {
                toast.hide(id);
                navigate('/client-settings-payment', { state: { activeTab: '2' } });
              }}
            >
              Proceed to settings
            </Button>
          </div>
        ),
        timeout: 50000000,
      });
      return () => toast.hide(id);
    }
  }, [isCreditCardUnavailable]);

  useEffect(() => {
    if (isDataUnavailable) {
      const id = toast.show({
        title: <div className='ml-[42px]'>Welcome to our platform!</div>,
        text: (
          <div className='h-max space-y-4 py-4'>
            <div className='flex gap-3'>
              <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
              <h2 className=''>
                Enhance your chances of being selected by security operatives by completing the basic validation information of your company.
              </h2>
            </div>
            <Button
              className='border-neutral bg-background-base ml-[42px] mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => {
                toast.hide(id);
                navigate('/first-step-validation-client');
              }}
            >
              Validate your profile
            </Button>
          </div>
        ),
        timeout: 50000000,
      });

      return () => toast.hide(id);
    }
  }, [isDataUnavailable]);

  return (
    <Container padding={0} className="mb-[92px] h-[110%] min-h-[100vh] w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover  bg-repeat-y  ">
      <Header />
      <EmailVerificationBanner />
      <main className=' align-center  mx-auto mt-[30px] w-full max-w-[1320px]   text-center    xl:mt-[62px] '>
        <section className='w-full  '>
          <ClientSearchBarTwo />
          <article className='mt-[43px] flex flex-wrap gap-5 pb-[43px]'>{children}</article>
          <div className='shadow-lg'>
            {/* {isDataUnavailable && <ClientValidateToast showCardToast={showCardToast} />} */}
            {/* <ClientValidateToast showCardToast={showCardToast} /> */}
            {/* {!hasCreditCard && <ClientBankToast onClose={() => setShowBankToast((prev) => !prev)} />} */}
          </div>
        </section>
      </main>
    </Container>
  );
};

export default DashboardLayout;
