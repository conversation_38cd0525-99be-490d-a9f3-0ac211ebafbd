// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Text, View, Switch, Divider, Button, useToast, Image, Breadcrumbs } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { addAvailability, getAvailability } from 'src/services/settings';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { AppContext } from 'src/context/AppContext';

const OperatorSettingsAvailability: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { fetchAppData } = useContext(AppContext);
  const [instantBook, setInstantBook] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSaveSettings = async () => {
    setIsSaving(true);
    const instantBookValue = instantBook ? 1 : 0;
    await addAvailability({ instantBook: instantBookValue })
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
      });
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  useEffect(() => {
    getAvailability().then((data: any) => {
      setInstantBook(!!data.instant_book);
    });
  }, []);

  return (
    <View className='flex flex-col justify-between overflow-hidden px-[12px] lg:flex-row'>
      <View className='flex flex-col  sm:w-[536px]'>
        <Breadcrumbs className='mb-[20px] flex items-baseline'>
          <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
            <span className='rubik text-[16px] text-[#3C455D]'>Profile</span>
          </Breadcrumbs.Item>
          <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
            <span className='rubik text-[16px] text-[#3C455D]'>Account settings</span>
          </Breadcrumbs.Item>
          <Breadcrumbs.Item onClick={() => {}}>
            <span className='rubik text-[16px] font-medium text-[#1A1A1A]'>Availability</span>
          </Breadcrumbs.Item>
        </Breadcrumbs>
        <Text className='text-foreground-neutral font-rufina-stencil text-[#323C58] lg:text-[32px] xl:leading-[20px]'>Availability</Text>
        <Text className='text-neutral rubik mt-[28px] text-[16px] font-medium leading-4'>Instant book</Text>
        <View className='flex w-full max-w-[488px] flex-row gap-[32px]'>
          <Text className='text-neutral-faded rubik mt-[5px] text-sm font-normal leading-[20px]'>
            Companies can quickly find and hire professionals like you, connecting you with immediate job opportunities. This feature will be
            deactivated daily at midnight, requiring you to manually enable it if you wish to be considered. A notification will be sent every morning
            at 7 am.
          </Text>
          <Switch name='switch' checked={instantBook} onChange={(event) => setInstantBook(event.checked)}></Switch>
        </View>
        <Divider className='mt-[36px] h-[1px] w-full'></Divider>

        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={() => navigate('/operator-settings')}
            className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleSaveSettings().then(() => {
                toast.show({
                  title: 'Done!',
                  text: 'You have updated your profile',
                  startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                });
              });
            }}
            className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
              isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
            } h-[48px] sm:w-[260px]`}
          >
            {isSaving ? 'Saving...' : 'Save settings'}
          </Button>
        </View>
      </View>
      <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
    </View>
  );
};

export default OperatorSettingsAvailability;
