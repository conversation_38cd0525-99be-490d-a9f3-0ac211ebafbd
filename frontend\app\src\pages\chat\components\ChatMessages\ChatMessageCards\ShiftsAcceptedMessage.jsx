import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useChatContext } from 'src/context/ChatContext';

const ShiftsAcceptedMessage = ({ message, shiftStatus, hourly }) => {
  const { contract } = useChatContext();
  const nameShown = contract?.operative?.name;
  const contractId = message?.contract_id;

  if (hourly) {
    return (
      <div className='flex items-center gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-1 text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>{message?.message}</h2>
          <div>
            By:{' '}
            <span className='text-base font-medium'>
              {message?.sender?.name || message?.sender_id === contract?.client?.id ? contract?.client?.name : contract?.operative?.name}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (shiftStatus === 'changed') {
    return (
      <div className='flex items-center gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-1 text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>{message?.message}</h2>
          <div>
            By:{' '}
            <span className='text-base font-medium'>
              {message?.sender?.name || message?.sender_id === contract?.client?.id ? contract?.client?.name : contract?.operative?.name}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (shiftStatus === 'accepted') {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Contract terms accepted</h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            <span className='text-[14px] font-medium leading-5 text-[#323C58]'>{nameShown}</span> accepted terms for{' '}
            <span className='text-[14px] font-medium leading-5 text-[#323C58]'>#{contractId}</span>
          </p>
        </div>
      </div>
    );
  }

  if (shiftStatus === 'confirmed') {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Shift hours confirmed</h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            <span className='text-[14px] font-medium leading-5 text-[#323C58]'>{nameShown}</span> confirmed shifts for{' '}
            <span className='text-[14px] font-medium leading-5 text-[#323C58]'>#{contractId}</span>
          </p>
        </div>
      </div>
    );
  }

  if (shiftStatus === 'reviewed') {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Shift hours reviewed</h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            <span className='text-[14px] font-medium leading-5 text-[#323C58]'>{nameShown}</span> confirmed shifts for{' '}
            <span className='text-[14px] font-medium leading-5 text-[#323C58]'>#{contractId}</span>
          </p>
        </div>
      </div>
    );
  }
};

export default ShiftsAcceptedMessage;
