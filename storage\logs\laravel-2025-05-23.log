[2025-05-23 15:52:33] production.ERROR: SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution (SQL: select * from information_schema.tables where table_schema = surelyback and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution (SQL: select * from information_schema.tables where table_schema = surelyback and table_name = migrations and table_type = 'BASE TABLE') at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php:712)
[stacktrace]
#0 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(764): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(675): Illuminate\\Database\\Connection->handleQueryException()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(376): Illuminate\\Database\\Connection->run()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#11 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#13 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#16 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#17 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#18 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#19 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#20 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#21 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#22 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#23 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#24 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1064): call_user_func()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback()
#12 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(764): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(675): Illuminate\\Database\\Connection->handleQueryException()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(376): Illuminate\\Database\\Connection->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select()
#16 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#17 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#22 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#26 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#27 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#28 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#29 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#30 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#33 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#34 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}

[previous exception] [object] (PDOException(code: 0): PDO::__construct(): php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1064): call_user_func()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback()
#12 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(764): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(675): Illuminate\\Database\\Connection->handleQueryException()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(376): Illuminate\\Database\\Connection->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select()
#16 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#17 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#22 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#26 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#27 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#28 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#29 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#30 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#33 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#34 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}
"} 
[2025-05-23 15:55:17] production.ERROR: SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution (SQL: select * from information_schema.tables where table_schema = surelyback and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution (SQL: select * from information_schema.tables where table_schema = surelyback and table_name = migrations and table_type = 'BASE TABLE') at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php:712)
[stacktrace]
#0 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(764): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(675): Illuminate\\Database\\Connection->handleQueryException()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(376): Illuminate\\Database\\Connection->run()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#11 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#13 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#16 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#17 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#18 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#19 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#20 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#21 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#22 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#23 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#24 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1064): call_user_func()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback()
#12 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(764): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(675): Illuminate\\Database\\Connection->handleQueryException()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(376): Illuminate\\Database\\Connection->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select()
#16 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#17 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#22 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#26 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#27 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#28 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#29 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#30 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#33 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#34 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}

[previous exception] [object] (PDOException(code: 0): PDO::__construct(): php_network_getaddresses: getaddrinfo for mysql-8 failed: Temporary failure in name resolution at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1064): call_user_func()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback()
#12 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(764): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(675): Illuminate\\Database\\Connection->handleQueryException()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Connection.php(376): Illuminate\\Database\\Connection->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select()
#16 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable()
#17 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#22 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#26 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#27 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#28 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#29 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#30 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#32 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#33 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#34 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}
"} 
