import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';

const EventSecurityButton: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="items-stretch self-stretch border border-[color:var(--border-neutral-faded,#DFE2EA)] shadow-sm bg-gray-100 flex grow flex-col w-full p-6 rounded-lg border-solid max-md:max-w-full max-md:mt-6 max-md:px-5">
    <div className="text-slate-700 text-xl leading-7">
    Event Security Jobs
    </div>
    <div className="items-start bg-zinc-200 flex shrink-0 h-px flex-col mt-3" />
    <div className="cursor-pointer justify-between items-stretch flex gap-2 mt-3 py-1 rounded-lg">
      <div className="text-sky-600 text-lg font-medium leading-6 grow shrink basis-auto" onClick={() => navigate('/security-jobs/event-security')}>
      Find your next Event Security job
      </div>
      <img
        loading="lazy"
        src="https://cdn.builder.io/api/v1/image/assets/TEMP/9e370b81-47cb-4b92-a973-841dd81fa2b6?apiKey=6d76af1151904a3d939e9c155a702b3b&"
        className="aspect-square object-contain object-center w-6 justify-center items-center overflow-hidden shrink-0 max-w-full"
      />
    </div>
  </div>
  );
};

export default EventSecurityButton;
