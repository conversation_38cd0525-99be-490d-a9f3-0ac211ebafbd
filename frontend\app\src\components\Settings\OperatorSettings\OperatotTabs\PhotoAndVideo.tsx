// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Text, View, Button, Divider } from 'reshaped';
import { editSettings } from 'src/services/settings';
import { getAll } from 'src/services/settings';

const PhotoAndVideo: React.FC = () => {
  const [profilePhoto1, setProfilePhoto1] = useState<string[]>([]);
  const [additionalPictures, setAdditionalPictures] = useState<string[]>(['', '', '']);
  const [profileVideo1, setProfileVideo1] = useState<string[]>([]);

  const profilePhoto = profilePhoto1[0];
  const profileVideo = profileVideo1[0];

  const navigate = useNavigate();

  const handlethirdTab = () => {
    const thirdTabSettings: any = {
      profilePhoto,
      additionalPictures,
      profileVideo,
    };
    editSettings(thirdTabSettings);
  };

  const handleImageUploadFirst = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfilePhoto1([reader.result as string]);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImageFirst = () => {
    setProfilePhoto1([]);
  };

  const handleFirstButtonClick = () => {
    document.getElementById('firstFileInput')?.click();
  };

  const handleImageUploadSecond = (index: number) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const newImages = [...additionalPictures];
        newImages[index] = reader.result as string;
        setAdditionalPictures(newImages);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImageSecond = (index: number) => () => {
    const newImages = [...additionalPictures];
    newImages[index] = '';
    setAdditionalPictures(newImages);
  };

  const handleThirdButtonClick = () => {
    document.getElementById('thirdFileInput')?.click();
  };

  const handleImageUploadThird = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileVideo1([reader.result as string]);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImageThird = () => {
    setProfileVideo1([]);
  };

  const handleSecondTab = () => {
    const secondTabSettings: any = {};
    editSettings(secondTabSettings);
  };

  useEffect(() => {
    getAll().then((data: any) => {
      const modifiedData = { ...data };
      if (data.profilePhoto) {
        modifiedData.profilePhoto = `http://192.168.0.103:8000/storage/${data.profilePhoto}`;
      }
      if (data.profileVideo) {
        modifiedData.profileVideo = `http://192.168.0.103:8000/storage/${data.profileVideo}`;
      }
      setProfilePhoto1([modifiedData.profilePhoto]);
      setProfileVideo1([modifiedData.profileVideo]);
    });
  }, []);

  return (
    <View className='flex flex-col mt-[16px]'>
      <Text className='text-neutral text-base rubik font-medium leading-4'>
        Upload your up-to-date profile picture
      </Text>

      <View className='flex flex-col mt-[16px]'>
        <View className='flex flex-row '>
          <Button
            variant='outline'
            icon={() => (
              <span className='material-icons-outlined align-middle text-black'>
                person
              </span>
            )}
            onClick={() => {}}
            className='flex items-center justify-center w-16 h-16 mt-3 rounded-full !bg-gray-300 mr-[5px]'
          ></Button>
          <Button
            variant='outline'
            icon={() => (
              <span className='material-icons-outlined align-middle text-[#0B80E7]'>
                file_download
              </span>
            )}
            onClick={handleFirstButtonClick}
            className='flex w-[452px] h-[60px] mt-[12px]'
          >
            <span className='align-middle'>Drop new photo or browse</span>
          </Button>
        </View>
        {profilePhoto1.length > 0 ? (
          <div className='relative w-[170px] h-[136px] ml-[35%] mt-[10px]'>
            <img
              src={profilePhoto1[0]}
              alt='First Image'
              className='w-[170px] h-[136px] object-cover rounded-md'
            />
            <button
              className='absolute top-2 right-1 p-1 bg-white rounded-full shadow-md text-gray-600 w-[22px] h-[22px] flex items-center justify-center transform translate-x-1/2 -translate-y-1/2'
              onClick={handleRemoveImageFirst}
            >
              <span className='material-icons text-[16px]'>close</span>
            </button>
          </div>
        ) : (
          <input
            type='file'
            accept='image/*'
            onChange={handleImageUploadFirst}
            style={{ display: 'none' }}
            id='firstFileInput'
          />
        )}
      </View>

      <Text className='text-neutral text-base rubik font-medium leading-4 mt-[16px]'>
        Your additional pictures
      </Text>
      <View className='w-[536px] mt-[16px]'>
        <View className='flex justify-between mt-2'>
          {[...Array(3)].map((_, index) => (
            <div key={index} className='relative'>
              {additionalPictures[index] ? (
                <div className='relative'>
                  <img
                    src={additionalPictures[index] || ''}
                    alt={`Uploaded Job ${index + 1}`}
                    className='w-[170px] h-[136px] object-cover rounded-md'
                  />
                  <button
                    className='absolute top-1 right-1 p-1 bg-white rounded-full shadow-md text-gray-600 w-[22px] h-[22px] flex items-center justify-center'
                    onClick={handleRemoveImageSecond(index)}
                  >
                    <span className='material-icons text-[16px]'>close</span>
                  </button>
                </div>
              ) : (
                <div className='flex items-center justify-center w-[170px] h-[136px] border border-gray-300 rounded-md'>
                  <label htmlFor={`imageInput${index}`}>
                    <span className='material-icons text-[30px]'>add</span>
                  </label>
                  <input
                    id={`imageInput${index}`}
                    type='file'
                    accept='image/*'
                    className='hidden'
                    onChange={handleImageUploadSecond(index)}
                  />
                </div>
              )}
            </div>
          ))}
        </View>
      </View>

      <Text className='text-neutral text-base rubik font-medium leading-4 mt-[16px]'>
        Upload your profile video
      </Text>
      <View className='flex flex-row mt-[16px]'>
        <View className='flex flex-col mr-[13px]'>
          <Button
            variant='outline'
            icon={() => (
              <span className='material-icons-outlined align-middle text-[#0B80E7]'>
                file_download
              </span>
            )}
            onClick={handleThirdButtonClick}
            className='flex w-[262px] h-[66px] mt-[12px]'
          >
            <span className='align-middle'>Drop or browse</span>
          </Button>
          <View className='flex items-center mb-3 mt-[12px]'>
            <hr className='flex-grow bg-neutral-faded mr-2' />
            <span className='text-sm rubik text-black-white-black'>
              Or
            </span>
            <hr className='flex-grow bg-neutral-faded ml-2' />
          </View>
          <Button
            variant='outline'
            icon={() => (
              <span className='material-icons-outlined align-middle text-[#0B80E7]'>
                videocam
              </span>
            )}
            onClick={() => {}}
            className='flex w-[262px] h-[66px] mt-[12px]'
          >
            <span className='align-middle'>Record now</span>
          </Button>
        </View>
        {profileVideo1.length > 0 ? (
          <video
            src={profileVideo1[0]}
            controls
            className='w-[262px] h-[185px] mt-[16px]'
          >
            Your browser does not support the video tag.
          </video>
        ) : (
          <input
            type='file'
            accept='video/*'
            onChange={handleImageUploadThird}
            style={{ display: 'none' }}
            id='thirdFileInput'
          />
        )}
        {profileVideo1.length > 0 && (
          <button
            className='absolute top-5 right-1 p-1 bg-white rounded-full shadow-md text-gray-600 w-[22px] h-[22px] flex items-center justify-center'
            onClick={handleRemoveImageThird}
          >
            <span className='material-icons text-[16px]'>close</span>
          </button>
        )}
      </View>
      <View className='w-[536px]'>
        <Text className='text-neutral-faded text-base rubik font-normal leading-5 mt-[16px]'>
          Upload or record a short video, maximum 15 seconds. The file should
          have not more than 150MB.
        </Text>
      </View>
      <Divider className='w-full h-[1px] mt-[16px]'></Divider>
      <View className='flex flex-row justify-between mt-[16px]'>
        <Button
          variant='outline'
          icon={() => <span className='material-icons -mt-1'>clear</span>}
          onClick={() => navigate('/operator-settings')}
          className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded bg-background-base w-[260px] h-[48px] mr-[10px]'
        >
          Cancel
        </Button>
        <Button
          onClick={handlethirdTab}
          className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded bg-background-base !text-white !bg-[#0B80E7] w-[260px] h-[48px]'
        >
          Save settings
        </Button>
      </View>
    </View>
  );
};

export default PhotoAndVideo;
