// @ts-nocheck
import React, { useState, useContext } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Text, View, Button, Divider, Modal, TextArea, useToast, Image, Tooltip, Actionable } from 'reshaped';
import submiticon from '../../../assets/icons/submiticon/submiticon.svg';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import { applyForJob } from 'src/services/operatives';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { useChatContext } from 'src/context/ChatContext';
import moment from 'moment';
import { AppContext } from 'src/context/AppContext';

interface ApplyNowModalProps {
  active: boolean;
  deactivate: () => void;
  selectedJob: any;
  onUpdateHasApply: (newHasApply: boolean, error: string | null) => void;
  chatId: number | string;
}

const ApplyNowModal: React.FC<ApplyNowModalProps> = ({ active, deactivate, selectedJob, onUpdateHasApply = () => {}, chatId }) => {
  const navigate = useNavigate();
  const params = useParams()
  const location = useLocation()

  const [proposal, setProposal] = useState<string>();
  const [payRate, setPayRate] = useState('0');
  const [positionId, setPositionId] = useState();
  const [checkboxChecked, setCheckboxChecked] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { handleGetMessages, currentChat, getCurrentChat, contract } = useChatContext();
  const { hasBankAccount } = useContext(AppContext);

  const id = selectedJob?.id;
  const toast = useToast();

  const openInNewTab = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const applyNowJob = () => {
    if (!hasBankAccount) {
      const toastId = toast.show({
        title: 'Bank Account Required',
        text: (
          <div className='h-max space-y-4'>
            <div className='flex gap-3'>
              <h2 className=''>
                In order to apply for jobs, you need to add your bank account details first.
              </h2>
              <Image src={surleyicon} className='h-[30px] w-[30px]' />
            </div>
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => {
                toast.hide(toastId);
                navigate('/operator-settings-payment', { state: { activeTab: '0' } });
              }}
            >
              Add Bank Details
            </Button>
          </div>
        ),
        timeout: 0,
      });
      return;
    }

    if (checkboxChecked) {
      const payload: any = {
        id,
        proposal,
        payRate,
        positionId: id,
        chat_id: chatId,
        type: location?.pathname?.includes('/operator-job/') ? null : 'applied_through_invitation',
      };

      applyForJob(payload)
        .then((res) => {
          getCurrentChat(+params?.id);
          handleGetMessages(params?.id)?.then((res) => {
            const latestMessages = [];
            res?.forEach((message, index) => {
              if (index === 0) {
                latestMessages.push({
                  type: 'dayChange',
                  dateChange: moment(message.created_at).format('D/M/YYYY'),
                });
              }

              latestMessages.push(message);

              const currentDay = moment(message.created_at).format('YYYY, M, D');
              const nextDay = moment(res[index + 1]?.created_at).format('YYYY, M, D');

              if (currentDay !== nextDay) {
                if (moment(message.created_at).format('YYYY, M, D') !== moment(res?.[res?.length - 1]?.created_at).format('YYYY, M, D')) {
                  latestMessages.push({
                    type: 'dayChange',
                    dateChange: moment(message.created_at).format('D/M/YYYY'),
                  });
                }
              }
            });
          });

          if (typeof res === 'string') {
            toast.show({
              title: 'Cancelled.',
              text: res,
              startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
            });
            onUpdateHasApply(false, res);
          } else {
            toast.show({
              title: 'Done!',
              text: 'You have applied for this job.',
              startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
            });
            onUpdateHasApply(true, '');
          }
          deactivate();
        })
        .catch((error) => {});
    } else {
      setError('Please agree to the Terms & Conditions');
      toast.show({
        title: '',
        text: 'Please agree to the Terms & Conditions',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
  };

  return (
    <Modal
      active={active}
      onClose={() => {
        setError(null);
        deactivate();
      }}
      className='!h-[auto] !w-[424px]'
    >
      <View className='flex flex-col '>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Apply Now</Text>
        </View>
        <View className='mt-[16px] flex flex-row gap-3'>
          <Text className='rubik text-[15px] font-medium leading-[20px] text-[#1A1A1A] '>Your proposal (optional)</Text>
          <Tooltip text='Your proposal will serve as a first impression to potential clients and showcases what makes you the perfect fit for this role. Make sure you stand out.'>
            {(attributes) => (
              <Actionable attributes={attributes} as='div'>
                <div className='flex h-[20px] w-[20px] items-center justify-center  rounded-full border bg-[#C7CDDB]'>
                  <span className='material-icons-outlined text-[12px]'>question_mark</span>
                </div>
              </Actionable>
            )}
          </Tooltip>
        </View>
        <TextArea
          name='present_yourself'
          placeholder='Present yourself, be creative...'
          className='mt-[10px] w-[393px]'
          onChange={(e) => setProposal(e.value)}
        />
        <div className='mb-3 mt-[12px] flex items-center'>
          <input type='checkbox' className='mr-2 h-4 w-4' checked={checkboxChecked} onChange={(e) => setCheckboxChecked(e.target.checked)} />
          <section className='rubik text-[14px] font-normal leading-[20px] text-[#1A1A1A] '>
            I agree to the job and payment&nbsp;
            <a
              // href='terms-of-use'
              target='_blank'
              onClick={() => openInNewTab('https://surelysecurity.com/terms-of-use')}
              className='rubik text-[14px] font-normal leading-[20px] text-[#388DD8]'
            >
              Terms & Conditions
            </a>
          </section>
        </div>
        {error && <Text className=' rubik mb-2 text-sm text-[#CB101D]'>{error}</Text>}
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
            endIcon={() => <img src={submiticon} />}
            onClick={() => {
              applyNowJob();
            }}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ApplyNowModal;
