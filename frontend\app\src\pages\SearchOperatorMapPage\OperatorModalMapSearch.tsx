// @ts-nocheck
import React, { useState,useContext } from 'react';
import { Modal, Image, Text, View, Button, Divider, Icon,useToast } from 'reshaped';

import { useNavigate } from 'react-router-dom';
import { IdVerified } from 'src/assets/icons';
import inclusivitypledgeicon from '../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import clientdropdownicon2 from '../../assets/icons/clientdropdownicon/clientdropdownicon2.svg';
import { OperativesContext } from 'src/context/OperativesContext';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

interface OperatorModalMapSearchProps {
  active: boolean;
  deactivate: () => void;
  selectedOperator: any | null;
}

const OperatorModalMapSearch: React.FC<
  OperatorModalMapSearchProps
> = ({ active, deactivate,
    selectedOperator
 }) => {
  const navigate = useNavigate();
  const {addFavorite, removeFavorite, fetchAllOperative} =
    useContext(OperativesContext);

  const onlycity = selectedOperator?.address_2?.split(', ', 1).pop();
  const toast = useToast();
  const [isFavourite, setIsFavourite] = useState(selectedOperator?.is_favorite || false);
  const baseURL = 'https://app.surelysecurity.com/storage/';

  return (
    <Modal active={active} onClose={deactivate} className='!w-[312px] '>
           {selectedOperator && (
        <>

        <View className='flex items-center justify-between '>
          <div className='flex items-center gap-[8px]'>
            {selectedOperator.sia_certificates?.[0]?.sia_licence === 1  ? (
              <span className='text-[#05751F] text-xs rubik font-normal leading-4 !bg-[#E6FEF3] rounded-md p-1 w-[auto] h-[auto] ml-[5px]'>
                SIA CERTIFIED
              </span>
            ) : (
              <span className='text-[#6A6A6A] text-xs rubik font-normal leading-4 !bg-[#EDEDED] rounded-md p-1 w-[auto] h-[auto] ml-[5px]'>
                SIA PENDING
              </span>
            )}
          </div>
          <span
            onClick={() => {
              setIsFavourite((prevFavourite: any) => !prevFavourite);
              if (isFavourite) {
                removeFavorite(selectedOperator.id);
                toast.show({
                  title: 'Done!',
                  text: 'Removed from favorites',
                  startSlot: <Image src={surleyicon} className='w-[30px] h-[30px]' />,
                });
                fetchAllOperative();
              } else {
                addFavorite(selectedOperator.id);
                toast.show({
                  title: 'Done!',
                  text: 'Added to favorites',
                  startSlot: <Image src={surleyicon} className='w-[30px] h-[30px]' />,
                });
                fetchAllOperative();
              }
            }}
            className={`material-icons-outlined -mt-1 text-base text-[20px] ${
              isFavourite ? 'text-red-500' : 'text-gray-500'
            }`}
          >
            favorite
          </span>
        </View>
          <View className='flex items-center p-0 mt-[16px]'></View>
          <View className='mt-[16px]'>
            <div className='flex flex-row items-center gap-4 mt-[7px]'>
              <View>
                {selectedOperator.profile_photo ? (
                  <div
                    className='rounded-full p-0.5'
                    style={{
                      background:
                        'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                    }}
                  >
                    <Image
                      className='flex flex-col w-[80px] h-[80px] items-start rounded-full bg-white'
                      alt='Profile'
                      src={selectedOperator?.profile_photo?.startsWith(baseURL) ? selectedOperator?.profile_photo : baseURL + selectedOperator?.profile_photo}
                    />
                  </div>
                ) : (
                  <View className='w-[80px] h-[80px]  flex items-center justify-center  bg-[#0B80E7] rounded-full'>
                    <Text className='text-white text-[30px] rubik'>
                      {selectedOperator.name?.charAt(0)}
                    </Text>
                  </View>
                )}
                <Icon
                  className='absolute bottom-0 right-0 w-[30px] h-[31px]'
                  svg={IdVerified}
                />
              </View>
              <div className='flex flex-col justify-start gap-1'>
                {/* <div className='flex items-center gap-2'>
                  <span className='material-icons text-yellow-400'>star</span>
                  <Text className='font-medium text-[#323c58]'>
                    {selectedOperator.rate.toFixed(1)} (
                    {Math.round(selectedOperator.rate)})
                  </Text>
                </div> */}
                <div>
                  <Text className='font-medium rubik text-[#1A1A1A] leading-[20px] text-[16px]'>
                    {selectedOperator.name}
                  </Text>
                </div>
                <div className='flex items-center'>
                  {selectedOperator.address_2 ? (
                    <>
                      <Text className='text-blue-400 rubik text-line-height text-[16px]   leading-5 '>
                        {onlycity}
                      </Text>
                      <span className='material-icons-outlined text-blue-400 icon-line-height text-[15px]'>
                        place
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </div>
              </div>
            </div>
            {/* <View className='flex flex-col items-start'>
              <Text className='font-medium rubik text-[#1A1A1A] mt-[20px] mr-[0px]'>
                Jobs completed: {selectedOperator?.jobs?.completed_jobs}
              </Text>
            </View> */}
            <View className='flex flex-wrap gap-2 mt-[20px]'>
              {selectedOperator.sia_licence_types?.map(
                (grade: any, index: any) => (
                  <Button
                    size='small'
                    key={index}
                    rounded={true}
                    elevated={false}
                    className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate'
                  >
                    <Text
                      color='positive'
                      className='flex rubik items-center gap-1'
                    >
                      <span className='material-icons text-[16px]'>star</span>
                      {grade}
                    </Text>
                  </Button>
                ),
              )}
              {selectedOperator?.sia_certificates?.[0]?.sia_licence === 1 &&
                selectedOperator?.sia_certificates?.[0]?.id_check === 1 &&
                selectedOperator?.sia_certificates?.[0]?.employment_history ===
                  1 &&
                selectedOperator?.sia_certificates?.[0]?.no_criminal_record ===
                  1 &&
                selectedOperator?.sia_certificates?.[0]?.credit_check === 1 &&
                selectedOperator?.sia_certificates?.[0]?.proof_of_address ===
                  1 && (
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border  !bg-[#7CDBEF] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px] '
                    >
                      <Text className='flex rubik items-center gap-1 text-[#323c58] font-medium text-[12px]'>
                        <span className='material-icons text-[16px] text-[#323c58]'>
                          security
                        </span>
                        BS7858
                      </Text>
                    </Button>
                  </View>
                )}
              {selectedOperator?.industry_sectors?.map(
                (industrySector: any, index: any) => (
                  <Button
                    key={index}
                    size='small'
                    rounded={true}
                    elevated={false}
                    className='px-[8px] py-[4px] border !bg-[#323C58] max-w-xs overflow-hidden truncate '
                  >
                    <Text className='text-[#FFFFFF] rubik font-normal text-[12px]  leading-[20px]'>
                      {industrySector}
                    </Text>
                  </Button>
                ),
              )}

            </View>

          </View>
          <Divider className='w-full h-[1px] mt-[20px]' />
          <View className='flex justify-end  mt-[20px]'>
            <button
              onClick={() =>
                navigate(`/operator-profile/${selectedOperator.id}`)
              }
              className='flex flex-row btn-no-hover gap-2 border border-neutral rounded bg-background-base'
            >
              <Text className='rubik text-medium leading-[24px] text-[16px] !text-[#0B80E7] '>More </Text>
              <div className='mt-[5px]'>
              <svg xmlns="http://www.w3.org/2000/svg" width="9" height="14" viewBox="0 0 9 14" fill="none">
  <path d="M0.25 12.5554L1.43607 13.7415L8.07 7.10757L1.43607 0.473633L0.25 1.6597L5.69787 7.10757L0.25 12.5554Z" fill="#0B80E7"/>
</svg>
</div>
            </button>

          </View>
        </>
      )}
    </Modal>
  );
};

export default OperatorModalMapSearch;
