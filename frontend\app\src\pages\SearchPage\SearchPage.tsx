// @ts-nocheck
import React, { useContext, useEffect, useRef } from 'react';
import { View, Button, Loader } from 'reshaped';
import { useLocation } from 'react-router-dom';
import { JobContext, initialJobFilters } from 'src/context/JobContext';
import SearchJobCard from './SearchJobCard';
import NoOperatorFavorite from 'src/components/NoData/NoOperatorFavorite';
import Loading from 'src/components/Loading/Loading';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

export const NoJobFound = ({ favorite }: { favorite: boolean }) => (
  <View className='mx-auto mb-[40px] mt-[40px] flex w-[234px] flex-col items-center justify-center'>
    <SurelyIconNoData />
    <div className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>{`${favorite ? 'No jobs marked as favorite' : 'No results'} `}</div>
  </View>
);

const SearchPage: React.FC = () => {
  const {
    jobs,
    favourites,
    fetchAllJobs,
    isLoadingJobs,
    isLoadingMoreJobs,
    handleLoadMore,
    handleSelectedJob,
    addFavorite,
    removeFavorite,
    setFilters,
    page,
    setPage,
    isLastPage,
    from,
  } = useContext(JobContext);

  const bottomElement = useRef(null);
  const loadMoreButton = useRef(null);

  const location = useLocation();
  const currentPath = location.pathname;
  const isFavoritePage = currentPath.includes('favorite-jobs');

  const isEmpty = jobs?.length === 0;

  const jobAggregate = isFavoritePage ? favourites : jobs;

  const loadMore = () => {
    handleLoadMore();
    loadMoreButton.current.scrollIntoView(false, { behavior: 'smooth' });
  };

  useEffect(() => {
    setPage(1);
    setFilters({ ...initialJobFilters, is_favorite: isFavoritePage });
  }, [isFavoritePage]);

  useEffect(() => {
    if (page !== 1) {
      if (bottomElement.current) {
        bottomElement.current.scrollIntoView(true, { behavior: 'smooth' });
      }
    }
  }, [jobAggregate]);

  return (
    <View className='mb-10 flex min-h-screen w-full flex-col'>
      {isLoadingJobs ? (
        <div className='shrink'>
          <Loading />
        </div>
      ) : (
        <>
          <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
            {(jobAggregate || [])?.length > 0 ? (
              (jobAggregate || []).map((job: any, index: number) => {
                if (index === from - 4) {
                  return (
                    <div ref={bottomElement} key={job.id + '_' + index} className='scroll-mt-[206px]'>
                      <SearchJobCard
                        job={job}
                        addFavorite={addFavorite}
                        removeFavorite={removeFavorite}
                        handleSelectedJob={handleSelectedJob}
                        fetchAllJobs={fetchAllJobs}
                      />
                    </div>
                  );
                } else
                  return (
                    <SearchJobCard
                      key={job.id + '_' + index}
                      job={job}
                      addFavorite={addFavorite}
                      removeFavorite={removeFavorite}
                      handleSelectedJob={handleSelectedJob}
                      fetchAllJobs={fetchAllJobs}
                    />
                  );
              })
            ) : (
              <div className='col-span-4'>
                <NoJobFound favorite={isFavoritePage} />
              </div>
            )}
          </div>
          {isLoadingMoreJobs && !isEmpty && (
            <div ref={loadMoreButton} className='mb-10 mt-4 h-[42.19px] w-full py-2'>
              <Loader size='medium' className='mx-auto' />
            </div>
          )}
          {!isLastPage && !isEmpty && !isLoadingMoreJobs && (
            <div>
              <Button
                variant='outline'
                onClick={loadMore}
                className='rubik mx-auto mb-10 mt-4 rounded-lg bg-[#D1E1FF] px-4 py-2 text-[16px] font-medium leading-6 !text-[#1A1A1A] hover:bg-[#0D2F87] hover:text-[#D1E1FF]'
              >
                Load More
              </Button>
            </div>
          )}
        </>
      )}
    </View>
  );
};

export default SearchPage;
