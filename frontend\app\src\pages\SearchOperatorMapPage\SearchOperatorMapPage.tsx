// @ts-nocheck
import React, { useContext } from 'react';
import { Container } from 'reshaped';
import Header from 'src/components/Header/Header';
import ClientSearchBarOne from 'src/components/ClientSearchBar/ClientSearchBarOne';
import { useLocation } from 'react-router-dom';
import { OperativesContext } from 'src/context/OperativesContext';
import { JobContext } from 'src/context/JobContext';
import MapContainerSearchOperator from './MapContainerSearchOperator';
import { AppContext } from 'src/context/AppContext';
import Loading from 'src/components/Loading/Loading';
interface Props {
  children?: React.ReactNode;
}

export const SearchOperatorMapPage = ({}: Props): JSX.Element => {
  const { latMap, lngMap } = useContext(AppContext);

  return (
    <Container padding={0} className='w-[100vw] h-[100vh] bg-cover'>
      <Header />
      <main className=' align-center mx-auto mt-[80px] w-full   max-w-[1320px]    text-center '>
        {latMap && lngMap && <MapContainerSearchOperator />}
        <section className='mt-[61px] w-full'>
          <ClientSearchBarOne />
          {!latMap && !lngMap && (
            <div className='mt-40'>
              <Loading />
            </div>
          )}
          <article className=' mt-[61px] flex flex-row flex-wrap justify-center gap-5'></article>
        </section>
      </main>
    </Container>
  );
};

export default SearchOperatorMapPage;
