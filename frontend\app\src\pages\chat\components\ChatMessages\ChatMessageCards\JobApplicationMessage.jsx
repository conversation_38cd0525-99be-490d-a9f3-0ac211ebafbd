import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from 'reshaped';
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useAuthContext } from 'src/context/AuthContext';
import { useChatContext } from 'src/context/ChatContext';
import Job from 'src/services/jobs';
import { applyForJob } from 'src/services/operatives';

const JobApplicationMessage = ({ message }) => {
  const { user, isClient } = useAuthContext();
  const { currentChat } = useChatContext();
  const navigate = useNavigate();
  const [showAcceptButton, setShowAcceptButton] = useState(false);

  if (!isClient) return;

  const isReceiver = message?.receiver?.id === user?.profile?.id;
  const invitationMessage = 'has applied for ';
  const nameShown = message?.sender?.name;
  const jobName = currentChat?.job?.post_name;
  const jobId = currentChat?.job?.id;

  const confirmApplication = async (jobId) => {
    const response = await Job.acceptApplicant(jobId, message?.sender?.id);
    if (!response.error) setShowAcceptButton(false);
  };

  // NOTE: Application/ Invitation message should be updated when accepting/confirming, so update the message when the action is finished
  // NOTE: When invitation/ application is declined, the message should not show an empty message but just to be updated
  // NOTE: When operator has applied to an invitation, the client should recieve a message type Im not sure exists now that informs him of the action,
  //       With a link to the manage-jobs/:id
  // NOTE: When an operator applies for a job OR client invites an operator, a new chat is created always, where it should only be added to the previous existing chat

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#F4F5F7] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Job application</h2>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          <span className='font-[500]'>{nameShown}</span> has applied for:{' '}
          <span onClick={() => navigate(`/manage-jobs/${jobId}`, { state: { activeTab: '2' } })} className='cursor-pointer text-[#323C58] underline'>
            {jobName}
          </span>
        </p>
      </div>
      {isReceiver && showAcceptButton && (
        <Button
          color='black'
          className='-mr-[54px] min-w-[60px] shrink-0 rounded !bg-[#323c58] px-2 py-1 text-[10px] text-sm font-medium leading-5 text-[#FFF] lg:text-[14px]'
          onClick={() => confirmApplication(message?.job?.id)}
        >
          Accept
        </Button>
      )}
    </div>
  );
};

export default JobApplicationMessage;
