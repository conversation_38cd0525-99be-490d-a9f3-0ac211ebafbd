// @ts-nocheck
import type {
  User,
  BackendLoginDataType,
  ClientBackendRegisterDataType,
  OperatorBackendRegisterDataType,
  UpdateUserInput,
  SocialLoginInputType,
  SendOtpCodeInputType,
  ForgotPasswordUserInput,
  ResetPasswordUserInput,
  VerifyForgotPasswordUserInput,
  ChangePasswordUserInput,
  PasswordChangeResponse,
  AuthResponse,
  OTPResponse,
  VerifyOtpInputType,
  OtpLoginInputType,
  OTPVerifyResponse,
  BackendSettings,
  // CreateContactUsInput,
} from '../types/user';

import { API_ENDPOINTS } from './api-endpoints';
import { HttpClient } from './http-client';
// import { ScrapeUrlInput, ScrapeUrlResponse } from "../types/general";

type Cities = {
  data: any[];
  error: boolean;
  message: string;
};

class Client {
  users = {
    // me: () => HttpClient.get<User>(API_ENDPOINTS.USERS_ME),
    // update: (user: UpdateUserInput) => HttpClient.put<User>(`${API_ENDPOINTS.USERS}/${user.id}`, user),
    login: (input: BackendLoginDataType) => HttpClient.post<AuthResponse>(API_ENDPOINTS.USERS_LOGIN, input),
    // socialLogin: (input: SocialLoginInputType) => HttpClient.post<AuthResponse>(API_ENDPOINTS.SOCIAL_LOGIN, input),
     sendOtpCode: (input: SendOtpCodeInputType) => HttpClient.post<OTPResponse>(API_ENDPOINTS.SEND_OTP_CODE, input),
     verifyOtpCode: (input: any) =>   HttpClient.post<OTPVerifyResponse>(API_ENDPOINTS.VERIFY_OTP_CODE, input),
    // OtpLogin: (input: OtpLoginInputType) => HttpClient.post<AuthResponse>(API_ENDPOINTS.OTP_LOGIN, input),
    register: (input: ClientBackendRegisterDataType | OperatorBackendRegisterDataType) =>
      HttpClient.post<AuthResponse>(API_ENDPOINTS.USERS_REGISTER, input),
    forgotPassword: (input: ForgotPasswordUserInput) =>
      HttpClient.post<PasswordChangeResponse>(API_ENDPOINTS.USERS_FORGOT_PASSWORD, input),
    // verifyForgotPasswordToken: (input: VerifyForgotPasswordUserInput) =>
    //   HttpClient.post<PasswordChangeResponse>(API_ENDPOINTS.USERS_VERIFY_FORGOT_PASSWORD_TOKEN, input),
    // resetPassword: (input: ResetPasswordUserInput) =>
    //   HttpClient.post<PasswordChangeResponse>(API_ENDPOINTS.USERS_RESET_PASSWORD, input),
    // changePassword: (input: ChangePasswordUserInput) =>
    //   HttpClient.post<PasswordChangeResponse>(API_ENDPOINTS.USERS_CHANGE_PASSWORD, input),
    logout: () => HttpClient.post<boolean>(API_ENDPOINTS.USERS_LOGOUT, {}),
    validateOperatorProfile: (input: ClientBackendRegisterDataType | OperatorBackendRegisterDataType) =>
      HttpClient.post<AuthResponse>(API_ENDPOINTS.OPERATOR_VALIDATE_PROFILE, input),
    validateClientProfile: (input: ClientBackendRegisterDataType | OperatorBackendRegisterDataType) =>
      HttpClient.post<AuthResponse>(API_ENDPOINTS.CLIENT_VALIDATE_PROFILE, input),
    // getValidateOperatorProfile: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_VALIDATE_PROFILE),
    getValidateOperatorProfile: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_PROFILE),

    getOperatorProfileData: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_PROFILE),

    validateEmail: (input: any) => HttpClient.post<any>(API_ENDPOINTS.VALIDATE_EMAIL, input),
    loginVerifyEmail: (input: ForgotPasswordUserInput) =>
    HttpClient.post<PasswordChangeResponse>(API_ENDPOINTS.LOGIN_VERIFY_EMAIL, input),

    addSubscribe: (input: any) => HttpClient.post<any>(API_ENDPOINTS.SUBSCRIBE, input),

    getReferFriend: () => HttpClient.get<any>(API_ENDPOINTS.REFERFRIEND),
    emailTaken: (input: any) => HttpClient.post<any>(API_ENDPOINTS.CHECK_EMAIL, input),
    getTopProfiles: () => HttpClient.get<any>(API_ENDPOINTS.TOP_PROFILES),
  };

  settings = {
    all: (input: BackendSettings) => HttpClient.post<any>(API_ENDPOINTS.SETTINGS, input),

    getAll: () => HttpClient.get<any>(API_ENDPOINTS.SETTINGS),
    // cities: () => HttpClient.get<any>(API_ENDPOINTS.CITIES),
    // cities: (postcode: any) => HttpClient.get<any>(API_ENDPOINTS.CITIES, { postcode }),
    cities: (postcode: any) => HttpClient.get<any>(`${API_ENDPOINTS.CITIES}/${postcode}`),
    getSiaLicenceValidation: (siaLicenceNumber: any) =>
      HttpClient.get<any>(`${API_ENDPOINTS.SIA_LICENCE_VALIDATION}/${siaLicenceNumber}`),

    languages: () => HttpClient.get<Cities>(API_ENDPOINTS.GLOBAL_LANGUAGES),
    password: (input: BackendSettings) => HttpClient.post<any>(API_ENDPOINTS.CHANGE_PASSWORD, input),

    addPosition: (input: any) => HttpClient.post<any>(API_ENDPOINTS.USER_ADD_POSITION, input),
    addQualification: (input: any) => HttpClient.post<any>(API_ENDPOINTS.USER_ADD_QUALIFICATION, input),
    addCuriculumVitae: (input: any) => HttpClient.post<any>(API_ENDPOINTS.USER_ADD_QUALIFICATION, input),
    getPosition: () => HttpClient.get<any>(API_ENDPOINTS.USER_ADD_POSITION),

    addProfileDetails: (input: any) => HttpClient.post<any>(API_ENDPOINTS.OPERATOR_SETTINGS_PROFILE_DETAILS, input),
    getProfileDetails: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_PROFILE_DETAILS),

    getLanguage: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_LANGUAGE),
    getLanguageLevel: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_LANGUAGE_LEVEL),

    deleteUser: () => HttpClient.delete<any>(API_ENDPOINTS.USER_DELETE_ACCOUNT),

    addAvailability: (input: any) => HttpClient.patch<any>(API_ENDPOINTS.OPERATOR_SETTINGS_AVAILABILITY, input),
    getAvailability: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_AVAILABILITY),

    addPaymentDetails: (input: any) => HttpClient.post<any>(API_ENDPOINTS.OPERATOR_SETTINGS_PAYMENT_DETAILS, input),
    getPaymentDetails: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_PAYMENT_DETAILS),
    addBankCardDetails: (input: any) => HttpClient.post<any>(API_ENDPOINTS.POST_BANK_CARD_DETAILS, input),
    getBankCardDetails: () => HttpClient.get<any>(API_ENDPOINTS.GET_BANK_CARD_DETAILS),
    addVatAndUtr:  (input: any) => HttpClient.post<any>(API_ENDPOINTS.ADD_VAT_AND_UTR, input),
    addBankAccount: (input: any) => HttpClient.post<any>(API_ENDPOINTS.POST_BANK_ACCOUNT, input),
    getBankAccount: () => HttpClient.get<any>(API_ENDPOINTS.GET_BANK_ACCOUNT),

    addGeneral: (input: any) => HttpClient.post<any>(API_ENDPOINTS.OPERATOR_SETTINGS_GENERAL, input),
    getGeneral: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_GENERAL),

    addSiaLicence: (input: any) => HttpClient.post<any>(API_ENDPOINTS.OPERATOR_SETTINGS_SIA_LICENCE, input),
    getSiaLicence: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SETTINGS_SIA_LICENCE),

    getProfileData: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_PROFILE),

    addSurelyProBadges: (input: any) => HttpClient.post<any>(API_ENDPOINTS.OPERATOR_SURELY_PRO_BADGE, input),
    getSurelyProBadges: () => HttpClient.get<any>(API_ENDPOINTS.OPERATOR_SURELY_PRO_BADGE),

    addNotifications: (input: any) => HttpClient.post<any>(API_ENDPOINTS.NOTIFICATIONS, input),
    getNotifications: () => HttpClient.get<any>(API_ENDPOINTS.NOTIFICATIONS),

    changeEmail: (input: BackendSettings) => HttpClient.post<any>(API_ENDPOINTS.CHANGE_EMAIL, input),
    changePhone: (input: BackendSettings) => HttpClient.post<any>(API_ENDPOINTS.CHANGE_PHONE, input),

    upgradeToClient: (input: any) => HttpClient.post<any>('/upgrade-to-client', input),
  };

  jobs = {
    addPostJob: (input: any) => HttpClient.post<any>(API_ENDPOINTS.CLIENT_POST_JOB, input),
    getJobs: () => HttpClient.get<any>(API_ENDPOINTS.CLIENT_POST_JOB),
    getClientJobs: () => HttpClient.get<any>(API_ENDPOINTS.CLIENT_POST_JOB),
    getApplied: (id: number) => HttpClient.get<any>(`/jobs/${id}/applications`),
    editPostJob: (id: number, input: any) => HttpClient.put<any>(`/jobs/${id}`, input),
    getAppliedJobs: () => HttpClient.get<any>(`/jobs?applied=true`),
  };

  operatives = {
    getAllOperatives: (filterString = '') => HttpClient.get<any>(`${API_ENDPOINTS.USERS_OPERATIVES}?${filterString}`),
    getOperative: (id: number, forClientId?: number) => HttpClient.get<any>(`/users/${id}/operative?for_client_id=${forClientId}`),
    applyForJob: (id: any, data: any) => HttpClient.post<any>(`/jobs/${id}/applications`, data),
    reportOperative: ( data: any) => HttpClient.post<any>(`/reports`, data),
  };

  favourites = {
    // getAllFavouriteJobs: () =>
    //   HttpClient.get<any>(API_ENDPOINTS.FAVOURITE_JOBS),
    // getAllFavouriteOperators: () =>
    //   HttpClient.get<any>(API_ENDPOINTS.FAVOURITE_OPERATORS),
    // toggleFavourite: (id: number) =>
    //   HttpClient.post<any>(API_ENDPOINTS.FAVOURITE_TOGGLE, id),
    removeOperatorsFavorite: (id: number) => HttpClient.delete<any>(`/users/${id}/favorite`),
    removeJobsFavorite: (id: number) => HttpClient.delete<any>(`/jobs/${id}/favorite`),
    addOperatorsFavorite: (id: number) => HttpClient.post<any>(`/users/${id}/favorite`, {}),
    addJobsFavorite: (id: number) => HttpClient.post<any>(`/jobs/${id}/favorite`, {}),
  };

  chat = {
    createChat: () => HttpClient.post<any>(API_ENDPOINTS.CREATE_CHAT, {}),
    createNoContractChat: (receiver_id?: number, message?: string, type?: string) =>
      HttpClient.post<any>(API_ENDPOINTS.CREATE_CHAT, { receiver_id, message, type }),
    getChats: (id: number) => HttpClient.get<any>(API_ENDPOINTS.MY_CHATS, { reciever_id: id }),
    getMessages: (id: number) => HttpClient.get<any>(`${API_ENDPOINTS.MY_CHATS}/${id}${API_ENDPOINTS.CHAT_MESSAGES}`),
    acceptJobInvitation: (chatId: string, contractId: string) =>
      HttpClient.post<any>(`/chats/${chatId}/contract/${contractId}/status`, {}),
    deleteChat: (id: string) => HttpClient.delete<any>(`${API_ENDPOINTS.DELETE_CHAT}/${id}`, {}),
  };

  blogs = {
    getBlogs: () => HttpClient.get<any>(API_ENDPOINTS.BLOGS),
  }

  scrape = {
    url: (input: any) => HttpClient.post<any>(`${API_ENDPOINTS.SCRAPE_URL}/`, input),
  };
}

export default new Client();
