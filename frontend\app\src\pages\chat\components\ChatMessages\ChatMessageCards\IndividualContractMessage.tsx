// @ts-nocheck
import { useAuthContext } from 'src/context/AuthContext';
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useChatContext } from 'src/context/ChatContext';

const IndividualContractMessage = ({ message }: any) => {
  const { currentChat } = useChatContext();

  const nameShown = currentChat?.sender_name;

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Contract created</h2>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          {nameShown} created contract #{message?.contract_id}
        </p>
      </div>
    </div>
  );
};

export default IndividualContractMessage;
