<?php

namespace App\Http\Controllers\Api\Buyer;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\BookingRequest;
use App\Models\Booking;
use App\Models\BookingNote;
use App\Models\MobileUser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{

    public function user_blocked_dates($id){

        $user = MobileUser::find($id);

        $bookings = Booking::where('provider_id', $user->id)
            ->whereDate('start_date','>=',date('Y-m-d'))
            ->where('status', Booking::approved)
            ->orderBy('start_date','asc')
            ->get();

        $data = array();
        foreach ($bookings as $booking) {
            $obj = [
                'id'=>$booking->id,
                'start_date'=>$booking->start_date,
                'end_date'=>$booking->end_date,
                'shift_start'=>$booking->shift_start,
                'shift_end'=>$booking->shift_end,
                'status'=>(int)$booking->status,
            ];
            array_push($data,$obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data,
        ]);
    }


   public function booking_notes(){

       $notes = BookingNote::select('name')->get();

       return response()->json([
           'error' => false,
           'message' => 'Success',
           'data' => $notes
       ]);
   }


   public function store_booking(BookingRequest $request){

       DB::beginTransaction();
       try {

           $user = Auth::guard('api')->user();
           $provider = MobileUser::find($request->provider_id);

           $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date);
           $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date);

           $diff_in_days = $end_date->diffInDays($start_date)  + 1; //including first day

           $shift_start = Carbon::createFromFormat('H:i', $request->shift_start);
           $shift_end = Carbon::createFromFormat('H:i', $request->shift_end);

           $diff_in_hours = $shift_end->diffInHours($shift_start);


           $total_hours = $diff_in_hours * $diff_in_days;

           Booking::create([

               'buyer_id'=>$user->id,
               'provider_id'=>$provider->id,
               'start_date'=>$start_date,
               'end_date'=>$end_date,
               'shift_start'=>$shift_start,
               'shift_end'=>$shift_end,
               'price_per_hour'=>$provider->pay_rate,
               'total_hours'=>$total_hours,
               'total_price'=>round($provider->pay_rate *  $total_hours, 2),
               'card_id'=>$request->card_id,
               'notes'=>$request->notes,
               'status'=>Booking::pending,
               'type'=>Booking::booking,
           ]);

           DB::commit();
           return response()->json([
               'error' => false,
               'message' => 'Success',
           ]);
       }
       catch (\Exception $e) {

           DB::rollback();

           return response()->json([
               'error' => true,
               'message' => 'An error occurred.',
           ]);
       }
   }

    public function my_bookings(){

        $bookings = Booking::where('buyer_id', Auth::guard('api')->user()->id)->orderBy('start_date','asc')->get();

        $data = array();
        foreach ($bookings as $booking) {
            $obj = [
                'id'=>$booking->id,
                'provider_name'=>$booking->provider->name,
                'start_date'=>$booking->start_date,
                'end_date'=>$booking->end_date,
                'shift_start'=>$booking->shift_start,
                'shift_end'=>$booking->shift_end,
                'total_hours'=>$booking->total_hours,
                'total_price'=>$booking->total_price,
                'notes'=>$booking->notes ?? '',
                'status'=>(int)$booking->status
            ];
            array_push($data,$obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data,
        ]);
    }

}
