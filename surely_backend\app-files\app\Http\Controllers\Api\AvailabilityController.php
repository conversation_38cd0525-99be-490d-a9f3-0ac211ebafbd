<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AvailabilityController extends Controller
{
    public function index(): JsonResponse
    {
        $user = auth()->user();

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'instant_book' => $user->instant_book
        ]);
    }

    public function availability(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (! $user->update($request->only(['instant_book']))) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot updated!',
                'data' => $user->instant_book
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data updated successfully!',
            'instant_book' => $user->instant_book
        ]);
    }
}
