import { HttpClient } from 'src/client/http-client';

export const getRating = async (id: any, 
  //page: any
  ) => {
    try {
      const response = await HttpClient.get<any>(`/user/${id}/reviews`)
      // const response = await HttpClient.get<any>(`/user/${id}/reviews?page=${page}`);
      if (!response.error) {
        return response;
      }
    } catch (error) {
      console.error(error);
    }
  };