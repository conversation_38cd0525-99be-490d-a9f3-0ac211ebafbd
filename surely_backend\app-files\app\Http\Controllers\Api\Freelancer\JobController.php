<?php

namespace App\Http\Controllers\Api\Freelancer;

use App\DataTables\JobsDatatable;
use App\Events\EmergencyJobEvent;
use App\Events\NewApplicationEvent;
use App\Events\NewMessageEvent;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\JobApplyRequest;
use App\Http\Resources\JobApplicantResource;
use App\Http\Resources\JobClientResource;
use App\Http\Resources\JobOperativeResource;
use App\Http\Resources\JobResource;
use App\Models\BookingForm;
use App\Models\Chat;
use App\Models\ChatMessage;
use App\Models\Contract;
use App\Models\FormApplicant;
use App\Models\Job;
use App\Models\MobileUser;
use App\Models\Notification;
use App\Notifications\EmergencyHireMessageNotification;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class JobController extends Controller
{
    use \App\Traits\Helpers;

    public function index(Request $request): JsonResponse
    {
        $resource = JobClientResource::class;
        $query = Job::query()
            ->leftJoin('mobile_users', 'mobile_users.id', '=', 'jobs.user_id')
            ->select([
                'jobs.*',
                'mobile_users.postal_code as postal_code',
                'mobile_users.location_range as location_range',
            ])
            ->orderBy('is_emergency_hire', 'DESC')
            ->orderBy('created_at', 'DESC');

        if (auth()->user()->account_type == MobileUser::business) {
            $query->where('jobs.user_id', auth()->id());
        }

        if (auth()->user()->account_type == MobileUser::freelancer) {
            $query->leftJoin('favorite_job_user', function ($join) {
                $join->on('jobs.id', '=', 'favorite_job_user.job_id')
                    ->where('favorite_job_user.user_id', auth()->id());
            });
            $query->addSelect('favorite_job_user.user_id as is_favorite');
            $query->addSelect('favorite_job_user.id as favorite_id');

            $query->where(function ($query) {
                $query->cursor()
                    ->each(function ($job) use ($query){
                        if (!$job->date_range) {
                            $query->where('jobs.id', '!=', $job->id);
                            return;
                        }

                        $array_of_dates = collect($job->date_range)->pluck('end')->filter();
                        $last_date = $array_of_dates->max() ?? null;

                        if ($last_date < Carbon::now()) {
                            $query->where('jobs.id', '!=', $job->id);
                        }
                    });
            });

            $resource = JobOperativeResource::class;

            if ($request->filled('status')) {
                $query->where('jobs.status', $request->get('status'));
            }

            if ($request->filled('is_favorite')) {
                $query->where('favorite_job_user.user_id', auth()->id());
            }

            if ($request->filled('applied')) {
                $query->leftJoin('form_applicants', 'form_applicants.form_id', '=', 'jobs.id')
                    ->where('form_applicants.provider_id', '=', auth()->id());
            }

        }

        if ($request->filled('qs')) {
            $query = $this->scopeSearchQueryString($query, $request->get('qs'));
        }

        if ($request->filled('postal_code')) {
            $query->where('mobile_users.postal_code', $request->get('postal_code'));
        }

        if ($request->filled('location_range')) {
            $query->where('jobs.location', $request->get('location_range'));
        }

        if ($request->filled('hourly_rate_min')) {
            $query->where('hourly_rate_min', '>=', $request->get('hourly_rate_min'));
        }

        if ($request->filled('hourly_rate_max')) {
            $query->where('hourly_rate_max', '<=', $request->get('hourly_rate_max'));
        }

        if ($request->filled('sia_licence')) {
            $siaLicences = explode(',', $request->get('sia_licence'));
            $siaLicences = array_map('trim', $siaLicences);
            $siaLicences = array_filter($siaLicences);

            if (!empty($siaLicences)) {
                $query->where(function ($query) use ($siaLicences) {
                    foreach ($siaLicences as $licence) {
                        $query->orWhereJsonContains('sia_licence', $licence);
                    }
                });
            }
        }

        if ($request->filled('industry_sector')) {
            $industrySectors = explode(',', $request->get('industry_sector'));
            $industrySectors = array_map('trim', $industrySectors);
            $industrySectors = array_filter($industrySectors);

            if (!empty($industrySectors)) {
                $query->where(function ($query) use ($industrySectors) {
                    foreach ($industrySectors as $industrySector) {
                        $query->orWhereJsonContains('industry_sector', $industrySector);
                    }
                });
            }
        }

        if ($request->filled('surely_pro_badge')) {
            $surelyProBadges = explode(',', $request->get('surely_pro_badge'));
            $surelyProBadges = array_map('trim', $surelyProBadges);
            $surelyProBadges = array_filter($surelyProBadges);

            if (!empty($surelyProBadges)) {
                $query->where(function ($query) use ($surelyProBadges) {
                    foreach ($surelyProBadges as $surelyProBadge) {
                        $query->orWhereJsonContains('surely_pro_badge', $surelyProBadge);
                    }
                });
            }
        }

        if ($request->filled('rating_level')) {
            $query->where('rating_level', '<=', $request->get('rating_level'));
        }

        if ($request->filled('is_emergency_hire')) {
            $query->where('is_emergency_hire', true);
        }

        if ($request->filled('is_inclusivity_pledge')) {
            $query->where('is_inclusivity_pledge', true);
        }

        if ($request->filled('no_paginate') || !$request->filled('page')) {
            $jobs = $query->get();
            $meta = [];
        } else {
            $perPage = $request->input('per_page', 8);
            $jobs = $query->paginate($perPage);
            $meta = [
                'current_page' => $jobs->currentPage(),
                'from' => $jobs->firstItem(),
                'last_page' => $jobs->lastPage(),
                'path' => $jobs->resolveCurrentPath(),
                'per_page' => $jobs->perPage(),
                'to' => $jobs->lastItem(),
                'total' => $jobs->total(),
            ];
        }

        if ($request->filled('resource')) {
            $resource = 'App\\Http\\Resources\\' . $request->get('resource');
        }

        $jobs = $resource::collection($jobs);

        return response()->json(['data' => $jobs, 'meta' => $meta]);
    }

    public function store(Request $request): JsonResponse
    {
        if (!auth()->user()->email_verified) {
            return response()->json([
                'error' => true,
                'message' => 'Please verify your email before posting a job.',
                'requires_verification' => true
            ], 403);
        }

        if (auth()->user()->account_type != MobileUser::business) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized user for this action!'
            ], 403);
        }

        $businessTypesArray = ['Company', 'ACS', 'Non', 'Agent', 'Private Individual'];

        if (!in_array(auth()->user()->client_represent_business_as, $businessTypesArray)) {
            return response()->json([
                'error' => true,
                'message' => 'You should validate your account to post a job!',
            ]);
        }

        $data = $request->all();
        $data['user_id'] = auth()->id();
        $images = $request->get('images');
        if (!empty($images)) {
            foreach ($images as $key => $value) {
                if (empty($value)) {
                    $data['images'][$key] = '';
                } else {
                    $image = $this->base64Upload('job_images', $value);
                    $data['images'][$key] = $image;
                }
            }
        }

        if (!empty($data['location'])) {
            $postcode = $data['location'];

            try {
                $client = new Client();
                $response = $client->request('GET', "http://api.postcodes.io/postcodes/$postcode");
                $dataPostcode = $response->getBody()->getContents();
                $dataPostcode = json_decode($dataPostcode);
                $data['lat'] = $dataPostcode->result->latitude;
                $data['lng'] = $dataPostcode->result->longitude;
                $data['city'] = $dataPostcode->result->admin_district;
            } catch (RequestException $exception) {
                if ($exception->getResponse()->getStatusCode() === 404) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Invalid postcode! Enter the correct postal code to get the correct location!',
                        'data' => [],
                    ], 422);
                }
            }
        }

        $job = Job::create($data);

        if (!$job) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot be saved!',
                'data' => []
            ]);
        }

        if($job->is_emergency_hire){
            broadcast(new EmergencyJobEvent($job))->toOthers();
        }

        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => $job
        ]);
    }

    public function show($jobId): JsonResponse
    {
        $job = Job::where('jobs.id', $jobId)
            ->leftJoin('favorite_job_user', function ($join) {
                $join->on('jobs.id', '=', 'favorite_job_user.job_id')
                    ->where('favorite_job_user.user_id', auth()->id());
            })
            ->select([
                'jobs.*',
                'favorite_job_user.user_id as is_favorite',
                'favorite_job_user.id as favorite_id',
            ])->first()->load('applicants');

        $resource = JobClientResource::class;

        if (auth()->user()->account_type == MobileUser::freelancer) {
            $resource = JobOperativeResource::class;
        }

        return !$job
            ? response()->json(['error' => true, 'message' => 'No data found!', 'data' => []])
            : response()->json([
                'error' => false,
                'message' => 'Data retrieved successfully!',
                'data' => new $resource($job),
            ]);
    }

    public function update(Request $request, $jobId): JsonResponse
    {
        if (auth()->user()->account_type != MobileUser::business) {
            return response()->json([
                'error' => false,
                'message' => 'Unauthorized user for this action!'
            ], 403);
        }

        $data = $request->all();
        $job = Job::find($jobId);

        if (!$job) {
            return response()->json([
                'error' => true,
                'message' => 'No data found for this job!',
                'data' => [],
            ]);
        }

        $images = $request->get('images', []);
        foreach ($images as $key => $value) {
            if (empty($value)) {
                Storage::delete('public/' . $data['images'][$key]);
                $data['images'][$key] = '';
            } else if (str_contains($value, 'https')) {
                $data['images'][$key] = $value;
            } else {
                $image = $this->base64Upload('job_images', $value);
                $data['images'][$key] = $image;
            }
        }

        if (!empty($data['location'])) {
            $postcode = $data['location'];

            try {
                $client = new Client();
                $response = $client->request('GET', "http://api.postcodes.io/postcodes/$postcode");
                $dataPostcode = $response->getBody()->getContents();
                $dataPostcode = json_decode($dataPostcode);
                $data['lat'] = $dataPostcode->result->latitude;
                $data['lng'] = $dataPostcode->result->longitude;
                $data['city'] = $dataPostcode->result->admin_district;
            } catch (RequestException $exception) {
                if ($exception->getResponse()->getStatusCode() === 404) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Invalid postcode! Enter the correct postal code to get the correct location!',
                        'data' => [],
                    ], 422);
                }
            }
        }

        if (!$job->update($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot be updated!',
                'data' => [],
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data updated successfully!',
            'data' => new JobResource($job),
        ]);
    }

    //@deprecated
    //TODO: Kjo metode duhet te fshihet dhe duhet te zevendesohet me nje filter te jobs : /api/jobs?status=open
    public function addToFavorites($jobId): JsonResponse
    {
        $job = Job::find($jobId);

        if (!$job) {
            return response()->json(['error' => 'Job not found'], 404);
        }

        $user = auth()->user();

        if ($user->favoriteJobs->contains($job)) {
            return response()->json(['message' => 'Job is already in favorites']);
        }

        $user->favoriteJobs()->attach($job);

        return response()->json(['message' => 'Job added to favorites']);
    }

    public function removeFromFavorites($jobId): JsonResponse
    {
        $job = Job::find($jobId);

        if (!$job) {
            return response()->json(['error' => 'Job not found'], 404);
        }

        $user = auth()->user();

        if (!$user->favoriteJobs->contains($job)) {
            return response()->json(['message' => 'Job is not in favorites']);
        }

        $user->favoriteJobs()->detach($job);

        return response()->json(['message' => 'Job removed from favorites']);
    }

    //TODO: jane metoda per mobile nuk duhet te preken por te perdoren pa ndryshuar
    public function open_positions()
    {
        $forms = BookingForm::where('status', BookingForm::open)
            ->orderBy('created_at', 'desc')
            ->get();

        $data = array();
        foreach ($forms as $form) {
            $obj = [
                'id' => $form->id,
                'title' => $form->title,
                'buyer_id' => $form->buyer_id,
                'buyer_name' => $form->buyer->name,
                'expertise' => $form->expertise->name,
                'start_date' => $form->start_date,
                'end_date' => $form->end_date,
                'shift_start' => $form->shift_start,
                'shift_end' => $form->shift_end,
                'total_hours' => $form->total_hours,
                'max_pay_per_hour' => (double) $form->price_per_hour,
                //  'years_of_experience'=>$form->years_of_experience,
                'notes' => $form->notes ?? '',
                'created_at' => date('Y-m-d', strtotime($form->created_at)),

            ];
            array_push($data, $obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data
        ]);
    }

    public function apply_to_position(JobApplyRequest $request, $jobId)
    {
        if (!auth()->user()->email_verified) {
            return response()->json([
                'error' => true,
                'message' => 'Please verify your email before applying to jobs.',
                'requires_verification' => true
            ], 403);
        }

        if (auth()->user()->account_type != MobileUser::freelancer) {
            return response()->json([
                'error' => false,
                'message' => 'Unauthorized user to apply!'
            ], 403);
        }

        $siaCertificate = auth()->user()->siaCertificate()->first();
        if ($siaCertificate?->sia_licence != true) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized user to apply! You are missing your Sia License validations!'
            ], 422);
        }

        $existingApplication = FormApplicant::where('form_id', $jobId)
            ->where('provider_id', auth()->id())
            ->first();

        $job = Job::find($jobId);

        if ($existingApplication && $existingApplication->status === 'declined') {
            return response()->json([
                'error' => true,
                'message' => 'You cannot apply for this job!',
            ], 422);
        }

        if ($existingApplication && $existingApplication->status === 'accepted') {
            return response()->json([
                'error' => true,
                'message' => 'You are already accepted for this job!',
            ], 422);
        }

        if ($existingApplication && $existingApplication->status === 'cancelled') {
            return response()->json([
                'error' => true,
                'message' => 'You are already accepted for this job!',
            ], 422);
        }

        if($request->type != 'applied_through_invitation'){
            if($existingApplication){
                return response()->json([
                    'error' => true,
                    'message' => 'You are already applied for this job!',
                ], 422);
            }
        }

        unset($request['id']);
        $request['provider_id'] = auth()->id();
        $request['sender_id'] = auth()->id();
        $request['receiver_id'] = $job->user_id;
        $request['full_name'] = auth()->user()->name;
        $request['email'] = auth()->user()->email;
        $request['pay_rate'] = $request->get('pay_rate', $job->pay_rate);
        $request['form_id'] = $request->get('job_id', $jobId) ?? $request->get('position_id'); //position_id duhet te mbaje vleren e job_id
        $request['expertise_id'] = auth()->user()->expertise_id;
        $request['notes'] = $request->get('notes');
        $exists = true;
        if (!$existingApplication || $existingApplication->status === 'invited') {
            $exists = false;
            if (!$existingApplication) {
                $existingApplication = FormApplicant::create($request->all());
            }

            //TODO: add new chat message type 'application'
            if($request->has('chat_id')){
                $request['job_id'] = $jobId;
                $chat = Chat::find($request->get('chat_id'));
                $message = $chat->writeMessage($request);

                // Check if this is an emergency hire job
                $job = Job::find($jobId);
                if ($job && $job->is_emergency_hire) {
                    // Send emergency hire message notification to the receiver
                    $receiver = MobileUser::find($message->receiver_id);
                    if ($receiver) {
                        $receiver->notify(new EmergencyHireMessageNotification($message, $job));

                        // Log the emergency hire message notification
                        Log::info("Sent emergency hire message notification to {$receiver->email} for job #{$job->id}");
                    }
                }

                broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

                ChatMessage::where('type','invite_to_apply')->where('chat_id',$request->get('chat_id'))->update(['type'=>'has_applied_to_invitation']);
            }

            $notification = [
                "operator_name" => $request['full_name'],
                "job_id" => $job->id,
                "post_name" => $job->post_name,
                "proposition_details" => $request['notes'],
                "receiver_id" => $job->user_id,
                "sender_id" => auth()->id(),
            ];

            broadcast(new NewApplicationEvent($notification))->toOthers();
        }

        $existingApplication->status = "pending";
        $existingApplication->save();

        return response()->json([
            'error' => false,
            'message' => 'Application saved successfully!',
            //            'chat' => $chat,
//            'chat_messages'=> $message,
//            'exists' => $exists,
//            'receiver_id' => $receiverId,
            'sender_id' => auth()->id(),

        ]);
    }

    public function applicants($jobId)
    {
        $resource = JobApplicantResource::class;
        $query = FormApplicant::query()
            ->leftJoin('jobs', 'jobs.id', '=', 'form_applicants.form_id')
            ->leftJoin('mobile_users', 'mobile_users.id', '=', 'form_applicants.provider_id')
            ->leftJoin('sia_certificates as sia', 'sia.user_id', '=', 'mobile_users.id')
            ->leftJoin('cities', 'cities.id', '=', 'mobile_users.city_id')
            ->leftJoin('surely_pro_badges as pro', 'pro.user_id', 'mobile_users.id')
            ->select([
                'form_applicants.*',
                'sia.sia_licence as sia_licence',
                'cities.name as city',
                'pro.type as surely_pro_type',
                'pro.text as surely_pro_text',
            ]);

        $query->where('form_id', $jobId);
        $query->distinct('form_applicants.id');
        $applicants = $query->get();

        $jobApplicants = $resource::collection($applicants);

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $jobApplicants
        ]);
    }

    public function changeStatus(Request $request, $jobId, $operatorId): JsonResponse
    {
        $applicant = FormApplicant::where('form_id', $jobId)->where('provider_id', $operatorId)->first();

        if (!$applicant->update($request->only('status'))) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot be processed at the moment!',
                'status' => $applicant->status
            ]);
        }

        $job = Job::find($jobId);
        if (auth()->id() == $operatorId || $job->user_id == $operatorId) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized user action!'
            ], 403);
        }
        $request['job_id'] = $jobId;
        $request['receiver_id'] = $operatorId;
        $request['sender_id'] = auth()->id();

        if ($applicant->status == 'accepted') {

            $chat = Chat::where('job_id', $jobId)->where(function ($q) use ($operatorId) {
                $q->where(function ($q) use ($operatorId) {
                    $q->where('sender_id', $operatorId)->where('receiver_id', auth()->id());
                })->orWhere(function ($q) use ($operatorId) {
                    $q->where('sender_id', auth()->id())->where('receiver_id', $operatorId);
                });
            })->first();

            if (!$chat) {
                $chat = Chat::create($request->all());
            }

            $request['type'] = $request->get('type', 'client_accepted_applicant');

            $contract = Contract::create([
                'job_id' => $jobId,
                'client_id' => auth()->id(),
                'operative_id' => $operatorId,
                'chat_id' => $chat->id,
                'hourly_rate' => $request->get('hourly_rate', $job->hourly_rate_min),
                'location' => $request->get('location', $job->location),
                'start_date' => now(),
                'date_range' => $request->get('date_range', $job->date_range),
                'payment_terms' => $request->get('payment_terms', $job->payment_terms),
                'type' => 'job_invite',
                'status' => Contract::pending,
                'shifts_status' => Contract::SHIFT_STATUS_PENDING,
                'escrow_status' => Contract::ESCROW_STATUS_PENDING,
                'payment_status' => Contract::PAYMENT_STATUS_PENDING,
            ]);

            $chat->update(['contract_id' => $contract->id]);

            $message = $chat->writeMessage($request);

            // Check if this is an emergency hire job
            if ($job && $job->is_emergency_hire) {
                // Send emergency hire message notification to the receiver
                $receiver = MobileUser::find($message->receiver_id);
                if ($receiver) {
                    $receiver->notify(new EmergencyHireMessageNotification($message, $job));

                    // Log the emergency hire message notification
                    Log::info("Sent emergency hire message notification to {$receiver->email} for job #{$job->id}");
                }
            }

            broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

            return response()->json([
                'error' => false,
                'message' => 'Job updated successfully!',
                'chat' => $chat,
                'chat_messages' => $message,
                'status' => $applicant->status
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Job updated successfully!',
            'status' => $applicant->status
        ]);
    }

    public function sendInvitation($operatorId, Request $request): JsonResponse
    {
        if (auth()->id() == $operatorId || auth()->user()->account_type != MobileUser::business) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized user action!'
            ], 403);
        }

        DB::beginTransaction();
        $request['receiver_id'] = $operatorId;
        $request['sender_id'] = auth()->id();
        $operator = MobileUser::find($operatorId);
        foreach ($request->get('job_ids') as $jobId) {
            $request['job_id'] = $jobId;
            $existingInvitation = FormApplicant::where('form_id', $jobId)
                ->where('provider_id', $operatorId)
                ->exists();

            if ($existingInvitation) {
                DB::rollBack();
                return response()->json([
                    'error' => true,
                    'message' => 'Existing application for operatorId:' . $operatorId . ' - jobId:' . $jobId,
                ], 422);
            }

            // Check if the operative user has already applied for the job
            $existingApplication = FormApplicant::where('form_id', $jobId)
                ->where('provider_id', $operatorId)
                ->whereIn('status', ['refused', 'approved', 'declined', 'invited'])
                ->exists();

            if ($existingApplication) {
                DB::rollBack();
                return response()->json([
                    'error' => true,
                    'message' => 'Operative has already applied for this job!',
                ], 422);
            }

            FormApplicant::create([
                'form_id' => $jobId,
                'provider_id' => $operatorId,
                'full_name' => $operator->name,
                'email' => $operator->email,
                'pay_rate' => 0,
                'notes' => $request['type'],
                'status' => 'invited',
                'is_invited' => true,
            ]);

            $chat = Chat::where('job_id', $jobId)->where(function ($q) use ($operatorId) {
                $q->where(function ($q) use ($operatorId) {
                    $q->where('sender_id', $operatorId)->where('receiver_id', auth()->id());
                })->orWhere(function ($q) use ($operatorId) {
                    $q->where('sender_id', auth()->id())->where('receiver_id', $operatorId);
                });
            })->first();

            if (!$chat) {
                $chat = Chat::create($request->all());
            }

            $message = $chat->writeMessage($request);

            // Check if this is an emergency hire job
            $job = Job::find($jobId);
            if ($job && $job->is_emergency_hire) {
                // Send emergency hire message notification to the receiver
                $receiver = MobileUser::find($message->receiver_id);
                if ($receiver) {
                    $receiver->notify(new EmergencyHireMessageNotification($message, $job));

                    // Log the emergency hire message notification
                    Log::info("Sent emergency hire message notification to {$receiver->email} for job #{$job->id}");
                }
            }

            broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();
        }

        DB::commit();
        return response()->json([
            'error' => false,
            'message' => 'Invitation sent successfully!',
            'data' => $chat->id,
        ]);
    }

    private function scopeSearchQueryString($query, $qs)
    {
        $qs = preg_replace('/\s/', '%', trim($qs));

        return $query->where(function ($q) use ($qs) {
            $q->where('jobs.post_name', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.title', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.description', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.relevant_qualification', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.hourly_rate_min', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.hourly_rate_max', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.location', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.city', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.duty_of_care', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.benefits', 'like', '%' . $qs . '%');
            $q->orWhere('mobile_users.name', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.sia_licence', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.industry_sector', 'like', '%' . $qs . '%');
            $q->orWhere('jobs.surely_pro_badge', 'like', '%' . $qs . '%');
        });
    }

    public function table(JobsDatatable $dataTable)
    {
        return $dataTable->render('jobs.index');
    }
}
