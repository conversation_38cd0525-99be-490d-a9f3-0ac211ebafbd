// @ts-nocheck
import React, { useContext, useState } from 'react';
import { Text, View, Button, Modal, TextField, Select, Switch, Divider } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import '../../Header/HeaderMenu/HeaderMenu.css';
import StarSelect from './StarSelect';

import surelyproicon1 from '../../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../../assets/icons/surelyproicon/surelyproicon7.svg';
import { OperativesContext } from 'src/context/OperativesContext';

interface JobsFilterProps {
  active: boolean;
  deactivate: () => void;
  innerFilters: any;
}

const OperativesFilter: React.FC<JobsFilterProps> = ({ active, deactivate, innerFilters }) => {
  const { handleFilters, resetFilters } = useContext(OperativesContext);

  const [postCode, setPostCode] = useState(innerFilters?.postal_code);
  const [locationRange, setLocationRange] = useState(innerFilters?.job_location_range);
  const [siaLicense, setSIALicense] = useState<string[]>(innerFilters?.sia_licence);
  const [industrySectors, setIndustrySectors] = useState<string[]>(innerFilters?.industry_sectors);
  const [surelyProBadge, setSurelyProBadge] = useState<string[]>(innerFilters?.surely_pro_badge);
  const [ratings, setRatings] = useState<any>(innerFilters?.rating);
  const [isInstantBook, setIsInstantBook] = useState<boolean>(innerFilters?.instant_book);
  const [isInclusivityPledge, setIsInclusivityPledge] = useState<boolean>(innerFilters?.is_inclusivity_pledge);
  const [isBS7858, setIsBS7858] = useState<boolean>(innerFilters?.is_inclusivity_pledge);

  const sliderBackground = `linear-gradient(to right, #323C58 0%, #323C58 ${locationRange}%, #BBC1D3 ${locationRange}%, #BBC1D3 100%)`;

  const filters = {
    postCode,
    locationRange,
    siaLicense,
    industrySectors,
    surelyProBadge,
    ratings,
    isInstantBook,
    isInclusivityPledge,
    isBS7858,
  };
  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocationRange(Number(event.target.value));
  };
  const placeholderOptionSia = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionIndustry = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionSurelyBadge = {
    label: 'Select or type...',
    value: '',
  };
  const handleSelectSurelyProBadge = (surelyBadge: string) => {
    if (!surelyProBadge.includes(surelyBadge)) {
      setSurelyProBadge((prevSurelyBadge) => [...prevSurelyBadge, surelyBadge]);
    }
  };

  const handleRemoveSurelyProBadge = (surelyBadge: string) => {
    setSurelyProBadge((prevSurelyBadge) => prevSurelyBadge.filter((selectedSurelyBadge) => selectedSurelyBadge !== surelyBadge));
  };

  const clearAllSelectedSurelyProBadge = () => {
    setSurelyProBadge([]);
  };
  const handleSelectIndustry = (industry: string) => {
    if (!industrySectors.includes(industry)) {
      setIndustrySectors((prevSectors) => [...prevSectors, industry]);
    }
  };
  const handleRemoveIndustry = (industry: string) => {
    setIndustrySectors((prevSectors) => prevSectors.filter((selectedIndustry) => selectedIndustry !== industry));
  };
  const clearAllSelectedIndustries = () => {
    setIndustrySectors([]);
  };

  const handleSliderLocationChange = ({ value }: { value: number }) => {
    setLocationRange(value);
  };

  const handleSelectSiaLicense = (sia: string) => {
    if (!siaLicense.includes(sia)) {
      setSIALicense((prevSia) => [...prevSia, sia]);
    }
  };

  const handleRemoveSiaLicense = (sia: string) => {
    setSIALicense((prevSia) => prevSia.filter((selectedSia) => selectedSia !== sia));
  };

  const clearAllSelectedSiaLicense = () => {
    setSIALicense([]);
  };
  const getIconForSurelyProBadge = (badge: string): string => {
    switch (badge) {
      case 'CustomerService':
        return surelyproicon3;
      case 'UseOfEquipment':
        return surelyproicon2;
      case 'DisabilityFocus':
        return surelyproicon6;
      case 'SubstanceAwareness':
        return surelyproicon4;
      case 'VulnerablePeople':
        return surelyproicon5;
      case 'ConflictManagament':
        return surelyproicon7;
      default:
        return surelyproicon1;
    }
  };

  const handleRatingsChange = (rating: number) => {
    setRatings(rating);
  };

  const handleInstantBook = () => {
    setIsInstantBook((prev) => !prev);
  };

  const handleInclusivityPledge = () => {
    setIsInclusivityPledge((prev) => !prev);
  };

  const handleReset = () => {
    setPostCode('');
    setLocationRange(0);
    setSIALicense([]);
    setIndustrySectors([]);
    setSurelyProBadge([]);
    setRatings(0);
    setIsInstantBook(false);
    setIsInclusivityPledge(false);

    resetFilters();
  };
  const countNonEmptyStates = (
    postCode: any,
    locationRange: any,
    siaLicense: any,
    industrySectors: any,
    surelyProBadge: any,
    ratings: any,
    isInstantBook: any,
    isInclusivityPledge: any,
  ) => {
    let count = 0;
    count += postCode.trim() !== '' ? 1 : 0;
    count += locationRange !== 0 ? 1 : 0;
    count += siaLicense.length > 0 ? 1 : 0;
    count += industrySectors.length > 0 ? 1 : 0;
    count += surelyProBadge.length > 0 ? 1 : 0;
    count += ratings !== 0 ? 1 : 0;
    count += isInstantBook ? 1 : 0;
    count += isInclusivityPledge ? 1 : 0;
    return count;
  };

  const handleSubmitFilters = () => {
    // const nonEmptyCount = countNonEmptyStates(
    //   postCode,
    //   locationRange,
    //   siaLicense,
    //   industrySectors,
    //   surelyProBadge,
    //   ratings,
    //   isInstantBook,
    //   isInclusivityPledge
    // );

    handleFilters(filters);
  };

  const handleRateClick = (value: number) => {
    setRatings(value);
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] !p-0 '>
      <View className='flex'>
        <View className='flex grow items-center  p-0 pl-[24px]  pt-[24px]'>
          <Text className='rubik py-[16px] text-[24px] font-normal leading-[32px] text-[#323C58]'>Filters</Text>
        </View>
        <button onClick={deactivate} className='btn-no-hover flex items-start justify-end  pb-0 pr-[12px] pt-[12px]'>
          <span className='material-icons text-500 p-[14px] align-middle  '>close</span>
        </button>
      </View>
      <Divider />

      <View className='flex flex-col items-start gap-[16px] p-[24px]'>
        <p className='rubik py-[4px] text-[16px] font-medium leading-[24px] text-[#323C58] underline' onClick={handleReset}>
          Reset
        </p>
        <View className='flex w-full flex-col items-start gap-[4px]'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Your postcode</Text>
          <TextField
            name='postcode'
            value={postCode}
            placeholder='E1 6AN'
            className='h-[48px] w-full rounded-[4px] lg:w-[376px]'
            onChange={(e) => setPostCode(e.value)}
          />
        </View>
        <View className='flex w-full flex-col items-start gap-[4px]'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Job location range</Text>
          <div className='mt-[8px] flex w-full flex-col items-center'>
            <div className='rubik text-center text-base font-normal text-[#14171F]'>
              <span id='sliderValue' className=' mt-[12px]'>
                {locationRange}
              </span>
              miles
            </div>
            <input
              type='range'
              min='0'
              max='100'
              value={locationRange}
              className='mt-[10px] h-1 w-full'
              style={{ background: sliderBackground }}
              id='slider'
              onChange={handleSliderChange}
            />
            <div className='mt-[12px] flex w-full justify-between'>
              <span className='rubik text-base font-medium leading-[16px] text-[#323C58]'>0 miles</span>
              <span className='rubik text-base font-medium leading-[16px] text-[#323C58]'>+60 miles</span>
            </div>
          </div>
        </View>
        <View className='flex w-full flex-col items-start gap-[4px] '>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Rating level</Text>
          {/* <View className='w-full flex flex-row items-start gap-[12px]'>
            <StarSelect
              ratings={ratings}
              className='w-full mt-2'
              onChange={handleRatingsChange}
            />
          </View> */}
          <View className='mt-[5px] flex flex-row gap-[8px]'>
            {[1, 2, 3, 4, 5].map((index) => (
              <span
                key={index}
                onClick={() => handleRateClick(index)}
                className={` material-icons-outlined  ${ratings >= index ? 'text-[#F4BF00]' : 'text-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
              >
                star
              </span>
            ))}
          </View>
        </View>
        <View className='flex flex-col items-start gap-[4px] lg:w-full '>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>SIA license</Text>
          <Select
            className='mt-2 w-full rounded-md border p-2'
            name='sia_license'
            placeholder={siaLicense?.length > 0 ? '' : placeholderOptionSia.label}
            options={[
              {
                label: 'Close Protection',
                value: 'Close Protection',
              },
              { label: 'Door Supervisor', value: 'Door Supervisor' },
              { label: 'Security Guard', value: 'Security Guard' },
              {
                label: 'Public Space Surveillance',
                value: 'Public Space Surveillance',
              },
            ]}
            onChange={(selectedOption: any) => {
              if (selectedOption.value !== '') {
                handleSelectSiaLicense(selectedOption.value);
              }
            }}
            startSlot={
              <>
                <div className='w-[160px] gap-2'>
                  {siaLicense.map((selectedSia) => (
                    <Button
                      key={selectedSia}
                      size='small'
                      rounded={true}
                      elevated={false}
                      onClick={() => handleRemoveSiaLicense(selectedSia)}
                      className='mr-[10px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs !text-[#323c58]'
                    >
                      <Text color='positive' className='flex items-center gap-1'>
                        <span className='material-icons text-[14px]'>star</span>
                        {selectedSia}
                      </Text>
                    </Button>
                  ))}
                </div>
                {siaLicense?.length > 0 && (
                  <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedSiaLicense}>
                    Clear all
                  </Button>
                )}
              </>
            }
          />
        </View>
        <View className='flex flex-col items-start gap-[4px] lg:w-full'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Industry sector</Text>
          <Select
            className='mt-2 w-full rounded-md border p-2'
            name='industry_sectors'
            placeholder={industrySectors?.length > 0 ? '' : placeholderOptionIndustry.label}
            options={[
              {
                label: 'Bars, Clubs & Restaurants',
                value: 'Bars, Clubs & Restaurants',
              },
              { label: 'Events & Festivals', value: 'Events & Festivals' },
              { label: 'Private Hire', value: 'Private Hire' },
              { label: 'Film, TV & Media', value: 'Film, TV & Media' },
              { label: 'Rail, Air & Road', value: 'Rail, Air & Road' },
              { label: 'Commercial Offices', value: 'Commercial Offices' },
              { label: 'Construction', value: 'Construction' },
              { label: 'Education', value: 'Education' },
              {
                label: 'Financial & Banking',
                value: 'Financial & Banking',
              },
              { label: 'Government', value: 'Government' },
              { label: 'Healthcare', value: 'Healthcare' },
              { label: 'High Street Retail', value: 'High Street Retail' },
              { label: 'Other', value: 'Other' },
            ]}
            onChange={(selectedOption: any) => {
              if (selectedOption.value !== '') {
                handleSelectIndustry(selectedOption.value);
              }
            }}
            startSlot={
              <>
                <div className='w-[160px] gap-2'>
                  {industrySectors.map((selectedIndustry) => (
                    <Button
                      key={selectedIndustry}
                      size='small'
                      rounded={true}
                      elevated={false}
                      onClick={() => handleRemoveIndustry(selectedIndustry)}
                      className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs'
                    >
                      <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedIndustry}</Text>
                    </Button>
                  ))}
                </div>
                {industrySectors?.length > 0 && (
                  <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedIndustries}>
                    Clear all
                  </Button>
                )}
              </>
            }
          />
        </View>
        <View className='flex flex-col items-start gap-[4px] lg:w-full'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>SurelyPro badge</Text>
          <Select
            className='mt-2 w-full rounded-md border p-2'
            name='industry_sectors'
            placeholder={surelyProBadge?.length > 0 ? '' : placeholderOptionSurelyBadge.label}
            options={[
              { label: 'Customer Service', value: 'CustomerService' },
              { label: 'Use of Equipment', value: 'UseOfEquipment' },
              { label: 'Disability Focus', value: 'DisabilityFocus' },
              { label: 'Substance Awareness', value: 'SubstanceAwareness' },
              { label: 'Vulnerable People', value: 'VulnerablePeople' },
              { label: 'Conflict Managament', value: 'ConflictManagament' },
            ]}
            onChange={(selectedOption: any) => {
              if (selectedOption.value !== '') {
                handleSelectSurelyProBadge(selectedOption.value);
              }
            }}
            startSlot={
              <>
                <div className='w-[160px] gap-2'>
                  {surelyProBadge.map((selectedSurelyBadge) => (
                    <Button
                      key={selectedSurelyBadge}
                      size='small'
                      rounded={true}
                      elevated={false}
                      onClick={() => handleRemoveSurelyProBadge(selectedSurelyBadge)}
                      className='mr-[5px] mt-[8px]  max-w-xs overflow-hidden truncate !bg-[#DDEFFF] px-2 py-1 text-xs'
                    >
                      <div className='flex flex-row'>
                        {' '}
                        <img src={getIconForSurelyProBadge(selectedSurelyBadge)} alt={`Icon for ${selectedSurelyBadge}`} className='mr-2 h-4 w-4' />
                        <Text className='rubik font-normal leading-4 text-[#053D6D]'>{selectedSurelyBadge}</Text>
                      </div>
                    </Button>
                  ))}
                </div>
                {surelyProBadge?.length > 0 && (
                  <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedSurelyProBadge}>
                    Clear all
                  </Button>
                )}
              </>
            }
          />
        </View>
        <View className='flex flex-row justify-between gap-[4px] lg:w-full'>
          <div className='flex flex-col'>
            <Text className='rubik text-center text-start text-[16px] text-[#1A1A1A] xl:font-medium xl:leading-5'>Instant Book</Text>
            <Text className='rubik text-sm text-gray-600'>
              Find and filter through security operatives who are available for an instant response.
            </Text>
          </div>
          <Switch name='emergency' checked={isInstantBook} onChange={handleInstantBook} />
        </View>
        <View className='flex flex-row justify-between gap-[4px] lg:w-full'>
          <div className='flex flex-col'>
            <Text className='rubik text-center text-start text-[16px] text-[#1A1A1A] xl:font-medium xl:leading-5'>Inclusivity Pledge</Text>

            <Text className='rubik text-sm text-gray-600'>
              This badge serves as a visible symbol of commitment to promoting inclusivity and diversity.
            </Text>
          </div>
          <Switch name='inclusivity' checked={isInclusivityPledge} onChange={handleInclusivityPledge} />
        </View>
        <Button
          onClick={() => {
            handleSubmitFilters();
            deactivate();
          }}
          className='flex items-center justify-center gap-2 self-stretch rounded-[8px] !bg-[#0B80E7] p-3'
        >
          <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFF]'>Show your results</Text>
        </Button>
      </View>
    </Modal>
  );
};

export default OperativesFilter;
