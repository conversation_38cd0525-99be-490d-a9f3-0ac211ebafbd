export type QuestionDataConflictManagement = {
  id: number;
  question: string;
  answer_1: string;
  answer_2: string;
  answer_3: string;
  answer_4: string;
  correct_answer: number;
};

export const conflictManagementQuestions: QuestionDataConflictManagement[] = [
  {
    id: 1,
    question:
      'Which is the correct order of the four primary stages of conflict?',
    answer_1: 'Frustration, Anger, Aggression, Violence',
    answer_2: 'Frustration, Aggression, Anger, Violence',
    answer_3: 'Anger, Frustration, Aggression, Violence',
    answer_4: 'Violence, Frustration, Anger, Aggression',
    correct_answer: 1,
  },
  {
    id: 2,
    question:
      'Which of the following approaches is not an effective communication skill?',
    answer_1: 'Listening attentively for a response',
    answer_2: 'Looking for a non-verbal confirmation in a noisy area',
    answer_3: 'Raising your voice and standing square on with a customer',
    answer_4: 'Reinforcing what you say with positive body language',
    correct_answer: 3,
  },
  {
    id: 3,
    question:
      'When we become angry or upset, what can happen that might make things worse?',
    answer_1: 'We engage the emotional side of the brain',
    answer_2: 'We engage the rational side of the brain',
    answer_3: 'The “fight or flight” response takes place',
    answer_4: 'A “trigger” response occurs',
    correct_answer: 1,
  },
  {
    id: 4,
    question:
      'Which of the following terms is used to describe a reaction that prevents action?',
    answer_1: 'Trigger',
    answer_2: 'Inhibitor',
    answer_3: 'Fight',
    answer_4: 'Aggression',
    correct_answer: 2,
  },
  {
    id: 5,
    question:
      'What is not a good action to take when dealing with unacceptable behaviour?',
    answer_1: 'Being appropriately assertive ',
    answer_2: 'Making a decision to confront it',
    answer_3: 'Recognising a communication block',
    answer_4: 'Directing criticism to the person',
    correct_answer: 4,
  },
];
