// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react';
import { Text, View, Button, Divider, Modal, TextField } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';

interface AddPositionEmploymentProps {
  active: boolean;
  deactivate: () => void;
  positionArray: any[];
  onAddPosition: (firstTabSettings: any) => void;
  selectedPosition: any;
  onEditPosition: (id: any) => void;
  isEditing: boolean;
}

const AddPositionEmployment: React.FC<AddPositionEmploymentProps> = ({
  active,
  deactivate,
  positionArray,
  onAddPosition,
  selectedPosition,
  onEditPosition,
  isEditing,
}) => {
  const [referenceLetter1, setReferenceLetter1] = useState<string[]>([]);
  const referenceLetter = referenceLetter1[0];
  const [fileName, setFileName] = useState<any>();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [validFileName, setValidFileName] = useState(true);

  const [companyName, setCompanyName] = useState('');
  const [companyWebsite, setCompanyWebsite] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [jobDescription, setJobDescription] = useState('');
  const [isCurrent, setIsCurrent] = useState<boolean>(false);
  const [startDateMM, setStartDateMM] = useState('');
  const [startDateYYYY, setStartDateYYYY] = useState('');
  const startDateDD = '01';
  const startDate = startDateYYYY + '-' + startDateMM.padStart(2, '0') + '-' + startDateDD;

  const endDateDD = '01';
  const [endDateMM, setEndDateMM] = useState('');
  const [endDateYYYY, setEndDateYYYY] = useState('');
  const endDate = isCurrent ? '' : endDateYYYY + '-' + endDateMM.padStart(2, '0') + '-' + endDateDD;

  const [errorMessage, setErrorMessage] = useState('');

  const [isReferenceLetter, setIsReferenceLetter] = useState<boolean>(false);

  const handleChange = (event: any) => {
    if (event.target.value.length <= 100) {
      setJobDescription(event.target.value);
    }
  };

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
          resolve(e.target.result);
        } else {
          reject(new Error('Failed to read file as base64.'));
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleIsCurrent = (e: any) => {
    setIsCurrent(e.target.checked);
  };
  const handleReferenceLetter = (e: any) => {
    setIsReferenceLetter(e.target.checked);
  };
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files: any = e.target.files;
    setFileName(files[0].name);
    if (files && files.length > 0) {
      const fileDataArray: string[] = [];
      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push(base64);
      }
      setReferenceLetter1(fileDataArray);
    }
  };
  const deleteFile = (indexToDelete: number) => {
    const updatedFiles = referenceLetter1.filter((_, index) => index !== indexToDelete);
    setReferenceLetter1(updatedFiles);
  };

  const openFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const isStartDateValid = startDateYYYY.length === 4;
  const isEndDateValid = endDateYYYY.length === 4;
  const isReferenceLetterEmpty = isReferenceLetter && referenceLetter1.length === 0;
  const isDateFieldsValid =
    /^\d+$/.test(startDateMM) && /^\d+$/.test(startDateYYYY) && (!isCurrent || (isCurrent && endDateMM === '' && endDateYYYY === ''));

  const handleDateChange = (
    value: string,
    setStateFunction: React.Dispatch<React.SetStateAction<string>>,
    maxLength: number,
    minValue = 0,
    maxValue = Infinity,
  ) => {
    const numericValue = value.replace(/\D/g, '');

    const numberValue = parseInt(numericValue, 10);

    if (numericValue.length === 0 || (numericValue.length <= maxLength && numberValue >= minValue && numberValue <= maxValue)) {
      setStateFunction(numericValue.toString());
    }
  };

  const savePosition = () => {
    if (!jobTitle || !startDateMM || !startDateYYYY) {
      setErrorMessage('Job title and start date are required');
      return;
    }
    if (isReferenceLetter && referenceLetter1.length === 0) {
      setErrorMessage('You must provide a reference letter.');
      return;
    }
    if (!isStartDateValid) {
      setErrorMessage('Start date should be a 4-digit year (YYYY) and a valid month (MM)');
      return;
    }
    if (!isCurrent && !isEndDateValid) {
      setErrorMessage('End date should be a 4-digit year (YYYY) and a valid month (MM) if not current.');
      return;
    }
    if (!isCurrent) {
      if (
        parseInt(startDateYYYY) > parseInt(endDateYYYY) ||
        (parseInt(startDateYYYY) === parseInt(endDateYYYY) && parseInt(startDateMM) > parseInt(endDateMM))
      ) {
        setErrorMessage('The end date cannot be earlier than the start date.');
        return;
      }
    }
    const newPositionData = {
      referenceLetter,
      companyName,
      companyWebsite: companyWebsite,
      jobTitle,
      jobDescription,
      isCurrent,
      startDate,
      endDate: isCurrent ? null : endDate,
      isReferenceLetter,
    };
    onAddPosition(newPositionData);
    setFileName('');
    setValidFileName(true);
    setCompanyName('');
    setCompanyWebsite('');
    setJobTitle('');
    setJobDescription('');
    setIsCurrent(false);
    setStartDateMM('');
    setStartDateYYYY('');
    setEndDateMM('');
    setEndDateYYYY('');
    setIsReferenceLetter(false);
    setErrorMessage('');
    deactivate();
  };

  const editPosition = () => {
    if (!jobTitle || !startDateMM || !startDateYYYY) {
      setErrorMessage('Job title and start date are required');
    } else if (isReferenceLetterEmpty) {
      setErrorMessage('You must provide a reference letter.');
    } else if (!isCurrent && !isEndDateValid) {
      setErrorMessage('End date should be a 4-digit year (YYYY) if not current.');
    } else {
      const editedPositionData = {
        id: selectedPosition.id,
        data: {
          referenceLetter,
          companyName,
          companyWebsite,
          jobTitle,
          jobDescription,
          isCurrent,
          startDate,
          endDate: isCurrent ? null : endDate,
          isReferenceLetter,
        },
      };

      onEditPosition(editedPositionData);
      deactivate();
    }
  };

  useEffect(() => {
    if (active) {
      if (active && selectedPosition) {
        setCompanyName(selectedPosition.companyName || '');
        setCompanyWebsite(selectedPosition.companyWebsite || '');
        setJobTitle(selectedPosition.jobTitle || '');
        setJobDescription(selectedPosition.jobDescription || '');
        setIsCurrent(selectedPosition.isCurrent || false);

        if (selectedPosition.startDate) {
          const startDateParts = selectedPosition.startDate.split('-');
          setStartDateYYYY(startDateParts[0] || '');
          setStartDateMM(startDateParts[1] || '');
        }

        if (selectedPosition.endDate) {
          const endDateParts = selectedPosition.endDate.split('-');
          setEndDateYYYY(endDateParts[0] || '');
          setEndDateMM(endDateParts[1] || '');
        }

        setIsReferenceLetter(selectedPosition.isReferenceLetter || false);
        setReferenceLetter1([selectedPosition.referenceLetter || '']);
      } else {
        setCompanyName('');
        setCompanyWebsite('');
        setJobTitle('');
        setJobDescription('');
        setIsCurrent(false);
        setStartDateYYYY('');
        setStartDateMM('');
        setEndDateYYYY('');
        setEndDateMM('');
        setIsReferenceLetter(false);
        setReferenceLetter1([]);
      }
    }
  }, [active, selectedPosition]);

  const handleFileDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const fileDataArray: string[] = [];
      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push(base64);
      }
      setReferenceLetter1(fileDataArray);
      setFileName(files[0].name);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  return (
    <Modal active={active} onClose={deactivate} className='padding-[12px 24px, 12px, 24px] !h-[auto] !w-[424px] !px-[0] !py-[24px]'>
      <View>
        <View className='px-[24px] '>
          <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
            <span className='material-icons text-500 align-middle'>close</span>
          </button>
          <View className='flex items-center p-0'>
            <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>{isEditing ? 'Edit' : 'Add'} position</Text>
          </View>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full' />

        <View className='h-[400px] overflow-auto px-[24px]'>
          <View className='flex flex-col'>
            <View className='mt-[16px] flex flex-col gap-[4px]'>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Company name</Text>
              <TextField name='text' value={companyName} onChange={(e) => setCompanyName(e.value)} className='h-[48px] w-full sm:w-[376px] ' />
            </View>
            <View className='mt-[16px] flex flex-col gap-[4px]'>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Company Website</Text>
              <TextField name='text' value={companyWebsite} onChange={(e) => setCompanyWebsite(e.value)} className='h-[48px] w-full sm:w-[376px] ' />
            </View>
            <View className='mt-[16px] flex flex-col gap-[4px]'>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Job Title</Text>
              <TextField name='text' className='h-[48px] w-full sm:w-[376px] ' value={jobTitle} onChange={(e) => setJobTitle(e.value)} />
            </View>
            <View className='mt-[16px] flex flex-col gap-[4px]'>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Description</Text>

              <div className='flex flex w-full flex-col justify-between rounded border border-solid border-gray-300 sm:w-[376px]'>
                <textarea
                  rows={10}
                  name='text'
                  className=' text-wrap ml-[7px] mt-[5px] h-[68px] border-none outline-none'
                  value={jobDescription}
                  onChange={handleChange}
                  maxLength={100}
                />
                <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{100 - jobDescription.length} characters left</p>
              </div>
            </View>
            <View className='mb-3 mt-[16px] flex items-center gap-[8px]'>
              <input type='checkbox' className=' h-4 w-4' checked={isCurrent} onChange={handleIsCurrent} />
              <Text className='rubik text-[14px] font-normal leading-[20px] text-[#1A1A1A]'>I am currently working here</Text>
            </View>
            <View className='mt-[16px] flex flex-col gap-[4px]'>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Start date</Text>
              <View className='flex flex-row'>
                <TextField
                  name='text'
                  className='mr-[10px] h-[48px] w-[182px]'
                  placeholder='MM'
                  value={startDateMM}
                  onChange={(e) => handleDateChange(e.value, setStartDateMM, 3, 1, 12)}
                />
                <TextField
                  name='text'
                  className='h-[48px] w-[182px] '
                  placeholder='YY'
                  value={startDateYYYY}
                  onChange={(e) => handleDateChange(e.value, setStartDateYYYY, 4)}
                />
              </View>
            </View>
            {!isCurrent && (
              <View className='mt-[16px] flex flex-col gap-[4px]'>
                <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>End date</Text>
                <View className='flex flex-row'>
                  <TextField
                    name='text'
                    className='mr-[10px] h-[48px]  w-[182px]'
                    placeholder='MM'
                    value={endDateMM}
                    onChange={(e) => handleDateChange(e.value, setEndDateMM, 3, 1, 12)}
                  />
                  <TextField
                    name='text'
                    className='h-[48px] w-[182px] '
                    placeholder='YYYY'
                    value={endDateYYYY}
                    onChange={(e) => handleDateChange(e.value, setEndDateYYYY, 4)}
                  />
                </View>
              </View>
            )}
          </View>
          <Divider className='mt-[18px] h-[1px] w-full' />
          <View className='mb-3 mt-[16px] flex items-center gap-[8px]'>
            <input type='checkbox' className=' h-4 w-4' required checked={isReferenceLetter} onChange={handleReferenceLetter} />
            <Text className='rubik text-[14px] font-normal leading-[20px] text-[#1A1A1A]'>I am willing to share the reference letter</Text>
          </View>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Upload reference letter</Text>
          <div onDrop={handleFileDrop} onDragOver={handleDragOver} onDragEnter={handleDragEnter}>
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined mt-[-1px] text-[21px] text-[#323C58]'>upload</span>}
              onClick={openFileInput}
              className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#000000]'
              disabled={!isReferenceLetter}
            >
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#323C58]'>
                Drop or&nbsp;
                <span className='rubik text-[14px] font-medium leading-[20px] text-[#0B80E7]'>browse</span>
              </Text>
              <input
                type='file'
                ref={fileInputRef}
                accept='.pdf,.doc,.docx,.jpg,.jpeg,.png'
                style={{ display: 'none' }}
                onChange={handleFileChange}
              />
            </Button>
          </div>
          {!validFileName && <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Specified document is required.</Text>}
          <ul>
            {referenceLetter1.map((file, index) => (
              <li key={index}>
                <View className='flex flex-row items-center'>
                  <Text className='rubik text-base font-normal leading-5 text-[#1A1A1A] '>{fileName}</Text>

                  <Button
                    variant='outline'
                    className='ml-[16px] mt-[10px] flex w-[78px] items-center justify-center !border-[#DFE2EA] !bg-[#fff] '
                    onClick={() => deleteFile(index)}
                  >
                    <Text className='rubik p-[4px 8px 4px 8px] flex items-center align-middle text-sm font-normal text-[#CB101D]'>
                      <span className='material-icons align-middle text-[20px]'>close</span>
                      Delete
                    </Text>
                  </Button>
                </View>
              </li>
            ))}
          </ul>
          <Text className='text-neutral rubik mt-[16px] text-[13px] leading-4 text-[#323C58]'>PDF, JPEG, PNG files only.</Text>
          {errorMessage && <Text className='rubik mt-[16px] text-[15px] font-normal leading-5 text-red-400'>{errorMessage}</Text>}
        </View>
        <Divider className='mt-[16px] h-[1px] w-full' />
        <View className='mt-[16px] flex flex-row justify-end px-[24px]'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border  !bg-[#0B80E7] px-4 py-2'
            onClick={() => {
              isEditing ? editPosition() : savePosition();
            }}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>{isEditing ? 'Edit' : 'Add'}</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default AddPositionEmployment;
