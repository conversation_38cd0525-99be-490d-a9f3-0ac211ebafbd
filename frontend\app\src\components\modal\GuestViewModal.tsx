// src/components/modal/GuestViewModal.tsx
import { Text, View, Divider } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import GuestLoginForm from '../auth/GuestLoginForm';

const GuestViewModal = () => {
  const { closeModal, openModal } = useModalAction();

  const handleSignUpClick = () => {
    closeModal();
    openModal('REGISTER');
  };

  return (
    <View className='flex flex-col items-start p-6'>
      <View className='flex w-full items-center justify-between'>
        <Text className='font-rufina-stencil text-[32px] text-[#323C58]'>
          Guest View Access
        </Text>
        <button
          onClick={() => closeModal()}
          className='btn-no-hover flex items-center justify-end'
        >
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
      </View>

      <Divider className='mt-4 mb-6 h-[1px] w-full' />
      
      <View className='w-full mb-6'>
        <Text className='rubik text-[16px] font-medium text-[#323C58] mb-2'>
          Welcome to Surley
        </Text>
        <Text className='rubik text-[14px] text-[#6B7280] mb-6'>
          Please enter your email address to continue browsing as a guest.
        </Text>
        
        <GuestLoginForm />

        <View className='flex items-center justify-center mt-2'>
          <Text className='rubik text-[14px] text-[#6B7280]'>
            or
            <button 
              onClick={handleSignUpClick}
              className='text-[#0B80E7] hover:underline focus:outline-none bg-transparent'
            >
              Sign up now
            </button>
          </Text>
        </View>
      </View>
    </View>
  );
};

export default GuestViewModal;