# HTTP Configuration (for redirection to HTTPS)
server {
    listen 80;
    server_name grun-oase.com;
    
    # Redirect all traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS Configuration
server {
    listen 443 ssl;
    server_name grun-oase.com;

    root /usr/share/nginx/html;
    index index.html;

    ssl_certificate /etc/letsencrypt/live/grun-oase.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/grun-oase.com/privkey.pem;

    # Handle Single Page Application routing
    location / {
        try_files $uri $uri/ /index.html;

        # Add CORS headers for SPA
        add_header 'Access-Control-Allow-Origin' 'https://grun-oase.com' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With,X-CSRF-TOKEN'>
    }

    # API proxy
    location /api/ {
        proxy_pass https://grun-oase.com;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;

        # CORS headers for API
        add_header 'Access-Control-Allow-Origin' 'https://grun-oase.com' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With,X-CSRF-TOKEN,>
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Expose-Headers' 'Authorization' always;

        # Handle OPTIONS method (preflight CORS request)
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}

