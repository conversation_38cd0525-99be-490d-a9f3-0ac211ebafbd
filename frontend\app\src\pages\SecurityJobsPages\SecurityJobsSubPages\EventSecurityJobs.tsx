// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion, Card } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import eventSecurityQuestions, {
  EventSecurityJobsQuestionsType,
} from '../../../store/dummydata/SecurityJobsQuestionsDummyData/EventSecurityJobsQuestionsDummyData';
import BodyguardButton from '../SecurityJobsButtons/BodyguardButton';
import CctvOperatorButton from '../SecurityJobsButtons/CctvOperatorButton';
import CloseProtectionButton from '../SecurityJobsButtons/CloseProtectionButton';
import DoorSupervisorButton from '../SecurityJobsButtons/DoorSupervisorButton';
import EventSecurityButton from '../SecurityJobsButtons/EventSecurityButton';
import PrivateSecurityButton from '../SecurityJobsButtons/PrivateSecurityButton';
import SecurityGuardButton from '../SecurityJobsButtons/SecurityGuardButton';
import eventsecurityjobs1 from '../../../assets/images/securityjobs/eventsecurityjobs1.png';
import eventsecurityjobs2 from '../../../assets/images/securityjobs/eventsecurityjobs2.png';
import eventsecurityjobs3 from '../../../assets/images/securityjobs/eventsecurityjobs3.png';
import eventsecurityjobs4 from '../../../assets/images/securityjobs/eventsecurityjobs4.png';

const EventSecurityJobs: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;
  const [activeId, setActiveId] = useState<number | null>(null);

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex lg:flex-row flex-col w-full max-w-[1320px] mx-auto items-center text-center py-[64px] '>
          <img src={eventsecurityjobs1} className='lg:w-[657px] w-full xl:h-[392px] ' />
          <View className='flex flex-col lg:w-[675px] xl:h-[392px]  p-[32px] xl:p-[50px] xl:px-[50px] gap-[12px] px-3  bg-[#FFFF]  '>
            <Text className='text-left font-rufina-stencil text-[30px] lg:text-[30px]  xl:text-[48px] font-normal xl:leading-[56px] text-[#323C58]'>
              Find event security jobs in London the easy way
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px]'>
              Looking for your next role in event security in London and the UK? We’re changing the face of hiring for
              event security jobs with our innovative new platform. Take control of your career and work on your terms
              with our help. You can easily apply for SIA event security jobs in London with the help of Surely
              Security.{' '}
            </Text>
            <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1100px]  sm:gap-[64px] mx-auto py-[64px] '>
          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[32px] gap-10 mt-[20px] sm:mt-[0] sm:mb-0 mb-8'>
            <View className='flex flex-col gap-3 justify-center w-full  xl:w-[532px] items-start xl:px-0 px-3  sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil lg:text-[30px]  xl:text-[48px] text-[30px] font-normal xl:leading-[56px]'>
                Freelance Event Security Jobs in London
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                With Surely Security, you’re in control. Simply sign up to the platform and create your profile. You can
                then search event security jobs in London and across the UK, including festival security jobs and event
                stewarding, and then apply directly to contractors and clients. No more unnecessary layers of middlemen
                taking a cut of your profits and driving up hiring costs to end-clients. We connect the best
                SIA-licensed professionals with reputable agents and clients with a simple and accessible platform. Find
                work, negotiate your rates and get paid on time with the help of Surely Security.{' '}
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={eventsecurityjobs2}
              className='  sm:mt-0 w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1'
            />
          </View>

          <View className='flex flex-col-reverse lg:flex-row max-w-[1100px] justify-between sm:gap-[32x] gap-10   sm:mb-0 mb-8 '>
            <img src={eventsecurityjobs3} className='w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1' />
            <View className='flex flex-col gap-3 justify-center w-full xl:w-[532px] items-start px-3 xl:px-0 sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[30px]  xl:text-[48px]  font-normal xl:leading-[56px]'>
                Find your next event security job today
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Skilled event security professionals like you will always be in demand. Find your next opportunity to
                start earning on your terms with our innovative platform. We offer a comprehensive platform to help you
                find work, negotiate contracts and get paid on time. With Surely Security, you will find event
                stewarding jobs in London and across the UK that work for your schedule.{' '}
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
          </View>

          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[32px] gap-10  '>
            <View className='flex flex-col gap-3 justify-center w-full xl:w-[532px] items-start px-3 xl:px-0  sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[30px]  xl:text-[48px] font-normal xl:leading-[56px]'>
                Work when, where and how you want
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Event security jobs are typically part-time roles which can make it difficult for self-employed event
                security professionals to find regular work without a lot of hard work and high risk. The problem with
                all the unnecessary layers of middlemen is that they drive up prices for contractors and clients and
                drive down rates for security operatives. We cut out all the unnecessary waste so you can find work
                directly and take control of your earning potential.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={eventsecurityjobs4}
              className=' mt-[20px] sm:mt-0 w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1'
            />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center xl:px-0 px-3 '>
          <Text className='!text-[#323C58] font-rufina-stencil sm:px-0 px-6  sm:text-center text-left  text-[32px] text-[30px] font-normal sm:leading-[40px] leading-[56px] items-center mt-[64px]  sm:ml-[0px]'>
            Frequently asked questions about event security jobs
          </Text>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-[36px] mb-[65px]'>
            <div className='flex flex-col gap-4 '>
              {eventSecurityQuestions.map((item: EventSecurityJobsQuestionsType) => {
                if (item.id % 2 === 1) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                            endIcon={() => (
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='22'
                                height='22'
                                viewBox='0 0 22 22'
                                fill='none'
                              >
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                {item.question}
                              </p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' xl:w-[600px] flex flex-row '>
                          <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]  '>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
            <div className='flex flex-col gap-4 '>
              {eventSecurityQuestions.map((item: EventSecurityJobsQuestionsType) => {
                if (item.id % 2 === 0) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                            endIcon={() => (
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='22'
                                height='22'
                                viewBox='0 0 22 22'
                                fill='none'
                              >
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                {item.question}
                              </p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className='w-[370px] sm:w-[600px] '>
                          <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]'>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
          </div>
        </View>
      </View>
      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1320px] mx-auto gap-[24px] xl:px-0 px-3 '>
          <View className='flex flex-col sm:flex-row justify-between lg:mt-[64px] gap-[24px]'>
            <SecurityGuardButton />
            <BodyguardButton />
            <CctvOperatorButton />
          </View>
          <View className='flex flex-col sm:flex-row justify-between gap-[24px] mb-[30px] lg:mb-[90px]'>
            <DoorSupervisorButton />
            <PrivateSecurityButton />
            <CloseProtectionButton />
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <Footer />
    </View>
  );
};

export default EventSecurityJobs;
