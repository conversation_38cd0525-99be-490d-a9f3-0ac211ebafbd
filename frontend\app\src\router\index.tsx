// @ts-nocheck
import { lazy } from 'react';
import { useRoutes } from 'react-router-dom';

// import PrivateGuard from '../guards/PrivateGuard';
import PublicGuard from '../guards/PublicGuard';
import Loadable from './Loadable';
import ClientGuard from 'src/guards/ClientGuard';
import OperativeGuard from 'src/guards/OperativeGuard';
import GuestGuard from 'src/guards/GuestGuard';
import GuestClientGuard from 'src/guards/GuestClientGuard';

// LAYOUTS
import GlobalLayout from 'src/layouts/GlobalLayout';
import OperatorJobLayout from 'src/layouts/OperatorJobLayout';
import OperatorProfileLayout from 'src/layouts/OperatorProfileLayout';
import SearchLayout from 'src/layouts/SearchLayout';
import ClientProfileLayout from 'src/layouts/ClientProfileLayout';
import SettingsLayout from 'src/layouts/SettingsLayout';
import SearchOperatorLayout from 'src/layouts/SearchOperatorLayout';
import PostJobLayout from 'src/layouts/PostJobLayout';
import SignInFlowLayout from 'src/layouts/SignInFlowLayout';
import DashboardLayout from 'src/layouts/DashboardLayout';
import FavoriteOperatorLayout from 'src/layouts/FavoriteOperatorLayout';
import ManageJobsLayout from 'src/layouts/ManageJobsLayout';
import SmallScreenMenuLayout from 'src/layouts/SmallScreenMenuLayout';
import SurelyProCourseLayout from 'src/layouts/SurelyProCourseLayout';

// GENERAL PAGES

import SearchPage from 'src/pages/SearchPage/SearchPage';
import SmallScreenHeaderMenu from 'src/components/Header/HeaderMenu/SmallScreenHeaderMenu';
import TermsAndConditions from 'src/pages/TermsAndConditions/TermsAndConditions';
import TermsOfUse from 'src/pages/TermsOfUse/TermsOfUse';
import PrivacyPolicy from 'src/pages/PrivacyPolicy/PrivacyPolicy';
import CookiesPolicy from 'src/pages/CookiesPolicy/CookiesPolicy';
import AccessibilityPolicy from 'src/pages/AccessibilityPolicy/AccessibilityPolicy';
import ContactUs from 'src/pages/ContactUs/ContactUs';
import ReferFriend from 'src/pages/ReferFriend/ReferFriend';

import VerifyEmail from 'src/pages/VerifyEmail/VerifyEmail';
// CLIENT PAGES
import ClientSettings from 'src/pages/Settings/ClientSettings';
import ClientProfile from 'src/pages/ClientProfile/ClientProfile';
import ClientSettingsGeneral from 'src/components/Settings/ClientSettings/ClientSettingsGeneral';
import ClientSettingsLogin from 'src/components/Settings/ClientSettings/ClientSettingsLogin';
import ClientSettingsNotification from 'src/components/Settings/ClientSettings/ClientSettingsNotification';
import ClientSettingsPayment from 'src/components/Settings/ClientSettings/ClientSettingsPayment';
import PostJob from 'src/pages/PostJob/PostJob';
import DashboardPage from 'src/pages/DashboardPage/DashboardPage';
import FavoriteOperator from 'src/pages/FavoriteOperator/FavoriteOperator';
import ManageJobsPage from 'src/pages/ManageJobsPage/ManageJobsPage';
import ClientPage from 'src/pages/ClientPage/ClientPage';
import EditPost from 'src/pages/EditPost/EditPost';

// OPERATOR PAGES
import OperatorJobPage from 'src/pages/JobPage/OperatorJobPage';
import OperatorProfilePage from 'src/pages/OperatorProfilePage/OperatorProfilePage';
import OperatorSettingsAvailability from 'src/components/Settings/OperatorSettings/OperatorSettingsAvailability';
import OperatorSettingsEmployment from 'src/components/Settings/OperatorSettings/OperatorSettingsEmployment';
import OperatorSettingsGeneral from 'src/components/Settings/OperatorSettings/OperatorSettingsGeneral';
import OperatorSettingsLogin from 'src/components/Settings/OperatorSettings/OperatorSettingsLogin';
import OperatorSettingsNotification from 'src/components/Settings/OperatorSettings/OperatorSettingsNotification';
import OperatorSettingsPayment from 'src/components/Settings/OperatorSettings/OperatorSettingsPayment';
import OperatorSettingsProfileDetails from 'src/components/Settings/OperatorSettings/OperatorSettingsProfileDetails';
import OperatorSettingsSiaCertificate from 'src/components/Settings/OperatorSettings/OperatorSettingsSiaCertificate';
import OperatorSettings from 'src/pages/Settings/OperatorSettings';
import SearchOperator from 'src/pages/SearchOperator/SearchOperator';
import FavoriteJobs from 'src/pages/FavoriteJobs/FavoriteJobs';
import OperativePage from 'src/pages/OperativePage/OperativePage';
import SurelyProTraningCategories from 'src/pages/SurelyProTraning/SurelyProTraningCategories';
import SurelyProTraningStartCourse from 'src/pages/SurelyProTraning/SurelyProTraningStartCourse';
import SurelyProTraningQuestions from 'src/pages/SurelyProTraning/SurelyProTraningQuestions';
import SurelyProPoints from 'src/pages/SurelyProTraning/SurelyProPoints';
import SecurityJobs from 'src/pages/SecurityJobsPages/SecurityJobs';
import BodyguardJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/BodyguardJobs';
import CctvOperatorJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/CctvOperatorJobs';
import DoorSupervisorJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/DoorSupervisorJobs';
import EventSecurityJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/EventSecurityJobs';
import PrivateSecurityJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/PrivateSecurityJobs';
import SecurityGuardJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/SecurityGuardJobs';
import CloseProtectionJobs from 'src/pages/SecurityJobsPages/SecurityJobsSubPages/CloseProtectionJobs';
import UsefulResources from 'src/pages/UsefulResources/UsefulResources';
import CodeofConduct from 'src/pages/CodeofConduct/CodeofConduct';
import SecurityMattersArchive from 'src/pages/SecurityMatters/SecurityMattersArchive';
import SecurityMattersPost from 'src/pages/SecurityMatters/SecurityMattersPost';
import SurelyPlusFilms from 'src/pages/SurelyPlusFilms/SurelyPlusFilms';

//Flow
import CrucialData from 'src/pages/SigninFlow/CrucialData/CrucialData';
import OperatorFirstStepFlow from 'src/pages/SigninFlow/OperatorSigninFlow/OperatorFirstStepFlow';
import OperatorSecondStepFlow from 'src/pages/SigninFlow/OperatorSigninFlow/OperatorSecondStepFlow';
import OperatorThirdStepFlow from 'src/pages/SigninFlow/OperatorSigninFlow/OperatorThirdStepFlow';
import OperatorFourthStepFlow from 'src/pages/SigninFlow/OperatorSigninFlow/OperatorFourthStepFlow';
import ClientFirstStepFlow from 'src/pages/SigninFlow/ClientSignInFlow/ClientFirstStepFlow';
import InclusivityPledgePage from 'src/pages/InclusivityPledgePage/InclusivityPledge';
import AboutUsPage from 'src/pages/AboutUsPage/AboutUsPage';
import SurleyProPage from 'src/pages/SurleyProPage/SurleyProPage';
import ChatPage from 'src/pages/chat/ChatPage.jsx';
import ChatPageLayout from 'src/layouts/ChatPageLayout';
import SearchOperatorMapPage from 'src/pages/SearchOperatorMapPage/SearchOperatorMapPage';
import SearchPageMap from 'src/pages/SearchPage/SearchPageMap';
import { SuccessGooglePage } from 'src/pages/Auth/SuccessGooglePage';
import { SuccessLinkedinPage } from 'src/pages/Auth/SuccessLinkedinPage';
import PrivateGuard from 'src/guards/PrivateGuard';
import VerifyOtpCode from 'src/components/auth/VerifyOtpCode';
import Invoice from 'src/pages/Invoice/Invoice';
import AppliedJobs from 'src/pages/AppliedJobs/AppliedJobs';
import NonGuestGuard from 'src/guards/NonGuestGuard';

const Router = () => {
  window.scrollTo(0, 0);

  return useRoutes([
    {
      path: '/',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <LandingPage />
          </GlobalLayout>
        </PublicGuard>
      ),
      index: true,
    },
    {
      path: 'login',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <LoginPage />
          </GlobalLayout>
        </PublicGuard>
      ),
      index: true,
    },
    {
      path: 'register',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <RegisterPage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'dashboard',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <LandingPage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'verify-email',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <VerifyEmail />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'menu',
      element: (
        <PublicGuard>
          <SmallScreenMenuLayout>
            <SmallScreenHeaderMenu />
          </SmallScreenMenuLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'refer-a-friend',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <ReferFriend />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-operatives',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <OperativePage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'surely-pro-films',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <SurelyPlusFilms />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'accessibility-policy',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <AccessibilityPolicy />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'contact-us',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <ContactUs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'privacy-policy',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <PrivacyPolicy />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'cookies-policy',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <CookiesPolicy />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'terms-and-conditions',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <TermsAndConditions />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'terms-of-use',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <TermsOfUse />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-matters-archive',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <SecurityMattersArchive />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-matters-post/:postId',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <SecurityMattersPost />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'code-of-conduct',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <CodeofConduct />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/bodyguard',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <BodyguardJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/cctv-operator',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <CctvOperatorJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/door-supervisor',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <DoorSupervisorJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/event-security',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <EventSecurityJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/private-security',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <PrivateSecurityJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/security-guard',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <SecurityGuardJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs/close-protection',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <CloseProtectionJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'security-jobs',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <SecurityJobs />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'useful-resources',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <UsefulResources />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'clients',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <ClientPage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'inclusivity-pledge',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <InclusivityPledgePage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'about-us',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <AboutUsPage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'surely-pro',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <SurleyProPage />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'operator-job/:id',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <OperatorJobLayout>
              <OperatorJobPage />
            </OperatorJobLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
      index: true,
    },
    {
      path: 'my-profile',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <OperatorProfileLayout>
              <OperatorProfilePage />
            </OperatorProfileLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: '/operator-profile/:operatorId',
      element: (
            <ClientProfileLayout>
              <ClientProfile />
            </ClientProfileLayout>
      ),
    },
    {
      path: 'search-jobs',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SearchLayout title='Find your job opportunity'>
              <SearchPage />
            </SearchLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'applied-jobs',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SearchLayout title='Applied jobs'>
              <AppliedJobs />
            </SearchLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'search-jobs-map',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SearchPageMap />
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'favorite-jobs',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SearchLayout title='Favorite Jobs'>
              <SearchPage />
            </SearchLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'post-job',
      element: (
        <PublicGuard>
          <ClientGuard>
            <PostJobLayout>
              <PostJob />
            </PostJobLayout>
          </ClientGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'edit-job/:id',
      element: (
        <PublicGuard>
          <ClientGuard>
            <PostJobLayout>
              <EditPost />
            </PostJobLayout>
          </ClientGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'search-operator',
      element: (
            <SearchOperatorLayout>
              <SearchOperator />
            </SearchOperatorLayout>
      ),
    },
    {
      path: 'search-operators-map',
      element: (
          <GuestClientGuard>
            <SearchOperatorMapPage />
          </GuestClientGuard>
      ),
    },
    {
      path: 'client-settings',
      element: (
        <SettingsLayout>
          <GuestClientGuard>
            <ClientSettings />
          </GuestClientGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'client-settings-general',
      element: (
        <SettingsLayout>
          <GuestClientGuard>
            <ClientSettingsGeneral />
          </GuestClientGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'client-settings-payment',
      element: (
        <SettingsLayout>
          <ClientGuard>
            <ClientSettingsPayment />
          </ClientGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'client-settings-login',
      element: (
        <SettingsLayout>
          <GuestClientGuard>
            <ClientSettingsLogin />
          </GuestClientGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'client-settings-notification',
      element: (
        <SettingsLayout>
          <ClientGuard>
            <ClientSettingsNotification />
          </ClientGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettings />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-general',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsGeneral />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },

    {
      path: 'operator-settings-profile-details',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsProfileDetails />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-availability',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsAvailability />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-sia-certificate',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsSiaCertificate />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-employment',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsEmployment />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-payment',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsPayment />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-login',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsLogin />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'operator-settings-notification',
      element: (
        <SettingsLayout>
          <OperativeGuard>
            <OperatorSettingsNotification />
          </OperativeGuard>
        </SettingsLayout>
      ),
    },
    {
      path: 'crucial-data',
      element: (
        <PublicGuard>
          <SignInFlowLayout>
            <CrucialData />
          </SignInFlowLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'first-step-validation-client',
      element: (
        <PublicGuard>
          <ClientGuard>
            <SignInFlowLayout>
              <ClientFirstStepFlow />
            </SignInFlowLayout>
          </ClientGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'first-step-validation-operator',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SignInFlowLayout>
              <OperatorFirstStepFlow />
            </SignInFlowLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'second-step-validation-operator',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SignInFlowLayout>
              <OperatorSecondStepFlow />
            </SignInFlowLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'third-step-validation-operator',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SignInFlowLayout>
              <OperatorThirdStepFlow />
            </SignInFlowLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'forth-step-validation-operator',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SignInFlowLayout>
              <OperatorFourthStepFlow />
            </SignInFlowLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'client-dashboard',
      element: (
        <PublicGuard>
          <ClientGuard>
            <DashboardLayout>
              <DashboardPage />
            </DashboardLayout>
          </ClientGuard>
        </PublicGuard>
      ),
      index: true,
    },
    {
      path: 'favorite-operator',
      element: (
            <FavoriteOperatorLayout>
              <SearchOperator />
            </FavoriteOperatorLayout>
      ),
    },
    {
      path: 'manage-jobs/:id?',
      element: (
        <PublicGuard>
          <ClientGuard>
            <ManageJobsLayout>
              <ManageJobsPage />
            </ManageJobsLayout>
          </ClientGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'surelypro-traning',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SurelyProCourseLayout>
              <SurelyProTraningCategories />
            </SurelyProCourseLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'surelypro-traning-start-course',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SurelyProCourseLayout>
              <SurelyProTraningStartCourse />
            </SurelyProCourseLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'surelypro-traning-questions',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SurelyProCourseLayout>
              <SurelyProTraningQuestions />
            </SurelyProCourseLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: 'surelypro-traning-points',
      element: (
        <PublicGuard>
          <OperativeGuard>
            <SettingsLayout>
              <SurelyProPoints totalQuestions={0} correctAnswers={0} />
            </SettingsLayout>
          </OperativeGuard>
        </PublicGuard>
      ),
    },
    {
      path: '/chat',
      element: (
        <PrivateGuard>
          <NonGuestGuard>
            <ChatPageLayout>
              <ChatPage />
            </ChatPageLayout>
          </NonGuestGuard>
        </PrivateGuard>
      ),
    },
    {
      path: '/chat/:id?',
      element: (
        <PrivateGuard>
          <NonGuestGuard>
            <ChatPageLayout>
              <ChatPage />
            </ChatPageLayout>
          </NonGuestGuard>
        </PrivateGuard>
      ),
    },
    {
      path: 'success-google',
      element: <SuccessGooglePage />,
    },
    {
      path: 'success-linkedin',
      element: <SuccessLinkedinPage />,
    },
    {
      path: 'verify-opt-code',
      element: (
        <PublicGuard>
          <GlobalLayout>
            <VerifyOtpCode />
          </GlobalLayout>
        </PublicGuard>
      ),
    },
    {
      path: 'invoice/:id',
      element: (
        <PrivateGuard>
          <Invoice />
        </PrivateGuard>
      ),
    },
    {
      path: '*',
      children: [
        {
          path: '404',
          element: <Error404Page />,
        },
      ],
    },
  ]);
};

export default Router;

// Guest routes
const LoginPage = Loadable(lazy(() => import('src/components/auth/LoginForm')));
const RegisterPage = Loadable(lazy(() => import('../pages/Auth/RegisterPage/RegisterPage')));

// Auth routes
const LandingPage = Loadable(lazy(() => import('../pages/LandingPage/LandingPage')));

// Error routes
const Error404Page = Loadable(lazy(() => import('../pages/NotFound/NotFound')));
