// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';

import surelyiconprofile from '../../../assets/icons/surleyicon/surleyicon.png';
import SurelyIcon from 'src/pages/LandingPage/SurelyIcon';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

const NoOperatorApplied: React.FC = () => {
  return (
    <View className='flex flex-col justify-center items-center w-[234px] mx-auto mt-[40px] mb-[40px]'>
      {/* <Image
        src={surelyiconprofile}
        className='w-[140px] h-[140px] flex-shrink-0'
      /> */}
      <SurelyIconNoData />
      <Text className='text-[#444B5F] text-center font-normal leading-[24px] rubik !text-[#383838] mt-[10px]'>
      No applicants to review at this moment.
      </Text>
    </View>
  );
};

export default NoOperatorApplied;
