// @ts-nocheck
import React, { useContext, useLayoutEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Container, Text } from 'reshaped';
import Header from '../components/Header/Header';
import Loading from 'src/components/Loading/Loading';
import JobDetailCard from 'src/components/JobDescription/JobDetailsCard/JobDetailsCard';
import { JobContext } from 'src/context/JobContext';

interface Props {
  children?: React.ReactNode;
}

export const OperatorJobLayout = ({ children }: Props): JSX.Element => {
  const { id } = useParams<{ id: any }>();
  const { selectedJob, handleSelectedJob, isLoadingJobs } = useContext(JobContext);

  const renderChildren = () => {
    return React.Children.map(children, (child) => {
      return React.cloneElement(child, {
        selectedJob,
      });
    });
  };

  useLayoutEffect(() => {
    handleSelectedJob(id);
  }, [id]);

  if (isLoadingJobs) return <Loading />;

  return (
    <Container padding={0} className="min-h-[100vh] w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />
      <main className='mx-auto mt-[90px] lg:mt-[122px] flex w-full max-w-[1320px] flex-col justify-between text-center sm:flex-col md:gap-12 lg:flex-row lg:gap-36'>
        <section className='mx-auto w-[90%] lg:w-2/3 '>{renderChildren()}</section>
        {selectedJob && (
          <section className='mx-auto w-[90%] h-full  lg:w-1/3 '>
            <JobDetailCard selectedJob={selectedJob} />
          </section>
        )}
      </main>
    </Container>
  );
};

export default OperatorJobLayout;
