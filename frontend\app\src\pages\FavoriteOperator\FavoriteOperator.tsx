// @ts-nocheck
import React, { useEffect, useState, useContext } from 'react';
import { Card, View, Divider, Image, Text, Icon, Button } from 'reshaped';
import { IdVerified } from 'src/assets/icons';
import { useNavigate } from 'react-router-dom';
import { OperativesContext } from 'src/context/OperativesContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import SearchOperatorCard from '../SearchOperator/SearchOperatorCard';
import NoOperatorFavorite from 'src/components/NoData/NoOperatorFavorite';

const FavoriteOperator: React.FC = () => {
  const navigate = useNavigate();
  const {
    allOperatives,
    handleLoadMore,
    handleSelectedOperatives,
    addFavorite,
    favourites,
    removeFavorite,
    fetchAllOperative,
  } = useContext(OperativesContext);

  return (
<View className='flex flex-col mb-10 '>
  {favourites?.length > 0 ? (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 '>
      {favourites.map((operator: any, index: number) => (
        <SearchOperatorCard
          operator={operator}
          addFavorite={addFavorite}
          removeFavorite={removeFavorite}
          fetchAllOperative={fetchAllOperative}
        />
      ))}
    </div>
  ) : (
    <div className=''>
      <NoOperatorFavorite />
    </div>
  )}
</View>

  );
};

export default FavoriteOperator;
