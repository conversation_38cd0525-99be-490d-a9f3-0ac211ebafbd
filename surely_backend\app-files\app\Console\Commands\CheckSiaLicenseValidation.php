<?php

namespace App\Console\Commands;

use App\Models\MobileUser;
use App\Notifications\SiaLicenceExpiresWeekNotification;
use App\Notifications\SiaLicenseExpireAlertNotification;
use App\Notifications\SiaLicenseExpireNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckSiaLicenseValidation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check-sia-license-validation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Sia License Validation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MobileUser::where('account_type', MobileUser::freelancer)
            ->cursor()
            ->each(function ($user) {
                if (!isset($user->sia_licence_expiry_date)) {
                    return;
                }
                if (Carbon::parse($user->sia_licence_expiry_date)->format('y-m-d') == Carbon::now()->format('y-m-d')) {
                    $user->notify(new SiaLicenseExpireNotification($user->id, $user->name));
                } elseif (Carbon::parse($user->sia_licence_expiry_date)->diffInDays(Carbon::now()->format('y-m-d')) == 7){
                    $user->notify(new SiaLicenceExpiresWeekNotification($user->id, $user->name));
                } elseif (Carbon::parse($user->sia_licence_expiry_date)->diffInDays(Carbon::now()->format('y-m-d')) == 30) {
                    $user->notify(new SiaLicenseExpireAlertNotification($user->id, $user->name));
                }
            });

        return 0;
    }
}
