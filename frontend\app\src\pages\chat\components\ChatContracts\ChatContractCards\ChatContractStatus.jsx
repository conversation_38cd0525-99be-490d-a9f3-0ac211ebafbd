import { useNavigate } from 'react-router-dom';
import { useAuthContext } from 'src/context/AuthContext';

const ChatContractStatus = ({ contract }) => {
  const navigate = useNavigate();
  const { isClient } = useAuthContext()

  const jobId = contract?.job?.id;
  const jobName = contract?.job?.post_name;
  const isContract = contract?.job?.post_name?.includes('Individual Contract #');
  const status = contract?.status;

  const handleNavigateToJob = (jobId) => {
    if (jobId) {
      if (isClient) {
        navigate(`/manage-jobs/${jobId}`)
      }
      if (isClient === false) {
        navigate(`/operator-job/${jobId}`)
      }
    }
  };

  switch (status) {
    case 'Invited':
      return (
        <div className='w-full rounded-lg bg-[#F4F5F7] p-4'>
          <h3
            onClick={() => handleNavigateToJob(jobId)}
            className={`rubik mb-3 w-fit ${isContract ? 'cursor-default' : 'cursor-pointer'} text-left text-[14px] font-medium text-[#323C58] underline`}
          >
            {jobName}
          </h3>
          <div className='w-full rounded bg-[#DBDFEA] py-[9px] text-center font-normal'>
            Status: <span className='font-medium text-[#3B3B3D]'>{status}</span>
          </div>
        </div>
      );
    case 'Pending':
      return (
        <div className='w-full rounded-lg bg-[#F4F5F7] p-4'>
          <h3
            onClick={() => handleNavigateToJob(jobId)}
            className={`rubik mb-3 w-fit ${isContract ? 'cursor-default' : 'cursor-pointer'} text-left text-[14px] font-medium text-[#323C58] underline`}
          >
            {jobName}
          </h3>
          <div className='w-full rounded bg-[#DBDFEA] py-[9px] text-center font-normal'>
            Status: <span className='font-medium text-[#323C58]'>{status}</span>
          </div>
        </div>
      );
    case 'In progress':
      return (
        <div className='w-full rounded-lg bg-[#F4F5F7] p-4'>
          <h3
            onClick={() => handleNavigateToJob(jobId)}
            className={`rubik mb-3 w-fit ${isContract ? 'cursor-default' : 'cursor-pointer'} text-left text-[14px] font-medium text-[#323C58] underline`}
          >
            {jobName}
          </h3>
          <div className='w-full rounded bg-[#DBDFEA] py-[9px] text-center font-normal'>
            Status: <span className='font-medium text-[#0B80E7]'>{status}</span>
          </div>
        </div>
      );
    case 'Completed':
      return (
        <div className='w-full rounded-lg bg-[#F4F5F7] p-4'>
          <h3
            onClick={() => handleNavigateToJob(jobId)}
            className={`rubik mb-3 w-fit ${isContract ? 'cursor-default' : 'cursor-pointer'} text-left text-[14px] font-medium text-[#323C58] underline`}
          >
            {jobName}
          </h3>
          <div className='w-full rounded bg-[#DBDFEA] py-[9px] text-center font-normal'>
            Status: <span className='font-medium text-[#05751F]'>{status}</span>
          </div>
        </div>
      );
    case 'Cancelled':
      return (
        <div className='w-full rounded-lg bg-[#F4F5F7] p-4'>
          <h3
            onClick={() => handleNavigateToJob(jobId)}
            className={`rubik mb-3 w-fit ${isContract ? 'cursor-default' : 'cursor-pointer'} text-left text-[14px] font-medium text-[#323C58] underline`}
          >
            {jobName}
          </h3>
          <div className='w-full rounded bg-[#DBDFEA] py-[9px] text-center font-normal'>
            Status: <span className='font-medium text-[#CB101D]'>{status}</span>
          </div>
        </div>
      );

    default:
      return (
        <div className='w-full rounded-lg bg-[#F4F5F7] p-4'>
          <h3
            onClick={() => handleNavigateToJob(jobId)}
            className={`rubik mb-3 w-fit ${isContract ? 'cursor-default' : 'cursor-pointer'} text-left text-[14px] font-medium text-[#323C58] underline`}
          >
            {jobName}
          </h3>
          <div className='w-full rounded bg-[#DBDFEA] py-[9px] text-center font-normal'>
            Status: <span className='font-medium text-[#323C58]'>{status}</span>
          </div>
        </div>
      );
  }
};

export default ChatContractStatus;
