export const storeUserData = (user: any) => {
  if (!user) return;
  
  const userData = {
    id: user.id,
    name: user.name,
    email: user.email,
    profile_photo: user.profile_photo,
    account_type: user.account_type,
    initials: getNameInitials(user.name)
  };
  
  localStorage.setItem('userData', JSON.stringify(userData));
};
  
export const getUserData = () => {
  const userData = localStorage.getItem('userData');
  return userData ? JSON.parse(userData) : null;
};
  
export const getNameInitials = (name: string) => {
  if (!name) return '';
  return name
    .split(' ')
    .map((word: string) => word[0])
    .join('')
    .toUpperCase();
};