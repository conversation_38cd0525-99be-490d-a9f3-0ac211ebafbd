// @ts-nocheck
import React, { ChangeEvent, useEffect, useRef, useState } from 'react';
import { Text, View, Button, Divider, Modal, TextField, Image, useToggle } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import limoreserveskews from '../../../../assets/images/postjob/limoreserveskews.svg';
import Contract from 'src/services/contracts.ts';
import CalendarPostJob from 'components/Calendars/CalendarPostJob.tsx';
import moment from 'moment';
import CalendarPostJobNew from 'src/components/Calendars/CalendarPostJobNew';
import { useChatContext } from 'src/context/ChatContext';

interface CreateContractModalProps {
  active: boolean;
  deactivate: () => void;
  operator: any;
  chatId: string;
}

const CreateContractModal: React.FC<CreateContractModalProps> = ({ active, deactivate, operator, chatId }) => {
  const operatorName = operator?.name || 'Operator';
  const { setContract, loadContract } = useChatContext();
  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const [hourlyRate, setHourlyRate] = useState('');
  const [location, setLocation] = useState('');
  const [selectedDates, setSelectedDates] = useState([]);
  const [selectedDatesRange, setSelectedDatesRange] = useState([]);
  const [showDateRange, setShowDateRange] = useState(false);
  const [shiftData, setShiftData] = useState<any>([]);
  const [tempShiftData, setTempShiftData] = useState<any>([]);
  const [errors, setErrors] = useState({ selectedDates: false, hourlyRate: false, location: false });
  const [closePopovers, setClosePopovers] = useState(false);

  const handleClosePopovers = async () => setClosePopovers((prevState) => !prevState);

  const handleDateRangeChange: any = (date: { start: string; end: string }) => {
    if (date?.start) {
      setSelectedDates([date]);
    } else if (date?.length > 0) {
      setSelectedDates(date);
    }
  };

  const createContract = async () => {
    if (selectedDates?.length <= 0 || !hourlyRate || !location) {
      if (selectedDates?.length <= 0) {
        setErrors((prevState) => ({ ...prevState, selectedDates: true }));
      }
      if (!hourlyRate) {
        setErrors((prevState) => ({ ...prevState, hourlyRate: true }));
      }
      if (!location) {
        setErrors((prevState) => ({ ...prevState, location: true }));
      }
      return;
    }

    const response = await Contract.create({
      operatorId: operator?.id,
      hourly_rate: hourlyRate,
      location,
      chat_id: chatId,
      date_range: selectedDates,
      payment_terms: 10,
      type: 'individual_contract',
    });

    if (response) {
      loadContract(response?.data?.id).then((res) => {
        setContract(res);
      });
      deactivate();
    }
  };

  useEffect(() => {
    if (!active) {
      setHourlyRate('');
      setLocation('');
      setSelectedDates([]);
      setSelectedDatesRange([]);
      setShowDateRange(false);
      setShiftData([]);
      setTempShiftData([]);
    }
  }, [active]);

  return (
    <>
      <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
        <View className='gap-[16px]'>
          <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
            <span className='material-icons text-500 align-middle'>close</span>
          </button>
          <View className='flex items-center p-0'>
            <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>
              Create contract for&nbsp;
              <span className='rubik text-[24px] font-medium leading-[32px] text-[#323C58]'>{operatorName}</span>
            </Text>
          </View>

          <View className='mt-[16px] flex flex-col'>
            <View className='flex flex-col gap-[4px]'>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Job date range</Text>
              <div className='mt-[4px] flex flex-row'>
                <Button
                  className='flex h-[56px] w-[56px] items-center justify-center rounded-[8px] border border-[#DFE2EA] !bg-[#F4F5F7] p-2'
                  icon={() => <span className='material-icons-outlined text-[20px]'>calendar_today</span>}
                  onClick={activate1}
                />
                <Text className='rubik ml-[20px] mt-4 text-[14px] font-medium leading-[20px]'>
                  {selectedDates?.length > 0
                    ? `From ${moment(selectedDates?.[0]?.start).format('D MMM YYYY')} to ${moment(selectedDates?.[selectedDates?.length - 1]?.end).format('D MMM YYYY')}`
                    : 'Select a date range'}
                </Text>
              </div>
              {errors.selectedDates && <span className='rubik mt-1 text-[#CB101D]'>No dates selected</span>}
            </View>
            <View className='mt-[16px] flex flex-col'>
              <View className='flex flex-col '>
                <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Hourly rate</Text>
                <TextField
                  name='hourlyrate'
                  className='mt-[4px] w-full rounded-md border px-[12] py-[14px]'
                  placeholder='£39'
                  onChange={(event) => setHourlyRate(event.value)}
                />
                {errors?.hourlyRate && <span className='rubik mt-1 text-[#CB101D]'>No hourly rate selected</span>}
              </View>
              <View className='mt-[16px] flex flex-col'>
                <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Location</Text>
                <TextField
                  name='location'
                  className='mt-[4px] w-full rounded-md border px-[12] py-[14px]'
                  placeholder='E.g. CRO 3RL or 36 Factory Lane'
                  onChange={(event) => setLocation(event.value)}
                />
                {errors?.location && <span className='rubik mt-1 text-[#CB101D]'>No location selected</span>}
                <Text className='rubik mt-2 text-[14px] text-gray-600'>Type a part of the address or postcode to begin</Text>
                <Text className='rubik mt-[16px] text-sm font-medium leading-5'>what3words (optional)</Text>
                <img src={limoreserveskews} className='mt-[8px] w-[185.768px]' />
              </View>
            </View>
          </View>
          <Divider className='mt-[16px] h-[1px] w-full'></Divider>
          <View className='mt-[16px] flex flex-row justify-end'>
            <Button
              variant='outline'
              icon={() => <span className='material-icons -mt-1'>clear</span>}
              onClick={deactivate}
              className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
            >
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
            </Button>
            <Button
              endIcon={() => (
                <svg xmlns='http://www.w3.org/2000/svg' width='8' height='15' viewBox='0 0 8 15' fill='none'>
                  <path
                    d='M0.0703125 12.8836L1.25638 14.0696L7.89031 7.43569L1.25638 0.801758L0.0703125 1.98782L5.51818 7.43569L0.0703125 12.8836Z'
                    fill='white'
                  />
                </svg>
              )}
              className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
              onClick={createContract}
            >
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
            </Button>
          </View>
        </View>
      </Modal>
      <CalendarPostJobNew
        handleClosePopovers={handleClosePopovers}
        closePopovers={closePopovers}
        active={active1}
        deactivate={() => handleClosePopovers().then(() => deactivate1())}
        handleParentDates={handleDateRangeChange}
      />
    </>
  );
};

export default CreateContractModal;
