<?php

namespace App\Http\Controllers\Api\Buyer;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ApplicantBookRequest;
use App\Http\Requests\Api\BookingRequest;
use App\Http\Requests\Api\BookingFormRequest;
use App\Models\Booking;
use App\Models\BookingForm;
use App\Models\FormApplicant;
use App\Models\MobileUser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FormController extends Controller
{
    public function store_form(BookingFormRequest $request)
    {

        DB::beginTransaction();
        try {

            $user = Auth::guard('api')->user();

            $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date);
            $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date);

            $diff_in_days = $end_date->diffInDays($start_date) + 1; //including first day

            $shift_start = Carbon::createFromFormat('H:i', $request->shift_start);
            $shift_end = Carbon::createFromFormat('H:i', $request->shift_end);

            $diff_in_hours = $shift_end->diffInHours($shift_start);


            $total_hours = $diff_in_hours * $diff_in_days;

            BookingForm::create([
                'buyer_id' => $user->id,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'shift_start' => $shift_start,
                'shift_end' => $shift_end,
                'price_per_hour' => $request->max_pay_rate,
                'total_hours' => $total_hours,
                'notes' => $request->notes,
                'title' => $request->title,
                'status' => BookingForm::open,
                'expertise_id' => $request->expertise_id,
                'postal_code' => $request->postal_code,

            ]);

            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Success',
            ]);
        } catch (\Exception $e) {

            DB::rollback();

            return response()->json([
                'error' => true,
                'message' => 'An error occurred.',
            ]);
        }
    }


    public function my_positions()
    {
        $forms = BookingForm::where('buyer_id', Auth::guard('api')->user()->id)
            ->where('status',BookingForm::open)
            ->orderBy('created_at', 'desc')
            ->get();

        $data = array();
        foreach ($forms as $form) {

            $obj = [
                'id' => $form->id,
                'title' => $form->title,
                'expertise' => $form->expertise->name,
                'start_date' => $form->start_date,
                'end_date' => $form->end_date,
                'shift_start' => $form->shift_start,
                'shift_end' => $form->shift_end,
                'total_hours' => $form->total_hours,
                'price_per_hour' =>(double) $form->price_per_hour,
               // 'years_of_experience' => $form->years_of_experience,
                'notes' => $form->notes ?? '',
                'created_at' => date('Y-m-d', strtotime($form->created_at)),
            ];

            array_push($data, $obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data
        ]);

    }

    public function close_position($id)
    {

        BookingForm::findOrFail($id)->update(['status'=> BookingForm::closed]);

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }


    //@deprecated kjo do kaloje te jobsController
    public function position_applicants($id)
    {

        $formApplicants = FormApplicant::where('form_id', $id)->get();


        $data = array();
        foreach ($formApplicants as $provider) {

            $obj = [
                'provider_id' => $provider->provider->id,
                'position_id' => (int)$id,
                'name' => $provider->full_name,
                'expertise' =>$provider->expertise ? $provider->expertise->name : '',
                'email' => $provider->email,
                'pay_rate' =>(double) $provider->pay_rate,
                'start_date' => $provider->form->start_date,
                'end_date' =>  $provider->form->end_date,
                'shift_start' =>  $provider->form->shift_start,
                'shift_end' =>  $provider->form->shift_end,
            ];

            array_push($data, $obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data
        ]);
    }


    public function book_applicant(ApplicantBookRequest $request)
    {
        $user = Auth::guard('api')->user();
        $form = BookingForm::find($request->position_id);
        $provider = MobileUser::find($request->provider_id);
        DB::beginTransaction();
        try {
            Booking::create([
                'buyer_id' => $user->id,
                'provider_id' => $provider->id,
                'start_date' => $form->start_date,
                'end_date' => $form->end_date,
                'shift_start' => $form->shift_start,
                'shift_end' => $form->shift_end,
                'price_per_hour' => $request->provider_pay_rate,
                'total_hours' => $form->total_hours,
                'total_price' => round($request->provider_pay_rate * $form->total_hours, 2),
                'card_id' => $request->card_id,
                'notes' => $form->notes,
                'status' => Booking::approved,
                'type' => Booking::booking,
            ]);

            $form->update(['status'=>BookingForm::closed]);
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Success',
            ]);
        } catch (\Exception $e) {

            DB::rollback();

            return response()->json([
                'error' => true,
                'message' => 'An error occurred.',
            ]);
        }
    }
}
