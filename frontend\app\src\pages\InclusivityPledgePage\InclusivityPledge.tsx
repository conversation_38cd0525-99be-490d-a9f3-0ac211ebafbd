// @ts-nocheck
import React, { useState, useContext } from 'react';
import { View, Image, Text, Button, Divider } from 'reshaped';
import inclusivity1 from '../../assets/images/inclusivity/inclusivity1.png';
import inclusivity2 from '../../assets/images/inclusivity/inclusivity2.png';
import inclusivity3 from '../../assets/images/inclusivity/inclusivity3.png';
import inclusivity4 from '../../assets/images/inclusivity/inclusivity4.png';
import inclusivity5 from '../../assets/images/inclusivity/inclusivity5.png';
import inclusivity6 from '../../assets/images/inclusivity/inclusivity6.png';
import { useModalAction } from 'src/context/ModalContext';
import Footer from '../Footer/Footer';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';

const InclusivityPledgePage: React.FC = () => {
  const { openModal } = useModalAction();
  const { isAuthenticated } = useContext(AuthContext);

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full mx-auto flex flex-col items-center gap-8 text-center mt-[66px]'>
        <View className='flex flex-col max-w-[1320px]'>
          <Image src={inclusivity1} className='xl:w-[1320px] h-[auto] sm:h-[580px]' />
          <View className='flex flex-col lg:flex-row justify-between'>
            <View className='flex flex-col items-start mt-[52px] px-[12px] xl:px-0  xl:w-[520px] md:mx-auto'>
              <Text className='text-left rubik text-[16px] font-medium text-[#388DD8] leading-[20px] ml-[0] xl:ml-[30px]'>
                Join us in making a difference 
              </Text>
              <Text className='text-left font-rufina-stencil sm:text-[48px] sm:mt-0 mt-3 text-[35px] text-[#323C58] ml-0 xl:ml-[30px] '>
                Inclusivity Pledge.
              </Text>
              <Button
                variant='outline'
                className='flex w-[195px] h-[44px]  justify-center items-center gap-[4px] self-stretch rounded-full border border-solid !border-[rgba(50,60,88,0.95)] bg-[#ffff] xl:ml-[30px]  mt-[25px] '
                icon={() => (
                  <svg xmlns='http://www.w3.org/2000/svg' width='28' height='14' viewBox='0 0 28 14' fill='none'>
                    <path
                      d='M26.1555 2.43677C24.9572 1.19375 23.4567 0.55542 21.7097 0.55542C21.0266 0.55542 20.3547 0.678633 19.694 0.902601C19.0333 1.13777 18.4398 1.50729 17.9247 2.00002L9.08915 10.3428C8.7644 10.634 8.39485 10.8467 7.99171 10.9923C7.58857 11.1267 7.16303 11.2051 6.72629 11.2051C5.58406 11.2051 4.66579 10.7907 3.8931 9.95087C3.13162 9.11099 2.75087 8.10314 2.75087 6.94971C2.75087 5.79628 3.13162 4.84442 3.9043 4.01575C4.67699 3.19826 5.59526 2.8063 6.72629 2.8063C7.17423 2.8063 7.61097 2.8735 8.0253 3.01907C8.41725 3.15345 8.77559 3.37742 9.07795 3.67977L11.7656 6.3114L13.3333 4.73242L10.5225 2.01123C10.0074 1.5185 9.4139 1.14894 8.7532 0.913776C8.10369 0.67861 7.42059 0.566629 6.71509 0.566629C4.97935 0.566629 3.50116 1.19373 2.30294 2.44795C1.10471 3.69097 0.5 5.20277 0.5 6.94971C0.5 8.69666 1.10471 10.2532 2.28054 11.5186C3.46757 12.7952 4.95695 13.4448 6.71509 13.4448C7.39819 13.4448 8.0701 13.3328 8.742 13.1088C9.4139 12.8848 10.0074 12.5265 10.5225 12.0562L19.3693 3.71337C19.6716 3.42221 20.0412 3.19824 20.4443 3.04146C20.8474 2.88468 21.273 2.8063 21.6985 2.8063C22.8183 2.8063 23.7478 3.19826 24.5317 4.01575C25.3156 4.83323 25.6963 5.79628 25.6963 6.94971C25.6963 8.10314 25.3044 9.11099 24.5429 9.95087C23.7702 10.7907 22.8407 11.2051 21.6985 11.2051C21.2618 11.2051 20.8362 11.1379 20.4107 10.9923C20.0076 10.8579 19.6604 10.6452 19.3693 10.3652L16.7376 7.73358L15.1699 9.30138L17.9135 12.0226C18.4062 12.5153 18.9997 12.8848 19.6716 13.1088C20.3323 13.3328 21.0154 13.4448 21.6873 13.4448C23.4343 13.4448 24.9348 12.7952 26.1331 11.5186C27.3201 10.2532 27.9248 8.70786 27.9248 6.94971C27.9248 5.19157 27.3201 3.69097 26.1219 2.44795L26.1555 2.43677Z'
                      fill='url(#paint0_linear_3409_20400)'
                    />
                    <defs>
                      <linearGradient
                        id='paint0_linear_3409_20400'
                        x1='14.2124'
                        y1='-4.46004'
                        x2='14.2124'
                        y2='18.3844'
                        gradientUnits='userSpaceOnUse'
                      >
                        <stop offset='0.536458' stopColor='#323C58' stopOpacity='0.95' />
                        <stop offset='0.921875' stopColor='#6B789C' stopOpacity='0.96' />
                      </linearGradient>
                    </defs>
                  </svg>
                )}
              >
                <Text className='rubik font-normal leading-[20px] text-[16px] text-transparent bg-clip-text bg-gradient-to-b from-[rgba(50,60,88,0.95)] via-[rgba(50,60,88,0.95)] to-[rgba(107,120,156,0.96)] '>
                  Inclusivity Pledge
                </Text>
              </Button>
            </View>
            <View className='flex flex-col mt-[52px]  lg:w-[625px] items-start mx-auto px-[12px] xl:px-0'>
              <View className='flex flex-col   xl:w-[545px] items-start '>
                <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px]  '>
                  The idea behind the Inclusivity Pledge was born out of discussions with clients and security
                  operatives, both of whom told us of their frustrations with regards to behaviours and expectations
                  around diversity, and the issues that needed to be addressed.
                </Text>
                <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px]  mt-[25px]'>
                  It’s really important that security operatives feel comfortable in the environment that they are
                  working in - regardless of the gender, race, gender identity, age and culture of the client’s
                  customers. It’s also a legal requirement to treat everyone with respect.
                </Text>
                <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px]  mt-[25px]'>
                  That’s where the Equality Act 2010 comes in. But we shouldn’t really need to go down the legal route
                  when it comes to being open and welcoming to everyone you meet, and looking for the similarities
                  rather than the differences between you and them.
                </Text>
                <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px]  mt-[25px]'>
                  That’s why we created the Inclusivity Pledge. It raises awareness around workplace diversity and
                  addresses unconscious bias and discrimination. It supports effective communication and the use of
                  appropriate language too.
                </Text>
                <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px]  mt-[25px]'>
                  At its heart, security operatives who are awarded with the Inclusivity Pledge badge signal to clients
                  their understanding of and compliance with the Equality Act 2010, as well as exploring additional
                  measures to stand up against discrimination in all its forms.
                </Text>
                <div className='items-start bg-[#388DD8] w-[149px] h-[1px] mt-[46px]' />
              </View>
              <View className='flex flex-col mt-[25px]  lg:w-[643px] items-start mx-auto'>
                <Text className='text-left rubik !italic text-[24px] font-normal leading-[32px] text-[#383838] mr-[0px] '>
                  “Surely Inclusivity Pledge badge holders are taking an important step towards providing a safe,
                  welcoming and inclusive environment.”
                </Text>
                <Text className='text-left rubik text-[16px] font-medium leading-[20px] text-[#383838] mr-[0px] sm:mr-[100px] sm:mt-[11px] mt-20 sm:whitespace-nowrap'>
                  James Cabot – Event Operations & Production Manager | Door Supervisor
                </Text>
                <Image
                  src={inclusivity2}
                  className='lg:w-[645.438px] w-full lg:h-[360.646px] mt-[34.6px]  mb-[70px] sm:flex hidden'
                />
              </View>
            </View>
            <Image src={inclusivity2} className='w-[645.438px] w-full h-[360.646px] mt-[24.6px] sm:hidden  mb-[ypx]' />
          </View>
        </View>
        <View className='w-full mx-auto flex flex-col items-center text-center gap-8 text-center bg-[#F4F5F7] '>
          <View className='xl:w-[1320px] mx-auto flex flex-col items-center text-center gap-8 text-center bg-[#F4F5F7] sm:mt-[70.6px]'>
            <View className='flex flex-col sm:flex-row mt-[22px] w-full  justify-between px-[12px] xl:px-0 sm:ml-[0px]'>
              <View className='flex flex-col w-auto  xl:w-[420px] items-start'>
                <svg xmlns='http://www.w3.org/2000/svg' width='33' height='41' viewBox='0 0 33 41' fill='none'>
                  <path
                    d='M16.1483 8.76111C18.3094 8.76111 20.0613 7.00921 20.0613 4.84814C20.0613 2.68707 18.3094 0.935181 16.1483 0.935181C13.9872 0.935181 12.2354 2.68707 12.2354 4.84814C12.2354 7.00921 13.9872 8.76111 16.1483 8.76111Z'
                    fill='#0B80E7'
                  />
                  <path
                    d='M23.7587 12.8893C22.9956 12.1263 21.6848 10.7176 19.1414 10.7176C18.7305 10.7176 16.3632 10.7176 14.1719 10.7176C8.79158 10.698 4.40906 6.31551 4.40906 0.935181H0.496094C0.496094 7.11766 4.62427 12.361 10.2785 14.0632V40.0648H14.1915V28.3259H18.1044V40.0648H22.0174V16.6849L29.7455 24.413L32.5041 21.6543L23.7587 12.8893Z'
                    fill='#0B80E7'
                  />
                </svg>
                <Text className='text-left text-[1A1A1A] rubik text-[24px] font-normal leading-[32px] mt-[12px]'>
                  Raising Awareness
                </Text>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] items-start mt-[4px]'>
                  60% of people don’t like discussing diversity because they’re worried about saying the wrong thing. 
                </Text>
              </View>
              <View className='flex flex-col  xl:w-[420px] items-start sm-mt-0 xl:mt-[18px] '>
                <svg xmlns='http://www.w3.org/2000/svg' width='39' height='39' viewBox='0 0 39 39' fill='none'>
                  <g clipPath='url(#clip0_6274_3380)'>
                    <path
                      d='M14.625 21.125C18.2162 21.125 21.125 18.2163 21.125 14.625C21.125 11.0338 18.2162 8.125 14.625 8.125C11.0337 8.125 8.125 11.0338 8.125 14.625C8.125 18.2163 11.0337 21.125 14.625 21.125ZM14.625 11.375C16.4125 11.375 17.875 12.8375 17.875 14.625C17.875 16.4125 16.4125 17.875 14.625 17.875C12.8375 17.875 11.375 16.4125 11.375 14.625C11.375 12.8375 12.8375 11.375 14.625 11.375ZM14.625 24.375C10.2863 24.375 1.625 26.5525 1.625 30.875V34.125H27.625V30.875C27.625 26.5525 18.9638 24.375 14.625 24.375ZM4.875 30.875C5.2325 29.705 10.2537 27.625 14.625 27.625C19.0125 27.625 24.05 29.7213 24.375 30.875H4.875ZM24.505 11.4563C25.87 13.3738 25.87 15.86 24.505 17.7775L27.235 20.5238C30.5175 17.2413 30.5175 12.285 27.235 8.71L24.505 11.4563ZM32.6138 3.25L29.965 5.89875C34.4663 10.8063 34.4663 18.1838 29.965 23.3512L32.6138 26C38.9512 19.6787 38.9675 9.83125 32.6138 3.25Z'
                      fill='#0B80E7'
                    />
                  </g>
                  <defs>
                    <clipPath id='clip0_6274_3380'>
                      <rect width='39' height='39' fill='white' />
                    </clipPath>
                  </defs>
                </svg>
                <Text className='text-left text-[#1A1A1A] rubik text-[24px] font-normal leading-[32px] mt-[12px]'>
                  Unconscious Biases
                </Text>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] items-start mt-[4px]'>
                  The first step in dealing with unconscious biases is to openly acknowledge and talk about them. 
                </Text>
              </View>
              <View className='flex flex-col  xl:w-[420px] items-start xl:mt-[18px]'>
                <svg xmlns='http://www.w3.org/2000/svg' width='39' height='19' viewBox='0 0 39 19' fill='none'>
                  <path
                    d='M6.8527 11.0809C8.59164 11.0809 10.0144 9.65808 10.0144 7.91915C10.0144 6.18021 8.59164 4.75745 6.8527 4.75745C5.11377 4.75745 3.691 6.18021 3.691 7.91915C3.691 9.65808 5.11377 11.0809 6.8527 11.0809ZM8.63906 12.8198C8.05415 12.7249 7.46923 12.6617 6.8527 12.6617C5.28766 12.6617 3.80166 12.9937 2.45794 13.5786C1.28811 14.0845 0.529297 15.2227 0.529297 16.5032V18.9851H7.64313V16.4399C7.64313 15.1278 8.00672 13.8948 8.63906 12.8198ZM32.1463 11.0809C33.8853 11.0809 35.308 9.65808 35.308 7.91915C35.308 6.18021 33.8853 4.75745 32.1463 4.75745C30.4074 4.75745 28.9846 6.18021 28.9846 7.91915C28.9846 9.65808 30.4074 11.0809 32.1463 11.0809ZM38.4697 16.5032C38.4697 15.2227 37.7109 14.0845 36.5411 13.5786C35.1974 12.9937 33.7114 12.6617 32.1463 12.6617C31.5298 12.6617 30.9449 12.7249 30.36 12.8198C30.9923 13.8948 31.3559 15.1278 31.3559 16.4399V18.9851H38.4697V16.5032ZM26.2023 12.1084C24.3527 11.2864 22.0763 10.6856 19.4995 10.6856C16.9227 10.6856 14.6463 11.3022 12.7967 12.1084C11.0894 12.8672 10.0144 14.5745 10.0144 16.4399V18.9851H28.9846V16.4399C28.9846 14.5745 27.9096 12.8672 26.2023 12.1084ZM13.2868 15.8234C13.429 15.4598 13.4923 15.2069 14.7253 14.7326C16.2588 14.1319 17.8712 13.8473 19.4995 13.8473C21.1278 13.8473 22.7403 14.1319 24.2737 14.7326C25.4909 15.2069 25.5542 15.4598 25.7123 15.8234H13.2868ZM19.4995 3.17659C20.369 3.17659 21.0804 3.88798 21.0804 4.75745C21.0804 5.62691 20.369 6.3383 19.4995 6.3383C18.63 6.3383 17.9187 5.62691 17.9187 4.75745C17.9187 3.88798 18.63 3.17659 19.4995 3.17659ZM19.4995 0.0148926C16.8753 0.0148926 14.757 2.13323 14.757 4.75745C14.757 7.38166 16.8753 9.5 19.4995 9.5C22.1237 9.5 24.2421 7.38166 24.2421 4.75745C24.2421 2.13323 22.1237 0.0148926 19.4995 0.0148926Z'
                    fill='#0B80E7'
                  />
                </svg>
                <Text className='text-left text-[#1A1A1A] rubik text-[24px] font-normal leading-[32px] mt-[15px]'>
                  Effective Communication
                </Text>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] items-start mt-[4px]'>
                  Ask if you’re not sure what language to use - and if you make a mistake, apologise and learn. 
                </Text>
              </View>
            </View>
            <View className='xl:w-[1320px] flex sm:justify-center '>
              <Text className='!text-[#323C58] sm:ml-0 text-start md:text-center px-[12px] lg:px-0   sm:w-auto font-rufina-stencil sm:text-[32px] text-[25px] leading-[40px] font-normal mt-[48px]'>
                More reasons to choose Surely.
              </Text>
            </View>

            <View className='flex flex-col sm:flex-row justify-between  mt-[12px] xl:w-[1320px] px-[12px] xl:px-0   sm:ml-[0%]  xl:gap-0 gap-4 '>
              <View className='flex flex-col bg-[#FFFFFF] border  xl:w-[424px] rounded-[8px] '>
                <Image src={inclusivity3} className='  lg:w-[424px] h-[292px] ' />
                <View className='p-[24px]'>
                  <Text className='text-left text-[#1A1A1A] rubik text-[16px] font-medium leading-[32px]   sm:ml-[0px] '>
                    Total Clarity 
                  </Text>
                  <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] items-start mt-[12px] '>
                    There’s never been a better way for clients and security operatives to match up with one another.
                    Surely makes sure that every single detail is covered off before a job is offered by a client and
                    accepted by a security operative. 
                  </Text>
                </View>
              </View>
              <View className='flex flex-col bg-[#FFFFFF] border  xl:w-[424px] rounded-[8px] '>
                <Image src={inclusivity4} className='  lg:w-[424px] h-[292px] ' />
                <View className='p-[24px]'>
                  <Text className='text-left text-[#1A1A1A] rubik text-[16px] font-medium leading-[32px]   sm:ml-[0px] '>
                    Instant Book 
                  </Text>
                  <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] items-start mt-[12px] '>
                    Security operatives are able to highlight to clients when they are free to work at short notice on
                    any given day using the Instant Book function. It’s a great way for clients to find someone if
                    they’ve been let down at short notice. 
                  </Text>
                </View>
              </View>
              <View className='flex flex-col bg-[#FFFFFF] border  xl:w-[424px] rounded-[8px] '>
                <Image src={inclusivity5} className='  lg:w-[424px] h-[292px] ' />
                <View className='p-[24px]'>
                  <Text className='text-left text-[#1A1A1A] rubik text-[16px] font-medium leading-[32px]  sm:ml-[0px] '>
                    Emergency Hire 
                  </Text>
                  <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] items-start mt-[12px] '>
                    Clients have the opportunity to mark their job as Emergency Hire if they need to find a security
                    operative urgently. This generates a system notification that is immediately sent out to every
                    Surely member who meets their specific needs. 
                  </Text>
                </View>
              </View>
            </View>
            <View className=' sm:w-auto w-full  px-[12px] sm:px-0'>
              <Text className='text-left sm:w-auto  !text-[#323C58] font-rufina-stencil sm:text-[48px] text-[35px] leading-[56px] mt-[19px]   sm:ml-[0px]'>
                Browse opportunities by skillset.
              </Text>
              <Text className='sm:w-auto  text-[#383838] rubik text-[16px] font-normal leading-[24px]  sm:ml-0 text-left sm:text-center mx-auto mt-[8px] mt-[10px]'>
                Looking for people?
                <span
                  onClick={() => {
                    if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className='text-[#0B80E7] rubik text-[16px] font-normal underline leading-[24px] mt-[8px] mt-[10px] sm:ml-1 ml-2 cursor-pointer'
                >
                  Sign up now
                </span>
              </Text>
            </View>

            <View className='flex flex-col lg:flex-row mt-[20px] sm:mt-[12px] gap-[8px] lg:gap-2  w-full mx-auto justify-between px-[12px] xl:px-0 xl:w-[1320px] sm:mb-[63.67px] mb-[30px]'>
              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  xl:w-[244px] h-[174px] items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md mt-[15px] sm:mt-[0px]'
              >
                <View className='flex flex-col  xl:w-[190px] mt-[18px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px] mt-[8px]'
                    >
                      <Text color='positive' className='flex rubik items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Door Supervisor
                      </Text>
                    </Button>
                  </View>
                  <Divider className='w-full h-[1px] mt-[20px]'></Divider>
                  <View className='flex flex-row justify-between mt-[20px]'>
                    <Text className='text-[#323C58] rubik text-[17px] font-medium leading-[24px]'>+786 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>

              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  xl:w-[244px] h-[174px] items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md mt-[15px] sm:mt-[0px]'
              >
                <View className='flex flex-col  xl:w-[190px] mt-[18px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px] mt-[8px]'
                    >
                      <Text color='positive' className='flex rubik items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Close Protection
                      </Text>
                    </Button>
                  </View>
                  <Divider className='w-full h-[1px] mt-[20px]'></Divider>
                  <View className='flex flex-row justify-between mt-[20px]'>
                    <Text className='text-[#323C58] rubik text-[17px] font-medium leading-[24px]'>+456 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>

              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  xl:w-[244px] h-[174px] items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md mt-[15px] sm:mt-[0px]'
              >
                <View className='flex flex-col  xl:w-[190px] mt-[18px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px] mt-[8px]'
                    >
                      <Text color='positive' className='flex rubik items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        CCTV
                      </Text>
                    </Button>
                  </View>
                  <Divider className='w-full h-[1px] mt-[20px]'></Divider>
                  <View className='flex flex-row justify-between mt-[20px]'>
                    <Text className='text-[#323C58] rubik text-[17px] font-medium leading-[24px]'>+118 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>

              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  xl:w-[244px] h-[174px] items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md mt-[15px] sm:mt-[0px]'
              >
                <View className='flex flex-col  xl:w-[190px] mt-[18px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px] mt-[8px]'
                    >
                      <Text color='positive' className='flex rubik items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Security Guard
                      </Text>
                    </Button>
                  </View>
                  <Divider className='w-full h-[1px] mt-[20px]'></Divider>
                  <View className='flex flex-row justify-between mt-[20px]'>
                    <Text className='text-[#323C58] rubik text-[17px] font-medium leading-[24px]'>+245 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>
              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  xl:w-[244px] h-[174px] items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md mt-[15px] sm:mt-[0px]'
              >
                <View className='flex flex-col  xl:w-[190px] mt-[18px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px] mt-[8px]'
                    >
                      <Text color='positive' className='flex rubik items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Public Space Surveillance
                      </Text>
                    </Button>
                  </View>
                  <Divider className='w-full h-[1px] mt-[20px]'></Divider>
                  <View className='flex flex-row justify-between mt-[20px]'>
                    <Text className='text-[#323C58] rubik text-[17px] font-medium leading-[24px]'>+345 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>
            </View>
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex  justify-between w-full h-[480px] md:w-full md:h-[312px] mx-0 md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Footer />
    </View>
  );
};

export default InclusivityPledgePage;
