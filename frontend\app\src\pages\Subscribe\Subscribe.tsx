// @ts-nocheck
import React, { useState } from 'react';
import { View, Text, Button, TextField, Checkbox, useToast, Image } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { addSubscribe } from 'src/services/user';

const Subscribe: React.FC = () => {
  const toast = useToast();

  const [name, setName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [type, setType] = useState('');
  const [subscribe, setSubscribe] = useState<boolean>(true);
  const [error, setError] =useState('')

  const submitSubscribe = async () => {
    if (!name || !lastName || !email || !type) {
      setError('Please fill all the fields.')
      toast.show({
        title: 'Error',
        text: 'Please fill all the fields.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
      return;
    }

    const subscriptionData = {
      name,
      lastName,
      email,
      type: Number(type),
      subscribe: true
    };

    await addSubscribe(subscriptionData);

    toast.show({
      title: 'Done!',
      text: 'You have submitted for subscribe',
      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
    });
  };

  return (
    <View className='w-full justify-between bg-[#323C58]'>
      <View className='!mx-auto ml-[0px] flex w-full  max-w-[1450px] flex-col items-center justify-center gap-5 px-[12px] sm:flex-row sm:justify-between xl:px-0'>
        <View className=' mx-auto mt-[20px] sm:mt-[0px] xl:w-[646px]'>
          <Text className='font-rufina-stencil text-left text-[36px]  leading-[44px] text-[#fff] sm:ml-[0px] sm:text-left sm:text-[48px] sm:leading-[56px]'>
            Subscribe to our newsletter, stay in the loop.
          </Text>
        </View>
        <View className='mx-auto mt-[14px]  flex w-full flex-col sm:mt-[40px] xl:w-[538px]'>
          <View className='ml-[0px] w-full flex-col gap-[8px] sm:flex sm:flex-row sm:justify-between  xl:gap-0'>
            <View className='w-full sm:flex sm:flex-col xl:w-[263px]'>
              <Text className='rubik text-left text-[14px] font-medium leading-[20px] text-[#fff]'>First name</Text>
              <TextField
                name='firstname'
                className='mt-2 h-10 w-full sm:mt-[8px] sm:h-[48px] xl:w-[263px] '
                placeholder='Your first name'
                value={name}
                // onChange={(e) => setName(e.value)}
                onChange={(e) => {
                  setName(e.value);
                  if (e.value && lastName && email && type) {
                    setError('');
                  }
                }}
              />
            </View>
            <View className='mt-2 w-full sm:flex sm:flex-col md:mt-0 xl:w-[263px] '>
              <Text className='rubik text-left text-[14px] font-medium leading-[20px] text-[#fff]'>Last name</Text>
              <TextField
                name='lastname'
                className='mt-2 h-10 w-full sm:mt-[8px] sm:h-[48px] xl:w-[263px]'
                placeholder='Your last name'
                value={lastName}
                // onChange={(e) => setLastName(e.value)}
                onChange={(e) => {
                  setLastName(e.value);
                  if (name && e.value && email && type) {
                    setError('');
                  }
                }}
              />
            </View>
          </View>

          <Text className='rubik ml-[0px] mt-2 text-left text-[14px] font-medium  leading-[20px] text-[#fff] sm:mt-[10px]'>Email</Text>
          <TextField
            name='email'
            className='ml-[0px] mt-2 h-10 w-full sm:mt-[8px] sm:h-[48px]  xl:w-[538px]'
            placeholder='Your email address'
            value={email}
            // onChange={(e) => setEmail(e.value)}
            onChange={(e) => {
              setEmail(e.value);
              if (name && lastName && e.value && type) {
                setError('');
              }
            }}
          />

          <View className='ml-[0px] mt-2 flex flex-col sm:mt-[16px] lg:flex-row'>
            <Checkbox
              name='role'
              value='1'
              checked={type === '1'}
              // onChange={() => handleCheckboxChange('operative')}
              onChange={() => {
                const newValue = '1';
                setType(newValue);
                if (name && lastName && email && newValue) {
                  setError('');
                }
              }}
              className='mb-2 sm:ml-[0px] sm:mr-4 sm:w-[33%] lg:mb-0'
            >
              <Text className='rubik whitespace-nowrap text-left text-[14px] font-medium leading-[20px] text-[#fff]'>I’m a security operative</Text>
            </Checkbox>
            <Checkbox
              name='role'
              value='2'
              checked={type === '2'}
              // onChange={() => handleCheckboxChange('client')}
              onChange={() => {
                const newValue = '2';
                setType(newValue);
                if (name && lastName && email && newValue) {
                  setError('');
                }
              }}
              className='mb-2 sm:mr-4 sm:w-[33%] lg:mb-0 lg:ml-[18px]'
            >
              <Text className='rubik whitespace-nowrap text-left text-[14px] font-medium leading-[20px] text-[#fff]'>I hire security operatives</Text>
            </Checkbox>
            <Checkbox
              name='role'
              value='0'
              checked={type === '0'}
              // onChange={() => handleCheckboxChange('other')}
              onChange={() => {
                const newValue = '0';
                setType(newValue);
                if (name && lastName && email && newValue) {
                  setError('');
                }
              }}
              className='mb-2 sm:mb-0 sm:w-[33%] lg:ml-[18px]'
            >
              <Text className='rubik whitespace-nowrap text-left text-[14px] font-medium leading-[20px] text-[#fff]'>Other</Text>
            </Checkbox>
          </View>

          <Button
            className='border-border-neutral-faded mb-6  ml-[0px]  mt-4 flex items-center justify-center gap-2 rounded-md border !bg-[#0B80E7]  p-4 sm:mt-[20px] lg:mb-[30px] xl:w-[538px]'
            onClick={() => {
              submitSubscribe();
            }}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] !text-[#fff] '>Subscribe</Text>
          </Button>
          {error && <p className='rubik text-[16px] font-medium leading-[24px] !text-[#fff] mt-[-10px] mb-[5px] '>{error}</p>}
        </View>
      </View>
    </View>
  );
};

export default Subscribe;
