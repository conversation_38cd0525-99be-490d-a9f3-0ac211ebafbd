<?php

namespace App\DataTables;

use App\Models\MobileUser;
use Illuminate\Support\Carbon;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class MobileUsersDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('action', 'mobile_users.action')
            ->addColumn('document', function ($user) {
                if ($user->account_type == MobileUser::freelancer) {
                    return view('mobile_users.document_button', ['id' => $user->id]);
                }
                return '';
            })
            ->editColumn('created_at', function ($user) {
                return Carbon::parse($user->created_at)->format('Y-m-d');
            })
            ->editColumn('updated_at', function ($user) {
                return Carbon::parse($user->updated_at)->format('Y-m-d');
            })
            ->editColumn('account_type', function ($user) {
                return MobileUser::role_name($user->account_type);
            })
            ->editColumn('name', function ($user) {
                return "
                <div>
                    <p class='m-0 h5 p-1'>$user->name</p>
                    <a href='mailTo:$user->email' class='p-1'> $user->email </a>
                </div>
                ";
            })
            ->editColumn('status', function ($user) {
                if ($user->checkDocuments() === null) {
                    return '<p class="text-warning h6">Pending</p>';
                }

                if ($user->checkDocuments() === false) {
                    return '<p class="text-danger h6">Rejected</p>';
                } else {
                    return '<p class="text-success h6">Approved</p>';
                } 
            })
            ->editColumn('active', function ($user) {
                if ($user->deleted_at) {
                    return '<p class="text-danger">Deleted</p>';
                }

                return '<p class="text-success">Active</p>';
            })
            ->filter(function ($query) {
                $query->where(function ($query) {
                    $q = request()->get('search')['value'];
                    $query->where('name', 'like', "%$q%")
                        ->orWhere('email', 'like', "%$q%")
                        ->orWhere('phone', 'like', "%$q%")
                        ->orWhere('city', 'like', "%$q%")
                        ->orWhere('created_at', 'like', "%$q%");
                });
            })
            ->rawColumns(['status', 'name', 'action', 'document', 'active']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\MobileUser $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(MobileUser $model)
    {
        $query = MobileUser::withTrashed();

        if ($this->attributes['account_type'] == 1) {
            $query->where('account_type', MobileUser::freelancer);
        } elseif ($this->attributes['account_type'] == 2) {
            $query->where('account_type', MobileUser::business);
        }

        return $query;
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('zero-config')
            ->addTableClass('table mb-4')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->ajax([
                'url' => '/mobile_users/mobile_users?account_type=' . $this->account_type,
                'type' => 'GET',
                'scheme' => 'https'
            ])
            ->parameters([
                "dom" => "<'dt--top-section'<'row'<'col-sm-12 col-md-6 d-flex justify-content-md-start justify-content-center'B><'col-sm-12 col-md-6 d-flex justify-content-md-end justify-content-center mt-md-0 mt-3'f>>>" .
                    "<'table-responsive'tr>" .
                    "<'dt--bottom-section d-sm-flex justify-content-sm-between text-center'<'dt--pages-count  mb-sm-0 mb-3'i><'dt--pagination'p><'dt--page-length'l>>",
                "lengthMenu" => array(10, 20, 50, 100, 10000),
                'buttons' => [
                    'buttons' => [
                        ['extend' => 'copy', 'className' => 'btn'],
                        ['extend' => 'csv', 'className' => 'btn'],
                        ['extend' => 'excel', 'className' => 'btn'],
                        ['extend' => 'print', 'className' => 'btn'],
                    ]
                ],
                'oLanguage' => [
                    'oPaginate' => [
                        'sPrevious' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                        "sNext" => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                    ],
                    'sInfo' => 'Showing page _PAGE_ of _PAGES_ | Results: _TOTAL_',
                    'sSearch' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                    'sSearchPlaceholder' => 'Search...',
                    "sLengthMenu" => "Results :  _MENU_",
                ],
                'stripeClasses' => [],
            ])
            ->orderBy(7, 'DESC');
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id'),
            Column::make('name'),
            Column::make('phone'),
            Column::make('account_type'),
            Column::make('address'),
            Column::make('city')->addClass('col-1'),
            Column::make('active')->orderable(false),
            Column::computed('status')->orderable(true),
            Column::make('created_at'),
            Column::computed('action')
                ->searchable(false)
                ->orderable(false)
                ->exportable(false)
                ->printable(false),
            Column::computed('document')
                ->title('Documents')
                ->searchable(false)
                ->orderable(false)
                ->exportable(false)
                ->printable(false)
                ->width(60)
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'MobileUsers_' . date('YmdHis');
    }
}
