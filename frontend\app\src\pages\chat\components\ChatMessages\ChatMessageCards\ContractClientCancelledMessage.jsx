import { useAuthContext } from 'src/context/AuthContext';
import getNameInitials from '../../../../../utils/getNameInitials';
import MessageStatus from './MessageStatus';
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useChatContext } from 'src/context/ChatContext';

const ContractClientCancelledMessage = ({ message }) => {
  const { user, isClient } = useAuthContext();
  const { currentChat } = useChatContext;

  const nameShown = user?.profile?.id === message.sender_id ? message.sender?.name : message.receiver?.name
  const rejectionNameShown = message?.sender?.name;
  const reason = message?.message;

  if (message.type === 'reject') {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#CE0E0E] bg-[#FBD0D4] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
            Contract rejected
          </h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            The contract was rejected by <span className='font-[500]'>{rejectionNameShown}</span>.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#CE0E0E] bg-[#FBD0D4] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
          Contract cancelled
        </h2>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          The contract was cancelled by <span className='font-[500]'> {nameShown} </span>. Reason: {reason}
        </p>
      </div>
    </div>
  );
};

export default ContractClientCancelledMessage;
