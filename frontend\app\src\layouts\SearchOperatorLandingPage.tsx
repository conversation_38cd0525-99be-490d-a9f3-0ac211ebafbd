import React from 'react';
import ClientSearchBarLanding from 'src/components/ClientSearchBar/ClientSearchBarLanding';

interface Props {
  children?: React.ReactNode;
}

export const SearchSecurityOperatorLayout = ({ children }: Props): JSX.Element => {
  return (

      <main className='align-center h-full w-full max-w-[1320px]   mx-auto    text-center '>
        <section className='h-full w-full'>
          <div className="lg:sticky lg:top-[90px] lg:z-50 lg:min-h-[56px]">
          <ClientSearchBarLanding />
          </div>
          <article className='h-full flex flex-row gap-5 flex-wrap xl:justify-center px-[12px] xl:px-0'>
            {children}
          </article>
        </section>
      </main>
  );
};

export default SearchSecurityOperatorLayout;
