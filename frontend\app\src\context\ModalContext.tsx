import { createContext, useContext, FunctionComponent, ReactNode, useReducer } from 'react';

type MODAL_VIEWS =
  | 'REGISTER'
  | 'LOGIN'
  | 'FORGOT_PASSWORD'
  | 'LOGIN_OTP'
  | 'FILTERS_CLIENT'
  | 'FILTERS_OPERATOR '
  | 'APPLICATION_DETAILS'
  | 'PROFILE_DETAILS'
  | 'OPERATOR_INVITE'
  | 'DESCTRUCTIVE_ACTION'
  | 'OPERATOR_ APPLY'
  | 'APPROVE_JOB'
  | 'PAYMENT_MODAL'
  | 'REVIEW_CLIENT'
  | 'REVIEW_OPERATOR'
  | 'EDIT_SHIFT'
  | 'GUEST_VIEW'
  | 'GUEST_UPGRADE';

interface ModalProviderProps {
  children: ReactNode;
}

interface State {
  view?: MODAL_VIEWS;
  data?: any;
  isOpen: boolean;
}

type Action = { type: 'open'; view?: MODAL_VIEWS; payload?: any } | { type: 'close' };

const initialState: State = {
  view: undefined,
  isOpen: false,
  data: null,
};

const modalReducer = (state: State, action: Action): State => {
  switch (action.type) {
    case 'open':
      return {
        ...state,
        view: action.view,
        data: action.payload,
        isOpen: true,
      };
    case 'close':
      return {
        ...state,
        view: undefined,
        data: null,
        isOpen: false,
      };
    default:
      throw new Error('Unknown Modal Action!');
  }
};

const ModalContext = createContext<State>(initialState);
ModalContext.displayName = 'ModalStateContext';
const ModalActionContext = createContext<React.Dispatch<Action> | undefined>(undefined);
ModalActionContext.displayName = 'ModalActionContext';

const ModalProvider: FunctionComponent<ModalProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(modalReducer, initialState);

  return (
    <ModalContext.Provider value={state}>
      <ModalActionContext.Provider value={dispatch}>{children}</ModalActionContext.Provider>
    </ModalContext.Provider>
  );
};

export function useModalState() {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error(`useModalState must be used within a ModalProvider`);
  }
  return context;
}

export function useModalAction() {
  const dispatch = useContext(ModalActionContext);

  if (dispatch === undefined) {
    throw new Error(`useModalAction must be used within a ModalProvider`);
  }
  return {
    openModal(view?: MODAL_VIEWS, payload?: unknown) {
      dispatch({ type: 'open', view, payload });
    },
    closeModal(shouldClose?: boolean) {
      if (shouldClose || localStorage?.getItem('wpRedirect') === 'true') {
        localStorage.removeItem('wpRedirect'); // Clear the redirect flag
      }
      dispatch({ type: 'close' });
    },
  };
}

export { ModalContext, ModalProvider };
