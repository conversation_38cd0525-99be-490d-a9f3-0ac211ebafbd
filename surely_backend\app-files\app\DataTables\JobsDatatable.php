<?php

namespace App\DataTables;

use App\Models\Job;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class JobsDatatable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        $front_url = config('app.front_url');

        return datatables()
            ->eloquent($query)
            ->editColumn('post_name', function ($job) {
                return "
                <div>
                    <h6 class='text-black'>$job->post_name</h6>
                </div>
                ";
            })
            ->editColumn('user', function ($job) {
                return "
                <div>
                    <h6 class='text-black'>$job->user_posted_name</h6>
                    <a href='mailTo:$job->user_posted_email'><h6 class='text-primary'>$job->user_posted_email</h6></a>
                </div>
                ";
            })
            ->addColumn('action', function ($row) use ($front_url) {
                $url = "$front_url/operator-job/$row->id";

                return view('jobs.action', ['url' => $url, 'row' => $row]);
            })
            ->editColumn('images', function ($row) {
                $image = isset($row->images) ? $row->images[0] : '';
                $link = "https://app.surelysecurity.com/storage/$image";
                // https://app.surelysecurity.com/storage/job_images/job_images_2024-03-11 10:05:43-65eed7772f42b2.29910134.png
                return "
                    <div>
                        <img src='$link' width='120px'/>
                    </div>
                ";
            })
            ->editColumn('industry_sector', function ($row) {
                $sectors = "";

                if (!isset($row->industry_sector)) {
                    return $sectors;
                }

                foreach ($row->industry_sector as $sector) {
                    $sectors .= $sector . " ";
                }

                return "<p class='text-black'>$sectors</p>";
            })
            ->rawColumns(['post_name', 'user', 'action', 'images', 'industry_sector'])
            ->filter(function ($query) {
                $query->where(function ($query) {
                    $q = request()->get('search')['value'];
                    $query->where('post_name', 'like', "%%$q%%")
                        ->orWhere('user.name', 'like', "%%$q%%")
                        ->orWhere('user.email', 'like', "%%$q%%")
                        ->orWhereJsonContains('jobs.industry_sector', "$q")
                        ->orWhere('jobs.relevant_qualification', 'like', "%%$q%%")
                        ->orWhere('jobs.title', 'like', "%%$q%%")
                        ->orWhere('jobs.location', 'like', "%%$q%%")
                        ->orWhere('jobs.city', 'like', "%%$q%%");
                });
            });
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Job $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Job $model)
    {
        return $model->newQuery()
            ->leftJoin('mobile_users as user', 'user.id', '=', 'jobs.user_id')
            ->select([
                'jobs.id as id',
                'jobs.post_name as post_name',
                'user.name as user_posted_name',
                'user.email as user_posted_email',
                'jobs.industry_sector as industry_sector',
                'jobs.relevant_qualification as relevant_qualification',
                'jobs.surely_pro_badge as surely_pro_badge',
                'jobs.hourly_rate_min as hourly_rate_min',
                'jobs.hourly_rate_max as hourly_rate_max',
                'jobs.title as title',
                'jobs.nr_of_operatives as nr_of_operatives',
                'jobs.location as location',
                'jobs.city as city',
                'jobs.description as description',
                'jobs.duty_of_care as duty_of_care',
                'jobs.benefits as benefits',
                'jobs.is_emergency_hire as emergency_hire',
                'jobs.is_inclusivity_pledge as inclusivity_pledge',
                'jobs.payment_terms as payment_terms',
                'jobs.created_at as published',
                'jobs.images as images',
            ]);
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('jobsdatatable-table')
            ->selectClassName('zero-config')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->parameters([
                "dom" => "<'dt--top-section'<'row'<'col-sm-12 col-md-6 d-flex justify-content-md-start justify-content-center'B><'col-sm-12 col-md-6 d-flex justify-content-md-end justify-content-center mt-md-0 mt-3'f>>>" .
                    "<'table-responsive'tr>" .
                    "<'dt--bottom-section d-sm-flex justify-content-sm-between text-center'<'dt--pages-count  mb-sm-0 mb-3'i><'dt--pagination'p><'dt--page-length'l>>",
                "lengthMenu" => array(10, 20, 50, 100, 10000),
                'buttons' => [
                    'buttons' => [
                        ['extend' => 'copy', 'className' => 'btn'],
                        ['extend' => 'csv', 'className' => 'btn'],
                        ['extend' => 'excel', 'className' => 'btn'],
                        ['extend' => 'print', 'className' => 'btn'],
                    ]
                ],
                'oLanguage' => [
                    'oPaginate' => [
                        'sPrevious' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                        "sNext" => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                    ],
                    'sInfo' => 'Showing page _PAGE_ of _PAGES_ | Results: _TOTAL_',
                    'sSearch' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                    'sSearchPlaceholder' => 'Search...',
                    "sLengthMenu" => "Results :  _MENU_",
                ],
                'stripeClasses' => [],
            ])
            ->ajax([
                'url' => '/jobs',
                'type' => 'GET',
                'scheme' => 'https',
            ]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-black')->orderable(true),
            Column::make('post_name')->addClass('col-2')->addClass('text-black')->orderable(true),
            Column::make('images')->title('image')->addClass('text-black')->orderable(false),
            Column::make('user')->addClass('text-black')->orderable(true),
            Column::make('industry_sector')->addClass('text-black')->orderable(true),
            // Column::make('relevant_qualification'),
            Column::make('surely_pro_badge')->addClass('text-black')->orderable(true),
            Column::make('hourly_rate_min')->title('Hourly Min')->addClass('text-black')->orderable(true),
            Column::make('hourly_rate_max')->title('Hourly Max')->addClass('text-black')->orderable(true),
            Column::make('title')->addClass('text-black')->orderable(true),
            // Column::make('nr_of_operatives'),
            Column::make('location')->addClass('text-black')->orderable(true),
            // Column::make('published'),
            Column::computed('action')
                ->exportable(false)
                ->printable(false)
                ->width(60)
                ->addClass('text-center')
                ->addClass('text-black')
                ->orderable(false),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'Jobs_' . date('YmdHis');
    }
}
