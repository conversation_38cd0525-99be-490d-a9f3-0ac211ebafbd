// @ts-nocheck
import React, { ChangeEvent, useRef, useState } from 'react';
import { Text, View, Button, Divider, Modal, useToast, Image } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import { operatorReview } from 'src/services/contracts';
import surleyicon from '../../../../assets/icons/surleyicon/surleyicon.png';
import { IdVerified } from 'src/assets/icons';
interface OperatorReviewModalProps {
  active: boolean;
  deactivate: () => void;
  operator: any;
  chat: any;
}

const OperatorReviewModal: React.FC<OperatorReviewModalProps> = ({ active, deactivate, operator, chat }) => {
  const toast = useToast();
  const [punctuality, setPunctuality] = useState<number>(0);
  const [communication, setCommunication] = useState<number>(0);
  const [helpfulness, setHelpfulness] = useState<number>(0);
  const [professionalism, setProfessionalism] = useState<number>(0);
  const [positivity, setPositivity] = useState<number>(0);
  const [dressCode, setDressCode] = useState<number>(0);
  const [details, setDetails] = useState<string>('');

  const lastname = chat?.sender?.name?.split(' ').pop()?.charAt(0).toUpperCase() + chat?.sender?.name?.split(' ').pop()?.slice(1);
  const firstname = chat?.sender?.name?.split(' ').shift()?.charAt(0).toUpperCase() + chat?.sender?.name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;
  const initialNameFirstCharAt = (firstname?.[0] || '') + (lastname?.[0] || '');

  const handlePunctualityClick = (value: number) => {
    setPunctuality(value);
  };
  const handleCommunicationClick = (value: number) => {
    setCommunication(value);
  };
  const handleHelpfulnessClick = (value: number) => {
    setHelpfulness(value);
  };
  const handleProfessionalismClick = (value: number) => {
    setProfessionalism(value);
  };
  const handlePositivityClick = (value: number) => {
    setPositivity(value);
  };
  const handleDressCodeClick = (value: number) => {
    setDressCode(value);
  };
  const handleDetailsChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setDetails(event.target.value);
  };

  const getImageSrc = (images: any) => {
    const baseUrl = 'https://app.surelysecurity.com/storage/';

    if (images && !images.startsWith(baseUrl)) {
      return baseUrl + images;
    }

    return images;
  };

  const handleOperatorReview = async () => {
    const operatorReviewData: any = {
      details,
      rating: {
        communication,
        professionalism,
        punctuality,
        positivity,
        helpfulness,
        dressCode,
      },
    };

    try {
      const response = await operatorReview({
        id: chat?.contract?.id, // change to contract id
        input: operatorReviewData,
      });

      if (response?.error) {
        toast.show({
          title: 'Oops!',
          text: 'There was an issue posting your review. Please try again later',
          startSlot: <Image key='errorImage' src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      } else {
        toast.show({
          title: 'Success!',
          text: 'Your review has been posted successfully',
          startSlot: <Image key='successImage' src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        deactivate();
      }
    } catch (error) {
      console.error('Error post review:', error);
    }
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[660px] p-[24px]'>
      <View className='gap-[16px]'>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Post a review</Text>
        </View>

        <View className='mt-[16px] flex flex-col'>
          <View className='flex flex-row gap-[20px]'>
            {/* <Image className='w-[64px] h-[64px] bg-[#C7CDDB] rounded-[100px]' 
            src={getImageSrc(chat?.sender?.profile_photo)}
            /> */}
            {chat?.sender?.profile_photo ? (
              <div
                className='rounded-full p-0.5'
                style={{
                  background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                }}
              >
                <Image
                  className='flex h-[64px] w-[64px] flex-col items-start rounded-full bg-white'
                  alt='Profile'
                  src={getImageSrc(chat?.sender?.profile_photo)}
                />
              </div>
            ) : (
              <View className='flex h-[64px] w-[64px] items-center justify-center  rounded-full bg-[#C7CDDB]'>
                <Text className='rubik text-[18px] font-medium text-[#323C58] '>{initialNameFirstCharAt}</Text>
              </View>
            )}
            <View className='mt-[5px] flex flex-col gap-[4px]'>
              <Text className=' rubik text-[15px] font-normal leading-[20px] text-[#323C58]'>{initialName}</Text>
              <Text className='rubik text-line-height text-[16px] leading-5   text-blue-400 '>
                London &nbsp; <span className='material-icons-outlined icon-line-height text-[15px] text-blue-400'>place</span>
              </Text>
            </View>
          </View>

          <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Please review and rate your experience</Text>
          <View className='flex flex-col md:flex-row justify-between'>
            <View className='flex flex-col '>
              <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Punctuality</Text>
              <View className='mt-[5px] flex flex-row gap-[8px]'>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    onClick={() => handlePunctualityClick(index)}
                    className={`h-[8px] w-[44px] ${punctuality >= index ? 'bg-[#388DD8]' : 'bg-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
                  />
                ))}
              </View>
              <Text className='rubik mt-[5px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{punctuality}.0</Text>
            </View>
            <View className='flex flex-col '>
              <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Professionalism</Text>
              <View className='mt-[5px] flex flex-row gap-[8px]'>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    onClick={() => handleProfessionalismClick(index)}
                    className={`h-[8px] w-[44px] ${professionalism >= index ? 'bg-[#388DD8]' : 'bg-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
                  />
                ))}
              </View>
              <Text className='rubik mt-[5px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{professionalism}.0</Text>
            </View>
          </View>

          <View className='flex flex-col md:flex-row justify-between'>
            <View className='flex flex-col '>
              <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Communication</Text>
              <View className='mt-[5px] flex flex-row gap-[8px]'>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    onClick={() => handleCommunicationClick(index)}
                    className={`h-[8px] w-[44px] ${communication >= index ? 'bg-[#388DD8]' : 'bg-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
                  />
                ))}
              </View>
              <Text className='rubik mt-[5px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{communication}.0</Text>
            </View>
            <View className='flex flex-col '>
              <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Positivity</Text>
              <View className='mt-[5px] flex flex-row gap-[8px]'>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    onClick={() => handlePositivityClick(index)}
                    className={`h-[8px] w-[44px] ${positivity >= index ? 'bg-[#388DD8]' : 'bg-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
                  />
                ))}
              </View>
              <Text className='rubik mt-[5px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{positivity}.0</Text>
            </View>
          </View>

          <View className='flex flex-col md:flex-row justify-between'>
            <View className='flex flex-col '>
              <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Helpfulness</Text>
              <View className='mt-[5px] flex flex-row gap-[8px]'>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    onClick={() => handleHelpfulnessClick(index)}
                    className={`h-[8px] w-[44px] ${helpfulness >= index ? 'bg-[#388DD8]' : 'bg-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
                  />
                ))}
              </View>
              <Text className='rubik mt-[5px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{helpfulness}.0</Text>
            </View>
            <View className='flex flex-col '>
              <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Dress Code</Text>
              <View className='mt-[5px] flex flex-row gap-[8px]'>
                {[1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    onClick={() => handleDressCodeClick(index)}
                    className={`h-[8px] w-[44px] ${dressCode >= index ? 'bg-[#388DD8]' : 'bg-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
                  />
                ))}
              </View>
              <Text className='rubik mt-[5px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{dressCode}.0</Text>
            </View>
          </View>

          <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Share your experience</Text>
          <div className='mt-[4px] flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px] '>
            <textarea
              name='job_description'
              placeholder='Write about your experience with this Security Operative...'
              className=' border-none  bg-transparent outline-none'
              value={details}
              onChange={handleDetailsChange}
              maxLength={500}
              rows={9}
              style={{
                wordWrap: 'break-word',
                overflowWrap: 'break-word',
                resize: 'none',
              }}
            />

            <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{500 - (details?.length || 0)} characters left</p>
          </div>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1 text-[#CB101D]'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#CB101D]'>Cancel</Text>
          </Button>
          <Button
            onClick={() => {
              handleOperatorReview();
              deactivate();
            }}
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default OperatorReviewModal;
