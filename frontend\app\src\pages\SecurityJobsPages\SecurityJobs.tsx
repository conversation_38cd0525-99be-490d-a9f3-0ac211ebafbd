// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion, Card,Link } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import Footer from '../Footer/Footer';
import { AuthContext } from 'src/context/AuthContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import securityjobs from '../../assets/images/securityjobs/securityjobs.png';
import { useNavigate } from 'react-router-dom';

const SecurityJobs: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex lg:flex-row flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <img src={securityjobs} className='lg:w-[660px] w-full sm:h-[392px] mt-[64px]  sm:mb-[64px]' />
          <View className='flex flex-col lg:w-[660px] sm:h-[392px] p-[50px] xl:px-[50px] px-3  bg-[#FFFF] lg:mt-[64px] mb-[64px]'>
            <Text className='text-left font-rufina-stencil text-[48px] lg:text-[30px]  xl:text-[48px] font-normal xl:leading-[56px] text-[#323C58]'>
              Security Jobs in London and the UK
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px]'>
              Looking for your next security job role? Cut out all the unnecessary layers of middlemen and work directly
              with employers and contractors. Our clients are looking for security professionals in London for immediate
              start roles, including full-time, part-time and contract security jobs. Sign up and create your profile
              today.
            </Text>
            <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#FFFF]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center  mb-[90px] gap-[24px]'>
          <View className='flex sm:flex-row flex-col justify-between gap-[24px]  xl:px-[0px] px-3 mt-[64px]'>
            <Card className='flex flex-col xl:w-[648px]  items-start p-[50px]  sm:px-[50px] px-4 bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain'  onClick={() => navigate('/security-jobs/security-guard')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>Security Guard Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                Skilled security guards like you will always be in high demand throughout London and the UK. Find your
                next job and negotiate directly with clients to secure the best possible rate.
              </Text>
              <button
                className='btn-no-hover  flex items-center h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/security-guard')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next security guard job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
            <Card className='flex flex-col xl:w-[648px] items-start p-[50px]  sm:px-[50px] px-4 bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain' onClick={() => navigate('/security-jobs/bodyguard')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>Bodyguard Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                If you’re a skilled and accomplished bodyguard looking for your next opportunity, we can help. We match
                SIA-certified bodyguards with clients looking for personal protection.
              </Text>
              <button
                className='btn-no-hover  flex items-center  h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/bodyguard')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next bodyguard job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
          </View>

          <View className='flex sm:flex-row flex-col justify-between xl:px-0 px-3 gap-[24px]'>
            <Card className='flex flex-col xl:w-[648px] items-start p-[50px]  sm:px-[50px] px-4 bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain' onClick={() => navigate('/security-jobs/cctv-operator')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>CCTV Operator Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                If you’re an SIA-certified CCTV professional, we can match you with employers looking for the unique
                skills and qualifications that you offer. Sign up today to explore all opportunities.
              </Text>
              <button
                className='btn-no-hover  flex items-center  h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/cctv-operator')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next CCTV operator job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
            <Card className='flex flex-col xl:w-[648px] items-start p-[50px]  sm:px-[50px] px-4  bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain' onClick={() => navigate('/security-jobs/event-security')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>Event Security Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                Find short-term and long-term event security roles that fit your schedule. We work with event organisers
                and reputable contractors who are looking for SIA event security staff like you.
              </Text>
              <button
                className='btn-no-hover  flex items-center  h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/event-security')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next event security job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
          </View>

          <View className='flex sm:flex-row flex-col justify-between xl:px-0 px-3 gap-[24px]'>
            <Card className='flex flex-col xl:w-[648px] items-start p-[50px]  sm:px-[50px] px-4 bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain' onClick={() => navigate('/security-jobs/door-supervisor')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>Door Supervisor Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                If you’re a qualified door supervisor, we will match you with your next opportunity. Work directly with
                agents, venues, pubs and clubs looking for skilled door supervisors like you.
              </Text>
              <button
                className='btn-no-hover  flex items-center  h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/door-supervisor')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next door supervisor job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
            <Card className='flex flex-col xl:w-[648px] items-start p-[50px]  sm:px-[50px] px-4 bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain'  onClick={() => navigate('/security-jobs/private-security')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>Private Security Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                We’ve got lots of opportunities on our platform for private security officers. Sign up today and match
                with the best contractors and employers looking for skilled private security officers.
              </Text>
              <button
                className='btn-no-hover  flex items-center  h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/private-security')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next private security job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
          </View>
          <View className='px-3 xl:px-0'>
            <Card className='flex flex-col xl:w-[648px] items-start p-[50px] sm:mx-0 mx-4 sm:px-[50px] px-4 bg-[#F4F5F7] shadow-md border-none'>
              <Link variant='plain' onClick={() => navigate('/security-jobs/close-protection')} className='text-[24px] font-normal rubik leading-[32px] text-[#0B80E7]'>Close Protection Jobs</Link>
              <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px]'>
                Looking for your next job as a close protection officer in London? Our platform makes it easier than
                ever for close protection officers like you to connect with employers in need of your services.
              </Text>
              <button
                className='btn-no-hover  flex items-center  h-[48px] mt-[2px]'
                onClick={() => navigate('/security-jobs/close-protection')}
              >
                <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                  Find your next close protection job
                </Text>
                <span className='material-icons text-[18px] !text-[#0B80E7] mt-[2px] ml-[5px] '>arrow_forward</span>
              </button>
            </Card>
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex mt-[62px] justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center sm:mt-[118px]'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <Footer />
    </View>
  );
};

export default SecurityJobs;
