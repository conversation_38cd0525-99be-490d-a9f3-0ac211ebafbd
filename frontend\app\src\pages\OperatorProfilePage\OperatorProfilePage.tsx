// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import CompleteProfileToast from 'src/components/Profile/OperatorProfileComponents/CompleteProfileToast/CompleteProfileToast';
import OperatorBio from 'src/components/Profile/OperatorProfileComponents/OperatorBio/OperatorBio';
import OperatorEmploymentHistory from 'src/components/Profile/OperatorProfileComponents/OperatorEmploymentHistory/OperatorEmploymentHistory';
import OperatorOverallRating from 'src/components/Profile/OperatorProfileComponents/OperatorOverallRating/OperatorOverallRating';
import OperatorRelevantQualification from 'src/components/Profile/OperatorProfileComponents/OperatorRelevantQualifications/OperatorRelevantQualifications';
import OperatorSecurityAchievement from 'src/components/Profile/OperatorProfileComponents/OperatorSecurityAchievement/OperatorSecurityAchievement';

import { AppContext } from 'src/context/AppContext';

import { Button, Text, View, TextField, Image, useToggle, useToast } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import BankAccountToast from 'src/components/Profile/OperatorProfileComponents/CompleteProfileToast/BankAccountToast';
import { useNavigate } from 'react-router-dom';

export const OperatorProfilePage = () => {
  const {
    profileTitle,
    name,
    cityInfo,
    profilePhoto,
    profileVideo,
    siaLicense,
    additionalPictures,
    instantBook,
    siaLicence,
    idCheck,
    proofOfAddress,
    employmentHistory,
    creditCheck,
    noCriminalRecord,
    profileDescription,
    positions,
    qualifications,
    industrySectors,
    testlang,
    languages,
    addressVerificationDocument,
    documentType,
    idBackDocument,
    idFrontDocument,
    selfieVerificationDocument,
    siaLicenceCardPhoto,
    siaLicenceExpiryDate,
    postalCity,
    siaLicenceNumber,
    hasBankAccount,
  } = useContext(AppContext);
  const navigate = useNavigate();
  const toast = useToast();

  const [showBankToast, setShowBankToast] = useState(!hasBankAccount);

  const isDataUnavailable =
    profileTitle === null ||
    profileDescription === null ||
    profileDescription === undefined ||
    industrySectors === null ||
    profilePhoto === null ||
    profileVideo === null ||
    siaLicense === null ||
    additionalPictures === null ||
    testlang === null ||
    profileTitle === undefined ||
    industrySectors === undefined ||
    profilePhoto === undefined ||
    profileVideo === undefined ||
    siaLicense === undefined ||
    additionalPictures === undefined ||
    testlang === undefined ||
    positions.length === 0 ||
    industrySectors.length === 0 ||
    qualifications.length === 0;

  const isNotCertified =
    addressVerificationDocument === null ||
    documentType === null ||
    idFrontDocument === null ||
    selfieVerificationDocument === null ||
    siaLicenceCardPhoto === null ||
    siaLicenceExpiryDate === null ||
    siaLicenceNumber === null ||
    addressVerificationDocument === undefined ||
    documentType === undefined ||
    idFrontDocument === undefined ||
    selfieVerificationDocument === undefined ||
    siaLicenceCardPhoto === undefined ||
    siaLicenceExpiryDate === undefined ||
    siaLicenceNumber === undefined;

  const isBankAccountUnavailable = hasBankAccount === null || undefined;

  useEffect(() => {
    if (isBankAccountUnavailable) {
      const id = toast.show({
        title: 'Welcome to our platform!',
        text: (
          <div className='h-max space-y-4'>
            <div className='flex gap-3'>
              <h2 className=''>In order to start working, you will need to fill in your bank account details in the payment settings section.</h2>
              <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
            </div>
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => {
                toast.hide(id);
                navigate('/operator-settings-payment', { state: { activeTab: '0' } });
              }}
            >
              Proceed to settings
            </Button>
          </div>
        ),
        timeout: ********,
      });
      return () => toast.hide(id);
    }
  }, [isBankAccountUnavailable]);

  useEffect(() => {
    if (isNotCertified) {
      const id = toast.show({
        title: 'Welcome to our platform!',
        text: (
          <div className='h-max space-y-4'>
            <div className='flex gap-3'>
              <h2 className=''>
                Complete your profile to increase your chances of getting selected for jobs by clients. Don't miss out on great opportunities – finish
                your profile today!
              </h2>
              <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
            </div>
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => {
                toast.hide(id);
                navigate('/first-step-validation-operator');
              }}
            >
              Validate your profile
            </Button>
          </div>
        ),
        timeout: ********,
      });

      return () => toast.hide(id);
    }
    if (isDataUnavailable) {
      const id = toast.show({
        title: 'Welcome to our platform!',
        text: (
          <div className='h-max space-y-4'>
            <div className='flex gap-3'>
              <h2 className=''>
                Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.
              </h2>
              <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
            </div>
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => {
                toast.hide(id);
                navigate('/operator-settings');
              }}
            >
              Complete your profile
            </Button>
          </div>
        ),
        timeout: ********,
      });

      return () => toast.hide(id);
    }
  }, [isNotCertified, isDataUnavailable]);

  return (
    <div className='flex flex-col gap-6'>
      <div className='flex flex-col gap-6 lg:flex-row'>
        <OperatorBio />
        <OperatorSecurityAchievement />
      </div>
      {/* <OperatorOverallRating /> */}
      <div className='flex flex-col gap-6 lg:flex-row'>
        <OperatorRelevantQualification />
        <OperatorEmploymentHistory />
      </div>
      {/* {isDataUnavailable && <CompleteProfileToast showBankToast={showBankToast} />}
      {showBankToast && <BankAccountToast onClose={() => setShowBankToast((prev) => !prev)} />} */}
    </div>
  );
};

export default OperatorProfilePage;
