const overrideDuplicates = (data) => {
  const dateMap = new Map();

  // Iterate over the data array
  data.forEach((entry, index) => {
      const { start } = entry;
      const date = start.slice(0, 10); // Extracting year, month, and day

      if (dateMap.has(date)) {
          // If date already exists in the map, override the entry with the latest one
          const existingIndex = dateMap.get(date);
          data[existingIndex] = entry;
      } else {
          // Store the index of the entry corresponding to the date
          dateMap.set(date, index);
      }
  });

  return data;
};

export default overrideDuplicates