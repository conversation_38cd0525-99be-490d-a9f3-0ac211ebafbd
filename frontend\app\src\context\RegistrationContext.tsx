// @ts-nocheck
import React, { createContext, useContext, useState } from 'react';

import {
  BaseRegisterDataType,
  CrucialRegisterDataType,
  SharedRegisterDataType,
  ClientRegisterFirstStepDataType,
  ClientRegisterDataType,
  OperatorRegisterFirstStepDataType,
  OperatorRegisterSecondStepDataType,
  OperatorRegisterThirdStepDataType,
  OperatorRegisterFourthStepDataType,
  OperatorRegisterDataType,
  RegistrationContextType,
} from '../types/register';

const initialBaseRegisterData = {
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  passwordConfirmation: '',
  loginType: 1,
  platform: 3,
  appVersion: 'web 1.0',
  firebaseToken: 'test',
  ref: 'register',
  referFriend: '',
};

const initialCrucialRegisterData = {
  accountType: 0,
  phone: '',
  postCode: '',
};

const initialSharedRegisterData = {
  baseData: initialBaseRegisterData,
  crucialData: initialCrucialRegisterData,
};

const initialClientRegisterFirstStep = {
  clientRepresentBusinessAs: '',
  companyRegisteredNumber: '',
  industrySectors: '',
  selectedIndustry: '',
};
const initialClientRegisterData = { firstStep: initialClientRegisterFirstStep };

const initialOperatorRegisterFirstStep = {
  siaLicenceNumber: '',
  expiryDate: '',
  capturedImage: '',
  siaLicenceTypes: '',
};

const initialOperatorRegisterSecondStep = {
  documentType: '',
  frontImage: '',
  backImage: '',
};

const initialOperatorRegisterThirdStep = {
  addressVerificationDocument: '',
};

const initialOperatorRegisterFourthStep = {
  capturedImageSelfie: '',
};

const initialOperatorRegisterData = {
  firstStep: initialOperatorRegisterFirstStep,
  secondStep: initialOperatorRegisterSecondStep,
  thirdStep: initialOperatorRegisterThirdStep,
  fourthStep: initialOperatorRegisterFourthStep,
};

const initialState = {
  sharedRegisterData: initialSharedRegisterData,
  clientRegisterData: initialClientRegisterData,
  operatorRegisterData: initialOperatorRegisterData,
  setSharedRegisterData: () => {},
  setClientRegisterData: () => {},
  setOperatorRegisterData: () => {},
  showValidateButton: false,
  setShowValidateButton: () => [],
};

const RegistrationContext = createContext<RegistrationContextType | undefined>(initialState);

export function useRegistrationContext() {
  const context = useContext(RegistrationContext);
  if (context === undefined) {
    throw new Error('useRegistrationContext must be used within an RegistrationProvider');
  }
  return context;
}

const RegistrationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sharedRegisterData, setSharedRegisterData] = useState<SharedRegisterDataType>(initialSharedRegisterData);

  const [clientRegisterData, setClientRegisterData] = useState<ClientRegisterDataType>(initialClientRegisterData);

  const [operatorRegisterData, setOperatorRegisterData] = useState<OperatorRegisterDataType>(initialOperatorRegisterData);

  const [showValidateButton, setShowValidateButton] = useState<boolean>(initialState.showValidateButton);

  const contextValue: any = {
    sharedRegisterData,
    setSharedRegisterData,
    clientRegisterData,
    setClientRegisterData,
    operatorRegisterData,
    setOperatorRegisterData,
    showValidateButton,
    setShowValidateButton,
  };

  return <RegistrationContext.Provider value={contextValue}>{children}</RegistrationContext.Provider>;
};

export { RegistrationContext, RegistrationProvider };
