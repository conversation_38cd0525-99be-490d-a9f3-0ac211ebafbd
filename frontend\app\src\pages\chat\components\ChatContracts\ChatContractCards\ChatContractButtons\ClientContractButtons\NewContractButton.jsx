import { useToggle } from 'reshaped';
import CreateContractModal from "src/pages/chat/components/ChatModals/CreateContractModal";

const ClientNewContractButton = ({
  operator,
  chatId,
}) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <div className='rubik height-[156px] flex flex-col gap-3 text-[16px] font-medium'>
      <div
        onClick={activate}
        className='flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg border border-[#DFE2EA] py-[10.5px] text-[#0B80E7]'
      >
        <span className='material-icons'>add</span>Set new contract
      </div>
      <CreateContractModal
        active={active}
        operator={operator}
        deactivate={deactivate}
        chatId={chatId}
      />
    </div>
  );
};

export default ClientNewContractButton