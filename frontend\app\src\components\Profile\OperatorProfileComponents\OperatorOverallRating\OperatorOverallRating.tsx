// @ts-nocheck
import React, { useContext } from 'react';
import { Card, Text, View, Divider } from 'reshaped';
import Coments from '../OperatorReview/OperatorReviewSection'
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import { AppContext } from 'src/context/AppContext';
import NoRatingDataOperator from 'src/components/NoData/NoRatingDataOperator';

const OperatorOverallRating: React.FC = () => {
  const { otherReview, overallReview,} = useContext(AppContext);

  return (
    <Card className='mt-[24px] h-[auto] w-full p-[24px] xl:w-[760px]'>
      <View className='flex flex-col items-start justify-between'>
        <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>Overall rating</Text>
      </View>
      {overallReview === null || overallReview === undefined ? (
        <NoRatingDataOperator />
      ) : (
        <>
          <View className='mt-[20px] flex items-center gap-2'>
            {overallReview ? (
              <>
                {[...Array(5)].map((_, index) => (
                  <span key={index} className={`material-icons ${index < Math.round(overallReview?.value) ? 'text-[#F4BF00]' : 'text-gray-400'}`}>
                    star
                  </span>
                ))}
                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>{overallReview?.value}</Text>
              </>
            ) : (
              <>
                <span className='material-icons text-[#EBEDF2]'>star</span>
                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#EBEDF2]'>No rating</Text>
              </>
            )}
          </View>
          <Divider className='mt-[20px] h-[1px] w-full' />
          <View className='mt-[20px] grid grid-cols-1 gap-4 md:grid-cols-2'>
            {otherReview &&
              Object.entries(otherReview).map(([section, star], index) => {
                const formattedSection = section.charAt(0).toUpperCase() + section.slice(1);
                const displaySection = formattedSection.replace(/_/g, ' ');
                return (
                  <View key={index} className='mt-[20px] flex flex-col items-start'>
                    <Text className='text-neutral xl:text-caption2 xl:font-medium xl:leading-4'>{displaySection}</Text>
                    <View className='mt-[16px] flex w-full gap-1'>
                      {[...Array(5)].map((_, starIndex) => (
                        <div
                          key={starIndex}
                          className={`h-[5px] w-full md:w-[54px] ${starIndex < Math.round(Number(star)) ? 'bg-[#0B80E7]' : 'bg-gray-400'} rounded-lg`}
                        ></div>
                      ))}
                    </View>
                    <Text className='text-neutral xl:text-caption2 mt-[6px] text-right xl:font-medium xl:leading-4'>{Number(star).toFixed(1)}</Text>
                  </View>
                );
              })}
          </View>
          <Divider className='mt-[20px] h-[1px] w-full' />
          <Coments  />
        </>
      )}
    </Card>
  );
};

export default OperatorOverallRating;
