// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Text, View, Image, Loader } from 'reshaped';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import surelyiconprofile from '../../assets/icons/surleyicon/surelyiconprofile.png';
import { verifyEmail as verifyEmailService } from 'src/services/user';
import { AuthContext } from 'src/context/AuthContext';
import { AppContext } from 'src/context/AppContext';

const VerifyEmail: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { authenticateUser, isAuthenticated, user } = useContext(AuthContext);
  const { fetchAppData } = useContext(AppContext);
  const [selectedType, setSelectedType] = useState<'client' | 'operative'>();

  const useQuery = () => {
    return new URLSearchParams(location.search);
  };

  useEffect(() => {
    const query = useQuery();
    const token = query.get('token');

    if (token) {
      verifyEmailService({ token })
        .then((response) => {
          if (response?.token) {
            authenticateUser(response);
            if (selectedType === 'client') {
              navigate('/client-dashboard');
            } else {
              navigate('/my-profile');
            }
          }
        })
        .catch((error) => {
          console.error('Failed to verify email:', error);
        });
    }
  }, [location, navigate, selectedType]);

  useEffect(() => {
    if (isAuthenticated) {
      if (user.account_type === 1) {
        navigate('/my-profile');
      } else if (user.account_type === 2) {
        navigate('/client-dashboard');
      }
    }
  }, [isAuthenticated, user, navigate]);

  return (
    <View className='mx-auto flex flex-col items-center justify-center sm:gap-[24px] md:px-[10px] lg:gap-[24px]'>
      <Text className='font-rufina-stencil mt-[10px] text-center text-[35px] font-normal leading-[46px] text-[#323C58] sm:mt-[10px] sm:text-[48px] sm:leading-[56px]'>
        Verify your email
      </Text>
      <Text className='rubik ml-[0px] mt-[10px] text-center text-[16px] font-medium leading-[20px] text-[#323C58] sm:ml-0'>
        Kindly check both your inbox and junk/spam folder.
      </Text>
      <div className='relative mt-[22px]'>
        <Loader size='small' className='flex h-[200px] w-[200px] items-center justify-center' />
        <img src={surelyiconprofile} className='absolute left-1/2 top-1/2 w-[50px] -translate-x-1/2 -translate-y-1/2 transform' />
      </div>
    </View>
  );
};

export default VerifyEmail;
