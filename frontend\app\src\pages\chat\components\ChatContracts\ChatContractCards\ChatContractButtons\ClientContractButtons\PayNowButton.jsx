import { useToggle, Button, Text } from 'reshaped';
import PaymentModal from 'src/pages/chat/components/ChatModals/PaymentModal';
import { useState } from 'react';
import { useChatContext } from 'src/context/ChatContext';


const ClientPayNowButton = ({ 
  payEscrow, 
  payOutstanding, 
  contract, 
  fromDashboard 
}) => {
  const { loadContract, handleGetChats, currentChat } = useChatContext();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const isOutstandingPayment = contract.escrow_status === 'paid';

  return (
    <>
      <Button
        onClick={() => setShowPaymentModal(true)}
        className="border-neutral flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2"
      >
        <Text className="rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]">
          {isOutstandingPayment ? 'Pay Outstanding' : 'Pay Escrow'}
        </Text>
      </Button>

      <PaymentModal
        active={showPaymentModal}
        deactivate={() => setShowPaymentModal(false)}
        payEscrow={payEscrow}
        payOutstanding={payOutstanding}
        contract={contract}
        isOutstanding={isOutstandingPayment}
        loadContract={loadContract}
        handleGetChats={handleGetChats}
        chatId={currentChat?.id}
      />
    </>
  );
};

export default  ClientPayNowButton;
