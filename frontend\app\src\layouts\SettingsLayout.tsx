import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';

interface Props {
  children?: React.ReactNode;
}

export const SettingsLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container
      padding={0}
      className="w-[100vw] min-h-[100vh] bg-[url('src/assets/altBg.jpg')] bg-cover"
    >
      <Header />
      <main className='flex flex-wrap items-center justify-center gap-16 mt-[40px] lg:mt-[68px] md:pb-20'>
        {children}
      </main>
    </Container>
  );
};

export default SettingsLayout;
