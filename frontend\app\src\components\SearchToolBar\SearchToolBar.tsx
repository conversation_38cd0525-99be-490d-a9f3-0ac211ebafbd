import { TextField, View, Text, But<PERSON>, Popover, useToggle } from 'reshaped';
import FilterButton from './FilterButton/FilterButton';
import GridSearchButton from './GridSearchButton/GridSearchButton';
import InstantBookButton from './InstantBookButton/InstantBookButton';
import PostJobButton from './PostJobButton/PostJobButton';

const SearchToolBar: React.FC = () => {
  const { active, activate, deactivate } = useToggle(false);
  return (
    <View
      height={'104px'}
      width={'100%'}
      borderRadius={'circular'}
      direction={'row'}
      align={'center'}
      className='w-full max-w-[1320px] bg-white bg-opacity-96 shadow-md justify-between px-6'
    >
      <View direction={'row'} justify={'start'} align={'center'} gap={3}>
        <View
          direction={'row'}
          justify={'center'}
          align={'center'}
          height='56px'
          width='360px'
          borderRadius='circular'
          padding={1}
          className='border border-1 border-solid border-gray-300'
          maxWidth='100%'
        >
          <TextField
            size='medium'
            variant='headless'
            startSlot={<span className='material-icons-outlined'>search</span>}
            name='searchInput'
            placeholder='Search for example “CCTV from London”'
            className='!pl-2 w-10/12'
          />
        </View>
        <View height={'20px'} justify={'center'}>
          <Text className='rubik text-base font-medium leading-16 tracking-normal text-left'>
            280 Results
          </Text>
        </View>
      </View>

      <View>
        <View
          className='lg:flex sm:hidden'
          direction={'row'}
          justify={'center'}
          align={'center'}
          gap={2}
        >
          <PostJobButton />
          <InstantBookButton />
          <GridSearchButton />
          <FilterButton />
        </View>
        <View width={55} className='lg:hidden md:block'>
          <Popover active={active} onClose={deactivate} onOpen={activate}>
            <Popover.Trigger>
              {(attributes) => (
                <Button
                  fullWidth
                  variant='outline'
                  rounded={true}
                  attributes={attributes}
                  size={'large'}
                  className='flex flex-row align-center'
                >
                  <View className='flex flex-row justify-center items-center gap-2  '>
                    <Text className='rubik font-medium text-base leading-5 text-black'>
                      Show Menu
                    </Text>
                    <span className='material-icons-outlined text-black'>
                      {active ? 'close' : 'menu'}
                    </span>
                  </View>
                </Button>
              )}
            </Popover.Trigger>

            <Popover.Content>
              <View direction={'column'} gap={3}>
                <PostJobButton />
                <InstantBookButton />
                <GridSearchButton />
                <FilterButton />
              </View>
            </Popover.Content>
          </Popover>
        </View>
      </View>
    </View>
  );
};
export default SearchToolBar;
