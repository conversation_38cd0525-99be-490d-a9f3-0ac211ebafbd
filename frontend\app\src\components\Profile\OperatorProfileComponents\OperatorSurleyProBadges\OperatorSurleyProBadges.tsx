// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Card, Text, Button, View } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import surelyproicon1 from '../../../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../../../assets/icons/surelyproicon/surelyproicon7.svg';
import inclusivitypledgeicon from '../../../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import { getSurelyProBadge } from 'src/services/settings';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import { AppContext } from 'src/context/AppContext';

const OperatorSurleyProBadges: React.FC = () => {
  // const { surelyProBadges } = useContext(AppContext)
  const navigate = useNavigate();
  const [surelyProBadges, setSurelyProBadges] = useState([]);
  const geticonSrc = (type: any) => {
    switch (type) {
      case 'CustomerService':
        return surelyproicon3;
      case 'UseOfEquipment':
        return surelyproicon2;
      case 'DisabilityFocus':
        return surelyproicon6;
      case 'SubstanceAwareness':
        return surelyproicon4;
      case 'VulnerablePeople':
        return surelyproicon5;
      case 'ConflictManagament':
        return surelyproicon7;
      case 'InclusivityPledge':
        return inclusivitypledgeicon;
      default:
        return surelyproicon1;
    }
  };

  const allBadgesAreInclusivityPledge = surelyProBadges.every((badge: any) => badge.type === 'InclusivityPledge');

  useEffect(() => {
    getSurelyProBadge().then((data: any) => {
      setSurelyProBadges(data?.data || []);
    });
  }, []);

  return (
    <Card className='lg:w-[424px] h-[auto] lg:mx-auto p-6'>
      <View className='flex flex-col gap-5'>
        <View className='flex items-center justify-between'>
          <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>SurelyPro badges</Text>
          <Button
            icon={() => <span className='material-icons-outlined text-white text-base'>add</span>}
            onClick={() => navigate('/surelypro-traning')}
            className='!w-[12px] !h-[12px] !rounded-full border border-[#323C58] !bg-[#323C58] p-0 pb-2'
          ></Button>
        </View>
        <View className='flex gap-2'>
          {surelyProBadges.length === 0 || allBadgesAreInclusivityPledge ? (
            <NoDataProfile />
          ) : (
            <div className='gap-2 flex flex-wrap gap-2'>
              {surelyProBadges
                .filter((badge: any) => badge.type !== 'InclusivityPledge')
                .map((badge: any, index: any) => (
                  <Button
                    key={index}
                    size='small'
                    rounded={true}
                    elevated={false}
                    className='px-2 py-1 border  !bg-[#DDEFFF] text-xs max-w-xs overflow-hidden truncate'
                  >
                    <View className='flex flex-row'>
                      {badge.type && <img src={geticonSrc(badge.type)} alt={badge.type} className='mr-2' />}
                      <Text className='text-[#053D6D] rubik font-normal leading-4'>{badge.text}</Text>
                    </View>
                  </Button>
                ))}
            </div>
          )}
        </View>
      </View>
    </Card>
  );
};

export default OperatorSurleyProBadges;
