import React, { useState, useContext, useEffect } from 'react';
import { View, Image, Text, Button } from 'reshaped';
import surleypro from '../../assets/images/surleypro/surleypro.png';
import conflictmanagement from '../../assets/images/surleypro/conflictmanagement.png';
import costumerservice from '../../assets/images/surleypro/costumerservice.png';
import disabilityfocus from '../../assets/images/surleypro/disabilityfocus.png';
import substanceawareness from '../../assets/images/surleypro/substanceawareness.png';
import surleyproperson from '../../assets/images/surleypro/surleyproperson.png';
import useofequipment from '../../assets/images/surleypro/useofequipment.png';
import vulnerablepeople from '../../assets/images/surleypro/vulnerablepeople.png';
import Footer from '../Footer/Footer';
import surelyproicon1 from '../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon1active from '../../assets/icons/surelyproicon/surelyproicon1active.svg';
import surelyproicon2 from '../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon2active from '../../assets/icons/surelyproicon/surelyproicon2active.svg';
import surelyproicon3 from '../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon3active from '../../assets/icons/surelyproicon/surelyproicon3active.svg';
import surelyproicon4 from '../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon4active from '../../assets/icons/surelyproicon/surelyproicon4active.svg';
import surelyproicon5 from '../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon5active from '../../assets/icons/surelyproicon/surelyproicon5active.svg';
import surelyproicon6 from '../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon6active from '../../assets/icons/surelyproicon/surelyproicon6active.svg';
import surelyproicon7 from '../../assets/icons/surelyproicon/surelyproicon7.svg';
import surelyproicon7active from '../../assets/icons/surelyproicon/surelyproicon7active.svg';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { useLocation } from 'react-router-dom';

const SurleyProPage: React.FC = () => {
  // const [selectedButton, setSelectedButton] = useState('surleypro');
  const location = useLocation();
  const [selectedButton, setSelectedButton] = useState<string>('');

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    setSelectedButton(searchParams.get('type') || '');
  }, [location.search]);

  const { openModal } = useModalAction();
  const { isAuthenticated } = useContext(AuthContext);

  const handleButtonClick = (buttonName: React.SetStateAction<string>) => {
    setSelectedButton(buttonName);
  };

  const getImageSrc = () => {
    switch (selectedButton) {
      case 'surleypro':
        return surleypro;
      case 'substanceawareness':
        return substanceawareness;
      case 'useofequipment':
        return useofequipment;
      case 'vulnerablepeople':
        return vulnerablepeople;
      case 'conflictmanagement':
        return conflictmanagement;
      case 'disabilityfocus':
        return disabilityfocus;
      case 'customerservice':
        return costumerservice;
      default:
        return surleypro;
    }
  };

  const getTextContent = () => {
    switch (selectedButton) {
      case 'surleypro':
        return {
          title: 'Increase your value',
          text1: 'Evidencing the skills that clients want.',
          text2:
            'We’ve spoken to a number of clients over the last couple of years. They told us that there are certain core skills that they value in security operatives and that it would be great to have a system in place that demonstrated their commitment to embracing the major themes around these skillsets.',
          text3:
            'That’s where the idea for our SurelyPro badges originated. We got in touch with a certified SIA trainer to create these modules. They’re not intended to be rocket science. A lot of the time, they’re nothing more than just good common sense.',
          text4:
            'The purpose is not just to remember a few facts to earn a badge to add to your profile. It is mainly to demonstrate to clients a security operative’s understanding of the importance of these six key skills, and that they are committed to delivering excellence in these areas.',
          text5:
            '“For all these modules, you need good communication skills. I believe that the best security operatives act like counsellors much of the time.”',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      case 'substanceawareness':
        return {
          title: 'SurelyPro Badge',
          text1: 'Substance Awareness.',
          text2:
            'In this module, we look at three main areas – the reasons and factors why people abuse substances; the types of substance that are of misused, and signs and symptoms of their effects, and misleading signs and symptoms to look out for.',
          text3:
            'Understanding these signs and symptoms can help you to more quickly and accurately assess whether an individual has issues that are likely to be associated with either substance misuse or mental health issues. If you are in any doubt, it is always recommended to consult a first aider or medical professional if at all possible to do so.',
          text4:
            'The effects of different types of drugs vary widely as well, depending on the strength of the dose, the amount taken, the abuser’s state of mind and health, and whether they have been taken in conjunction with other substances such as alcohol.',
          text5:
            '“All drugs are potentially dangerous, but more so when they are misused or abused for non-medical or recreational purposes.”',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      case 'useofequipment':
        return {
          title: 'SurelyPro Badge',
          text1: 'Use of Equipment.',
          text2:
            'In this module, we look at five key areas – the PPE (personal protective equipment) that might be issued to a security operative, the equipment they might be asked to use, communication devices, body-worn cameras and how to use the radio properly.',
          text3:
            'It may seem obvious that a security operative should know how to use their equipment properly, but we’ve been told by both clients and security operatives how frustrating it is when a team member is not up to scratch when it comes to this area.',
          text4:
            'If you are trying to use the radio to deal with an emergency and someone keeps butting in on the conversation, this can lead to delays in remedial action taking place. Not only that, but poor use of body cams can mean the difference between accumulating usable evidence and perpetrators getting away with it.',
          text5:
            '“We need every team member to be on the ball when it comes to the proper use of equipment to reduce the risk of bad things happening.”',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      case 'vulnerablepeople':
        return {
          title: 'SurelyPro Badge',
          text1: 'Vulnerable People.',
          text2:
            'In this module, we look at three key areas – we define precisely what we mean by a vulnerable person, we look at the different types of vulnerable people we need to be aware of, and we outline the actions and behaviour patterns we need to consider when dealing with a vulnerable person.',
          text3:
            'Dealing with vulnerable people is important for security, legal and compassionate reasons. Security operatives have a duty of care to look out for people who are not able – for whatever reason – to take care of themselves.',
          text4:
            'It’s critically important to be wide awake when these situations arise. Good communication skills are an essential. Listening, responding, thinking, monitoring. Being respectful at all times. Above and beyond everything else, having an empathetic and genuine attitude at all times will often deliver a win-win solution.',
          text5:
            '“I thoroughly recommend the CARE model when dealing with vulnerable people – Comprehend, Assess, Retain and Evaluate.”',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      case 'conflictmanagement':
        return {
          title: 'SurelyPro Badge',
          text1: 'Conflict Management.',
          text2:
            'In this module, we look at six key areas – the importance of good communication, human responses in conflict situations, fight or flight strategies, the four stages of conflict, assessing and reducing risks, and ways to diffuse and dissuade conflict from taking place.',
          text3:
            'Good communication skills are one of the key tools in diminishing the likelihood of situations escalating out of control. That means not just the words you say and how you say them, but mostly your body language to command respect and demonstrate authority in a non-threatening way.',
          text4:
            'You may have to deal with someone whose behavior is unacceptable. Remember that you always have a choice about whether you decide to confront their behaviour or not. If you feel it is necessary to do so, focus on their behaviour, not on them personally.',
          text5:
            '“It is my contention that most conflict situation will either never take place or can be prevented from escalating by a skilled security operative.”',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      case 'disabilityfocus':
        return {
          title: 'SurelyPro Badge',
          text1: 'Disability Focus.',
          text2:
            'In this module, we look at five key areas – defining the word disability, looking at invisible disabilities, considering reasonable adjustments, providing support for people with disabilities, and agencies to go to for further information, help and advice.',
          text3:
            'There are approximately 1.8 billion people globally with a disability of some description, so they make up a fair proportion of the whole population (about 20%). Interestingly enough, a whopping 70% of them won’t return to a venue that treats them poorly in some way.',
          text4:
            'The challenge is made more difficult when the disability is invisible, which is something that we are all more aware of these days than ever before. And whilst you should always try to accommodate their needs, you need to know your way around the reasonable adjustment guidelines too.',
          text5:
            '“The bottom line is that it always works well just to be a nice person, behave well, and treat everyone the same as much as possible.” ',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      case 'customerservice':
        return {
          title: 'SurelyPro Badge',
          text1: 'Customer Service.',
          text2:
            'In this module, we look at three main areas – the importance of effective communication skills, customer expectations and how to manage them, and the different types of behaviour you can choose from in how you react to situations.',
          text3:
            'Customer service is a crucial element of a security operative’s role. Very often, they are the first person who a customer interacts with, and they are often perceived to represent the client’s brand, and the impression they make can last a lifetime.',
          text4:
            'Out of all the six SurelyPro badges, Customer Service was the one voted as most important by the vast majority of clients. The character and personality of a security operative can have a direct impact on customer behaviour.',
          text5:
            '“It’s critically important to hold regular team meetings to learn from each other and to evolve better processes and procedures.”',
          text6:
            'Daniel Riley – Licensed SIA Trainer | Close Protection Officer',
        };
      default:
        return {
          title: '',
          text1: '',
          text2: '',
          text3: '',
          text4: '',
          text5: '',
          text6: '',
        };
    }
  };

  const imageSrc = getImageSrc();
  const textContent = getTextContent();

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='max-w-[1320px] mx-auto flex flex-col items-center gap-8 text-center  '>
        <View className='flex flex-col mt-[66px]'>
          <Image src={imageSrc} className='w-full h-auto lg:h-[580px]' />
          <View className='flex flex-col sm:flex-row justify-between  mt-[31px] px-[12px] xl:px-0  w-full   xl:w-[1000px] mx-auto xl:mx-[10px] gap-[4px] lg:gap-0  xl:ml-[11%]'>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]  mt-[10px] sm:mt-[63px] justify-center items-center  self-stretch rounded-full  ${
                selectedButton === 'surleypro'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'surleypro'
                      ? surelyproicon1active
                      : surelyproicon1
                  }
                  className='w-[13px] h-[13px] mt-[1px] ml-[2px]'
                />
              )}
              onClick={() => handleButtonClick('surleypro')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                SurelyPro
              </Text>
            </Button>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]   mt-[10px] sm:mt-[63px] justify-center items-center  self-stretch rounded-full  ${
                selectedButton === 'useofequipment'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'useofequipment'
                      ? surelyproicon2active
                      : surelyproicon2
                  }
                  className='w-[13px] h-[13px] mt-[1px] ml-[2px]'
                />
              )}
              onClick={() => handleButtonClick('useofequipment')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                Use of Equipment
              </Text>
            </Button>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]   mt-[10px] sm:mt-[63px] justify-center items-center  self-stretch rounded-full  ${
                selectedButton === 'customerservice'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'customerservice'
                      ? surelyproicon3active
                      : surelyproicon3
                  }
                  className='w-[13px] h-[13px] mt-[1px] ml-[2px]'
                />
              )}
              onClick={() => handleButtonClick('customerservice')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                Customer Service
              </Text>
            </Button>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]   mt-[10px] sm:mt-[63px] justify-center items-center  self-stretch rounded-full  ${
                selectedButton === 'substanceawareness'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'substanceawareness'
                      ? surelyproicon4active
                      : surelyproicon4
                  }
                  className='w-[13px] h-[13px] mt-[1px] ml-[2px]'
                />
              )}
              onClick={() => handleButtonClick('substanceawareness')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                Substance Awareness
              </Text>
            </Button>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]   mt-[10px] sm:mt-[63px] justify-center items-center  self-stretch rounded-full  ${
                selectedButton === 'vulnerablepeople'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'vulnerablepeople'
                      ? surelyproicon5active
                      : surelyproicon5
                  }
                  className='w-[13px] h-[13px] mt-[1px] ml-[2px]'
                />
              )}
              onClick={() => handleButtonClick('vulnerablepeople')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                Vulnerable People
              </Text>
            </Button>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]   mt-[10px] sm:mt-[63px] justify-center items-center  self-stretch rounded-full  ${
                selectedButton === 'disabilityfocus'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'disabilityfocus'
                      ? surelyproicon6active
                      : surelyproicon6
                  }
                  className='w-[13px] h-[13px] mt-[1px] ml-[2px]'
                />
              )}
              onClick={() => handleButtonClick('disabilityfocus')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                Disability Focus
              </Text>
            </Button>
            <Button
              className={`flex btn-no-hover w-[auto] h-[28px] gap-[4px] px-[8px] py-[4px]   mt-[10px] sm:mt-[63px] text-[16px] justify-center items-center gap-3 self-stretch rounded-full  ${
                selectedButton === 'conflictmanagement'
                  ? '!bg-[#053D6D] !text-[#DDEFFF]'
                  : '!bg-[#E7E7E7] !text-[#383838]'
              }`}
              icon={() => (
                <img
                  src={
                    selectedButton === 'conflictmanagement'
                      ? surelyproicon7active
                      : surelyproicon7
                  }
                  className='w-[14px] h-[17px] '
                />
              )}
              onClick={() => handleButtonClick('conflictmanagement')}
            >
              <Text className='rubik text-[12px] font-normal leading-[20px]'>
                Conflict Management
              </Text>
            </Button>
          </View>
        </View>

        <View className='flex flex-row justify-between w-full max-w-[645px]'>
          <View className='flex flex-col mt-[30px] sm:mt-[29px] px-[12px] lg:px-0 sm:w-auto  items-start mx-auto sm:mx-0'>
            <Text className='text-left font-rufina-stencil text-[#388DD8] rubik text-[16px] font-medium leading-[20px]'>
              {textContent.title}
            </Text>
            <Text className='text-left text-[#323C58] font-rufina-stencil mt-[10px] text-[48px] font-normal leading-[55px] sm:leading-[56px]'>
              {textContent.text1}
            </Text>
            <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[20px]'>
              {textContent.text2}
            </Text>
            <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[25px]'>
              {textContent.text3}
            </Text>
            <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[25px]'>
              {textContent.text4}
            </Text>

            <div className='items-start bg-[#388DD8] w-[160px] h-[4px] mt-[42px]' />
          </View>
        </View>
        <div className='sm:w-[645px] px-[12px] lg:px-0 mx-auto sm:mx-0'>
          <Text className='text-left text-[#323C58] rubik sm:text-[24px] text-[21px] font-normal italic leading-[32px] '>
            {textContent.text5}
          </Text>
          <Text className='text-left font-rufina-stencil text-[#323C58] rubik text-[16px] font-medium leading-[24px] sm:mt-[11px] mt-7'>
            {textContent.text6}
          </Text>
        </div>
        <Image src={surleyproperson} className='w-[645.438px] h-[360.646px] ' />
      </View>
      <div
        className='bg-left-top flex mt-[62px] justify-between w-full h-[480px] md:w-full md:h-[310px] mx-0 md:mx-auto bg-cover bg-no-repeat bg-center sm:mt-[91.84px]'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Footer />
    </View>
  );
};

export default SurleyProPage;
