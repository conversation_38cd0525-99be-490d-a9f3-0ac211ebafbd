import { useAuthContext } from 'src/context/AuthContext';
import getNameInitials from '../../../../../utils/getNameInitials';
import MessageStatus from './MessageStatus';
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useChatContext } from 'src/context/ChatContext';

const ContractMessage = ({ message }) => {
  const { user, isClient } = useAuthContext();
  const { currentChat } = useChatContext();

  const invitationMessage = user?.profile?.id == message?.receiver_id ? 'has invited you to job' : 'was invited to job';
  const imageShown =
    message?.sender_id === user?.profile?.id ? user?.profile?.profile_photo : message.sender?.profile_photo;
  const nameShown = currentChat?.sender_name;
  const jobName = currentChat?.job?.post_name;

  if (message?.type === 'operator_accepted_contract') {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#CDEDD5] pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
            {`Contract accepted`}
          </h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            {nameShown} accepted contract {jobName}
          </p>
        </div>
      </div>
    );
  }
};

export default ContractMessage;
