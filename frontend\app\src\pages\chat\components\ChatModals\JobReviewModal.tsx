// @ts-nocheck
import React, { ChangeEvent, useRef, useState } from 'react';
import { Text, View, Button, Divider, Modal, Checkbox, useToast, Image, Icon } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import { jobReview } from 'src/services/contracts';
import surleyicon from '../../../../assets/icons/surleyicon/surleyicon.png';
import { IdVerified } from 'src/assets/icons';

interface JobReviewModalProps {
  active: boolean;
  deactivate: () => void;
  chat: any;
}

const JobReviewModal: React.FC<JobReviewModalProps> = ({ active, deactivate, chat }) => {
  const toast = useToast();
  const [details, setDetails] = useState<string>('');
  const [shareWithSurely, setShareWithSurely] = useState<any>(true);
  const [shareWithClient, setShareWithClient] = useState<any>(false);

  const lastname = chat?.receiver_name?.split(' ').pop()?.charAt(0).toUpperCase() + chat?.receiver_name?.split(' ').pop()?.slice(1);
  const firstname = chat?.receiver_name?.split(' ').shift()?.charAt(0).toUpperCase() + chat?.receiver_name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;
  const initialNameFirstCharAt = (firstname?.[0] || '') + (lastname?.[0] || '');

  const handleDetailsChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setDetails(event.target.value);
  };

  const getImageSrc = (images: any) => {
    const baseUrl = 'https://app.surelysecurity.com/storage/';

    if (images && !images.startsWith(baseUrl)) {
      return baseUrl + images;
    }

    return images;
  };

  const handleJobReview = async () => {
    const operatorReviewData: any = {
      details,
      shareWithSurely: shareWithSurely ? 1 : 0,
      shareWithClient: shareWithClient ? 1 : 0,
    };

    try {
      const response = await jobReview({
        id: chat?.contract?.id, // change to contract id
        input: operatorReviewData,
      });
      
      if (response?.error) {
        toast.show({
          title: 'Success!',
          text: 'Your review has been posted successfully',
          startSlot: <Image key='successImage' src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        deactivate();
      } else {
        toast.show({
          title: 'Oops!',
          text: 'There was an issue posting your review. Please try again later',
          startSlot: <Image key='errorImage' src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }
    } catch (error) {
      console.error('Error post review:', error);
    }
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View className='gap-[16px]'>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Job review</Text>
        </View>

        <View className='mt-[16px] flex flex-col'>
          <View className='flex flex-row gap-[20px]'>
            <View className='flex h-[64px] w-[64px]  items-center justify-center  rounded-full bg-[#C7CDDB]'>
              <Text className='rubik text-[18px] font-medium leading-[20px] text-[#323C58]'>{initialNameFirstCharAt}</Text>
            </View>

            <View className='mt-[5px] flex flex-col gap-[4px]'>
              <Text className=' rubik text-[15px] font-normal leading-[20px] text-[#323C58]'>{initialName}</Text>
              <Text className=' rubik text-[15px] font-medium leading-[20px] text-[#323C58]'>#213245</Text>
            </View>
          </View>

          <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Share your job experience</Text>
          <div className='flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px] '>
            <textarea
              name='job_description'
              placeholder='Tell us more about the client and the job task you’ve completed...'
              className=' border-none  bg-transparent outline-none'
              value={details}
              onChange={handleDetailsChange}
              maxLength={500}
              rows={9}
              style={{
                wordWrap: 'break-word',
                overflowWrap: 'break-word',
                resize: 'none',
              }}
            />

            <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{500 - (details?.length || 0)} characters left</p>
          </div>

          <View className='mt-[16px] flex flex-row gap-[8px]'>
            <label className='relative flex h-fit cursor-not-allowed items-center'>
              <input type='checkbox' id='siaLicence' name='siaLicence' checked className='sr-only !border-0' disabled />
              <span className='flex items-center justify-center rounded bg-[#EBEDF2]'>
                <span className='material-icons-outlined inline-block p-0.5 text-base leading-4 text-[#C7CDDB]'>done</span>
              </span>
            </label>
            <Text className='rubik text-[14px] font-normal leading-[20px] text-[#1A1A1A]'>Share with Surely</Text>
          </View>
          <View className='mt-[16px] flex flex-row gap-[8px]'>
            <Checkbox name='role' value={shareWithClient} className='mb-2 sm:mb-0 sm:w-[33%]' onChange={() => setShareWithClient(!shareWithClient)}>
              <Text className='rubik whitespace-nowrap text-[14px] font-normal leading-[20px] text-[#1A1A1A]'>Share with Client</Text>
            </Checkbox>
          </View>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            onClick={() => {
              handleJobReview();
              deactivate();
            }}
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default JobReviewModal;
