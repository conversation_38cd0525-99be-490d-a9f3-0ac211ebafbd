// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion, Card } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import cctvOperatorQuestions, {
  CctvOperatorJobsQuestionsType,
} from '../../../store/dummydata/SecurityJobsQuestionsDummyData/CctvOperatorJobsQuestionsDummyData';
import BodyguardButton from '../SecurityJobsButtons/BodyguardButton';
import CctvOperatorButton from '../SecurityJobsButtons/CctvOperatorButton';
import CloseProtectionButton from '../SecurityJobsButtons/CloseProtectionButton';
import DoorSupervisorButton from '../SecurityJobsButtons/DoorSupervisorButton';
import EventSecurityButton from '../SecurityJobsButtons/EventSecurityButton';
import PrivateSecurityButton from '../SecurityJobsButtons/PrivateSecurityButton';
import SecurityGuardButton from '../SecurityJobsButtons/SecurityGuardButton';
import cctvjobs1 from '../../../assets/images/securityjobs/cctvjobs1.png';
import cctvjobs2 from '../../../assets/images/securityjobs/cctvjobs2.png';
import cctvjobs3 from '../../../assets/images/securityjobs/cctvjobs3.png';
import cctvjobs4 from '../../../assets/images/securityjobs/cctvjobs4.png';

const CctvOperatorJobs: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;
  const [activeId, setActiveId] = useState<number | null>(null);

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex lg:flex-row flex-col w-full max-w-[1320px] mx-auto items-center text-center py-[64px] '>
          <img src={cctvjobs1} className='lg:w-[675px] w-full xl:h-[472px] ' />
          <View className='flex flex-col lg:w-[675px] xl:h-[472px]  p-[32px] xl:p-[50px] xl:px-[50px] gap-[12px] px-3  bg-[#FFFF]  '>
            <Text className='text-left font-rufina-stencil text-[30px] lg:text-[30px]  xl:text-[48px] font-normal xl:leading-[56px] text-[#323C58]'>
              Find CCTV operator jobs in London and the UK
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px]'>
              Connect with clients and contractors looking for qualified CCTV operators in London and across the UK just
              like you. Our innovative platform puts you back in control of your CCV operator career. Set your rates.
              Set your hours. And find the work you love on your terms. If you’re looking for your next CCTV operator
              role in London or the UK, sign up with Surely Security today.
            </Text>
            <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1100px]  sm:gap-[64px] mx-auto py-[64px] '>
          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[32px] gap-10 mt-[20px] sm:mt-[0] sm:mb-0 mb-8'>
            <View className='flex flex-col gap-3 justify-center w-full  xl:w-[532px] items-start xl:px-0 px-3  sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil lg:text-[30px]  xl:text-[48px] text-[30px] font-normal xl:leading-[56px]'>
                Freelance CCTV operator jobs in London
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                With our unique security job platform, you’re always in control. To start talking to professional
                clients and reputable agents today, simply sign up to our platform and create your profile. Share your
                experience and credentials and search for your next role. Clients and contractors can also find you,
                taking all the work out of finding work. We connect SIA-licenced CCTV operators with reputable employers
                and the best agents. Everything is managed within our digital ecosystem, from finding work to
                negotiating rates. We’ll even make sure you get paid on time, so you aren’t left out of pocket or waste
                time chasing payments.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img src={cctvjobs2} className='  sm:mt-0 w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1' />
          </View>

          <View className='flex flex-col-reverse lg:flex-row max-w-[1100px] justify-between sm:gap-[32x] gap-10   sm:mb-0 mb-8 '>
            <img src={cctvjobs3} className='w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1' />
            <View className='flex flex-col gap-3 justify-center w-full xl:w-[532px] items-start px-3 xl:px-0 sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[30px]  xl:text-[48px]  font-normal xl:leading-[56px]'>
                Find your next CCTV operator job today
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Trained CCTV operators are in big demand in London and throughout the UK. Put your training to good use
                and find your next opportunity with Surely Security. With our platform, you can find work, negotiate
                contracts and get paid quickly and accurately. We cut out all the unnecessary middleman to improve the
                experience for those who want the best. Start your search for your next CCTV operator role today.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
          </View>

          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[32px] gap-10  '>
            <View className='flex flex-col gap-3 justify-center w-full xl:w-[532px] items-start px-3 xl:px-0  sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[30px]  xl:text-[48px] font-normal xl:leading-[56px]'>
                Work when, where and how you want
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Flexibility is at the heart of everything we do. As a self-employed CCTV operator, you should have the
                freedom to work on your terms, but this is rarely the case – particularly when multiple middlemen get
                involved. We help CCTV controller professionals negotiate directly with client and through professional
                agents to find contracts that suit their needs. So, whether you’re looking for full-time, part-time or
                weekend work, we will match you with your ideal job.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={cctvjobs4}
              className=' mt-[20px] sm:mt-0 w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1'
            />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center xl:px-0 px-3 '>
          <Text className='!text-[#323C58] font-rufina-stencil sm:px-0 px-6  sm:text-center text-left  text-[32px] text-[30px] font-normal sm:leading-[40px] leading-[56px] items-center mt-[64px]  sm:ml-[0px]'>
            Frequently asked questions about CCTV operator jobs
          </Text>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-[36px] mb-[65px]'>
            <div className='flex flex-col gap-4 '>
              {cctvOperatorQuestions.map((item: CctvOperatorJobsQuestionsType) => {
                if (item.id % 2 === 1) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                            endIcon={() => (
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='22'
                                height='22'
                                viewBox='0 0 22 22'
                                fill='none'
                              >
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                {item.question}
                              </p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' xl:w-[600px] flex flex-row '>
                          <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]  '>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
            <div className='flex flex-col gap-4 '>
              {cctvOperatorQuestions.map((item: CctvOperatorJobsQuestionsType) => {
                if (item.id % 2 === 0) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                            endIcon={() => (
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='22'
                                height='22'
                                viewBox='0 0 22 22'
                                fill='none'
                              >
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                {item.question}
                              </p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className='w-[370px] sm:w-[600px] '>
                          <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]'>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
          </div>
        </View>
      </View>
      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1320px] mx-auto gap-[24px] xl:px-0 px-3 '>
          <View className='flex flex-col sm:flex-row justify-between lg:mt-[64px] gap-[24px]'>
            <SecurityGuardButton />
            <BodyguardButton />
            <EventSecurityButton />
          </View>
          <View className='flex flex-col sm:flex-row justify-between gap-[24px] mb-[30px] lg:mb-[90px]'>
            <DoorSupervisorButton />
            <PrivateSecurityButton />
            <CloseProtectionButton />
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <Footer />
    </View>
  );
};

export default CctvOperatorJobs;
