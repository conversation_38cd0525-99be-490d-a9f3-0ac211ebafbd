// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, Button, View } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { AppContext } from '../../../../context/AppContext';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import moment from 'moment';

const OperatorRelevantQualification: React.FC = () => {
  const navigate = useNavigate();
  const { qualifications } = useContext(AppContext);

  return (
    <Card className='xl:w-full  xl:mx-auto p-6  xl:max-w-[424px] '>
      <View className='flex items-center justify-between'>
        <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>
          Relevant qualifications
        </Text>
        <Button
          icon={() => (
            <span className='material-icons-outlined text-white text-base flex items-center justify-center'>edit</span>
          )}
          onClick={() =>
            navigate('/operator-settings-employment', {
              state: {
                activeTab: '1',
              },
            })
          }
          className='!w-[32px] !h-[32px] !rounded-full border border-[#323C58] !bg-[#323C58]  '
        ></Button>
      </View>
      <View className='mt-[20px] flex flex-col gap-5'>
        {qualifications.length === 0 ? (
          <NoDataProfile />
        ) : (
          qualifications.map(({ id, qualificationTitle, qualificationDescription, expiryDate }: any) => {
            let formattedExpiryDate;
            expiryDate ? (formattedExpiryDate = moment(expiryDate).format('DD.MM.YYYY')) : (formattedExpiryDate = null);

            return (
              <View key={id} className='flex items-center gap-3  px-2'>
                <span className='material-icons text-xs bg-gradient-to-t from-blue-200 to-blue-900 text-transparent bg-clip-text mb-[10px]'>
                  fiber_manual_record
                </span>
                <View className='flex flex-col '>
                  <Text className='  rubik font-normal leading-5 text-[15px] rubik !text-[#1A1A1A]'>
                    {qualificationTitle}
                  </Text>
                  <Text className=' text-[14px] rubik font-normal leading-5 rubik !text-[#383838]'>
                    {qualificationDescription}
                  </Text>
                  <Text className=' text-[15px] rubik font-normal leading-6 rubik !text-[#323C58]'>
                    Expiry date: {formattedExpiryDate}
                  </Text>
                </View>
              </View>
            );
          })
        )}
      </View>
    </Card>
  );
};

export default OperatorRelevantQualification;
