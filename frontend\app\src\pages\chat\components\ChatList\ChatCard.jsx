import { is } from 'date-fns/locale';
import moment from 'moment';
import { useParams } from 'react-router-dom';
import { useAuthContext } from 'src/context/AuthContext';
import getNameInitials from 'src/utils/getNameInitials';
import getValidImageUrl from 'src/utils/getValidImageUrl';
import { getUserData } from 'src/utils/userStorage';
import { useChatContext } from 'src/context/ChatContext';

const ChatCard = ({ chat, onClick }) => {
  const params = useParams();
  const { userData } = useChatContext();
  const imageShown = chat?.receiver_id === userData?.id ? chat?.sender?.profile_photo : chat?.receiver?.profile_photo;
  const nameShown = chat?.receiver_id === userData?.id ? chat?.sender_name : chat?.receiver_name;
  const lastMess = chat?.last_message || '';
  const lastMessTime = moment(chat?.updated_at).format('ddd D MMM');
  const jobOrContractName = chat?.type === 'no_contract' ? chat?.contract?.id ? '- #' + chat?.contract?.id : '' : ' - ' + chat?.job?.post_name;

  const isOnline = false
  const hasUnreadMessages = chat.messages?.some(msg => 
    msg.receiver_id === userData?.id && !msg.read
  );

  return (
    <div
      onClick={onClick}
      className={
        'flex items-center gap-4 hover:bg-[#F4F5F7] active:bg-[#F4F5F7] rounded-lg px-2 py-3 cursor-pointer ' +
        (params?.id == chat?.id ? 'bg-[#F4F5F7]' : '')
      }
    >
      <div className='shrink-0 w-14 h-14'>
        {imageShown ? (
          <img
            className={`w-full h-full border-2 rounded-full bg-[#C7CDDB] ${
              isOnline ? 'border-[rgba(10,158,44,0.80)]' : 'border-transparent'
            }`}
            src={getValidImageUrl(imageShown)}
          />
        ) : (
          <div
            className={`w-full h-full border-2  rounded-full bg-[#C7CDDB] ${
              isOnline ? 'border-[rgba(10,158,44,0.80)]' : 'border-transparent'
            } flex items-center justify-center`}
            src=''
          >
            {getNameInitials(nameShown?.toUpperCase())}
          </div>
        )}
      </div>
      <div className='w-full flex flex-col text-left gap-[3px]'>
        <h2 className='rubik text-[#1A1A1A] text-[16px] font-medium leading-5 overflow-ellipsis'>{`${nameShown} ${jobOrContractName}`}</h2>
        <span className='rubik text-[#444B5F] text-[13px]'>{lastMessTime}</span>
        <p className='rubik text-[14px] text-[#383838] leading-5 overflow-ellipsis'>{lastMess}</p>
        {hasUnreadMessages && <span className='w-fit ml-auto text-xs text-[#0B80E7] material-icons'>circle</span>}
      </div>
    </div>
  );
};

export default ChatCard;
