// @ts-nocheck
import { lazy } from 'react';
import { Modal } from 'reshaped';
import Loadable from '../../router/Loadable';
import { useModalAction, useModalState } from 'src/context/ModalContext';
import OperativesFilter from '../filters/OperativesFilter/OperativesFilter';
import RegisterForm from 'src/components/auth/RegisterForm';
import LoginModal from 'src/components/modal/LoginModal';
import GuestViewModal from 'src/components/modal/GuestViewModal';
import GuestUpgradeModal from 'src/components/modal/GuestUpgradeModal';

const LoginView = Loadable(
  lazy(() => import('src/components/modal/LoginModal')),
);
const OtpLoginView = Loadable(
  lazy(() => import('src/components/auth/OtpLoginForm')),
);
const RegisterView = Loadable(
  lazy(() => import('src/components/auth/RegisterForm')),
);

const ManagedModal = () => {
  const { isOpen, view, data } = useModalState();
  const { closeModal } = useModalAction();

  return (
    <Modal active={isOpen} onClose={() => !data?.nonClosing ? closeModal() : null}>
      {view === 'LOGIN' && <LoginModal />}
      {view === 'REGISTER' && (
        <RegisterForm userType={data?.userType ?? null} />
      )}
      {view === 'LOGIN_OTP' && <OtpLoginView />}
      {view === 'FILTERS_CLIENT' && <OperativesFilter />}
      {view === 'GUEST_VIEW' && <GuestViewModal />}
      {view === 'GUEST_UPGRADE' && <GuestUpgradeModal />}
    </Modal>
  );
};

export default ManagedModal;
