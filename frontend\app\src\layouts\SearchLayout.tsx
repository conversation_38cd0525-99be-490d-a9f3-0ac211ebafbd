import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';
import OperatorSearchBar from 'src/components/OperatorSearchBar/OperatorSearchBar';

interface Props {
  children?: React.ReactNode;
  title?: string;
}

export const SearchLayout = ({ children, title }: Props): JSX.Element => {
  return (
    <Container padding={0} className="relative w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />

      <main className='align-center sm:mt-[80px] mt-[32px] w-full max-w-[1320px] mx-auto text-center'>
        <section className='w-full sm:mt-[61px] px-[12px] xl:px-0 flex flex-col gap-[16px]'>
          <p className='text-center lg:text-start font-rufina-stencil text-2xl font-normal leading-10 text-left mb-0 sm:mb-[38px] '>
            {title}
          </p>

          <div className="lg:sticky lg:top-[90px] lg:z-50 lg:min-h-[56px]">
            <OperatorSearchBar />
          </div>
          <article className='flex flex-row gap-5 flex-wrap justify-center mt-[10px] sm:mt-[64px]'>{children}</article>
        </section>
      </main>
    </Container>
  );
};

export default SearchLayout;
