// @ts-nocheck
import { useContext, useEffect } from "react"
import { useNavigate, useSearchParams } from "react-router-dom";
import { AuthContext } from "src/context/AuthContext";
import { loginUser } from "src/services/user";
import { Image, useToast } from "reshaped";
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

export const SuccessLinkedinPage = () => {
  const { authenticateUser } = useContext(AuthContext);
  const [searchParams1, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const toast = useToast()

  useEffect(() => {
    if (localStorage.getItem('isLinkedInReady') === 'true') {
        localStorage.removeItem('isLinkedInReady')
        return;
    }
    const linkedInCode = String(searchParams1.get('code') ?? "")
    if (linkedInCode) {
      localStorage.setItem('linkedIn_code', linkedInCode)
      localStorage.setItem('isLinkedInReady', "true")
      close() 
    }
  
  }, []);
   
    const isDataUnavailable = (userData: any) => {
      return (
        userData.user.profile_title === null ||
        userData.user.profile_description === null ||
        userData.user.profile_description === undefined ||
        userData.user.profile_photo === null ||
        userData.user.profile_video === null ||
        userData.languages.length === 0 ||
        userData.user.profile_title === undefined ||
        userData.user.profile_photo === undefined ||
        userData.user.profile_video === undefined ||
        userData.employments.length === 0 ||
        userData.qualifications.length === 0
      );
    };

    useEffect(() => {
      const serchParam = String(searchParams1.get('code') ?? "")

      // const accessToken = searchParams.get('access_token');
      // if (accessToken && accessToken !== '') {
      //   localStorage.setItem('google_token', accessToken);
      // } else {
      //   navigate('/');
      // }
      const linkedin_token = localStorage.getItem('linkedIn_code')
      if(linkedin_token && !serchParam){
      loginUser({ 
        linkedin_token,
        redirect_uri: import.meta.env.VITE_REACT_APP_LINKEDIN_LOGIN_REDIRECT_URL,
        email: '',
        password: '',
        password_confirmation: '',
        loginType: 1,
        platform: 3,
        appVersion: "1.0",
        firebaseToken: 'test',
        phone: '***********',
        account_type: '', 
      }).then((loginResponse) => {
        if (loginResponse?.token) {
          authenticateUser(loginResponse);
          if (loginResponse?.data.user.account_type == '2') {
            navigate('/client-dashboard');
          } else {
            if (isDataUnavailable(loginResponse.data)) {
              navigate('/my-profile');
            } else {
              navigate('/search-jobs');
            }
          }
        } else{
          navigate("/")
          toast.show({
            title: 'Error.',
            text: 'User not found!',
            startSlot: <Image key='error' src={surleyicon} className='w-[30px] h-[30px]' />,
          });
        }
        
      })
    }
    }, [])


  return (<></>)
}