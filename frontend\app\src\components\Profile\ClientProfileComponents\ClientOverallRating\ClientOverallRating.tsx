// @ts-nocheck
import React from 'react';
import { Card, Text, View, Divider } from 'reshaped';
import Coments from '../ClientReview/ClientOperatorReviewSection';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';
import NoRatingData from 'src/components/NoData/NoRatingData';

interface ClientOverallRatingProps {
  oper: any;
}

const ClientOverallRating: React.FC<ClientOverallRatingProps> = ({ oper }) => {
  const { reviews, overall_ratings } = oper;

  return (
    <Card className='mt-[24px] h-[auto] w-full p-[24px] xl:w-[760px]'>
      <View className='flex flex-col items-start justify-between'>
        <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>Overall rating</Text>
      </View>
      {overall_ratings !== null || overall_ratings !== null ? (
        <>
          <View className='mt-[20px] flex items-center gap-2'>
            {overall_ratings ? (
              <>
                {[...Array(5)].map((_, index) => (
                  <span key={index} className={`material-icons ${index < Math.round(overall_ratings?.value) ? 'text-[#F4BF00]' : 'text-gray-400'}`}>
                    star
                  </span>
                ))}
                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>{overall_ratings?.value}</Text>
              </>
            ) : (
              <>
                <span className='material-icons text-[#EBEDF2]'>star</span>
                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#EBEDF2]'>No rating</Text>
              </>
            )}
          </View>
          <Divider className='mt-[20px] h-[1px] w-full' />
          <View className='mt-[20px] grid grid-cols-1 gap-4 md:grid-cols-2'>
            {reviews?.[0]?.other_ratings &&
              Object.entries(reviews[0].other_ratings).map(([section, star], index) => {
                const formattedSection = section.charAt(0).toUpperCase() + section.slice(1);
                const displaySection = formattedSection.replace(/_/g, ' ');
                return (
                  <View key={index} className='mt-[20px] flex flex-col items-start'>
                    <Text className='text-neutral xl:text-caption2 xl:font-medium xl:leading-4'>{displaySection}</Text>
                    <View className='mt-[16px] flex w-full gap-1'>
                      {[...Array(5)].map((_, starIndex) => (
                        <div
                          key={starIndex}
                          className={`h-[5px] w-full md:w-[54px] ${starIndex < Math.round(Number(star)) ? 'bg-[#0B80E7]' : 'bg-gray-400'} rounded-lg`}
                        ></div>
                      ))}
                    </View>
                    <Text className='text-neutral xl:text-caption2 mt-[6px] text-right xl:font-medium xl:leading-4'>{Number(star).toFixed(1)}</Text>
                  </View>
                );
              })}
          </View>
          <Divider className='mt-[20px] h-[1px] w-full' />
          <Coments oper={oper} />
        </>
      ) : (
        <div className='w-[100%] h-[100%]'>
        <NoRatingData />
        </div>
      )}
    </Card>
  );
};

export default ClientOverallRating;
