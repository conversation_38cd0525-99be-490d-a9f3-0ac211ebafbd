import dummyOperator from "src/store/dummydata/OperatorsDummyData";

export type Job = {
  id: number;
  date: string;
  title: string;
  proposals: number;
  status: string;
  applied_operators: any;
};
export type AppliedOperator = {
  operatorId: number;
  firstName: string;
  lastName: string;
  city: string;
  photo: string;
  jobsComplete: number;
  rate: number;
  grades: string[];
  securityCode: string;
  isFavorite: boolean;
  bio: {
    title: string;
    body: string;
  };
  industrySectors: string[];
};

const ClientJobsDummyData: Job[] = [
  {
    id: 1,
    date: 'Wed 7 June 2023',
    title: 'Goodwood Festival',
    proposals: 12,
    status: "In progress",
    applied_operators:  dummyOperator.filter((operator) =>
    [1,2,3,5,8,9].includes(operator.operatorId)
  ),
  },
  {
    id: 2,
    date: 'Mon 6 June 2023',
    title: 'Office building. I need someone for this simple job.',
    proposals: 8,
    status: "In progress",
    applied_operators:  dummyOperator.filter((operator) =>
    [1, 2].includes(operator.operatorId)
  ),
  },
  {
    id: 3,
    date: 'Wed 7 June 2023',
    title:
      'Zoo security staff. Good payment, flexible hours. Professionals only.',
    proposals: 10,
    status: "In progress",
    applied_operators:  dummyOperator.filter((operator) =>
    [1, 6, 10 ].includes(operator.operatorId)
  ),
  },
  {
    id: 4,
    date: 'Wed 7 June 2023',
    title: 'Goodwood Festival',
    proposals: 12,
    status: "In progress",
    applied_operators: [],
  },
  {
    id: 5,
    date: 'Mon 6 June 2023',
    title: 'Office building. I need someone for this simple job.',
    proposals: 6,
    status: "In progress",
    applied_operators:  dummyOperator.filter((operator) =>
    [4, 5, 8].includes(operator.operatorId)
  ),
  },
  {
    id: 6,
    date: 'Wed 7 June 2023',
    title:
      'Zoo security staff. Good payment, flexible hours. Professionals only.',
    proposals: 3,
    status: "In progress",
    applied_operators:  dummyOperator.filter((operator) =>
    [3, 7, 9].includes(operator.operatorId)
  ),
  },
];

export default ClientJobsDummyData;
