<?php

namespace App\Charts;

use ArielMejiaDev\LarapexCharts\LarapexChart;

class ContractsChart
{
    protected $chart;

    public function __construct(LarapexChart $chart)
    {
        $this->chart = $chart;
    }

    public function build($contracts)
    {
        return $this->chart->areaChart()
            ->setTitle('Total Contracts')
            ->addData('Contracts', array_column($contracts, 'earnings'))
            ->setColors(['#323C58'])
            ->setXAxis(['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December',])
            ->setHeight(300);
    }
}
