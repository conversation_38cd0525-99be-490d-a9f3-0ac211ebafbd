// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Text, Button, Divider, Breadcrumbs } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { Link, useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Subscribe from '../Subscribe/Subscribe';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import homebreadcrumbsicon from '../../assets/icons/homebreadcrumbsicon/homebreadcrumbsicon.svg';
import { getBlogs } from 'src/services/blogs';
import moment from 'moment';

const SecurityMattersArchive: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const navigate = useNavigate();

  const [allBlogs, setAllBlogs] = useState<any>();
  const [showAll, setShowAll] = useState(false);

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  useEffect(() => {
    getBlogs().then((data: any) => {
      setAllBlogs(data.blogs);
    });
  }, []);

  const truncateText = (htmlContent: any, maxLength: any) => {
    const strippedContent = htmlContent.replace(/(<([^>]+)>)/gi, '');
    if (strippedContent?.length > maxLength) {
      return `${strippedContent.substring(0, maxLength)}...`;
    } else {
      return strippedContent;
    }
  };

  const baseURL = 'https://app.surelysecurity.com/storage/';

  return (
    <View className='mt-[-90px] w-full overflow-x-hidden  bg-[#F8F8F8]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='mx-auto flex w-full max-w-[1320px] flex-col '>
          <Breadcrumbs className='mt-[30px] sm:mt-[60px]'>
            <Breadcrumbs.Item onClick={() => navigate('/')}>
              <div className='flex flex-row gap-[4px]'>
                <img src={homebreadcrumbsicon} />
                <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Homepage</Text>
              </div>
            </Breadcrumbs.Item>
            <Breadcrumbs.Item onClick={() => {}}>
              <Text className='rubik text-[16px] font-medium text-[#1A1A1A]'>Security Matters</Text>
            </Breadcrumbs.Item>
          </Breadcrumbs>
          <View className='mx-auto mt-[68px] flex flex-col items-center justify-center px-[12px] lg:px-0 xl:w-[620px]'>
            <Text className='font-rufina-stencil text-start text-[38px]  font-normal leading-[56px] text-[#323C58] sm:text-center sm:text-[48px]'>
              Security Matters
            </Text>
            <Text className=' rubik  mt-[12px] text-start text-[16px] font-normal leading-[24px] text-[#323C58] sm:w-auto sm:text-center'>
              Please take a look at our collection of articles written by many Surely members, who outline their views on what we need to do make the
              security marketplace better for both clients and operatives. We also invite third parties to share their knowledge and expertise with us
              too. 
            </Text>
            <div className='mx-auto my-4 h-[4px] w-[200px] items-center bg-[#388DD8]' />
          </View>
        </View>
        <View className='mx-auto mb-10 mt-[48px] grid w-full max-w-[1320px] grid-cols-1 items-center gap-7 bg-[#F8F8F8]  text-center md:grid-cols-2  lg:grid-cols-3 '>
          {allBlogs
            ?.sort((a: any, b: any) => new Date(b.created_at) - new Date(a.created_at))
            .slice(0, showAll ? allBlogs?.length : 9)
            .map((item: any) => {
              return (
                <View
                  key={item.id}
                  className='mx-auto mb-[35px] flex flex-shrink-0 flex-col items-start px-[12px] sm:mx-0 sm:mb-[0px]  sm:w-[424px] xl:px-0'
                >
                  <img
                    src={item.img_path.startsWith(baseURL) ? item.img_path : baseURL + item.img_path}
                    className='rounded-br-0 rounded-bl-0 w-full rounded-tl-[8px] rounded-tr-[8px]'
                  />
                  <View className='flex flex-col items-start gap-[16px] self-stretch rounded-bl-[8px] rounded-br-[8px]  rounded-tl-[0px] rounded-tr-[0px] bg-[#FFFF] p-[24px] shadow-md'>
                    <Text className='rubik text-left text-[14px] font-normal leading-[20px] text-[#383838]'>
                      {moment(item.created_at).format('DD MMMM YYYY')}
                    </Text>
                    <Text className='rubik mt-[12px] h-[30px] text-left text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>{item.title}</Text>
                    <Divider className='mt-[16px] h-[1px] w-full'></Divider>
                    <Text className='rubik mt-[12px] text-left text-[14px] font-normal leading-[20px] text-[#383838]'>
                      {item.content && <div dangerouslySetInnerHTML={{ __html: truncateText(item.content, 150) }} />}
                    </Text>

                    <button
                      className='btn-no-hover rubik text-[16px] font-[500] leading-[24px] text-[#0B80E7]'
                      onClick={() => navigate(`/security-matters-post/${item.id}`, { state: { itemData: item, allBlogs: allBlogs } })}
                    >
                      Read more
                    </button>
                  </View>
                </View>
              );
            })}
        </View>
        <div className='mt-2 bg-[#F8F8F8]'>
          {allBlogs?.length > 9 && (
            <div className=' mt-[40px] flex  justify-center '>
              <Button className='border-5 mb-[50px] h-[40px] w-[140px] border-[#000] !bg-[#fff]' onClick={toggleShowAll}>
                {showAll ? 'Load Less' : 'Load More'}
              </Button>
            </div>
          )}
        </div>
      </View>
      <div
        className='flex h-[480px] w-full justify-between bg-cover bg-center bg-left-top bg-no-repeat   md:mx-auto md:h-[312px] md:w-full'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='mx-auto my-auto flex flex-col'>
          <Text className='font-rufina-stencil text-center text-[48px] font-normal leading-[56px] text-[#FFF]'>Don’t miss the opportunity.</Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='mx-auto my-auto mt-[32px] flex flex-col sm:flex-row'>
              <Button
                className='rubik mr-[24px] flex h-[56px] w-full items-center justify-center gap-2 rounded-[8px]  !bg-[#fff] p-4 text-[17px] sm:w-[271px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='rubik mt-[10px] flex h-[56px] w-full items-center justify-center gap-2 rounded-[8px] border-[0px] !bg-[#0B80E7] p-4 text-[17px]  !text-[#ffff] sm:mt-0 sm:w-[166px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default SecurityMattersArchive;
