import { FunctionComponent, ReactNode } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAuthContext } from "src/context/AuthContext.tsx";

interface NonGuestGuardProps {
  children: ReactNode;
}

const NonGuestGuard: FunctionComponent<NonGuestGuardProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuthContext();

  const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
  const isGuest = accountType === '5';

  if (isGuest) {
    navigate('/client-settings-general');

    return <Navigate to="/" state={{ from: location }} />;
  }

  return <>{children}</>;
};

export default NonGuestGuard;