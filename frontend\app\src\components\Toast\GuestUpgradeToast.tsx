import { View, Text, Button } from 'reshaped';

interface GuestUpgradeToastProps {
  onClose: () => void;
  onNavigate: () => void;
}

const GuestUpgradeToast = ({ onClose, onNavigate }: GuestUpgradeToastProps) => {
  return (
    <View className=''>
      <button
        className='ml-auto flex items-center justify-end border border-transparent bg-transparent hover:border-transparent p-2'
        onClick={onClose}
      >
        <span className='material-icons text-500 align-middle text-[#fff]'>close</span>
      </button>
      <View className='mt-[-12px]'>
        {/* <Image src={surleyicon} alt='Surely Icon' className='h-[64px] w-[64px]' /> */}
        <View className='w-full text-left'>
          <Text className='rubik text-[14px] font-bold leading-[20px] text-[#EFF0F1]'>Become a client</Text>
          <Text className='rubik mt-1 text-[14px] font-normal leading-[20px] text-[#EFF0F1]'>
            Complete your profile details to upgrade your account and get full access to all client features.
          </Text>
          <Button
            className='border-neutral bg-background-base mt-3 flex w-fit min-w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
            onClick={() => {
              onClose();
              onNavigate();
            }}
          >
            Complete Profile
          </Button>
        </View>
      </View>
    </View>
  );
};

export default GuestUpgradeToast;