// @ts-nocheck
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { useModalAction, useModalState } from 'src/context/ModalContext';
import { useRegistrationContext } from 'src/context/RegistrationContext';
import linkedin from '../../assets/icons/socialicon/linkedin.svg';
import google from '../../assets/icons/socialicon/google.png';
import { useState, useEffect } from 'react';
import { LinkedIn } from 'react-linkedin-login-oauth2';
import { Text, View, useToast, Image } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { checkTakenEmail } from '../../services/user';

type FormDataType = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  agreeToTerms: boolean;
};

const RegisterForm = ({ toOtpLogin, userType }: { toOtpLogin?: Function; userType: string | null }) => {
  const navigate = useNavigate();
  const { closeModal } = useModalAction();
  const { setSharedRegisterData } = useRegistrationContext();
  const location = useLocation();
  const toast = useToast();
  const nonClosing = localStorage?.getItem('wpRedirect') === 'true';

  const [referralToken, setReferralToken] = useState('');
  const [emailTaken, setEmailTaken] = useState(false);

  const schema = yup.object().shape({
    firstName: yup.string().max(32, 'First name must not be longer than 32 characters.').required('First name is required.'),
    lastName: yup.string().max(32, 'First name must not be longer than 32 characters.').required('Last name is required.'),
    email: yup.string().email('Please enter a valid email address.').required('Email is required.'),
    password: yup
      .string()
      .min(8, 'Password must contain at least 8 characters.')
      .max(32, 'Password must not be longer than 32 characters.')
      .required('Please enter your password')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})/,
        'Password must contain a minimum of 8 characters, including at least one uppercase letter, one lowercase letter, one number, and one special character.',
      ),
    agreeToTerms: yup.boolean().required('You must agree to the Terms & Conditions').oneOf([true], 'You must agree to the user Terms & Conditions'),
  });
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const navigateHandler = () => {
    if (localStorage.getItem('isLinkedInReady') === 'true') {
      closeModal();
      navigate('/crucial-data', { state: { userType: userType } });
    }
  };

  useEffect(() => {
    window.addEventListener('storage', navigateHandler);

    return () => {
      // Remove event listener
      window.removeEventListener('storage', navigateHandler);
    };
  }, []);

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const action = queryParams.get('action');
    const referalToken = queryParams.get('referal_token');
    if (action === 'sign_up' && referalToken) {
      // openModal('REGISTER');
      setReferralToken(referalToken);
    }
  }, []);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormDataType>({
    resolver: yupResolver(schema),
  });
  const { openModal } = useModalAction();

  const onSubmitHandler = (data: any) => {
    setSharedRegisterData((sharedRegisterData: any) => ({
      ...sharedRegisterData,
      baseData: {
        ...sharedRegisterData.baseData,
        ...data,
        passwordConfirmation: data.password,
      },
    }));

    reset();
    closeModal(true);
    navigate('/crucial-data', { state: { userType: userType, referralToken } });
  };

  const onSubmit = async (data: any) => {
    const response = await checkTakenEmail(data);

    if (response.error === false) {
      setEmailTaken(false);
      onSubmitHandler(data);
    }
    if (response.error === true) {
      setEmailTaken(true);
    } else {
    }
  };

  const googleRegisterHandler = () => {
    window.location.href = import.meta.env.VITE_REACT_APP_GOOGLE_URL_REGISTER as string;
  };

  const onSuccessLinkedinHandler = (e: any) => {};

  useEffect(() => {
    if (errors.firstName) {
      toast.show({
        title: 'Enter your first name.',
        text: 'This field cannot be left blank.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }

    if (errors.lastName) {
      toast.show({
        title: 'Enter your last name.',
        text: 'This field cannot be left blank.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }

    if (errors.email) {
      toast.show({
        title: 'Enter your email.',
        text: 'Alternatively, you can enter your mobile number. This field cannot be left blank.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
    if (errors.password) {
      toast.show({
        title: 'Enter your password.',
        text: 'Password must contain a minimum of 8 characters, including at least one uppercase letter, one lowercase letter, one number, and one special character.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
    if (errors.agreeToTerms) {
      toast.show({
        title: ' You must agree to the Terms & Conditions.',
        // text: 'This field cannot be left blank.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
  }, [errors, toast]);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <header className='flex flex-row justify-between'>
          <p className='font-rufina-stencil text-[32px] font-normal leading-[40px] text-[#323C58]'>Sign up</p>
          <span className='material-icons text-500 mr-0 justify-end' onClick={() => closeModal()}>
            close
          </span>
        </header>
        <section className=' rubik mt-[10px]  text-[14px] font-normal leading-[20px]  text-[#444B5F]'>
          Create a new account. Do you have an account already?
        </section>
        <a href='#' className='rubik  text-[14px] font-normal text-[#0B80E7]' onClick={() => openModal('LOGIN')}>
          Log in now
        </a>
        <div className='mx-auto flex flex-col justify-between sm:flex-row'>
          <div className='flex flex-col sm:mr-2'>
            <section className=' rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>First name</section>
            <input
              {...register('firstName')}
              placeholder='Your first name'
              type='text'
              className={`rubik mb-2 h-[48px] w-full rounded border border-[#BBC1D3] px-2 text-[14px] text-[#323C58] sm:mb-0 sm:w-[175px] ${errors?.firstName?.message && 'border-[#CB101D]'}`}
            />
            <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.firstName?.message}</p>
          </div>

          <div className='flex flex-col'>
            <section className=' rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Last name</section>
            <input
              {...register('lastName')}
              placeholder='Your last name'
              type='text'
              className={`rubik h-[48px] w-full rounded border border-[#BBC1D3] px-2 text-[14px] text-[#323C58] sm:w-[175px] ${errors?.lastName?.message && 'border-[#CB101D]'}`}
            />
            <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.lastName?.message}</p>
          </div>
        </div>
        <section className=' rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Email</section>
        <input
          {...register('email')}
          placeholder='Your email address'
          type='email'
          className={`rubik h-[48px] w-full  rounded border border-[#BBC1D3] px-2 text-[14px] text-[#323C58] sm:w-[368px] ${errors?.email?.message && 'border-[#CB101D]'}`}
        />
        <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.email?.message}</p>
        {emailTaken && <p className='mt-1.5 font-medium text-[#CB101D]'>Email is already taken</p>}
        {/* <a
        href='#'
        onClick={() => toOtpLogin()}
        className='text-black underline font-normal'
      >
        Use phone number instead
      </a> */}
        <section className=' rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Password</section>
        <div className='relative flex items-center'>
          <input
            type={isPasswordVisible ? 'text' : 'password'}
            {...register('password')}
            placeholder='..........'
            className={`rubik h-[48px] w-full rounded border border-[#BBC1D3] px-2 text-[14px] text-[#323C58] sm:w-[368px] ${errors?.password?.message && 'border-[#CB101D]'}`}
          />
          <span className='material-icons-outlined absolute right-4 mt-[3px] cursor-pointer' onClick={() => setIsPasswordVisible(!isPasswordVisible)}>
            {isPasswordVisible ? 'visibility' : 'visibility_off'}
          </span>
        </div>
        <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.password?.message}</p>
        <section className=' rubik mt-[4px] text-[14px] font-normal  leading-[20px] text-[#444B5F]'>
          Use more than 8 characters and a combination of uppercase and lowercase letters, numbers, and symbols.
        </section>
        <div className='mt-[12px] flex items-center'>
          <input type='checkbox' className='mr-2 inline-block h-4 w-4 checked:bg-[#323C58]' {...register('agreeToTerms')} />
          <section className=' rubik text-[14px]  font-normal leading-[20px]   text-[#1A1A1A] '>
            I agree to the
            <a
              className='rubik ml-1 cursor-pointer text-[14px] font-normal leading-[20px] text-blue-500'
              onClick={() => {
                window.open('https://surelysecurity.com/terms-of-use', '_blank', 'noopener,noreferrer');
                closeModal();
              }}
            >
              Terms & Conditions
            </a>
            &nbsp;and&nbsp;the&nbsp;
            <a
              className='rubik ml-1 cursor-pointer text-[14px] font-normal leading-[20px] text-blue-500'
              onClick={() => {
                window.open('https://surelysecurity.com/code-of-conduct', '_blank', 'noopener,noreferrer');
                closeModal();
              }}
            >
              Code of Coduct
            </a>
            &nbsp;
          </section>
        </div>
        <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.agreeToTerms?.message}</p>
        <button
          type='submit'
          className='border-neutral bg-background-base rubik mt-4 flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border bg-[#0B80E7] px-4 py-2 text-[16px]  font-medium leading-[24px] text-white sm:w-[368px]'
        >
          Continue
        </button>
        <div className='mt-[12px] flex items-center'>
          <hr className='bg-neutral-faded mr-2 flex-grow' />
          <span className='rubik text-[14px] text-[#323C58]'>Or</span>
          <hr className='bg-neutral-faded ml-2 flex-grow' />
        </div>
        <button
          type='button'
          onClick={() => openModal('GUEST_VIEW')}
          className='bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#BBC1D3] !bg-[#ffff] px-2 px-4 py-2 sm:w-[368px'
        >
          <p className='rubik mt-[2px] text-[14px] font-medium leading-[24px] text-[#1A1A1A]'>Continue as Guest</p>
       
        </button>
        
        <button
          type='button'
          onClick={googleRegisterHandler}
          className='bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#BBC1D3] !bg-[#ffff] px-2 px-4 py-2 sm:w-[368px]'
        >
          <img src={google} alt='Google' />{' '}
          <p className='rubik mt-[2px] text-[14px] font-medium leading-[24px] text-[#1A1A1A]'>Continue with Google</p>
        </button>

        
        {/* <button
        type='button'
        className='flex justify-center items-center self-stretch px-4 py-2 gap-2 self-stretch border border-[#BBC1D3] rounded-[8px] px-2 bg-background-base !bg-[#ffff] w-full sm:w-[368px] h-[48px] mt-[12px]'
      >
        <img src={linkedin} alt='Linkedin' /> Continue with Linkedin
      </button> */}
      </form>
      <LinkedIn
        scope='openid email profile'
        clientId={import.meta.env.VITE_REACT_APP_LINKEDIN_CLIENT_ID}
        redirectUri={import.meta.env.VITE_REACT_APP_LINKEDIN_REGISTER_REDIRECT_URL}
        onSuccess={onSuccessLinkedinHandler}
        onError={(e) => console.error(e)}
      >
        {(renderProps) => (
          <button
            onClick={renderProps.linkedInLogin}
            className='bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#BBC1D3] !bg-[#ffff] px-2 px-4 py-2 sm:w-[368px]'
          >
            <img src={linkedin} alt='Linkedin' />{' '}
            <p className='rubik mt-[2px] text-[14px] font-medium leading-[24px] text-[#1A1A1A]'>Continue with Linkedin</p>
          </button>
        )}
      </LinkedIn>
    </>
  );
};

export default RegisterForm;
