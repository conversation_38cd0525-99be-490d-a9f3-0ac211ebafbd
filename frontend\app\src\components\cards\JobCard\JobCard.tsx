// @ts-nocheck
import React, { useState, useContext } from 'react';
import { Link } from 'react-router-dom';
import { Card, View, Divider, Image, Text, Button } from 'reshaped';
import BadgeEmergencyHire from './BadgeEmergencyHire/BadgeEmergencyHire';
import jobs from '../../../store/dummydata/JobsdummyData';
import homepage28 from '../../../assets/images/homepage/homepage28.png';
import homepage29 from '../../../assets/images/homepage/homepage29.png';
import inclusivitypledgeicon from '../../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import { isValid, format } from 'date-fns';
import { getValidImageUrl } from '../../../utils/';

import { AppContext } from 'src/context/AppContext';

const JobCard: React.FC = () => {
  const { jobs, handleSelectedJob } = useContext(AppContext);

  const [showAll, setShowAll] = useState(false);
  const [dummyJob, setDummyJob] = useState(jobs);

  const handleToggleShowAll = () => {
    setShowAll(!showAll);
  };

  const toggleFavorite = (jobsIndex: number) => {
    const updatedJobs = [...dummyJob];
    updatedJobs[jobsIndex].is_favorite = !updatedJobs[jobsIndex].is_favorite;
    setDummyJob(updatedJobs);
  };

  return (
    <View className='flex flex-col  '>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 '>
        {(showAll ? jobs : jobs?.slice(0, 8))?.map((job: any, index: any) => (
          <Card
            key={job.id}
            className='w-full p-6 border border-[#dfe2ea] drop-shadow-md rounded-lg sm:max-w-[312px] cursor-pointer'
          >
            <View className='flex flex-col items-center gap-5'>
              <View className='w-full flex items-center justify-between'>
                <View className='flex items-center'>{job.is_emergency_hire ? <BadgeEmergencyHire /> : null}</View>
                <img
                  src={job.is_favorite ? homepage28 : homepage29}
                  className=''
                  onClick={() => toggleFavorite(index)}
                />
              </View>

              <Link to={`/operator-job/${job.id}`} onClick={() => handleSelectedJob(job.id)}>
                <View>
                  <img className='w-fill h-[176px] bg-contain rounded-xl' src={getValidImageUrl(job.images)} />
                </View>
                <View className='w-full flex flex-col justify-start gap-1'>
                  <View className='mt-[14px]'>
                    <Text className='rubik text-base font-medium leading-5 text-[#1A1A1A]'>{job.title}</Text>
                  </View>
                  <View>
                    <Text className='rubik text-xl font-medium leading-8 my-3 text-[#383838]'>
                      £{job.hourly_rate_min}
                    </Text>
                  </View>
                  <View direction='row' gap={1}>
                    <Text className='rubik text-sm font-medium leading-5 text-[#383838]'>I need:</Text>
                    <Text className='rubik text-sm font-medium leading-5 text-[#0B80E7]'>
                      {job.sia_licence && job.sia_licence.join(', ')}
                    </Text>
                  </View>
                  <View direction={'row'} align={'center'} className='my-1'>
                    <span className='material-icons-outlined  text-sm w-1/12 text-[#383838]'>calendar_today</span>

                    <View className='w-11/12'>
                      <Text className='text-[#383838] font-normal rubik text-[14px]'>
                        From&nbsp;
                        <span className='text-[#383838] font-medium rubik text-[14px]'>
                          {job.date_range.length > 0 &&
                            isValid(new Date(job.date_range[0].start)) &&
                            format(new Date(job.date_range[0].start), 'dd MMM yyyy')}
                        </span>
                        &nbsp;to&nbsp;
                        <span className='text-[#383838] font-medium rubik text-[14px]'>
                          {job.date_range.length > 0 &&
                            isValid(new Date(job.date_range[job.date_range.length - 1].end)) &&
                            format(new Date(job.date_range[job.date_range.length - 1].end), 'dd MMM yyyy')}
                        </span>
                      </Text>
                    </View>
                    <View className='my-2'>
                      <Text className='rubik text-sm font-normal leading-5 text-[#383838]'>
                        Published&nbsp;
                        {format(new Date(job.updated_at), 'dd MMM yyyy')}
                      </Text>
                    </View>
                  </View>
                  <View gap={2} className='bg-[#F4F5F7] rounded-[8px]'>
                    <View direction={'row'} gap={2} className='mt-[10px] ml-[10px]'>
                      <View direction={'row'} gap={2}>
                        <span className='material-icons-outlined text-base text-[#3C455D]'>place</span>
                        <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>{job.location}</Text>
                      </View>
                      <View direction={'row'} align={'center'} gap={2}>
                        <span className='material-icons text-base text-[#3C455D]'>person</span>
                        <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>
                          {job.nr_of_operatives} People
                        </Text>
                      </View>
                    </View>
                    <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                      <span className='material-icons-outlined text-base text-[#3C455D]'>credit_card</span>
                      <View direction={'row'} gap={1}>
                        <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>Earnings:</Text>
                        <Text className='rubik text-sm font-medium leading-5 text-[#3C455D]'>
                          £{job.hourly_rate_min}
                        </Text>
                      </View>
                    </View>
                    <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                      <span className='material-icons-outlined text-base text-[#3C455D]'>lock</span>
                      <View direction={'row'} gap={1}>
                        <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>Escrow deposit:</Text>
                        <Text className='rubik text-sm font-medium leading-5 text-[#3C455D]'>10%</Text>
                      </View>
                    </View>
                    <View direction={'row'} align={'center'} gap={2} className='mb-[10px] ml-[10px]'>
                      <span className='material-icons-outlined text-base text-[#3C455D]'>payments</span>
                      <View direction={'row'} gap={1}>
                        <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>Payment terms:</Text>
                        <Text className='rubik text-sm font-medium leading-5 text-[#3C455D]'>15 days</Text>
                      </View>
                    </View>
                  </View>
                </View>
                <Divider className='w-full h-[1px] mt-[16px]'></Divider>
                <View className='gap-2 mt-[16px]'>
                  {job.sia_licence.map((licence: any, licenceIndex: any) => (
                    <Button
                      key={licenceIndex}
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border border-[#05751F] !bg-[#E6FEF3] text-[#323c58] text-xs max-w-xs overflow-hidden truncate mr-[5px]  mt-[8px]'
                    >
                      <Text color='positive' className='flex items-center gap-1 rubik '>
                        <span className='material-icons text-[16px]'>star</span>
                        {licence}
                      </Text>
                    </Button>
                  ))}
                  {job?.industry_sector?.map((sector: any, sectorIndex: any) => (
                    <Button
                      key={sectorIndex}
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='px-2 py-1 border !bg-[#323C58] text-xs max-w-xs overflow-hidden truncate mt-[8px]'
                    >
                      <Text className='text-[#FFFFFF] rubik font-normal leading-4'>{sector}</Text>
                    </Button>
                  ))}

                  {job.is_inclusivity_pledge && (
                    <Button
                      variant='outline'
                      className='flex w-[170px] justify-center items-center self-stretch rounded-full border border-dark-gradient !bg-[#ffff]  mt-2'
                      icon={() => <img src={inclusivitypledgeicon} className='w-[20px]' />}
                    >
                      <Text className='rubik font-normal '>Inclusivity Pledge</Text>
                    </Button>
                  )}
                </View>
              </Link>
            </View>
          </Card>
        ))}
      </div>
      <Button
        variant='outline'
        onClick={handleToggleShowAll}
        className='mt-4 mx-auto bg-[#D1E1FF] text-[#0D2F87] hover:bg-[#0D2F87] hover:text-[#D1E1FF] rounded-lg py-2 px-4 text-sm font-medium mb-[36px]'
      >
        {showAll ? 'Load Less' : 'Load More'}
      </Button>
    </View>
  );
};

export default JobCard;
