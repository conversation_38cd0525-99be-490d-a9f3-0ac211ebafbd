// @ts-nocheck
import { useEffect, useState } from 'react';
import LoginForm from '../auth/LoginForm';
import OtpLoginForm from '../auth/OtpLoginForm';
import { useModalAction, useModalState } from 'src/context/ModalContext';
import linkedin from '../../assets/icons/socialicon/linkedin.svg';
import google from '../../assets/icons/socialicon/google.png';
import { LinkedIn } from 'react-linkedin-login-oauth2';
import { useNavigate } from 'react-router-dom';

const LoginModal = () => {
  const [isOtp, setIsOtp] = useState(false);
  const { openModal, closeModal } = useModalAction();
  const navigate = useNavigate();
  const nonClosing = localStorage?.getItem('wpRedirect') === 'true';

  const navigateHandler = () => {
    if (localStorage.getItem('isLinkedInReady') === 'true') {
      closeModal();
      navigate('/success-linkedin');
    }
  };

  useEffect(() => {
    window.addEventListener('storage', navigateHandler);

    return () => {
      // Remove event listener
      window.removeEventListener('storage', navigateHandler);
    };
  }, []);

  const handleGoToOtp = () => {
    setIsOtp(!isOtp);
  };

  const googleRegisterHandler = () => {
    window.location.href = import.meta.env.VITE_REACT_APP_GOOGLE_URL_LOGIN;
  };

  const handleGoToRegister = () => {
    openModal('REGISTER');
  };

  const onSuccessLinkedinHandler = (e: any) => {};

  const guestLoginHandler = () => {
    openModal('GUEST_VIEW');
  };

  return (
    <>
      <div className='flex justify-between pl-3 sm:pl-0'>
        <header className='font-rufina-stencil text-[32px] text-[#323C58]'>Log in</header>
        <span className='material-icons text-500 mr-0 justify-end' onClick={() => closeModal()}>
          close
        </span>
      </div>
      {!isOtp ? (
        <LoginForm toOtpLogin={handleGoToOtp} handleGoToRegister={handleGoToRegister} />
      ) : (
        <OtpLoginForm toStandardLogin={handleGoToOtp} handleGoToRegister={handleGoToRegister} />
      )}
      <div className='mb-3 mt-[12px] flex items-center'>
        <hr className='bg-neutral-faded mr-2 flex-grow' />
        <span className='rubik text-black-white-black text-[14px]'>Or</span>
        <hr className='bg-neutral-faded ml-2 flex-grow' />
      </div>
      <div className='pl-3 pr-4 sm:pl-0 sm:pr-0'>
        <button
          onClick={guestLoginHandler}
          className='bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch rounded border border-[#BBC1D3] !bg-[#ffff] px-2 px-4 py-2 sm:w-[368px]'
        >
          <p className='rubik mt-1'>Continue as Guest</p>
        </button>
        <button
          onClick={googleRegisterHandler}
          className='bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded rounded border border-[#BBC1D3] !bg-[#ffff] px-2 px-4 py-2 sm:w-[368px]'
        >
          <img src={google} alt='Google' /> <p className='rubik mt-1'> Continue with Google</p>
        </button>
        <LinkedIn
          scope='openid email profile'
          clientId={import.meta.env.VITE_REACT_APP_LINKEDIN_CLIENT_ID}
          redirectUri={import.meta.env.VITE_REACT_APP_LINKEDIN_LOGIN_REDIRECT_URL}
          onSuccess={onSuccessLinkedinHandler}
          onError={(e) => console.error(e)}
        >
          {(renderProps) => (
            <button
              onClick={renderProps.linkedInLogin}
              className='bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#BBC1D3] !bg-[#ffff] px-2 px-4 py-2 sm:w-[368px]'
            >
              <img src={linkedin} alt='Linkedin' /> <p className='rubik mt-1'> Continue with Linkedin</p>
            </button>
          )}
        </LinkedIn>
      </div>
    </>
  );
};

export default LoginModal;
