import React from 'react';
import surelyMessageIcon from '../../../../../assets/icons/surleyicon/surleyicon.png';
import { useAuthContext } from 'src/context/AuthContext';

interface PaymentStatusMessageProps {
  message: {
    id: number;
    message: string;
    type: 'payment_status';
    sender_id: number;
    receiver_id: number;
    contract_id?: number;
    payment_id?: number;
    sender?: {
      id: number;
      name: string;
      profile_photo?: string;
    };
    metadata?: {
      payment_type: 'escrow' | 'outstanding';
      amount: number;
      status?: 'paid' | 'pending' | 'failed';
    };
  };
}

const PaymentStatusMessage: React.FC<PaymentStatusMessageProps> = ({ message }) => {
  const { isClient } = useAuthContext();

  if (isClient) {
    return (
      <div className={`flex items-start gap-4 rounded-lg border p-4 pr-[60px] lg:items-center ${
        message.metadata?.status === 'failed' 
          ? 'border-[#FF4D4F] bg-[#FFF1F0]'
          : message.metadata?.status === 'pending'
          ? 'border-[#FAAD14] bg-[#FFF7E6]'
          : message.metadata?.status === 'paid'
          ? 'border-[#0DA934] bg-[#E6FEF3]'
          : 'border-[#0DA934] bg-[#E6FEF3]'
      }`}>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} alt="Surely Icon" />
        </div>
        <div className='rubik flex w-full flex-col gap-[3px] text-left'>
          <h2 className='overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
            {message.metadata?.status === 'failed' ? 'Payment failed' :
             message.metadata?.status === 'pending' ? 'Payment pending' :
             message.metadata?.payment_type === 'outstanding' ? 'Payment completed' : 'Money added to escrow'}
          </h2>
          <p className='overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
           {message.message}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} alt="Surely Icon" />
      </div>
      <div className='rubik flex w-full flex-col gap-[3px] text-left'>
        <h2 className='overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
          {message.metadata?.payment_type === 'outstanding' ? 'Payment completed' : 'Money added to escrow'}
        </h2>
        <p className='overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
        {message.message}
        </p>
      </div>
    </div>
  );
};

export default PaymentStatusMessage; 