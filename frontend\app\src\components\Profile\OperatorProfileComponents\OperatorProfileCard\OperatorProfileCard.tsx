// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, View, Divider, Image, Text, Icon, Button } from 'reshaped';
import { IdVerified } from 'src/assets/icons';
import guard from '../../../../pages/OperatorProfilePage/store/OperatorProfilePageDummyData';
import { useNavigate } from 'react-router-dom';
import PhotoAndVideoModal from '../PhotoAndVideoModal/PhotoAndVideoModal';
import CompleteProfileCheck from '../CompleteProfileCheck/CompleteProfileCheck';
import { AppContext } from '../../../../context/AppContext';
import { getSurelyProBadge } from 'src/services/settings';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import reporticon from '../../../../assets/icons/reporticon/reporticon.svg';
import inclusivitypledgeicon from '../../../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import GradientSVG from '../../../../pages/SurelyProTraning/gradientSVG';

const OperatorProfileCard: React.FC = () => {
  const navigate = useNavigate();
  const {
    profileTitle,
    name,
    cityInfo,
    profilePhoto,
    profileVideo,
    siaLicense,
    additionalPictures,
    instantBook,
    siaLicence,
    idCheck,
    proofOfAddress,
    employmentHistory,
    creditCheck,
    noCriminalRecord,
    profileDescription,
    positions,
    qualifications,
    industrySectors,
    testlang,
    addressVerificationDocument,
    documentType,
    idBackDocument,
    idFrontDocument,
    selfieVerificationDocument,
    siaLicenceCardPhoto,
    siaLicenceExpiryDate,
    postalCity,
    siaLicenceNumber,
    surelyProBadges,
    otherReview,
    overallReview,
    reportCount,
    jobDataOperator,
  } = useContext(AppContext);


  const onlycity = postalCity?.split(', ', 1).pop();

  const [modalActive, setModalActive] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<string[] | null>(null);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);

  const openModal = (mediaList: string[], index: number) => {
    setSelectedMedia(mediaList);
    setCurrentMediaIndex(index);
    setModalActive(true);
  };

  const handleNext = () => {
    if (selectedMedia && currentMediaIndex < selectedMedia.length - 1) {
      setCurrentMediaIndex(currentMediaIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (selectedMedia && currentMediaIndex > 0) {
      setCurrentMediaIndex(currentMediaIndex - 1);
    }
  };
  const isDataUnavailable =
    profileTitle === null ||
    profileDescription === null ||
    industrySectors === null ||
    profilePhoto === null ||
    profileVideo === null ||
    siaLicense === null ||
    additionalPictures === null ||
    testlang === null ||
    profileTitle === undefined ||
    profileDescription === undefined ||
    industrySectors === undefined ||
    profilePhoto === undefined ||
    profileVideo === undefined ||
    siaLicense === undefined ||
    additionalPictures === undefined ||
    testlang === undefined ||
    positions?.length === 0 ||
    industrySectors?.length === 0 ||
    qualifications?.length === 0;

  const isValidateUnavailable =
    addressVerificationDocument === null ||
    documentType === null ||
    idFrontDocument === null ||
    selfieVerificationDocument === null ||
    siaLicenceCardPhoto === null ||
    siaLicenceExpiryDate === null ||
    siaLicenceNumber === null ||
    addressVerificationDocument === undefined ||
    documentType === undefined ||
    idFrontDocument === undefined ||
    selfieVerificationDocument === undefined ||
    siaLicenceCardPhoto === undefined ||
    siaLicenceExpiryDate === undefined ||
    siaLicenceNumber === undefined;

  const lastname = name?.split(' ').pop()?.charAt(0).toUpperCase() + name?.split(' ').pop()?.slice(1);
  const firstname = name?.split(' ').shift()?.charAt(0).toUpperCase() + name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;

  const baseURL = 'https://app.surelysecurity.com/storage/';

  return (
    <View className='lg:mx-auto'>
      <Card className=' flex h-auto  flex-col justify-between p-6 xl:w-[424px]'>
        <View className=' flex flex-col gap-5'>
          <View className='flex items-center justify-between gap-1'>
            <div className='flex items-center gap-2'>
              {siaLicence === 1  ? (
                <span className='rubik ml-[5px] h-[auto] w-[auto] rounded-md !bg-[#E6FEF3] p-1 text-xs font-normal leading-4 text-[#05751F]'>
                  SIA CERTIFIED
                </span>
              ) : (
                <span className='rubik ml-[5px] h-[auto] w-[auto] rounded-md !bg-[#EDEDED] p-1 text-xs font-normal leading-4 text-[#6A6A6A]'>
                  SIA PENDING
                </span>
              )}
              {instantBook ? (
                <span className='rubik w-[auto] rounded-md !bg-[#D1E1FF] px-[8px] py-1 text-[12px] font-normal leading-4 text-[#0D2F87]'>
                  INSTANT BOOK
                </span>
              ) : (
                <div />
              )}

              {reportCount === 0 ? (
                <button className='inline-flex h-[24px] w-[44px] items-center justify-center gap-1 rounded-[999px] border border-[#323C58] bg-white px-2 py-1'>
                  <div className='inline-flex items-center justify-center'>
                    <span className='material-icons-outlined mt-[2px] text-[17px] text-[#323C58]'>report</span>
                  </div>
                  <div className='rubik mt-[2px] text-[12px] font-medium leading-tight text-[#323C58]'>0</div>
                </button>
              ) : (
                <button className='inline-flex h-[24px] w-[44px] items-center justify-center gap-1 rounded-[999px] border border-[#FBD0D4] bg-[#FBD0D4] px-2 py-1'>
                  <div className='inline-flex items-center justify-center'>
                    <span className='material-icons-outlined mt-[2px] text-[17px] text-[#CB101D]'>report</span>
                  </div>
                  <div className='rubik mt-[2px] text-[12px] font-medium leading-tight text-[#CB101D]'>{reportCount}</div>
                </button>
              )}
            </div>
            <Button
              onClick={() => navigate('/operator-settings')}
              icon={() => <span className='material-icons-outlined -mt-1 text-base text-white'>settings</span>}
              className='flex w-[32px] items-center justify-center gap-4 rounded-full !bg-[#323C58] '
            ></Button>
          </View>
          <View className='flex flex-row items-center gap-5'>
            <View>
              {profilePhoto ? (
                <div
                  className='rounded-full p-0.5'
                  style={{
                    background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                  }}
                >
                  <Image
                    className='flex h-[80px] w-[80px] flex-col items-start rounded-full bg-white'
                    alt='Profile'
                    src={profilePhoto?.startsWith(baseURL) ? profilePhoto : baseURL + profilePhoto}
                  />
                </div>
              ) : (
                <View className='flex h-[80px]  w-[80px] items-center justify-center  rounded-full bg-[#0B80E7]'>
                  <Text className='rubik text-[30px] uppercase text-white'>{name?.charAt(0)}</Text>
                </View>
              )}
              {idCheck === 1 && <Icon className='absolute bottom-0 right-0 h-[32px] w-[32px]' svg={IdVerified} />}
            </View>
            <View className='flex flex-col justify-start gap-1'>
              {overallReview ? (
                <div className='flex flex-row gap-2'>
                  <span className='material-icons text-[20px] text-[#F4BF00]'>star</span>
                  <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>
                    {overallReview?.value} ({Math.round(overallReview?.value)})
                  </Text>
                </div>
              ) : (
                <div className='flex flex-row gap-2'>
                  <span className='material-icons text-[20px] text-[#C7CDDB]'>star</span>
                  <Text className='rubik text-[15px] font-medium leading-[20px] text-[#C7CDDB]'>No rating</Text>
                </div>
              )}
              <View>
                <Text className='rubik text-[16px] font-medium leading-5 text-[#1A1A1A]'>{initialName}</Text>
              </View>
              <View className='flex items-center justify-center gap-1'>
                {postalCity ? (
                  <>
                    <Text className='rubik text-line-height text-[16px] leading-5   text-blue-400 '>{onlycity}</Text>
                    <span className='material-icons-outlined icon-line-height text-[15px] text-blue-400'>place</span>
                  </>
                ) : (
                  <></>
                )}
              </View>
            </View>
          </View>

          {isDataUnavailable ? <CompleteProfileCheck /> : <></>}

          <View className='mt flex flex-col gap-3 sm:flex-row'>
            {/* <Text className='rubik mr-[0px] mt-[0px] font-medium text-[#1A1A1A]'>Jobs completed: {jobDataOperator?.completed_jobs}</Text> */}
            <Text className='rubik mt-[8px] font-medium text-[#383838] sm:mt-[0px]'>Total earnings: £{jobDataOperator?.total_earnings}</Text>
          </View>
          <View className='flex flex-col items-start'>
            <Divider className='h-[1px] w-full ' />
          </View>
          <View className='flex flex-col gap-2 py-1'>
            <View className='flex flex-wrap  gap-2 '>
              {siaLicense.map((sia: any, index: any) => (
                <Button
                  size='small'
                  key={index}
                  rounded={true}
                  elevated={false}
                  className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                >
                  <Text color='positive' className='rubik flex items-center gap-1'>
                    <span className='material-icons text-[16px]'>star</span>
                    {sia}
                  </Text>
                </Button>
              ))}

              {siaLicence === 1 &&
                idCheck === 1 &&
                proofOfAddress === 1 &&
                employmentHistory === 1 &&
                creditCheck === 1 &&
                noCriminalRecord === 1 && (
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                    >
                      <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                        <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                        BS7858
                      </Text>
                    </Button>
                  </View>
                )}
              {surelyProBadges?.some((badge: any) => badge.type === 'InclusivityPledge') && (
                <Button
                  size='small'
                  variant='outline'
                  className='border-dark-gradient flex w-[170px] items-center justify-center gap-1 self-stretch rounded-full border !bg-[#ffff] '
                  icon={() => <img src={inclusivitypledgeicon} className='w-[20px]' />}
                >
                  <Text className='rubik text-[12px] font-medium not-italic leading-5 '>Inclusivity Pledge</Text>
                </Button>
              )}
            </View>
          </View>
        </View>
      </Card>
      {isValidateUnavailable ? (
        <Button
          onClick={() => navigate('/first-step-validation-operator')}
          className='rubik border-neutral bg-background-base mt-[24px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[#0B80E7] px-4 py-2 !text-white xl:w-[424px]'
        >
          Validate your profile
        </Button>
      ) : (
        <></>
      )}

      <Card className='mt-[24px]  h-[424px] flex-wrap p-6 xl:w-[424px]'>
        {additionalPictures?.length > 0 || profileVideo ? (
          <View className='grid grid-cols-2 gap-x-[12px] gap-y-[12px]'>
            {additionalPictures.map((photo: any, index: any) => (
              <img
                key={index}
                className='h-[182px] w-[182px] cursor-pointer overflow-hidden rounded-lg object-cover '
                src={photo}
                onClick={() => openModal(additionalPictures, index)}
              />
            ))}
            {profileVideo && (
              <video
                src={profileVideo}
                controls
                className='h-[182px] w-[182px] cursor-pointer overflow-hidden rounded-lg object-cover '
                onClick={() => openModal([profileVideo], 0)}
              />
            )}
          </View>
        ) : (
          <div className='mt-[25%]'>
            <NoDataProfile />
          </div>
        )}
      </Card>

      <PhotoAndVideoModal
        active={modalActive}
        deactivate={() => {
          setSelectedMedia(null);
          setCurrentMediaIndex(0);
          setModalActive(false);
        }}
        mediaList={selectedMedia}
        currentMediaIndex={currentMediaIndex}
        onNext={handleNext}
        onPrevious={handlePrevious}
      />
    </View>
  );
};

export default OperatorProfileCard;
