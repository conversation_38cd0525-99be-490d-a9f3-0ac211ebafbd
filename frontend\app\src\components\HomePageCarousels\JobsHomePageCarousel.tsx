// @ts-nocheck
import { useContext, useState } from 'react';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import { Button, Text, Tabs, Image, View, Divider, Card, Icon } from 'reshaped';
import jobs from '../../store/dummydata/JobsHomePageDummy';
import BadgeEmergencyHire from '../cards/JobCard/BadgeEmergencyHire/BadgeEmergencyHire';

const JobsHomePageCarousel = () => {
  const { openModal } = useModalAction();
  const { user, isAuthenticated } = useContext(AuthContext);
  const [selectedJobIndex, setSelectedJobIndex] = useState(0);

  return (
    <>
      <Carousel
        showArrows={false}
        showStatus={false}
        showThumbs={false}
        infiniteLoop={true}
        showIndicators={false}
        className='p-4'
        onChange={(index) => setSelectedJobIndex(index)}
      >
        {jobs.map((job: any) => (
          <div
            key={job?.title + job?.jobsId}
            onClick={() => {
              if (!isAuthenticated) {
                openModal('REGISTER');
              }
            }}
          >
            <Card className='mx-auto flex h-full min-h-[700px] w-full cursor-pointer flex-col justify-between  shadow-md  '>
              <View className='flex flex-col items-center gap-5'>
                <View className='mt-2 flex w-full items-center justify-between'>
                  <View className='flex items-center'>{job.emergency ? <BadgeEmergencyHire /> : null}</View>
                  {job.is_favorite ? (
                    <svg xmlns='http://www.w3.org/2000/svg' width='22' height='19' viewBox='0 0 22 19' fill='none'>
                      <path
                        d='M9.87576 17.1178L9.87504 17.1172C7.27067 14.7555 5.1495 12.8312 3.6734 11.0265C2.20305 9.22886 1.42505 7.61102 1.42505 5.87108C1.42505 3.04413 3.63934 0.829834 6.4663 0.829834C8.06796 0.829834 9.613 1.57788 10.6194 2.75968L11 3.20671L11.3807 2.75968C12.3871 1.57788 13.9321 0.829834 15.5338 0.829834C18.3608 0.829834 20.5751 3.04413 20.5751 5.87108C20.5751 7.61102 19.7971 9.22889 18.3266 11.0279C16.8528 12.8309 14.7362 14.7544 12.1377 17.1157L12.1257 17.1266L12.1247 17.1276L11.0013 18.1425L9.87576 17.1178Z'
                        fill='#323C58'
                        stroke='#323C58'
                      />
                    </svg>
                  ) : (
                    <svg xmlns='http://www.w3.org/2000/svg' width='22' height='19' viewBox='0 0 22 19' fill='none'>
                      <path
                        d='M9.87576 17.1178L9.87504 17.1172C7.27067 14.7555 5.1495 12.8312 3.6734 11.0265C2.20305 9.22886 1.42505 7.61102 1.42505 5.87108C1.42505 3.04413 3.63934 0.829834 6.4663 0.829834C8.06796 0.829834 9.613 1.57788 10.6194 2.75968L11 3.20671L11.3807 2.75968C12.3871 1.57788 13.9321 0.829834 15.5338 0.829834C18.3608 0.829834 20.5751 3.04413 20.5751 5.87108C20.5751 7.61102 19.7971 9.22889 18.3266 11.0279C16.8528 12.8309 14.7362 14.7544 12.1377 17.1157L12.1257 17.1266L12.1247 17.1276L11.0013 18.1425L9.87576 17.1178Z'
                        fill='#C7CDDB'
                        stroke='#BBC1D3'
                      />
                    </svg>
                  )}
                </View>

                <div>
                  <View>
                    <Image className='w-fill h-[176px] rounded-xl bg-contain' src={job.photo} />
                  </View>
                  <View className='flex w-full flex-col justify-start gap-1'>
                    <View className='mt-[14px]'>
                      <Text className='rubik text-base font-medium leading-5'>{job.title}</Text>
                    </View>
                    <View>
                      <Text className='rubik my-3 text-xl font-medium leading-8'>£{job.pay_hour}/h</Text>
                    </View>
                    <View direction='row' gap={1}>
                      <Text className='rubik text-sm font-medium leading-5'>I need:</Text>
                      <Text className='rubik text-sm font-medium leading-5 text-[#0B80E7]'>{job.operator_type}</Text>
                    </View>
                    <View direction={'row'} align={'center'} className='my-1'>
                      <span className='material-icons-outlined  w-1/12 text-sm'>calendar_today</span>

                      <View className='w-11/12'>
                        <Text>
                          From {job.start_date} to {job.end_date}
                        </Text>
                      </View>
                      <View className={'my-2'}>
                        <Text className='rubik text-sm font-normal leading-5 '>Published 2 days ago</Text>
                      </View>
                    </View>
                    <View gap={1} className='rounded bg-[#F4F5F7]'>
                      <View direction={'row'} gap={2} className='ml-[10px] mt-[10px]'>
                        <View direction={'row'} gap={2}>
                          <span className='material-icons-outlined text-base'>place</span>
                          <Text className='rubik text-sm font-normal leading-5'>{job.place}</Text>
                        </View>
                        <View direction={'row'} align={'center'} gap={2}>
                          <span className='material-icons text-base'>person</span>
                          <Text className='rubik text-sm font-normal leading-5'>{job.number_operator} People</Text>
                        </View>
                      </View>
                      <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                        <span className='material-icons-outlined text-base'>credit_card</span>
                        <View direction={'row'} gap={1}>
                          <Text className='rubik text-sm font-normal leading-5'>Earnings:</Text>
                          <Text className='rubik text-sm font-medium leading-5'>£{job.earnings}</Text>
                        </View>
                      </View>
                      <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                        <span className='material-icons-outlined text-base'>lock</span>
                        <View direction={'row'} gap={1}>
                          <Text className='rubik text-sm font-normal leading-5'>Escrow deposit:</Text>
                          <Text className='rubik text-sm font-medium leading-5'>{job.escrow_deposit}%</Text>
                        </View>
                      </View>
                      <View direction={'row'} align={'center'} gap={2} className='mb-[10px] ml-[10px]'>
                        <span className='material-icons-outlined text-base'>payments</span>
                        <View direction={'row'} gap={1}>
                          <Text className='rubik text-sm font-normal leading-5'>Payment terms:</Text>
                          <Text className='rubik text-sm font-medium leading-5'>{job.payment_terms} days</Text>
                        </View>
                      </View>
                    </View>
                  </View>
                  <Divider className='h-[1px] w-full'></Divider>
                  <View className='mt-[10px] flex flex-wrap items-center justify-start gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Close Protection
                      </Text>
                    </Button>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Security Guard
                      </Text>
                    </Button>
                  </View>
                </div>
              </View>
            </Card>
          </div>
        ))}
      </Carousel>
      <View className=' flex justify-center'>
        {jobs.map((_, index) => (
          <div
            key={index}
            className={`mx-1 h-[12px] cursor-pointer ${
              selectedJobIndex === index ? 'w-[22px] rounded-[8px] bg-[#0B80E7]' : 'w-[12px] rounded-[100%] bg-[#C4BCBD]'
            }`}
          />
        ))}
      </View>
    </>
  );
};

export default JobsHomePageCarousel;
