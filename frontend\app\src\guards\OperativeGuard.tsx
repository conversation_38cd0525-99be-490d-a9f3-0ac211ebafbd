// @ts-nocheck
import { FunctionComponent, ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import useAuth from '../hooks/useAuth';
import { useAuthContext } from 'src/context/AuthContext.tsx';

interface OperativeGuardProps {
  children: ReactNode;
}

const OperativeGuard: FunctionComponent<OperativeGuardProps> = ({ children }) => {
  const location = useLocation();

  const { user } = useAuthContext();

  const isOperative = user?.profile ? user?.profile?.account_type == '1' : user?.account_type == '1';

  if (!isOperative) {
    return <Navigate to='/' state={{ from: location }} />;
  }

  return <>{children}</>;
};

export default OperativeGuard;
