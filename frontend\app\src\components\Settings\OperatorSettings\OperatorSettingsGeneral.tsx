// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Text, View, Button, TextField, Tabs, Divider, useToast, Image, Breadcrumbs, useToggle } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

import { addGeneral, getGeneral, getCities } from 'src/services/settings';
import { AppContext } from 'src/context/AppContext';

import ChangeEmailModal from '../ChangeEmailAndPhoneModal/ChangeEmailModal';
import ChangePhoneModal from '../ChangeEmailAndPhoneModal/ChangePhoneModal';

const OperatorSettingsGeneral: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [activeTab, setActiveTab] = useState('0');

  const [firstName, setFirstName] = useState<string>();
  const [lastName, setLastName] = useState<string>();
  const [email, setEmail] = useState<string>();
  const [prefix, setPrefix] = useState('');
  const [phoneNumber, setPhoneNumber] = useState<string>();

  const [postalCity, setPostalCity] = useState<string>();
  const [townCity, setTownCity] = useState<string>();
  const [county, setCounty] = useState<string>();

  const [postalCode, setPostalCode] = useState<string>();
  const [addressLine, setAddressLine] = useState<string>();
  const [locationRange, setLocationRange] = useState(50);
  const sliderBackground = `linear-gradient(to right, #323C58 0%, #323C58 ${locationRange}%, #BBC1D3 ${locationRange}%, #BBC1D3 100%)`;

  const [isSaving, setIsSaving] = useState(false);

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  const { fetchAppData } = useContext(AppContext);

  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const { active: active2, activate: activate2, deactivate: deactivate2 } = useToggle(false);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  useEffect(() => {
    if (postalCode) {
      const formattedPostCode = postalCode.replace(/\s/g, '');
      getCities(formattedPostCode).then((data: any) => {
        setPostalCity(`${data?.admin_district}, ${data?.region}, ${data?.country}`);
      });
    }
  }, [postalCode]);

  useEffect(() => {
    getGeneral().then((data: any) => {
      setFirstName(data.data.name.split(' ', 1).pop());
      setLastName(data.data.name.split(' ').pop());
      setEmail(data.data.email);
      setPhoneNumber(data.data.phone);
      setPostalCode(data.data.postal_code);
      setAddressLine(data.data.address);
      setTownCity(data.data.address_1);
      setCounty(data.data.address_3);
      setPostalCity(data.data.address_2);
      data.data.location_range && setLocationRange(data.data.location_range);
    });
  }, []);

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocationRange(Number(event.target.value));
  };

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const submitFirstTab = async () => {
    setIsSaving(true);

    const firstTabSettings = {
      name: firstName + ' ' + lastName,
      email,
      phone: phoneNumber,
      postal_code: postalCode,
      address: addressLine,
      address_1: townCity,
      address_2: county,
      location_range: locationRange
    };

    try {
      await addGeneral(firstTabSettings)
        .then(() => {
          fetchAppData();
          toast.show({
            title: 'Done!',
            text: 'You have updated your profile',
            startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
          });
        })
        .finally(() => {
          setIsSaving(false);
        });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.show({
        title: 'Error',
        text: 'Failed to save settings. Please try again.',
        type: 'error',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
      setIsSaving(false);
    }
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  return (
    <View className=' mx-auto w-full overflow-hidden px-[12px] sm:w-auto'>
      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='rubik text-[16px] font-medium text-[#1A1A1A]'>General</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='mb-[16px] flex items-center p-0 lg:mb-5'>
        <Text className='text-foreground-neutral font-rufina-stencil text-[#323C58] lg:text-[32px] xl:leading-10'>General</Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          <Tabs.Item value='0'>
            <span className='rubik text-[#14171F]'>General</span>
          </Tabs.Item>
          <Tabs.Item value='1'>
            <span className='rubik text-[#14171F]'>Location</span>
          </Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-6 flex flex-col gap-4 '>
            <View className='flex flex-col gap-3 sm:flex-row'>
              <View className='flex flex-col gap-1'>
                <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>First name</Text>
                <input
                  name='firstName'
                  placeholder={firstName}
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-[262px]'
                />
              </View>
              <View className='flex flex-col gap-1'>
                <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Last name</Text>
                <input
                  name='lastName'
                  placeholder={lastName}
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-[262px]'
                />
              </View>
            </View>
            <View className='flex flex-col gap-1'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Email</Text>
              <TextField
                name='email'
                placeholder={email}
                value={email}
                // onChange={(e) => setEmail(e.value)}
                className='h-[48px] gap-[8px] rounded-md  rounded-md border border-[#BBC1D3]  bg-white px-[12px] py-[14px] focus:outline-none sm:w-[536px] '
                endSlot={
                  <Button
                    variant='ghost'
                    className='material-icons-outlined  cursor-pointer  bg-transparent'
                    icon={() => <span className='material-icons-outlined'>manage_accounts</span>}
                    onClick={activate1}
                  />
                }
              />
            </View>
            <View className='flex flex-col gap-1'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Your phone number</Text>
              <View className='relative mt-[5px] flex flex-row sm:w-[536px]'>
                <PhoneInput
                  country={'gb'}
                  inputStyle={{
                    width: isSmallScreen ? '100%' : '536px',
                    height: '48px',
                    color: '#3C455D',
                    fontSize: '14px',
                    cursor: 'pointer' 
                  }}
                  value={phoneNumber}
                  // onChange={(e) => setPhoneNumber(e)}
                  disabled={true}
                />
                <Button
                  variant='ghost'
                  className='material-icons-outlined absolute right-[9.5px] top-0.5 cursor-pointer  bg-transparent'
                  icon={() => <span className='material-icons-outlined'>manage_accounts</span>}
                  onClick={activate2}
                />
              </View>
              <Text className='text-neutral-faded rubik mr-[10px] mt-1 text-[14px] font-normal leading-5'>
                We'll verify your number via text message with a four digit code.
              </Text>
            </View>

            <ChangeEmailModal active={active1} deactivate={deactivate1} email={email} />
            <ChangePhoneModal active={active2} deactivate={deactivate2} phoneNumber={phoneNumber} />
            <Divider className='h-[1px] w-full'></Divider>

            <View className='flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitFirstTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {activeTab === '1' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-6 flex flex-col gap-4'>
            <View className='flex flex-col gap-3 sm:flex-row'>
              <View className='flex flex-col gap-1'>
                <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Your postcode</Text>
                <input
                  placeholder={postalCode}
                  name='text'
                  value={postalCode}
                  onChange={(e) => setPostalCode(e.target.value)}
                  className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-[262px]'
                />
              </View>

              <View className='flex flex-col gap-1'>
                <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Your postal city/town</Text>

                <input
                  name='text'
                  className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-[262px]'
                  value={postalCity}
                  onChange={(e) => setPostalCity(e.target.value)}
                />
              </View>
            </View>

            <View className='mt-[16px] flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Job location range</Text>
              <div className='mt-[8px] flex flex-col items-center'>
                <div className='rubik text-center text-base font-normal text-[#14171F]'>
                  <div className='flex items-center gap-2'>
                    <span id='sliderValue'>{locationRange}</span>
                    miles
                  </div>
                </div>
                {/* <Slider
                  className='w-full text-[#323C58]'
                  name='settings-slider'
                  value={locationRange}
                  minValue={0}
                  maxValue={100}
                  onChange={handleSliderChange}
                /> */}
                <input
                  type='range'
                  min='0'
                  max='100'
                  value={locationRange}
                  className='mt-[10px] h-1 w-full'
                  style={{ background: sliderBackground }}
                  id='slider'
                  onChange={handleSliderChange}
                />
                <div className='mt-[12px] flex w-full justify-between'>
                  <span className='rubik text-base font-medium leading-[16px] text-[#323C58]'>0 miles</span>
                  <span className='rubik text-base font-medium leading-[16px] text-[#323C58]'>+100 miles</span>
                </div>
              </div>
            </View>

            <View className='flex flex-col gap-1'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Address line</Text>
              <input
                name='text'
                placeholder={addressLine}
                value={addressLine}
                onChange={(e) => setAddressLine(e.target.value)}
                className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-full'
              />
            </View>

            <View className='flex flex-col gap-1'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Town / City</Text>
              <input
                name='text'
                placeholder={townCity}
                value={townCity}
                onChange={(e) => setTownCity(e.target.value)}
                className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-full'
              />
            </View>

            <View className='flex flex-col gap-1'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>County (optional)</Text>
              <input
                name='text'
                placeholder={county}
                value={county}
                onChange={(e) => setCounty(e.target.value)}
                className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-full'
              />
            </View>
            <Divider className='h-[1px] w-full'></Divider>

            <View className='flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitFirstTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
    </View>
  );
};

export default OperatorSettingsGeneral;
