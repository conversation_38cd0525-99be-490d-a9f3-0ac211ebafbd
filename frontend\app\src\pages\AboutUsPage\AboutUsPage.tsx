// @ts-nocheck
import React, { useState, useContext, useEffect } from 'react';
import { View, Image, Text, Button } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import aboutpage1 from '../../assets/images/aboutpage/aboutpage1.png';
import aboutpage4 from '../../assets/images/aboutpage/aboutpage4.svg';
import aboutpage5 from '../../assets/images/aboutpage/aboutpage5.svg';
import aboutpage6 from '../../assets/images/aboutpage/aboutpage6.svg';
import aboutpage7 from '../../assets/images/aboutpage/aboutpage7.svg';
import aboutpage8 from '../../assets/images/aboutpage/aboutpage8.svg';
import aboutpage9 from '../../assets/images/aboutpage/aboutpage9.jpg';
import aboutpage10 from '../../assets/images/aboutpage/aboutpage10.jpg';
import aboutpage11 from '../../assets/images/aboutpage/aboutpage11.jpg';
import aboutpage12 from '../../assets/images/aboutpage/aboutpage12.jpg';
import Footer from '../Footer/Footer';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import Signature from '../LandingPage/Signature';
import Five from '../AboutUsPage/Five';
import Page from 'src/components/UI/layout/Page/Page';
import Section from 'src/components/UI/layout/Section/Section';
import Container from 'src/components/UI/layout/Container/Container';
import Row from 'src/components/UI/layout/Row/Row';
import Col from 'src/components/UI/layout/Col/Col';

const AboutUsPage: React.FC = () => {
  const { openModal } = useModalAction();
  const { isAuthenticated } = useContext(AuthContext);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <Page className={'page'}>
      <Section className='pt-[62px]'>
        <Container className=''>
          <Row className='items-center justify-center'>
            <Image src={aboutpage1} className='w-[1320px] h-[auto] sm:h-[580px]' />
          </Row>
          <Row className='intro rounded-b-[12px] !bg-[#F4F5F7] pt-[70px] justify-between xl:pl-[110px] flex-col sm:flex-row justify-between max-w-[1320px] mx-auto sm:gap-[24px]'>
            <Col className='items-start px-[12px] xl:px-0 xl:mx-auto  sm:w-[520px] sm:mx-0'>
              <Text className='text-left rubik xl:text-[16px] text-[15px] font-[500] text-[#388DD8] leading-[20px]'>
                Transforming the security marketplace
              </Text>
              <Text className='text-left font-rufina-stencil text-[30px] xl:text-[48px] font-normal text-[#323C58] leading-[46px] sm:leading-[56px] sm:mt-[7px] mt-[10px]'>
                It’s time for change. 
              </Text>
            </Col>
            <Col className='  sm:w-[625px] items-start mx-auto px-[12px] xl:px-0'>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-0 mt-[52px] mr-[0px] xl:mr-[100px] '>
                Hi, we’re Aliona & Aurela, and with your help we want to create a fairer, better, easier way to match up
                clients and security operatives to the mutual benefit of both parties. Our dad is in the industry, and
                that’s all we’ve ever talked about around the kitchen table since we were born.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] xl:mr-[100px] mt-[23px]'>
                We’re combining innovative technologies with a fresh approach to deliver a game changing transformation
                of the security operative marketplace. The current system just doesn’t work that well – having a badge
                is not enough, there’s much more to it.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] xl:mr-[100px] mt-[23px]'>
                We’ve listened to what you have to say – all the things that frustrate you about how things work today,
                and what you think needs to be done to create a better tomorrow. And we’ve implemented all these
                suggestions – and many more – to create Surely.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] xl:mr-[100px] mt-[23px]'>
                One of the key benefits is that clients pay less, and security operatives earn more. Both parties are
                also protected by our internal chat functionality, that delivers seamless communication by text, audio
                and video – the system also records and saves every interaction too.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] xl:mr-[100px] mt-[23px]'>
                And this is just the start – we’re going to be rolling out new technologies and fresh ideas from the
                word go. If there’s anything you don’t like or anything you want, tell us. We’ll put it to a poll and if
                enough other people agree, then we’ll get it implemented ASAP.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] xl:mr-[100px] mt-[23px]'>
                Give it a go – it’s free to use – and see for yourself. It’s all about achieving a win-win solution for
                both clients and security operatives. Clients get to select the ideal security operative for their
                needs, security operatives get to choose the jobs that suit them best. With warmest regards,
              </Text>

              <View className='mb-[42px] sm:mt-[37.5px] mt-[45px]'>
                <Signature />
                <div id='surelycharter'></div>
              </View>
            </Col>
          </Row>
          <Row className='fivestart sm:flex-row mt-[105px] xl:pl-[110px] gap-5 sm:gap-0'>
            <Col className='  mx-auto px-[12px] xl:px-0  sm:mx-0 sm:w-[535px] items-start'>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-[20px]'>
                The Surely Charter 
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[48px] font-normal leading-[56px] mt-[7px]'>
                Our five star promise. 
              </Text>
            </Col>
            <Col className='flex flex-col w-full sm:w-[625px] items-start ml-[0px] sm:ml-[30px] sm:gap-0 gap-5'>
              <View className='sm:mr-[30px] :mb-[35px] w-full flex justify-center mt-4 sm:mt-0'>
                <Five />
              </View>
              <Text className='text-left text-[#383838] sm:w-[488px]  px-[12px] xl:px-0 rubik text-[16px] font-normal leading-[24px]  mx-auto '>
                Ethics and morality lie at the heart of everything we do. We’ve created the five star promise to define
                the most important values and values that have guided how Surely works. 
              </Text>
            </Col>
          </Row>
          <Row className='alwaystransparent mb-[80px] sm:mb-[105px] mt-[50px] sm:mt-[107px] xl:pr-[40px] flex-col-reverse gap-[50px] xl:pl-[110px]'>
            {/* <Col className='sm:flex-row justify-between gap-[89px] mt-[20px] max-w-[1100px]'> */}
            {isSmallScreen && (
              <Image src={aboutpage4} className='w-full sm:w-auto h-auto  xl:ml-[40px]  mt-[20px] xl:mt-[10px]' />
            )}
            <Col className=' mt-[64px] px-[12px] xl:px-0 mx-auto sm:mx-0 sm:w-[533px] items-start'>
              <Text className='text-left text-[#323C58] rubik sm:text-[32px] text-[31px] font-normal leading-[40px]'>
                We’re always transparent
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] sm:mt-[8px] mt-[10px]'>
                Surely is an open book in every possible way. Clients get to chat directly with security operatives. The
                fee structure is clearly defined to both parties. There are no hidden agendas, no middlemen and no nasty
                surprises. Why not try us out for yourself? 
              </Text>
              <div className='items-start bg-[#388DD8] w-[201px] h-[1px] mt-[28px]' />
            </Col>
            {!isSmallScreen && (
              <Col className='w-[625px] sm:w-[625px]'>
                <img src={aboutpage4} className='w-[422px] h-auto ml-[90px]' />
              </Col>
            )}
            {/* </Col> */}
          </Row>
        </Container>
      </Section>
      {/* <View className='flex flex-col  w-full mx-auto'> */}
      <Section className='bg-[#D4F0F6] mx-auto '>
        <Container className='mx-auto w-full max-w-[1100px]'>
          <Row className=' mt-[115px] sm:gap-[89px] gap-[20px] xl:pl-[110px]'>
            <img src={aboutpage5} className='xl:ml-[10px] w-full sm:w-[350px] xl:w-auto  h-auto m46' />
            <Col className=' mt-[68px]  px-[12px] xl:px-0 sm:w-[482px] items-start mx-auto sm:ml-[0px] sm:mr-[30px]'>
              <Text className='text-left text-[#323C58] rubik text-[32px] font-normal leading-[40px]'>
                We give great customer service
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[12px]'>
                The problem with some online companies is that it’s impossible to speak to a real person. That’s never
                going to be the case with Surely. There’s a human on hand to step in and help out - 24/7, 365 - to make
                sure you’re never left out in the cold. 
              </Text>
              <div className='items-start bg-[#388DD8] w-[200px] h-[1px] mt-[32px]' />
            </Col>
          </Row>
          <Row className='sm:gap-[89px] gap-[50px] mt-[99px] sm:mb-[130px] mb-[70px] xl:pl-[110px]'>
            {isSmallScreen && (
              <img
                src={aboutpage6}
                className=' sm:ml-[0px] mr-[0px] xl:mr-[30px] w-full md:w-[377px]  xl:w-[477px] h-auto xl:h-[252px] xl:mt-[40px]'
              />
            )}
            <View className='flex flex-col mt-[27.5px]  mx-auto px-[12px] xl:px-0 sm:mx-0 xl:w-[511px] items-start'>
              <Text className='text-left text-[#323C58] rubik text-[32px] font-normal leading-[40px]'>
                We promote excellence
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[12px]'>
                There are 400,000 people with an SIA license in the UK. We know that many of these individuals are
                passionate about their job. Surely provides a framework that proactively encourages greater
                professionalism by both security operatives and clients. 
              </Text>
              <div className='items-start bg-[#388DD8] w-[200px] h-[1px] mt-[28px]' />
            </View>
            {!isSmallScreen && (
              <img src={aboutpage6} className=' sm:ml-[0px] mr-[0px] sm:mr-[30px] sm:w-[477px] h-auto sm:h-[252px]' />
            )}
          </Row>
        </Container>
      </Section>
      {/* </View> */}
      <Section className='w-full sm:pt-[110px] pt-[110px]  bg-[#F4F5F7]'>
        <Container className='mx-[180px] max-w-[1100px] mx-auto'>
          <Row className=' sm:gap-[89px] gap-[50px] xl:pl-[110px]'>
            <img src={aboutpage7} className='flex flex-col w-full sm:w-[350px] xl:w-auto  h-auto' />
            <Col className=' mt-[60px] px-[12px] xl:px-0 mx-auto xl:w-[482px] sm:ml-0 items-start xl:mr-[30px]'>
              <Text className='text-left text-[#323C58] rubik text-[32px] font-normal leading-[40px]'>
                We’re all about fair play
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[12px]'>
                The best clients and the best security operatives all want to work in a positive environment. That’s why
                everyone who joins Surely will be asked to sign our Code of Conduct, to demonstrate their commitment to
                act responsibly and respectfully at all times. 
              </Text>
              <div className='items-start bg-[#388DD8] w-[200px] h-[1px] mt-[32px]' />
            </Col>
          </Row>
          <Row className='xl:pl-[110px] sm:gap-[89px] gap-[50px] sm:mb-[118px] sm:mt-[99px] mt-[60px]'>
            {isSmallScreen && (
              <img src={aboutpage8} className=' w-full sm:w-[377px] sm:w-auto xl:mr-[30px] mt-[20px] sm:mt-[10px]' />
            )}

            <Col className='sm:mt-[70px] px-[12px] xl:px-0 mx-auto sm:mx-0 xl:w-[511px] items-start mb-10 sm:mb-0'>
              <Text className='text-left text-[#323C58] rubik text-[32px] font-normal leading-[56px]'>
                We drive positive change
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] mt-[8px]'>
                It’s not enough just to keep things the same. We want to raise the bar in every possible way. Life is
                hard enough without making everything more difficult than it needs to be. One example of this is our
                Inclusivity Pledge, focusing on the Equality Act 2010. 
              </Text>
              <div className='items-start bg-[#388DD8] w-[200px] h-[1px] mt-[32px]' />
            </Col>

            {!isSmallScreen && <img src={aboutpage8} className='mr-[0px] sm:mr-[30px]' />}
          </Row>
        </Container>
      </Section>
      {/* <View className=' w-full max-w-[1320px] mx-auto'>
        <Text className='!text-[#323C58] font-rufina-stencil text-[32px] leading-[56px] mt-[50px] text-center'>
          More reasons to choose Surely
        </Text>
        <View className='flex flex-col sm:flex-row w-full gap-[20px] h-auto sm:w-[1320px] ml-[-5%] sm:ml-[0px] justify-between mb-[30px] sm:mt-[44.4px]'>
          <div
            className='rounded-[8px] bg-left-top flex flex-col justify-between w-full h-[212px] md:w-[312px] md:h-[312px] mx-4 md:mx-auto bg-cover bg-no-repeat bg-center cursor-pointer '
            style={{
              backgroundImage: `linear-gradient(180deg, #323C58 0%, rgba(32, 39, 57, 0.00) 64.76%), url(${aboutpage9})`,
              backgroundRepeat: 'no-repeat',
              backgroundColor: 'lightgray',
            }}
            onClick={() => navigate('/security-matters-archive')}
          >
            <View className='flex flex-row justify-between ml-[24px] mt-[40px] mr-[24px] mx-[10px]'>
              <Text className='text-[#FFF] rubik text-[17px] font-medium leading-[24px]'>Security Matters</Text>
              <span className='material-icons-outlined text-[8px] sm:text-[17px] text-[#FFF]'>arrow_forward_ios</span>
            </View>
          </div>
          <div
            className='rounded-[8px] bg-left-top flex flex-col justify-between w-full h-[212px] md:w-[312px] md:h-[312px] mx-4 md:mx-auto bg-cover bg-no-repeat bg-center mt-[20px] sm:mt-[0px] cursor-pointer '
            style={{
              backgroundImage: `linear-gradient(180deg, #323C58 0%, rgba(32, 39, 57, 0.00) 64.76%), url(${aboutpage10})`,
              backgroundRepeat: 'no-repeat',
              backgroundColor: 'lightgray',
            }}
            onClick={() => navigate('/code-of-conduct')}
          >
            <View className='flex flex-row justify-between ml-[24px] mt-[40px] mr-[24px] mx-[10px]'>
              <Text className='text-[#FFF] rubik text-[17px] font-medium leading-[24px]'>Code of Conduct</Text>
              <span className='material-icons-outlined text-[8px] sm:text-[17px] text-[#FFF]'>arrow_forward_ios</span>
            </View>
          </div>
          <div
            className='rounded-[8px] bg-left-top flex flex-col justify-between w-full h-[212px] md:w-[312px] md:h-[312px] mx-4 md:mx-auto bg-cover bg-no-repeat bg-center mt-[20px] sm:mt-[0px] cursor-pointer'
            style={{
              backgroundImage: `linear-gradient(180deg, #323C58 0%, rgba(32, 39, 57, 0.00) 64.76%), url(${aboutpage11})`,
              backgroundRepeat: 'no-repeat',
              backgroundColor: 'lightgray',
            }}
            onClick={() => navigate('/useful-resources')}
          >
            <View className='flex flex-row justify-between ml-[24px] mt-[40px] mr-[24px] mx-[10px]'>
              <Text className='text-[#FFF] rubik text-[17px] font-medium leading-[24px]'>Useful Resources</Text>
              <span className='material-icons-outlined text-[8px] sm:text-[17px] text-[#FFF]'>arrow_forward_ios</span>
            </View>
          </div>
          <div
            className='rounded-[8px] bg-left-top flex flex-col justify-between w-full h-[212px] md:w-[312px] md:h-[312px] mx-4 md:mx-auto bg-cover bg-no-repeat bg-center mt-[20px] sm:mt-[0px] cursor-pointer'
            style={{
              backgroundImage: `linear-gradient(180deg, #323C58 0%, rgba(32, 39, 57, 0.00) 64.76%), url(${aboutpage12})`,
              backgroundRepeat: 'no-repeat',
              backgroundColor: 'lightgray',
            }}
            onClick={() => navigate('/surely-pro-films')}
          >
            <View className='flex flex-row justify-between ml-[24px] mt-[40px] mr-[24px] mx-[10px]'>
              <Text className='text-[#FFF] rubik text-[17px] font-medium leading-[24px]'>SurelyPlus Films</Text>
              <span className='material-icons-outlined text-[8px] sm:text-[17px] text-[#FFF]'>arrow_forward_ios</span>
            </View>
          </div>
        </View>
      </View> */}
      <div
        className='bg-left-top flex mt-[62px] justify-between w-full h-[480px] md:w-full md:h-[315px] mx-0 md:mx-auto bg-cover bg-no-repeat bg-center mt-[20px] sm:mt-[110px]'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Footer />
    </Page>
  );
};

export default AboutUsPage;
