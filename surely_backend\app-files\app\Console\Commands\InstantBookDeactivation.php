<?php

namespace App\Console\Commands;

use App\Models\MobileUser;
use Illuminate\Console\Command;

class InstantBookDeactivation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instant-book-deactivation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Instant book deactivation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        MobileUser::query()->update(['instant_book' => false]);
        $this->info('Executed');
    }
}
