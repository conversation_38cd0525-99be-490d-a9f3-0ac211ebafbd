// @ts-nocheck
import React, { useState, useMemo, useEffect, useRef, ChangeEvent, useContext } from 'react';
import { AppContext } from 'src/context/AppContext';
import { Text, View, Button, Tabs, Divider, Card, Select, TextArea, TextField, useToggle, useToast, Image, Breadcrumbs } from 'reshaped';
import { useNavigate, useLocation } from 'react-router-dom';
import { addProfileDetails, getProfileDetails } from 'src/services/settings';
import AddLanguageProfileDetails from './ModalsOperatorSettings/AddLanguageProfileDetails';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { headerLogo } from 'src/assets/images';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';

interface LanguageData {
  language: any;
  level: any;
}

interface LanguageDataPost {
  language_id: number;
  level_id: number;
}

interface AdditionalPhotos {
  [key: number]: string;
}

const OperatorSettingsProfileDetails: React.FC = () => {
  const navigate = useNavigate();
  const { fetchAppData } = useContext(AppContext);

  const { active, activate, deactivate } = useToggle(false);
  const location = useLocation();
  const redirectActiveTab = useMemo(() => location?.state?.activeTab, []);
  const toast = useToast();

  const [activeTab, setActiveTab] = useState(redirectActiveTab || '0');

  // const [profileTitle, setProfileTitle] = useState<string | undefined>();
  // const [profileDescription, setProfileDescription] = useState<
  //   string | undefined
  // >();
  const [profileTitle, setProfileTitle] = useState<string>('');
  const [profileDescription, setProfileDescription] = useState<string>('');

  const [industrySectors, setIndustrySectors] = useState<string[]>([]);
  const placeholderOption = {
    label: 'Select or type...',
    value: '',
  };

  const [profilePhoto1, setProfilePhoto1] = useState<string[]>([]);
  const [additionalPictures, setAdditionalPictures] = useState<AdditionalPhotos>({ 0: '', 1: '', 2: '' });
  const [profileVideo1, setProfileVideo1] = useState<string[]>([]);

  const profilePhoto = profilePhoto1[0];
  const profileVideo = profileVideo1[0];

  const [languages, setLanguages] = useState<LanguageData[]>([]);
  const [languagesData, setLanguagesData] = useState<LanguageDataPost[]>([]);

  const [testlang, settestlang] = useState<any>([]);
  const [recording, setRecording] = useState(false);

  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [recorder, setRecorder] = useState<MediaRecorder | null>(null);
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([]);
  const [capturedVideo, setCapturedVideo] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const [isSaving, setIsSaving] = useState(false);

  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValueTitle = event.target.value;

    if (inputValueTitle?.length <= 30) {
      setProfileTitle(inputValueTitle);
    }
  };

  const openCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      setStream(cameraStream);
      setIsCameraOpen(true);
    } catch (error) {
      console.error('Error accessing camera:', error);
    }
  };

  const startRecording = () => {
    if (stream) {
      const mediaRecorder = new MediaRecorder(stream, { audio: true, video: true });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const recordedBlob = new Blob(recordedChunks, { type: 'video/webm' });
        const videoURL = URL.createObjectURL(recordedBlob);
        setCapturedVideo(videoURL);
        recordedChunks.length = 0;

        const reader = new FileReader();
        reader.onloadend = () => {
          const base64Video = reader.result as string;
          setProfileVideo1([base64Video]);
        };
        reader.readAsDataURL(recordedBlob);
      };

      setRecorder(mediaRecorder);
      mediaRecorder.start();
    }
  };

  const stopRecording = () => {
    if (recorder && recorder.state === 'recording') {
      recorder.stop();
      closeCamera();
    }
  };

  const closeCamera = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    setStream(null);
    setIsCameraOpen(false);
  };

  const toggleRecording = () => {
    if (recording) {
      stopRecording();
    } else {
      setCapturedVideo(null);
      startRecording();
    }
    setRecording(!recording);
  };

  useEffect(() => {
    if (isCameraOpen && stream) {
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    }
  }, [isCameraOpen, stream]);

  const addLanguage = (data: any) => {
    const { language, level } = data;
    settestlang((prevData: any) => [
      ...prevData,
      {
        language: language.name,
        language_id: language.id,
        level: level.name,
        level_id: level.id,
      },
    ]);
  };

  const deleteLanguage = (index: number) => {
    settestlang((prevData: any) => {
      const updatedData = [...prevData];
      updatedData.splice(index, 1);
      return updatedData;
    });
  };

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const handleSelectIndustry = (industry: string) => {
    if (!industrySectors.includes(industry)) {
      setIndustrySectors((prevSectors) => [...prevSectors, industry]);
    }
  };

  const handleRemoveIndustry = (industry: string) => {
    setIndustrySectors((prevSectors) => prevSectors.filter((selectedIndustry) => selectedIndustry !== industry));
  };

  const clearAllSelectedIndustries = () => {
    setIndustrySectors([]);
  };

  const [SIALicense, setSIALicense] = useState([]);

  const handleButtonDragStart = (e: any, index: any) => {
    e.dataTransfer.setData('text/plain', index);
  };

  const handleButtonDragOver = (e: any) => {
    e.preventDefault();
  };

  const handleButtonDrop = (e: any, newIndex: any) => {
    const draggedIndex = e.dataTransfer.getData('text/plain');
    const updatedSIALicense = [...SIALicense];
    const [draggedButton] = updatedSIALicense.splice(draggedIndex, 1);
    updatedSIALicense.splice(newIndex, 0, draggedButton);
    setSIALicense(updatedSIALicense);
  };

  const renderButtons = (
    <div className='flex flex-row  flex-wrap gap-2 '>
      {SIALicense.map((license, index) => (
        <div
          key={license}
          draggable
          onDragStart={(e) => handleButtonDragStart(e, index)}
          onDragOver={(e) => handleButtonDragOver(e)}
          onDrop={(e) => handleButtonDrop(e, index)}
        >
          <Button
            size='small'
            rounded={true}
            elevated={false}
            className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs !text-[#323c58]'
          >
            <Text color='positive' className='flex items-center gap-1'>
              <span className='material-icons text-[14px]'>star</span>
              {license}
            </Text>
          </Button>
        </div>
      ))}
    </div>
  );

  const handleImageUploadFirst = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfilePhoto1([reader.result as string]);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImageFirst = () => {
    setProfilePhoto1([]);
  };

  const handleFirstButtonClick = () => {
    const input = document.getElementById('firstFileInput') as HTMLInputElement;
    input.onchange = async () => {
      const files = input.files;
      if (files && files[0].size > 5242880) {
        toast.show({
          title: 'Error',
          text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }
      if (files && files.length > 0) {
        const base64 = await readFileAsBase64(files[0]);
        setProfilePhoto1([base64]);
      }
    };
    input.click();
  };

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
          resolve(e.target.result);
        } else {
          reject(new Error('Failed to read file as base64.'));
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleImageUploadSecond = (index: number) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const images = Object.assign({ ...additionalPictures }, { [index]: reader.result });
        setAdditionalPictures(images);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDropImageSecond = (index: number) => async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file.size > 5242880) {
        toast.show({
          title: 'Error',
          text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      } else {
        const reader = new FileReader();
        reader.onloadend = () => {
          const images = Object.assign({ ...additionalPictures }, { [index]: reader.result });
          setAdditionalPictures(images);
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const handleRemoveImageSecond = (index: number) => () => {
    const newImages = { ...additionalPictures };
    newImages[index] = '';
    setAdditionalPictures(newImages);
  };

  const handleThirdButtonClick = () => {
    const fileInput = document.getElementById('thirdFileInput') as HTMLInputElement;

    if (fileInput) {
      fileInput.click();

      fileInput.addEventListener('change', (event) => {
        const selectedFile = (event.target as HTMLInputElement)?.files?.[0];

        if (selectedFile) {
          const fileSizeInMB = selectedFile.size / (1024 * 1024);

          if (fileSizeInMB > 150) {
            alert('File size must be less than 150MB.');
            fileInput.value = '';
          }
        }
      });
    }
  };

  const handleImageUploadThird = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileVideo1([reader.result as string]);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImageThird = () => {
    setProfileVideo1([]);
  };

  useEffect(() => {
    getProfileDetails().then((data: any) => {
      if (data.profilePhoto) {
        if (!data.profilePhoto?.includes('app.surelysecurity.com/storage/')) {
          data.profilePhoto = `https://app.surelysecurity.com/storage/${data.profilePhoto}`;
        }
      }
      if (data.profileVideo) {
        if (!data.profileVideo?.includes('app.surelysecurity.com/storage/')) {
          data.profileVideo = `https://app.surelysecurity.com/storage/${data.profileVideo}`;
        }
      }

      if (data.additionalPictures?.length !== 0) {
        data.additionalPictures?.forEach((pic: string, index: number) =>
          setAdditionalPictures((prev) => ({
            ...prev,
            [index]: pic?.length !== 0 ? `https://app.surelysecurity.com/storage/${pic}` : '',
          })),
        );
      }

      data.profilePhoto && setProfilePhoto1([data.profilePhoto]);
      data.profileVideo && setProfileVideo1([data.profileVideo]);
      setProfileTitle(data.profileTitle);
      setProfileDescription(data.profileDescription);
      setSIALicense(data.SIALicense || []);
      settestlang(data.languages);
      // setLanguagesData(data.languages || []);
      setIndustrySectors(data.industrySectors || []);
    });
  }, []);

  const handleThirdTab = async () => {
    setIsSaving(true);
    const thirdTabSettings = {
      profileTitle,
      profileDescription,
      industrySectors,
      SIALicense,
      profilePhoto,
      additionalPictures,
      profileVideo,
      languages: testlang,
    };

    await addProfileDetails(thirdTabSettings)
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
        // if (profilePhoto) {
        //   toast.show({
        //     title: 'Congratulations!',
        //     text: 'Your photo has been successfully uploaded.',
        //     startSlot: <Image src={surleyicon} className='w-[30px] h-[30px]' />,
        //   });
        // }
        // if (profileVideo) {
        //   toast.show({
        //     title: 'Congratulations!',
        //     text: 'Your video has been successfully uploaded.',
        //     startSlot: <Image src={surleyicon} className='w-[30px] h-[30px]' />,
        //   });
        // }
      });

    closeCamera();
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  const handleDescriptionChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setProfileDescription(event.target.value);
  };

  const handleDropProfilePhoto = async (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    const fileDataArray = [];
    for (const file of files) {
      if (file.size > 5242880) {
        toast.show({
          title: 'Error',
          text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }
      const base64 = await readFileAsBase64(file);
      fileDataArray.push(base64);
    }
    setProfilePhoto1(fileDataArray);
  };

  const handleDropProfileVideo = async (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    const fileDataArray = [];
    for (const file of files) {
      const base64 = await readFileAsBase64(file);
      fileDataArray.push(base64);
    }
    setProfileVideo1(fileDataArray);
  };

  return (
    <View className=' mx-auto w-full overflow-hidden px-[12px] sm:w-auto md:px-0'>
      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='rubik text-[16px] font-medium text-[#1A1A1A]'>Profile Details</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='mb-[16px] flex items-center p-0 lg:mb-5'>
        <Text className='text-foreground-neutral font-rufina-stencil text-[#323C58] lg:text-[32px] xl:leading-10'>Profile details</Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          <Tabs.Item value='0'>
            <span className='rubik text-[14px] text-[#14171F]'>Skills</span>
          </Tabs.Item>
          <Tabs.Item value='1'>
            <span className='rubik text-[14px] text-[#14171F]'>Photo & Video</span>
          </Tabs.Item>
          <Tabs.Item value='2'>
            <span className='rubik text-[14px] text-[#14171F]'>Description</span>
          </Tabs.Item>
          <Tabs.Item value='3'>
            <span className='rubik text-[14px] text-[#14171F]'>Languages</span>
          </Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-6 flex flex-col gap-4'>
            <div className='flex flex-col gap-1'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>SIA license (drag to change order)</Text>

              <Card className=' w-full overflow-hidden p-2 sm:w-[536px]'>{renderButtons}</Card>
            </div>
            <div className='gap1 flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Industry sectors</Text>

              <View className=''>
                <Select
                  className='mt-2 h-[auto] w-full rounded-md border  p-2 sm:w-[536px]'
                  name='industry_sectors'
                  placeholder={industrySectors?.length > 0 ? '' : placeholderOption.label}
                  options={[
                    {
                      label: 'Bars, Clubs & Restaurants',
                      value: 'Bars, Clubs & Restaurants',
                    },
                    {
                      label: 'Events & Festivals',
                      value: 'Events & Festivals',
                    },
                    { label: 'Private Hire', value: 'Private Hire' },
                    { label: 'Film, TV & Media', value: 'Film, TV & Media' },
                    { label: 'Rail, Air & Road', value: 'Rail, Air & Road' },
                    {
                      label: 'Commercial Offices',
                      value: 'Commercial Offices',
                    },
                    { label: 'Construction', value: 'Construction' },
                    { label: 'Education', value: 'Education' },
                    {
                      label: 'Financial & Banking',
                      value: 'Financial & Banking',
                    },
                    { label: 'Government', value: 'Government' },
                    { label: 'Healthcare', value: 'Healthcare' },
                    {
                      label: 'High Street Retail',
                      value: 'High Street Retail',
                    },
                    { label: 'Other', value: 'Other' },
                  ]}
                  onChange={(selectedOption: any) => {
                    if (selectedOption.value !== '') {
                      handleSelectIndustry(selectedOption.value);
                    }
                  }}
                  startSlot={
                    <>
                      <div className='w-[170px] gap-2  sm:w-[250px]'>
                        {industrySectors.map((selectedIndustry) => (
                          <Button
                            key={selectedIndustry}
                            size='small'
                            rounded={true}
                            elevated={false}
                            onClick={() => handleRemoveIndustry(selectedIndustry)}
                            className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs'
                          >
                            <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedIndustry}</Text>
                          </Button>
                        ))}
                      </div>
                      {industrySectors?.length > 0 && (
                        <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedIndustries}>
                          Clear all
                        </Button>
                      )}
                    </>
                  }
                />
              </View>
            </div>
            <Divider className='h-[1px] w-full'></Divider>

            <View className='flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleThirdTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {activeTab === '1' && (
        <View className='flex flex-col-reverse justify-between lg:flex-row'>
          <View className='mt-6 flex flex-col sm:w-[536px]'>
            <div className='flex flex-col gap-2 '>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Upload your up-to-date profile picture</Text>
              <View className='flex flex-col'>
                <View className='flex flex-row items-center gap-3'>
                  <Button
                    variant='outline'
                    icon={() => <span className='material-icons-outlined -mt-1 align-middle text-black'>person</span>}
                    onClick={() => {}}
                    className='flex h-16 w-16 items-center justify-center rounded-full !bg-gray-300 '
                  ></Button>
                  <div
                    className='w-full'
                    onDrop={handleDropProfilePhoto}
                    onDragOver={(event) => {
                      event.preventDefault();
                      event.stopPropagation();
                    }}
                  >
                    <Button
                      variant='outline'
                      onClick={handleFirstButtonClick}
                      className='flex h-[60px] w-full max-w-[250px] items-center justify-center gap-2 rounded-lg sm:max-w-[452px]'
                    >
                      <span className='material-icons-outlined mr-2 w-5 align-middle'>upload_2</span>
                      <span className='align-middle font-medium'>
                        Drop new photo or
                        <span className='text-[#0B80E7]'> browse</span>
                      </span>
                    </Button>
                  </div>
                </View>
                {profilePhoto1?.length > 0 ? (
                  <div className='relative ml-[15%] mt-[20px] h-[136px] w-[170px] sm:ml-[35%] sm:mt-[10px]'>
                    <img src={profilePhoto1[0]} alt='First Image' className='h-[136px] rounded-md object-cover sm:w-[170px]' />
                    <button
                      className='absolute right-1 top-2 flex h-[22px] w-[22px] -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                      onClick={handleRemoveImageFirst}
                    >
                      <span className='material-icons text-[16px]'>close</span>
                    </button>
                  </div>
                ) : (
                  <input type='file' accept='image/*' onChange={handleImageUploadFirst} style={{ display: 'none' }} id='firstFileInput' />
                )}
              </View>
            </div>
            <Text className='text-neutral rubik mt-5 font-medium leading-4 text-[#1A1A1A]'>Your additional pictures</Text>
            <View className=' sm:w-[536px]'>
              <View className='mt-3 flex flex-col items-center justify-between gap-3 sm:flex-row sm:items-start'>
                {Object.keys(additionalPictures)?.length &&
                  Object.entries(additionalPictures).map((pic, index) => (
                    <div
                      key={index}
                      className='relative w-[170px] sm:w-auto'
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={handleDropImageSecond(index)}
                    >
                      {pic[1]?.length !== 0 ? (
                        <div className='relative'>
                          <img
                            src={pic[1] || ''}
                            alt={`Uploaded Job ${index + 1}`}
                            className='h-[120px] w-full rounded-md sm:h-[136px] sm:w-[170px] sm:object-cover'
                          />
                          <button
                            className='absolute right-1 top-1 flex h-[22px] w-[22px] items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                            onClick={handleRemoveImageSecond(index)}
                          >
                            <span className='material-icons text-[16px]'>close</span>
                          </button>
                        </div>
                      ) : (
                        <div
                          className='flex h-[120px] w-[110px] items-center justify-center rounded-lg border border-gray-300 bg-[#DBDFEA] sm:h-[136px] sm:w-[170px]'
                          onDragOver={(event) => event.preventDefault()}
                          onDrop={handleDropImageSecond(index)}
                        >
                          <label htmlFor={`imageInput${index}`}>
                            <span className='material-icons text-[30px]'>add</span>
                          </label>
                          <input
                            id={`imageInput${index}`}
                            type='file'
                            accept='image/*'
                            className='hidden'
                            onChange={handleImageUploadSecond(index)}
                          />
                        </div>
                      )}
                    </div>
                  ))}
                {Object.keys(additionalPictures)?.length === 0 &&
                  [0, 1, 2].map((_, i) => (
                    <div className='flex h-[136px] w-[170px] items-center justify-center rounded-md border border-gray-300'>
                      <label htmlFor={`imageInput${i}`}>
                        <span className='material-icons text-[30px]'>add</span>
                      </label>
                      <input id={`imageInput${i}`} type='file' accept='image/*' className='hidden' onChange={handleImageUploadSecond(i)} />
                    </div>
                  ))}
              </View>
            </View>

            <Text className='text-neutral rubik mt-6 font-medium leading-4 text-[#1A1A1A]'>Upload your profile video</Text>
            <View className='mt-2 flex flex-col  justify-between sm:-ml-[0px] sm:flex-row'>
              <View className='flex flex-col justify-end sm:justify-start '>
                <div
                  className='w-full'
                  onDrop={handleDropProfileVideo}
                  onDragOver={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                  }}
                >
                  <Button
                    variant='outline'
                    onClick={handleThirdButtonClick}
                    className='border-neutral bg-background-base rubik flex items-center justify-center gap-2 rounded-lg border !border-[#DFE2EA] px-4 py-[18px] text-[15px] font-medium sm:w-[260px]'
                  >
                    <span className='material-icons-outlined mr-2 w-5 align-middle'>upload_2</span>
                    <span className='align-middle font-medium'>
                      Drop or <span className='text-[#0B80E7]'>browse</span>
                    </span>
                  </Button>
                </div>
                <View className='my-[12px] mb-3 flex items-center'>
                  <hr className='bg-neutral-faded mr-2 flex-grow' />
                  <span className='rubik text-black-white-black my-0.5 w-[37px] text-center text-sm'>OR</span>
                  <hr className='bg-neutral-faded ml-2 flex-grow' />
                </View>
                {!isCameraOpen ? (
                  <Button
                    variant='outline'
                    onClick={openCamera}
                    className='border-neutral bg-background-base rubik flex items-center justify-center gap-2 rounded-lg border !border-[#DFE2EA] px-4 py-[18px] text-[15px] font-medium sm:w-[260px]'
                  >
                    <span className='material-icons-outlined mr-2 w-5 align-middle'>videocam</span>
                    <span className='align-middle'>Record now</span>
                  </Button>
                ) : (
                  <Button
                    variant='outline'
                    onClick={toggleRecording}
                    className='border-neutral bg-background-base rubik flex items-center justify-center gap-2 rounded-lg border !border-[#DFE2EA] px-4 py-[18px] text-[15px] font-medium sm:w-[260px]'
                  >
                    <span className='material-icons-outlined mr-2 align-middle text-[#0B80E7]'>videocam</span>
                    <span className='align-middle'>{recording ? 'Stop recording' : 'Start recording'}</span>
                  </Button>
                )}
              </View>
              <div className='relative mt-[20px] sm:mt-0'>
                {profileVideo1?.length === null && <div className='hidden' />}
                {profileVideo1?.length > 0 && (
                  <button
                    className='absolute right-0 top-0 z-50 flex h-[22px] w-[22px] cursor-pointer items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                    onClick={handleRemoveImageThird}
                  >
                    <span className='material-icons text-[16px]'>close</span>
                  </button>
                )}
                {profileVideo1?.length !== 0 ? (
                  <video src={profileVideo1[0]} controls className='h-[185px] w-full sm:w-[262px]'>
                    Your browser does not support the video tag.
                  </video>
                ) : (
                  <input type='file' accept='video/*' onChange={handleImageUploadThird} style={{ display: 'none' }} id='thirdFileInput' />
                )}
              </div>
              <div>
                {isCameraOpen && (
                  <div className='relative'>
                    <button
                      className='absolute right-1 top-1 z-50 flex h-[22px] w-[22px] cursor-pointer items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                      onClick={closeCamera}
                    >
                      <span className='material-icons text-[16px]'>close</span>
                    </button>
                    <video ref={videoRef} id='camera-feed' className='max-h-[170px] w-full' autoPlay playsInline></video>
                  </div>
                )}
              </div>
            </View>
            <View className='mt-2 w-auto sm:w-[536px]'>
              <Text className='text-neutral-faded rubik text-[14px] font-normal leading-5'>
                Upload or record a short video, maximum 15 seconds. The file should have not more than 150MB.
              </Text>
              <Divider className='my-4 h-[1px] w-full'></Divider>

              <View className='flex flex-row justify-between'>
                <Button
                  variant='outline'
                  icon={() => <span className='material-icons -mt-1'>clear</span>}
                  onClick={() => navigate('/operator-settings')}
                  className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    handleThirdTab().then(() => {
                      toast.show({
                        title: 'Done!',
                        text: 'You have updated your profile',
                        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                      });
                    });
                  }}
                  className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                    isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                  } h-[48px] sm:w-[260px]`}
                >
                  {isSaving ? 'Saving...' : 'Save settings'}
                </Button>
              </View>
            </View>
          </View>
          <View className='mb-0 ml-[0px] mt-[15px] flex flex-col gap-[14px] sm:mb-[10px] sm:mt-[10px] sm:w-[313px] lg:ml-[135px]'>
            <Image src={headerLogo} className='ml-[-4px] ml-[-5px] h-[41.274px] w-[109.76px] flex-shrink-0' />
            <Text className='rubik mt-[10px] text-[14px] font-normal leading-[20px] text-[#3C455D] sm:mt-[14px]'>
              Please ensure that your profile image is a minimum of 300 by 300 pixels in size. Opt for a simple background and avoid excessive
              shadows. The image should prominently display your face from the front and it would be wonderful if you could wear a smile. Both a good
              profile photo and an engaging presentation video are essential for enhancing your online presence. Keep in mind that the video's maximum
              duration is 15 seconds, and the file size should not exceed 150 MB.
            </Text>
          </View>
        </View>
      )}

      {activeTab === '2' && (
        <View className='flex flex-col-reverse justify-between lg:flex-row'>
          <View className='mt-4 flex h-auto flex-col sm:w-[536px]'>
            <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Profile title</Text>
            <div className='mt-1 flex  flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] sm:w-[536px]'>
              <input
                name='text'
                className=' rubik border-none bg-transparent p-2 text-[14px] text-[#3C455D] outline-none sm:w-[536px] '
                value={profileTitle}
                onChange={handleTitleChange}
                maxLength={30}
              />
              <p className='mb-[1px] mr-[5px] text-right text-[#999999]'>{30 - (profileTitle?.length || 0)} characters left</p>
            </div>
            <Text className='text-neutral rubik mt-4 font-medium leading-4 text-[#1A1A1A]'>Profile description</Text>

            <div className='mt-1  flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] sm:w-[536px]'>
              <textarea
                name='text'
                className=' rubik border-none bg-transparent p-2 text-[14px] text-[#3C455D] outline-none sm:w-[534px]'
                value={profileDescription}
                onChange={handleDescriptionChange}
                maxLength={600}
                rows={12}
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                  resize: 'none',
                }}
              />

              <p className='mb-[1px] mr-[5px] text-right text-[#999999]'>{600 - (profileDescription?.length || 0)} characters left</p>
            </div>

            <Divider className='my-4 h-[1px] w-full'></Divider>

            <View className='flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleThirdTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex flex-col gap-[14px] sm:w-[313px] lg:ml-[135px] lg:mt-[0]'>
            <Image src={headerLogo} className='ml-[-5px] h-[41.274px] w-[109.76px] flex-shrink-0' />
            <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D] '>
              In your profile description, it's essential to provide a concise and engaging overview of yourself. You should highlight your
              experience, qualifications and soft skills.
            </Text>
          </View>
        </View>
      )}

      {activeTab === '3' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='flex flex-col sm:w-[536px]'>
            <button onClick={activate} className='btn-no-hover flex w-[134px] items-center'>
              <div className='flex flex-row gap-1'>
                <div className='mt-1'>
                  <svg xmlns='http://www.w3.org/2000/svg' width='17' height='16' viewBox='0 0 17 16' fill='none'>
                    <path
                      d='M15.9199 9.00781H9.91992V15.0078H7.91992V9.00781H1.91992V7.00781H7.91992V1.00781H9.91992V7.00781H15.9199V9.00781Z'
                      fill='#0B80E7'
                    />
                  </svg>
                </div>
                <Text className='rubik pl-[4px] text-[16px] font-medium leading-[24px] text-[#323C58]'>Add language</Text>
              </div>
            </button>

            <AddLanguageProfileDetails active={active} deactivate={deactivate} addLanguage={addLanguage} />
            <View className='mt-[12px] flex flex-col gap-1'>
              {testlang
                .slice()
                .sort((a: any, b: any) => a.language.localeCompare(b.language))
                .map((data: any, index: any) => (
                  <div key={index} className='flex items-center justify-between'>
                    <div className='flex flex-col py-1'>
                      <Text className='rubik text-[15px] font-normal leading-5 !text-[#1A1A1A]'>{data.language}</Text>
                      <Text className='rubik text-[14px] font-normal leading-5 !text-[#14171F]'>{data.level}</Text>
                    </div>
                    <div className='flex flex-row gap-[5px]'>
                      {/* <Button
                      variant='outline'
                      icon={() => (
                        <span className='material-icons align-middle text-[#0B80E7] text-[17px] !m-0 !-mt-0.3'>
                          edit
                        </span>
                      )}
                      className='flex items-center justify-center w-[78px] h-[28px] !bg-[#fff]'
                    >
                      <Text className='align-middle text-[14px] rubik text-[#0B80E7]'>
                      Edit
                      </Text>
                    </Button> */}
                      <Button
                        onClick={() => deleteLanguage(index)}
                        variant='outline'
                        icon={() => <span className='material-icons !m-0 !-mt-0.5 align-middle text-[20px] text-[#CB101D]'>close</span>}
                        className='flex h-[28px] w-[78px] items-center justify-center !bg-[#fff]'
                      >
                        <Text className='rubik align-middle text-[14px] text-[#CB101D]'>Delete</Text>
                      </Button>
                    </div>
                  </div>
                ))}
            </View>
            <Divider className='my-4 h-[1px] w-full'></Divider>

            <View className='flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleThirdTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
    </View>
  );
};

export default OperatorSettingsProfileDetails;
