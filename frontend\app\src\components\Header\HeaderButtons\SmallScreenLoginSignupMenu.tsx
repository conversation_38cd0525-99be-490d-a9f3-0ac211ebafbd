// @ts-nocheck
import { Button, Text, Image, View, DropdownMenu } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { headerLogo } from '../../../assets/images';
import { useState } from 'react';
import { useModalAction } from 'src/context/ModalContext';

const SmallScreenLoginSignupMenu = () => {
  const navigate = useNavigate();
  const { openModal } = useModalAction();
  const [isOpened, setIsOpened] = useState(false);

  return (
    <DropdownMenu
      onOpen={() => setIsOpened(true)}
      onClose={() => setIsOpened(false)}
      contentGap={6}
      position='bottom'
    >
      <DropdownMenu.Trigger>
        {(attributes) => (
          <Button
            type='submit'
            rounded={true}
            className='w-[40px] h-[40px] !bg-[#323c58] flex items-center justify-center rounded-full'
            attributes={attributes}
          >
            <span className='material-icons-outlined text-[#F4F5F7] text-[20px] mt-[2px]'>
              {isOpened ? 'close' : 'menu'}
            </span>
          </Button>
        )}
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Section>
          <DropdownMenu.Item onClick={() => openModal('LOGIN')}>
            <Text className='flex items-center gap-2 rubik text-[#323c58]'>
              <span className='material-icons-outlined'>logout</span>
              Log in
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>

        <DropdownMenu.Section>
          <DropdownMenu.Item onClick={() => openModal('REGISTER')}>
            <Text className='flex items-center gap-2 rubik text-[#323c58]'>
              <span className='material-icons-outlined'>edit</span>
              Sign up
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};

export default SmallScreenLoginSignupMenu;
