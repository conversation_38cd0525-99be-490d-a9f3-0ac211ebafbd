import React, { useState, useEffect } from 'react';
import { Text, Button, Divider, Modal, Popover, useToast, Image, Switch } from 'reshaped';
import {
  format,
  addMonths,
  subMonths,
  getDay,
  getMonth,
  getYear,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  isSameYear,
  isSameMonth,
  isSameDay,
  addDays,
  isBefore,
  parseISO,
  isPast,
  startOfDay,
} from 'date-fns';
import { enGB } from 'date-fns/locale';
import { editPostJob } from 'src/services/jobs';
import CalendarDayGrid from './CalendarDayGrid';
// import right from '../../assets/icons/calendaricon/right.svg';
// import left from '../../assets/icons/calendaricon/left.svg';
import Right from 'src/assets/icons/calendaricon/right';
import Left from 'src/assets/icons/calendaricon/left';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { HttpClient } from 'src/client/http-client';
import overrideDuplicates from 'src/utils/overrideDuplicateDates';

const sameDayCheck = (date1, date2) =>
  isSameDay(parseISO(date1?.start), parseISO(date2?.start)) && date1.start === date2.start && date1.end === date2.end;

const CalendarEditJobNew = ({ handleClosePopovers, closePopovers, contractId, active, deactivate, selectedDates, handleParentDates, readOnly }) => {
  const today = new Date();
  const toast = useToast();
  const [currentMonthStart, setCurrentMonthStart] = useState(startOfMonth(today));
  const [nextMonthStart, setNextMonthStart] = useState(addMonths(today, 1));
  const [selectedDatesNew, setSelectedDatesNew] = useState([]);
  const [isDateRangeSelected, setIsDateRangeSelected] = useState(false);
  const [rangeStart, setRangeStart] = useState();
  const [rangeEnd, setRangeEnd] = useState();
  const [calendarDays, setCalendarDays] = useState([]);

  useEffect(() => {
    setSelectedDatesNew(selectedDates);
  }, [selectedDates]);

  useEffect(() => {
    getCalendarDays();
  }, [currentMonthStart]);

  const toggleSwitch = () => {
    // if (isDateRangeSelected) {
    //   setRangeStart();
    //   setRangeEnd();
    // }
    setIsDateRangeSelected((prevState) => !prevState);
  };

  const rangeGroup = { isDateRangeSelected, toggleSwitch, rangeStart, rangeEnd };

  const filterUniqueDates = (array) => {
    const uniqueDatesMap = new Map();

    array.forEach((dateObj) => {
      const parsedStartDate = parseISO(dateObj.start);
      const dayKey = parsedStartDate.toISOString().substr(0, 10);

      if (uniqueDatesMap.has(dayKey)) {
        const existingStartDate = parseISO(uniqueDatesMap.get(dayKey).start);
        if (isSameDay(existingStartDate, parsedStartDate)) {
          uniqueDatesMap.set(dayKey, dateObj);
        }
      } else {
        uniqueDatesMap.set(dayKey, dateObj);
      }
    });

    return Array.from(uniqueDatesMap.values());
  };

  const updateContractShifts = async () => {
    const url = `contracts/${contractId}/shifts`;

    const date_range = filterUniqueDates(selectedDatesNew);

    try {
      const response = await HttpClient.put(url, { date_range, message: 'Shift time changed', type: 'shift_update_request' });
      if (!response.error) {
        return response;
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleSelectDatesNew = async (dates) => {
    if (dates?.length) {
      const updatedDates = [...selectedDatesNew, ...dates].map((date) => {
        const startDate = parseISO(date.start);
        const endDate = parseISO(date.end);
        const daysDiff = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
        const totalMinutes = Math.floor((endDate - startDate) / (1000 * 60));
        const totalHours = Math.floor(totalMinutes / 60);
        const remainingMinutes = totalMinutes % 60;

        return {
          ...date,
          start: format(startDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx", { timeZone: 'local' }),
          end: format(endDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx", { timeZone: 'local' }),
          days: daysDiff,
          hours: totalHours,
          minutes: remainingMinutes,
          totalHours: Number((totalHours + remainingMinutes / 60).toFixed(2)),
          duration: `${daysDiff > 0 ? `${daysDiff}d ` : ''}${totalHours % 24}h ${remainingMinutes}m`,
        };
      });

      const filteredDates = updatedDates.reduce((acc, curr) => {
        const existingDateIndex = acc.findIndex((date) => isSameDay(parseISO(date.start), parseISO(curr.start)));
        if (existingDateIndex >= 0) {
          acc[existingDateIndex] = curr;
        } else {
          acc.push(curr);
        }
        return acc;
      }, []);

      setSelectedDatesNew(filteredDates);
    }
    if (dates?.start) {
      const updatedDates = [...selectedDatesNew, dates].map((date) => ({
        ...date,
        start: format(parseISO(date.start), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx", { timeZone: 'local' }),
        end: format(parseISO(date.end), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx", { timeZone: 'local' }),
      }));
      const filteredDates = updatedDates.reduce((acc, curr) => {
        const existingDateIndex = acc.findIndex((date) => isSameDay(parseISO(date.start), parseISO(curr.start)));
        if (existingDateIndex >= 0) {
          acc[existingDateIndex] = curr;
        } else {
          acc.push(curr);
        }
        return acc;
      }, []);

      setSelectedDatesNew(filteredDates);
    }
  };

  const handleRemoveDates = (dates) => {
    if (dates?.length) {
      const filteredDates = selectedDatesNew.filter((date) => {
        return !dates.some((dateToRemove) => {
          return sameDayCheck(date, dateToRemove);
        });
      });
      setSelectedDatesNew(filteredDates);
    }
    if (dates.start && dates.end) {
      const filteredDates = selectedDatesNew.filter((date) => {
        return !sameDayCheck(date, dates);
      });
      setSelectedDatesNew(filteredDates);
    } else {
      toast.show({
        title: 'Invalid dates format.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
  };

  const prevMonth = () => {
    setCurrentMonthStart(subMonths(currentMonthStart, 1));
    setNextMonthStart(subMonths(nextMonthStart, 1));
  };

  const nextMonth = () => {
    setCurrentMonthStart(addMonths(currentMonthStart, 1));
    setNextMonthStart(addMonths(nextMonthStart, 1));
  };

  const getCalendarDays = () => {
    const firstDayOfMonth = startOfMonth(currentMonthStart);

    const lastDayOfMonth = endOfMonth(currentMonthStart);
    const startDate = startOfWeek(firstDayOfMonth, { weekStartsOn: 1 });
    const endDate = endOfWeek(lastDayOfMonth, { weekStartsOn: 1 });

    const calendarDays = [];
    let currentDate = startDate;

    while (currentDate <= endDate) {
      const formattedDate = format(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx", { timeZone: 'local' });
      calendarDays.push(formattedDate);
      currentDate = addDays(currentDate, 1);
    }

    setCalendarDays(calendarDays);
    return calendarDays;
  };

  const handleSubmit = async () => {
    const filteredDates = overrideDuplicates(selectedDatesNew);

    if (contractId) {
      const response = await updateContractShifts(filteredDates);
      if (!response.error) {
        toast.show({
          title: 'Done!',
          text: 'Shift dates updated successfully',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      } else {
        console.error('Error updating shifts:', response.error);
      }
    } else {
      handleParentDates(selectedDatesNew);
    }
  };

  return (
    <Modal
      active={active}
      onClose={() => {
        handleClosePopovers().then(() => {
          setIsDateRangeSelected(false);
          setRangeStart();
          setRangeEnd();
          deactivate();
        });
      }}
      className='rubik !h-[auto] !w-[924px]'
    >
      <div className=''>
        <div className='mx-4 flex items-center justify-between border-b border-b-[#DFE2EA] py-3'>
          <Text className='rubik text-[24px] font-normal text-[#323C58]'>Job days and shift hours</Text>
          {/* <span
            className='material-icons-outlined cursor-pointer'
            onClose={() => {
              handleClosePopovers().then(() => deactivate());
            }}
          >
            close
          </span> */}
        </div>
        <div className='mt-6 flex flex-col items-start justify-between gap-3 px-4 py-3 md:flex-row lg:items-center lg:gap-0'>
          <div className='flex w-full items-center justify-between lg:w-1/2'>
            <div className='flex items-center justify-between'>
              <div className='px-3 py-2.5' onClick={prevMonth}>
                <Left isDisabled={isBefore(currentMonthStart, startOfDay(new Date()))} />
              </div>
              <Text className='rubik w-[156px] text-lg font-normal leading-[28px] text-[#323C58] sm:text-[20px]'>
                <div className='mx-auto w-fit'>{format(currentMonthStart, 'MMMM yyyy')}</div>
              </Text>
              <div className='px-3 py-2.5' onClick={nextMonth}>
                <Right isDisabled={isPast(nextMonthStart)} />
              </div>
            </div>
            <div className='flex w-fit items-center gap-1 sm:gap-2'>
              <span className='w-fit text-[13px] leading-5 text-[#383838]'>Range select</span>
              <Switch name='Date Range' className='' onChange={toggleSwitch} checked={isDateRangeSelected} />
            </div>
          </div>
          <div className='flex gap-5'>
            <div className='flex items-center gap-2'>
              <span className='inline-block h-5 w-[25px] grow-0 rounded-md border border-[#D5D4DF] bg-[#F2F3F7]' />
              <span className='text-[13px] leading-5 text-[#383838]'>Past days</span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='inline-block h-5 w-[25px] grow-0 rounded-md border border-[#0B80E7] bg-[#0B80E7]' />
              <span className='text-[13px] leading-5 text-[#383838]'>Days selected</span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='inline-block h-5 w-[25px] grow-0 rounded-md border border-[#D5D4DF]' />
              <span className='text-[13px] leading-5 text-[#383838]'>Days available</span>
            </div>
          </div>
        </div>
        <div className='border-b-[#DFE2EA border-b pb-6'>
          <CalendarDayGrid
            calendarDays={calendarDays}
            selectedDates={selectedDatesNew}
            rangeGroup={rangeGroup}
            currentMonthStart={currentMonthStart}
            handleSelectDates={handleSelectDatesNew}
            handleRemoveDates={handleRemoveDates}
            closePopovers={closePopovers}
            handleClosePopovers={handleClosePopovers}
            readOnly={readOnly}
          />
        </div>
        <div className='mt-6 flex items-center justify-between gap-4'>
          <Button
            onClick={() => {
              handleClosePopovers().then(() => {
                setIsDateRangeSelected(false);
                setRangeStart();
                setRangeEnd();
                deactivate();
              });
            }}
            className='rubik border-neutral h-[48px] w-full rounded-[8px] border !border-[#DFE2EA] !bg-white px-4 py-2
'
          >
            <div className='flex items-center'>
              <span className='material-icons'>keyboard_arrow_left</span>
              <Text className='rubik text-[16px] font-medium leading-[24px]'>Back</Text>
            </div>
          </Button>
          {!readOnly && (
            <Button
              onClick={() => {
                handleClosePopovers().then(() => {
                  setIsDateRangeSelected(false);
                  setRangeStart();
                  setRangeEnd();
                  handleSubmit();
                  deactivate();
                });
              }}
              className='rubik border-neutral h-[48px] w-full rounded-[8px] border !border-[#0B80E7] !bg-[#0B80E7] px-4 py-2
'
            >
              <Text className='rubik text-[16px] font-medium leading-[24px] !text-[#ffff]'>Confirm</Text>
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default CalendarEditJobNew;
