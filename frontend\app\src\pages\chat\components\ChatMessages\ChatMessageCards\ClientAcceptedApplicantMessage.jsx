import { Button } from 'reshaped';

import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useAuthContext } from 'src/context/AuthContext';
import { useChatContext } from 'src/context/ChatContext';

const ClientAcceptedApplicantMessage = ({ message, fromInvitation }) => {
  const { isClient } = useAuthContext();
  const { currentChat } = useChatContext();

  const nameShown = currentChat?.sender_name;
  const jobName = currentChat?.job?.post_name;

  if (fromInvitation) {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Applied successfully</h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            <span className='font-medium'>{message?.sender?.name}</span> successfully applied to:{' '}
            <span className='font-medium text-[#323C58]'>{jobName}</span>
          </p>
        </div>
      </div>
    );
  }

  else if (!fromInvitation) {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Application accepted</h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            <span className='font-medium'>{nameShown}</span> accepted the application for:{' '}
            <span className='font-medium text-[#323C58]'>{jobName}</span>
          </p>
        </div>
      </div>
    );
  }
};

export default ClientAcceptedApplicantMessage;
