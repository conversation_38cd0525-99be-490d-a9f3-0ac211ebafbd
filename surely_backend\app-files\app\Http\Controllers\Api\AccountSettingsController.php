<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserLanguageLevelResource;
use App\Http\Resources\UserResource;
use App\Models\EmailRequest;
use App\Models\LanguageLevel;
use App\Models\MobileUser;
use App\Models\UserLanguageLevel;
use Illuminate\Http\Request;

class AccountSettingsController extends Controller
{
    use \App\Traits\Helpers;

    public function upload($arrayOfFiles) {
        $data = [];
        foreach ($arrayOfFiles as $key => $file) {
            $fileName = $file->getClientOriginalName();
            $fileExtension = $file->getClientOriginalExtension();
            $name = pathinfo($fileName, PATHINFO_FILENAME);
            $fileName = $name . '_' . uniqid() . '_' . time() . '.' . $fileExtension;
            $path = $file->storeAs($key, $fileName, 'local');
            $data[$key] = $path;
        }

        return $data;
    }

    public function getProfileSettings(): \Illuminate\Http\JsonResponse
    {
        $user = MobileUser::find(auth()->id());
        $data = new UserResource($user);

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => $data
        ]);
    }

    public function updateProfileSettings(Request $request) {

        $user = auth()->user();
        $data = $request->all();

        if ($user->account_type == 1) {
            $data['profile_photo'] = $this->base64Upload('profile_photo', $request->get('profile_photo'));
            $data['profile_video'] = $this->base64Upload('profile_video', $request->get('profile_video'));
        }

        $user->update($data);

        return new UserResource($user);
    }

    public function getAllLanguages(Request $request): \Illuminate\Http\JsonResponse
    {
        $query = LanguageLevel::where('context', $request->context)
            ->select(
                'id',
                'name'
            );
        
        if ($request->context == 'level') {
            $query->orderBy('created_at', 'ASC');
        } else {
            $query->orderBy('name', 'ASC');
        }
            
        $data = $query->get();

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' =>$data
        ]);
    }

    public function getLanguages(): \Illuminate\Http\JsonResponse
    {
        $data = UserLanguageLevel::query()
            ->join('language_level as language', 'user_language_level.language_id', 'language.id')
            ->join('language_level as level', 'user_language_level.level_id', 'level.id')
            ->where('user_language_level.mobile_user_id', auth()->id())
            ->select(
                'user_language_level.id as id',
                'language.name as language',
                'level.name as level'
            )
            ->get();

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' =>$data
        ]);
    }

    public function storeLanguage(Request $request): \Illuminate\Http\JsonResponse
    {
        if(! $request->filled('language_id') || ! $request->filled('level_id')) {
            return response()->json([
                'error' => true,
                'message' => 'Fail!'
            ]);
        }
        $query = UserLanguageLevel::where('language_id', $request->get('language_id'))->where('mobile_user_id', auth()->id())->get();

        if(! $query->isEmpty())
        {
            return response()->json([
                'error' => true,
                'message' => 'Language exists!'
            ]);
        }

        $language = LanguageLevel::where('id', $request->get('language_id'))->where('context', 'language')->first();
        $level = LanguageLevel::where('id', $request->get('level_id'))->where('context', 'level')->first();
        
        if (! $language || ! $level) {
            return response()->json([
                'error' => true,
                'message' => 'Please check language or level!',
            ]);
        }

        $data = [
            'mobile_user_id' => auth()->id(),
            'language_id'  => $request->get('language_id'),
            'level_id' => $request->get('level_id')
        ];

        $userLanguageLevel = UserLanguageLevel::create($data);

        if (! $userLanguageLevel) {
            return response()->json([
                'error' => true,
                'message' => 'Fail! Language or level cannot be stored!',
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => new UserLanguageLevelResource($userLanguageLevel)
        ]);
    }

    public function deleteLanguage($id): \Illuminate\Http\JsonResponse
    {
        $userLanguageLevel = UserLanguageLevel::find($id);

        if (! $userLanguageLevel || ! $userLanguageLevel->delete()) {
            return response()->json([
                'error' => true,
                'message' => 'Fail'
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success'
        ]);
    }

    public function requestEmailChange(Request $request)
    {
        $email = $request->get('new_email');

        if (!$email) {
            return response()->json([
                'error' => true,
                'message' => 'New email should be present in request!',
            ]);
        }

        if ($email == auth()->user()->email) {
            return response()->json([
                'error' => true,
                'message' => 'Email is used!',
            ]);
        }

        if (MobileUser::where('email', $email)->first()) {
            return response()->json([
                'error' => true,
                'message' => 'Email is used!',
            ]);
        }

        $row = EmailRequest::where('new_email', $email)->first();
        
        if ($row) {
            return response()->json([
                'error' => true,
                'message' => 'You have requested once email change with this email!',
            ]);
        }
        
        $emailRequest = EmailRequest::create([
            'user_id' => auth()->id(),
            'old_email' => auth()->user()->email,
            'new_email' => $email,
            'old_email_verified' => auth()->user()->email_verified,
            'new_email_verified' => false,
        ]);

        if (!$emailRequest) {
            return response()->json([
                'error' => true,
                'message' => 'Request for email is not sent successfully!',
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Request for email change is sent successfuly!',
        ]);
    }
}
