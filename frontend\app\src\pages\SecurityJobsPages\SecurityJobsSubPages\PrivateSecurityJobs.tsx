// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion, Card } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import privateSecurityQuestions, {
  PrivateSecurityJobsQuestionsType,
} from '../../../store/dummydata/SecurityJobsQuestionsDummyData/PrivateSecurityJobsQuestionsDummyData';
import BodyguardButton from '../SecurityJobsButtons/BodyguardButton';
import CctvOperatorButton from '../SecurityJobsButtons/CctvOperatorButton';
import CloseProtectionButton from '../SecurityJobsButtons/CloseProtectionButton';
import DoorSupervisorButton from '../SecurityJobsButtons/DoorSupervisorButton';
import EventSecurityButton from '../SecurityJobsButtons/EventSecurityButton';
import PrivateSecurityButton from '../SecurityJobsButtons/PrivateSecurityButton';
import SecurityGuardButton from '../SecurityJobsButtons/SecurityGuardButton';
import privatesecurityjobs1 from '../../../assets/images/securityjobs/privatesecurityjobs1.png';
import privatesecurityjobs2 from '../../../assets/images/securityjobs/privatesecurityjobs2.png';
import privatesecurityjobs3 from '../../../assets/images/securityjobs/privatesecurityjobs3.png';
import privatesecurityjobs4 from '../../../assets/images/securityjobs/privatesecurityjobs4.png';
import DoorSupervisorJobs from './DoorSupervisorJobs';

const PrivateSecurityJobs: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;
  const [activeId, setActiveId] = useState<number | null>(null);

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex lg:flex-row flex-col w-full max-w-[1320px] mx-auto items-center text-center py-[64px] '>
          <img src={privatesecurityjobs1} className='sm:w-[657.25px] w-full sm:h-[472px] ' />

          <View className='flex flex-col lg:w-[675px] xl:h-[472px]  p-[32px] xl:p-[50px] xl:px-[50px] gap-[12px] px-3  bg-[#FFFF]  '>
            <Text className='text-left font-rufina-stencil text-[30px] lg:text-[30px]  xl:text-[48px] font-normal xl:leading-[56px] text-[#323C58]'>
              Find private security jobs in London and the UK
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px]'>
              Looking for your next private security role in London and the UK? Find work the easy way with our bespoke
              private security job platform. We help to connect private security professionals with reputable companies.
              By removing unnecessary layers of middlemen, you will increase your earning potential and take control of
              your private security career path.
            </Text>
            <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1100px]  sm:gap-[64px] mx-auto py-[64px] '>
          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[32px] gap-10 mt-[20px] sm:mt-[0] sm:mb-0 mb-8'>
            <View className='flex flex-col gap-3 justify-center w-full  xl:w-[532px] items-start xl:px-0 px-3  sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil lg:text-[30px]  xl:text-[48px] text-[30px] font-normal xl:leading-[56px]'>
                Freelance private security jobs in London and the UK
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Set your own rates. Set your ideal hours. And enjoy protection from low paying work with our minimum
                rate policy. With Surely Security, you take control of your freelance private security career and start
                working on your own terms. Find full time work to boost your earning potential, or look for part time
                work that fits around your existing commitments. We help to pair talented private security contractors
                with a wide range of reputable agents and clients. If you’re looking for private security jobs for
                ex-military in London and across the UK, we will match you with the ideal role for your skills and
                experience.{' '}
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={privatesecurityjobs2}
              className='  sm:mt-0 w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1'
            />
          </View>

          <View className='flex flex-col-reverse lg:flex-row max-w-[1100px] justify-between sm:gap-[32x] gap-10   sm:mb-0 mb-8 '>
            <img src={privatesecurityjobs3} className='w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1' />
            <View className='flex flex-col gap-3 justify-center w-full xl:w-[532px] items-start px-3 xl:px-0 sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[30px]  xl:text-[48px]  font-normal xl:leading-[56px]'>
                Find your next private security job today
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Surely Security is the only security job platform of its kind in the UK. We are a dedicated private
                security recruitment facilitator, bringing together talented private security contractors like you with
                clients and agents in London and the UK. With our platform, you manage the entire process in one place,
                from finding jobs to negotiating rates, exchanging contracts and getting paid on time. Surely Security
                makes private security hiring easier than ever before.{' '}
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
          </View>

          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[32px] gap-10  '>
            <View className='flex flex-col gap-3 justify-center w-full xl:w-[532px] items-start px-3 xl:px-0  sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[30px]  xl:text-[48px] font-normal xl:leading-[56px]'>
                Work when, where and how you want
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Flexibility is at the heart of everything we do. We empower private security professionals to negotiate
                better rates and work the hours that suit their schedules. Whether you’re looking for full time or part
                time work, we match you with private security jobs that perfectly suit your needs.{' '}
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={privatesecurityjobs4}
              className=' mt-[20px] sm:mt-0 w-full xl:w-[532px] sm:h-[473.77px]  sm:ml-[0px] shrink-1'
            />
          </View>
        </View>
      </View>

      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center xl:px-0 px-3 '>
          <Text className='!text-[#323C58] font-rufina-stencil sm:px-0 px-6  sm:text-center text-left  text-[32px] text-[30px] font-normal sm:leading-[40px] leading-[56px] items-center mt-[64px]  sm:ml-[0px]'>
            Frequently asked questions about private security jobs{' '}
          </Text>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-[36px] mb-[65px]'>
            <div className='flex flex-col gap-4 '>
              {privateSecurityQuestions.map((item: PrivateSecurityJobsQuestionsType) => {
                if (item.id % 2 === 1) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                            endIcon={() => (
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='22'
                                height='22'
                                viewBox='0 0 22 22'
                                fill='none'
                              >
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                {item.question}
                              </p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' xl:w-[600px] flex flex-row '>
                          <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]  '>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
            <div className='flex flex-col gap-4 '>
              {privateSecurityQuestions.map((item: PrivateSecurityJobsQuestionsType) => {
                if (item.id % 2 === 0) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                            endIcon={() => (
                              <svg
                                xmlns='http://www.w3.org/2000/svg'
                                width='22'
                                height='22'
                                viewBox='0 0 22 22'
                                fill='none'
                              >
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                {item.question}
                              </p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className='w-[370px] sm:w-[600px] '>
                          <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]'>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
          </div>
        </View>
      </View>
      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1320px] mx-auto gap-[24px] xl:px-0 px-3 '>
          <View className='flex flex-col sm:flex-row justify-between lg:mt-[64px] gap-[24px]'>
            <SecurityGuardButton />
            <BodyguardButton />
            <CctvOperatorButton />
          </View>
          <View className='flex flex-col sm:flex-row justify-between gap-[24px] mb-[30px] lg:mb-[90px]'>
            <EventSecurityButton />
            <DoorSupervisorButton />
            <CloseProtectionButton />
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <Footer />
    </View>
  );
};

export default PrivateSecurityJobs;
