<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UserExperienceRequest;
use App\Http\Resources\UserRatingResource;
use App\Models\MobileUserExperience;
use App\Models\MobileUserRating;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MobileUserRatingController extends Controller
{
    public function index(Request $request, $userId = null): JsonResponse
    {
        $resource = UserRatingResource::class;
        $query = MobileUserRating::query();
        if (auth()->user()) {
            $query->where('reviewed_id', auth()->id());
        } else {
            $query->where('reviewed_id', $userId);
        }

        if ($request->filled('no_paginate') || !$request->filled('page')) {
            $userRatings = $query->get();
            $averageRatings = $userRatings->isNotEmpty() ?
                collect([
                    'punctuality',
                    'professionalism',
                    'communication',
                    'positivity',
                    'helpfulness',
                    'dress_code',
                ])->map(function ($column) use ($userRatings) {
                    return $userRatings->avg($column);
                })->avg()
             : null;

            $meta = [
                'total' => count($userRatings) ?? 0,
                'average_ratings' => $averageRatings,
            ];
        } else {
            $perPage = $request->input('per_page', 4);
            $userRatings = $query->paginate($perPage);
            $averageRatings = $userRatings->isNotEmpty() ?
                collect([
                    'punctuality',
                    'professionalism',
                    'communication',
                    'positivity',
                    'helpfulness',
                    'dress_code',
                ])->map(function ($column) use ($userRatings) {
                    return $userRatings->avg($column);
                })->avg() : null;

            $meta = [
                'current_page' => $userRatings->currentPage(),
                'from' => $userRatings->firstItem(),
                'last_page' => $userRatings->lastPage(),
                'path' => $userRatings->resolveCurrentPath(),
                'per_page' => $userRatings->perPage(),
                'to' => $userRatings->lastItem(),
                'total' => $userRatings->total(),
                'average_ratings' => $averageRatings,
            ];
        }

        $userRatings = $resource::collection($userRatings);

        return response()->json(['data' => $userRatings, 'meta' => $meta]);
    }

    public function store(Request $request, $userId): JsonResponse
    {
        $request->merge([
            'reviewer_id' => auth()->id()
        ]);
        $request['reviewed_id'] = $userId;

        $ratings = MobileUserRating::create($request->all());
        if(! $ratings) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot be saved at the moment! Try again later...',
                'data' => []
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => $ratings
        ]);
    }
}
