import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';

interface Props {
  children?: React.ReactNode;
}

export const PostJobLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container
      padding={0}
      className="w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover"
    >
      <Header />
      <main className=' align-center mt-[80px] w-full max-w-[1320px] mx-auto text-center '>
        <article className=' flex flex-row gap-5 flex-wrap justify-center mt-[61px]'>
          {children}
        </article>
      </main>
    </Container>
  );
};

export default PostJobLayout;
