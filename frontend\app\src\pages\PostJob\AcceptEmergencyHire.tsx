import React, { useState } from 'react';
import { Text, View, Button, Modal } from 'reshaped';

interface AcceptEmergencyHireProps {
  active: boolean;
  deactivate: () => void;
  onAccept: (accepted: boolean) => void;
  onDecline: () => void;
}

const AcceptEmergencyHire: React.FC<AcceptEmergencyHireProps> = ({
  active,
  deactivate,
  onAccept,
  onDecline
}) => {
  const [accepted, setAccepted] = useState(false);

  const handleAccept = () => {
    setAccepted(true);
    onAccept(true);
    deactivate();
  };

  return (
    <Modal
      active={active}
      onClose={() => {
        if (!accepted) {
          onAccept(false);
        }
        deactivate();
      }}
      className='!w-[424px] !h-[auto]'
    >
      <View className='flex flex-col'>
        <Text className='text-[#1A1A1A] rubik text-[20px] font-normal mt-[10px]'>
          Do you want to accept Emergency Hire?
        </Text>
        <Text className='text-[#323C58] rubik text-[15px] font-normal leading-5 mt-[3px]'>
          The Emergency Hire feature enables businesses to quickly fill urgent
          security positions, ensuring the safety and protection of their
          premises. With this option, there is a 5% extra fee.
        </Text>

        <View className='flex flex-row justify-between mt-[20px]'>
          <Button
            variant='outline'
            onClick={onDecline}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base w-[173px] h-[48px] mr-[10px]'
          >
            <Text className='rubik text-[16px] leading-[24px] font-medium text-[#323C58]'>
              No, go back
            </Text>
          </Button>
          <Button
            onClick={handleAccept}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base !text-white !bg-[#0B80E7] w-[173px] h-[48px]'
          >
            <Text className='rubik text-[16px] leading-[24px] font-medium text-[#FFFFFF]'>
            Accept
            </Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default AcceptEmergencyHire;
