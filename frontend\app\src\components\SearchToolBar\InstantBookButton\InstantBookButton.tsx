import { Button, Text, View } from 'reshaped';

const InstantBookButton = () => {
  return (
    <Button variant='ghost'>
      <View direction={'row'} justify={'center'} align={'center'} gap={1}>
        <span className='material-icons-outlined text-[#0B80E7]'>
          notifications_active
        </span>

        <Text className='w-99 h-23 rubik font-medium text-base leading-6'>
          Instant Book
        </Text>
      </View>
    </Button>
  );
};

export default InstantBookButton;
