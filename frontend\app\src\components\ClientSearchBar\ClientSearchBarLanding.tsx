// @ts-nocheck
import { TextField, View, Text, Button, Popover, useToggle, Badge } from 'reshaped';
import { useNavigate, useLocation } from 'react-router-dom';
import { useContext, useState } from 'react';
import OperativesFilter from '../filters/OperativesFilter/OperativesFilter';
import { OperativesContext } from 'src/context/OperativesContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';

const ClientSearchBarLanding: React.FC = () => {
  const navigate = useNavigate();
  const { handleSearch, totalCount, favouritesCount, toggleInstantBook, resetOuterFilters, search, filters, toggleBS7858 } =
    useContext(OperativesContext);
  const location = useLocation();
  const currentPath = location.pathname;
  const isFavoritePage = currentPath.includes('favorite-operator');
  const isMapView = window.location.pathname === '/search-operators-map';
  const isSearchView = window.location.pathname === '/search-operator';

  const resultsCount = isFavoritePage ? favouritesCount : totalCount;

  const { active, activate, deactivate } = useToggle(false);

  const innerFilters = { ...filters };

  if (isFavoritePage || isMapView || isSearchView) delete innerFilters.is_favorite && delete innerFilters.instant_book && delete innerFilters.bs7858;

  const outerFilters = { search, is_emergency_hire: filters.is_emergency_hire };

  const innerFilterCount = Object.values(innerFilters).reduce((filterCount, currentFilter) => {
    if (!!currentFilter && currentFilter?.length !== 0) filterCount++;
    return filterCount;
  }, 0);
  const outerFilterCount = Object.values(outerFilters).reduce((filterCount, currentFilter) => {
    if (!!currentFilter && currentFilter?.length !== 0) filterCount++;
    return filterCount;
  }, 0);

  return (
    <>
      <View className='mx-auto flex flex-col rounded rounded-[55px] bg-[#F8F8F8] p-[12px] mt-0 lg:hidden lg:w-[380px] lg:px-0'>
        <View
          justify={'center'}
          align={'center'}
          borderRadius='circular'
          padding={1}
          className='border-1 h-[56px] border border-solid border-gray-300 lg:w-[380px] '
          maxWidth='100%'
        >
          <TextField
            defaultValue={search}
            size='medium'
            variant='headless'
            startSlot={<span className='material-icons-outlined'>search</span>}
            name='searchInput'
            placeholder='Search for example "CCTV from London"'
            className='w-10/12 !pl-2'
            onChange={({ value }) => handleSearch(value)}
          />
        </View>
      </View>
      <View
        height={'104px'}
        width={'50%'}
        borderRadius={'circular'}
        direction={'row'}
        align={'center'}
        className='hidden lg:flex'
      >
        <View direction={'row'} justify={'start'} align={'center'} gap={3}>
          <View
            direction={'row'}
            justify={'center'}
            align={'center'}
            height='56px'
            borderRadius='circular'
            padding={1}
            className='border-1 border border-solid border-gray-300 bg-[#F8F8F8]  xl:w-[360px] bg-white'
            maxWidth='100%'
          >
            <TextField
              defaultValue={search}
              size='medium'
              variant='headless'
              startSlot={<span className='material-icons-outlined'>search</span>}
              name='searchInput'
              placeholder='Search for example "CCTV from London"'
              className='w-10/12 !pl-2'
              onChange={({ value }) => handleSearch(value)}
            />
          </View>
        </View>

        <View>
          <View className='sm:hidden lg:flex' direction={'row'} justify={'center'} align={'center'} gap={4}>
            <View className='flex gap-2'>
            </View>
            <View className='flex gap-4 '>
              <Badge.Container>
                {innerFilterCount !== 0 && (
                  <Badge className='cursor-default border border-[#9C9999] bg-[#F4F5F7]' rounded>
                    <Text className='rubik'>{innerFilterCount}</Text>
                  </Badge>
                )}
                
              </Badge.Container>
            </View>
          </View>
        </View>
      </View>
      <OperativesFilter active={active} deactivate={deactivate} innerFilters={innerFilters} />
    </>
  );
};
export default ClientSearchBarLanding;
