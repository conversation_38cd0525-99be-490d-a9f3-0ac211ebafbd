// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Text, Button } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Subscribe from '../Subscribe/Subscribe';

import '../../components/Header/HeaderMenu/HeaderMenu.css';

const CookiesPolicy: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-center mx-auto mt-[100px]'>
            <Text className='text-center font-rufina-stencil text-[48px] font-normal leading-[56px] text-[#323C58]'>
              COOKIES POLICY
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Most websites you visit will use cookies in order to improve your
              user experience by enabling that website to ‘remember’ you, either
              for the duration of your visit (using a ‘session cookie’) or for
              repeat visits (using a ‘persistent cookie’). Most cookies are
              harmless and are designed to help your online experience.
            </Text>
            <div className='items-center mx-auto bg-[#388DD8] w-[200px] h-[4px] my-4 mt-[35px]' />
          </View>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-start mx-auto mt-[15px] mb-[20px]'>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Some people find the idea of a website storing information on
              their computer or mobile device intrusive, particularly when this
              information is stored and used by a third party without their
              knowledge. Although this is generally quite harmless if you
              prefer, it is possible to block some or all cookies or even to
              delete cookies that have already been set; but you need to be
              aware that you might lose some functionality for those websites
              where you do this.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              If you don’t want to receive cookies, you can modify your browser
              so that it notifies you when cookies are sent to it, or you can
              refuse cookies altogether. You can also delete cookies that have
              already been set.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              If you wish to restrict or block web browser cookies which are set
              on your device then you can do this through your browser settings;
              the Help function within your browser should tell you how to do
              this.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              A cookie is a simple text file that is stored on your computer or
              mobile device by a website’s server. Only that server will be able
              to retrieve or read the contents of that cookie. Each cookie is
              also unique to each of your web browsers. The cookie will contain
              anonymous information, such as a unique identifier and the site
              name and some digits and numbers.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Cookies do lots of different jobs, like letting you navigate
              between pages efficiently, and generally improving your experience
              of a website. Cookies make the interaction between you and the
              website faster and easier. If a website doesn’t use cookies, it
              will think you are a new visitor every time you move to a new page
              on the site.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Cookies may be set by the website you are visiting (‘first party
              cookies’) or they may be set by other websites that run content on
              the page you are viewing (‘third party cookies’).
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              First-party cookies are set by the website you are visiting and
              they can only be read by that site. Third-party cookies are set by
              a different organisation to the owner of the website you are
              visiting. For example, like many other website owners, we use an
              analytics tool from Google, which allows us to view general
              information about how users interact with our website. We use this
              information to improve the online experience for our website
              visitors.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              The website you are visiting may also contain content embedded
              from external social media websites and these sites may also set
              their own cookies. We use various social media on our website,
              such as Facebook, LinkedIn, Instagram, TikTok and Twitter. This
              will not affect you unless you click on these links.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              The website you are visiting may also have links to other
              third-party websites. We may introduce links on our page to other
              websites. Please note that our cookies policy only relates to the
              cookies that are found on our website and that the management of
              other websites is outside of our control. However, we will make
              reasonable attempts to ensure that we are not directing you to
              visit websites that might use cookies in a disreputable fashion.
              It is your responsibility to read the cookies policy for every
              website you visit if you wish to understand how each site intends
              to use them.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Some websites also use cookies to enable them to target
              advertising or marketing messages based, for example, on your
              location and/or browsing habits. We do not use these types of
              cookies on our website.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Session cookies are only stored temporarily during a browsing
              session and are deleted from the user’s device when the browser is
              closed.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Persistent cookies are saved on your computer for a fixed period
              (usually a year or longer) and are not deleted when the browser is
              closed. Persistent cookies are used where we need to know who you
              are for more than one browsing session.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              The Information Commissioner’s Office is the UK’s independent
              authority, It is designed to uphold information rights in the
              public interest, promoting openness by public bodies and data
              privacy for individuals. If you are interested in finding out more
              about guidance and best practices with regard to the use of
              cookies on websites, please visit their website at www.ico.org.uk.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Alternatively, you may wish to visit www.aboutcookies.org, which
              contains comprehensive information on how to do this for a wide
              variety of desktop browsers.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              We also recommend that our website users visit the BBC cookies web
              page. You will find a lot of useful information at{' '}
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='http://www.bbc.co.uk/privacy/cookies'
                target='_blank'
                rel='noopener noreferrer'
              >
                http://www.bbc.co.uk/privacy/cookies
              </a>
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              We welcome feedback from our users: please contact&nbsp;
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='mailto:<EMAIL>'
              >
                <EMAIL>
              </a>
              &nbsp; with any helpful comments and criticisms. We will use this
              information to help us to improve the way we use cookies on our
              website for the benefit of all users.
            </Text>
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default CookiesPolicy;
