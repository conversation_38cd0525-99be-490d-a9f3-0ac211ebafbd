import { Button, Text } from 'reshaped';
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useAuthContext } from 'src/context/AuthContext';

const PaymentMessage = ({ message }) => {
  const { user, isClient } = useAuthContext();

  const nameShown = message?.sender_id;
  const payment = message?.payment || '300$';

  if (isClient) {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#F4F5F7] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
            Add money to escrow
          </h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            {`Please add ${payment} to escrow. We'll use Stripe to ensure secure payment.`}
          </p>
        </div>
        <Button
          color='black'
          className='min-w-[60px] shrink-0 rounded !bg-[#323c58] px-2 py-1 text-[10px] text-sm font-medium leading-5 text-[#FFF] lg:text-[14px]'
        >
          Pay now
        </Button>
      </div>
    );
  }
  if (isClient === false) {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#F4F5F7] p-4 lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='flex w-full flex-col gap-[3px] text-left'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>
            Money added to escrow
          </h2>
          <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            {`${nameShown} added ${payment} to escrow. We'll release the funds 10 days after the job is successfully completed.`}
          </p>
        </div>
      </div>
    );
  }
};

export default PaymentMessage;
