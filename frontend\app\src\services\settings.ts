import client from '../client';

import { Settings, BackendSettings } from '../types/user';

export const editSettings = async (settingsData: Settings) => {
  const {
    name,
    email,
    phone,
    postalCode,
    postalTown,
    addressLine,
    lat,
    lng,
    town,
    county,
    siaLicenseType,
    industrySector,
    profilePhoto,
    additionalPictures,
    profileVideo,
    profileTitle,
    profileDescription,
    languages,
    locationRange,

    accountName,
    sortCode,
    accountNumber,
    utrNumber,
    vatNumber,
    companyNumber,

    oldPassword,
    password,

    // companyName,
    // companyWebside,
    // jobTitle,
    // jobDescription,
    // isCurrent,
    // startDate,
    // endDate,
    // isReferenceLetter,
    // referenceLetter,
  } = settingsData;

  const mappedData: BackendSettings = {
    name,
    email,
    phone,
    postal_code: postalCode,
    city_id: postalTown,
    address: addressLine,
    location_range: locationRange,
    lat,
    lng,
    address_1: town,
    address_2: county,
    sia_license_type: siaLicenseType,
    industry_sector: industrySector,
    profile_photo: profilePhoto,
    additional_pictures: additionalPictures,
    profile_video: profileVideo,
    profile_title: profileTitle,
    profile_description: profileDescription,
    languages,

    account_name: accountName,
    sort_code: sortCode,
    account_number: accountNumber,
    utr_number: utrNumber,
    vat_number: vatNumber,
    company_number: companyNumber,

    old_password: oldPassword,
    password: password,

    // company_name: companyName,
    // company_website: companyWebsite,
    // job_title: jobTitle,
    // job_description: jobDescription,
    // is_current: isCurrent,
    // start_date: startDate,
    // end_date: endDate,
    // is_reference_letter: isReferenceLetter,
    // reference_letter: referenceLetter,
  };

  try {
    const response = await client.settings.all(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

// export const getCities = async () => {
//   try {
//     const response = await client.settings.cities();
//     if (!response.error) {
//       return response.data;
//     }
//   } catch (error) {
//     console.error(error);
//   }
// };
export const getCities = async (postcode: any) => {
  try {
    const response = await client.settings.cities(postcode);
    if (!response.error) {
      return response.data;
    }
  } catch (error) {
    console.error(error);
  }
};

// export const getSiaLicenceValidation = async (siaLicenceNumber: any) => {
//   try {
//     const response = await client.settings.getSiaLicenceValidation(siaLicenceNumber);
//     if (!response.error) {
//       return response.data;
//     }
//   } catch (error) {
//     console.error(error);
//   }
// };
export const getSiaLicenceValidation = async (siaLicenceNumber: any) => {
  try {
    const response = await client.settings.getSiaLicenceValidation(siaLicenceNumber);
    if (!response.error) {
      return response.data;
    } else {
      return { error: response.error };
    }
  } catch (error) {
    console.error(error);
    return { error: 'An error occurred during SIA licence validation.' };
  }
};

export const getLanguages = async () => {
  try {
    const response = await client.settings.getLanguage();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};
export const getLanguagesLevel = async () => {
  try {
    const response = await client.settings.getLanguageLevel();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getAll: any = async () => {
  try {
    const response = await client.settings.getAll();
    const data = response.data;

    const mappedUser = {
      accountName: data.account_name,
      accountNumber: data.account_number,
      companyNumber: data.company_number,
      email: data.email,
      id: data.id,
      instantBook: data.instant_book,
      name: data.name,
      payRate: data.pay_rate,
      phone: data.phone,
      postalCode: data.postal_code,
      profileTitle: data.profile_title,
      profileDescription: data.profile_description,
      profilePhoto: data.profile_photo,
      profileVideo: data.profile_video,
      siaLicenceNumber: data.sia_license_number,
      sortCode: data.sort_code,
      utrNumber: data.utr_number,
      vatNumber: data.vat_number,
      webside: data.webside,
      yearsOfExperience: data.years_of_experience,
      addressLine: data.address,
      townCity: data.address_1,
      county: data.address_2,
      locationRange: data.location_range,
    };

    return mappedUser;
  } catch (error) {
    console.error(error);
  }
};

export const editPassword = async (settingsData: Settings) => {
  const { oldPassword, password, passwordConfirmation } = settingsData;

  const mappedData: BackendSettings = {
    old_password: oldPassword,
    password: password,
    password_confirmation: passwordConfirmation,
  };

  try {
    const response = await client.settings.password(mappedData);

    return response;
  } catch (error) {
    throw error;
  }
};

export const getPosition: any = async () => {
  try {
    const response = await client.settings.getPosition();
    const { employments, qualifications, cv } = response.data;

    const mappedPosition = {
      employments: employments.map((employment: any) => ({
        companyName: employment.company_name,
        companyWebsite: employment.company_website,
        startDate: employment.start_date,
        endDate: employment.end_date,
        id: employment.id,
        isCurrent: employment.is_current,
        isReferenceLetter: employment.is_reference_letter,
        jobDescription: employment.job_description,
        jobTitle: employment.job_title,
        referenceLetter: employment.reference_letter,
      })),
      qualifications: qualifications.map((qualification: any) => ({
        expiryDate: qualification.expiry_date,
        id: qualification.id,
        qualificationDescription: qualification.qualification_description,
        qualificationDocument: qualification.qualification_document,
        qualificationTitle: qualification.qualification_title,
      })),
      cv: {
        id: cv?.id,
        cv: cv?.cv,
        cv_name: cv?.cv_name,
      },
    };

    return mappedPosition;
  } catch (error) {
    console.error(error);
  }
};

export const addPosition = async (settingsData: { employment: any; qualifications: any; cv: { cv: string; cv_name: string } }) => {
  const { employment, qualifications, cv } = settingsData;

  const mappedEmploymentData = employment?.map(
    (setting: {
      companyName: any;
      companyWebsite: any;
      jobTitle: any;
      jobDescription: any;
      isCurrent: any;
      startDate: any;
      endDate: any;
      isReferenceLetter: any;
      referenceLetter: any;
    }) => ({
      company_name: setting.companyName,
      company_website: setting.companyWebsite,
      job_title: setting.jobTitle,
      job_description: setting.jobDescription,
      is_current: setting.isCurrent,
      start_date: setting.startDate,
      end_date: setting.endDate,
      is_reference_letter: setting.isReferenceLetter,
      reference_letter: setting.referenceLetter,
    }),
  );

  const mappedQualificationData = qualifications?.map(
    (setting: { qualificationTitle: any; qualificationDescription: any; expiryDate: any; qualificationDocument: any }) => ({
      qualification_title: setting.qualificationTitle,
      qualification_description: setting.qualificationDescription,
      expiry_date: setting.expiryDate,
      qualification_document: setting.qualificationDocument,
    }),
  );

  const finalData = {
    employments: mappedEmploymentData,
    qualifications: mappedQualificationData,
    cv: cv.cv,
    cv_name: cv.cv_name,
  };

  try {
    const response = await client.settings.addPosition(finalData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addCuriculumVitae = async (settingsData: Settings) => {
  const { selectedFiles } = settingsData;

  const mappedData: BackendSettings = {
    cv: selectedFiles,
  };

  try {
    const response = await client.settings.all(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addProfileDetails = async (settingsData: {
  languages: any;
  SIALicense: any;
  industrySectors: any;
  profilePhoto: any;
  additionalPictures: any;
  profileVideo: any;
  profileTitle: any;
  profileDescription: any;
}) => {
  const { languages, SIALicense, industrySectors, profilePhoto, additionalPictures, profileVideo, profileTitle, profileDescription } = settingsData;

  const finalData = {
    languages,
    sia_licence_types: SIALicense,
    industry_sectors: industrySectors,
    profile_photo: profilePhoto,
    additional_pictures: additionalPictures,
    profile_video: profileVideo,
    profile_title: profileTitle,
    profile_description: profileDescription,
  };

  try {
    const response = await client.settings.addProfileDetails(finalData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getProfileDetails: any = async () => {
  try {
    const response = await client.settings.getProfileDetails();
    const data = response.data;
    const mappedUser = {
      profileTitle: data.profile_title,
      profileDescription: data.profile_description,
      profilePhoto: data.profile_photo,
      profileVideo: data.profile_video,
      industrySectors: data.industry_sectors,
      SIALicense: data.sia_licence_types,
      additionalPictures: data.additional_pictures,
      languages: data.languages,
    };

    return mappedUser;
  } catch (error) {
    console.error(error);
  }
};

export const deleteUser = async () => {
  try {
    const response = await client.settings.deleteUser();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addAvailability = async (settingsData: Settings) => {
  const { instantBook } = settingsData;

  const mappedData: BackendSettings = {
    instant_book: instantBook,
  };

  try {
    const response = await client.settings.addAvailability(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getAvailability = async () => {
  try {
    const response = await client.settings.getAvailability();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getPaymentDetails = async () => {
  try {
    const response = await client.settings.getPaymentDetails();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const paymentDetails = async (settingsData: Settings) => {
  const { accountName, sortCode, accountNumber, utrNumber, vatNumber, companyNumber } = settingsData;

  const mappedData: BackendSettings = {
    account_name: accountName,
    sort_code: sortCode,
    account_number: accountNumber,
    utr_number: utrNumber,
    vat_number: vatNumber,
    company_number: companyNumber,
  };

  try {
    const response = await client.settings.addPaymentDetails(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addBankCardDetails = async (data: { 
  payment_method_id: string;
  holder_name: string;
}) => {
  try {
    const response = await client.settings.addBankCardDetails(data);
    if (!response.error) {
      return response;
    }
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const getBankCardDetails = async () => {
  try {
    const response = await client.settings.getBankCardDetails();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addBankAccount = async (bankAccount: any) => {
  try {
    const response = await client.settings.addBankAccount(bankAccount);
    return response;
  } catch (error) {
    console.error(error);
  }
};

export const addVatAndUtr = async (vatAndUtr: any) => {
  try {
    const response = await client.settings.addVatAndUtr(vatAndUtr);
    return response;
  } catch (error) {
    console.error(error);
  }
};

export const getBankAccount = async () => {
  try {
    const response = await client.settings.getBankAccount();
    if (!response.error) {
      return response;
    }
    throw new Error(response.message || 'Failed to fetch bank account');
  } catch (error) {
    console.error('Bank account fetch error:', error);
    return {
      error: true,
      message: (error as Error).message || 'Failed to fetch bank account details',
      data: null
    };
  }
};

export const addGeneral = async (settingsData: any) => {
  const mappedData = {
    name: settingsData.name,
    email: settingsData.email,
    phone: settingsData.phone,
    postal_code: settingsData.postal_code,
    address: settingsData.address,
    address_1: settingsData.address_1,
    address_2: settingsData.address_2,
    location_range: settingsData.location_range
  };

  try {
    const response = await client.settings.addGeneral(mappedData);
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const getGeneral = async () => {
  try {
    const response = await client.settings.getGeneral();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addSiaLicence = async (settingsData: Settings) => {
  const { siaLicence, idCheck, proofOfAddress, employmentHistory, creditCheck, noCriminalRecord } = settingsData;

  const mappedData: BackendSettings = {
    sia_licence: siaLicence,
    id_check: idCheck,
    proof_of_address: proofOfAddress,
    employment_history: employmentHistory,
    credit_check: creditCheck,
    no_criminal_record: noCriminalRecord,
  };

  try {
    const response = await client.settings.addSiaLicence(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getSiaLicence = async () => {
  try {
    const response = await client.settings.getSiaLicence();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

// export const getOperatorProfileData: any = async () => {
//   try {
//     const response = await client.settings.getProfileData();
//     const data = response.data;
//     const mappedUser = {
//       // profileTitle: data.profile_title,
//       // profileDescription: data.profile_description,
//       // profilePhoto: data.profile_photo,
//       // profileVideo: data.profile_video,
//       // industrySectors: data.industry_sectors,
//       // SIALicense: data.sia_licence_types,
//       // additionalPictures: data.additional_pictures,
//       // languages: data.languages,
//     };

//     return mappedUser;
//   } catch (error) {
//     console.error(error);
//   }
// };

export const addSurelyProBadge = async (settingsData: Settings) => {
  const { text, type } = settingsData;

  const mappedData: BackendSettings = {
    text: text,
    type: type,
  };

  try {
    const response = await client.settings.addSurelyProBadges(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getSurelyProBadge = async () => {
  try {
    const response = await client.settings.getSurelyProBadges();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getNotifications = async () => {
  try {
    const response = await client.settings.getNotifications();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addNotifications = async (settingsData: any) => {
  const {
    switchContractStatusWeb,
    switchSurelyProBadgesWeb,
    switchInstantBookReminderWeb,
    switchContractStartDateWeb,
    switchNewChatMessageWeb,
    switchPaymentsWeb,
    switchEmergencyHireWeb,

    switchContractStatusEmail,
    switchSurelyProBadgesEmail,
    switchInstantBookReminderEmail,
    switchContractStartDateEmail,
    switchNewChatMessageEmail,
    switchPaymentsEmail,
    switchEmergencyHireEmail,
  } = settingsData;

  const mappedData: any = {
    contract_status_alert: switchContractStatusWeb,
    surelypro_badges_alert: switchSurelyProBadgesWeb,
    instant_book_reminder_alert: switchInstantBookReminderWeb,
    contract_start_date_reminder_alert: switchContractStartDateWeb,
    new_chat_message_alert: switchNewChatMessageWeb,
    payments_alert: switchPaymentsWeb,
    new_emergency_hire_job_alert: switchEmergencyHireWeb,

    contract_status_mail: switchContractStatusEmail,
    surelypro_badges_mail: switchSurelyProBadgesEmail,
    instant_book_reminder_mail: switchInstantBookReminderEmail,
    contract_start_date_reminder_mail: switchContractStartDateEmail,
    new_chat_message_mail: switchNewChatMessageEmail,
    payments_mail: switchPaymentsEmail,
    new_emergency_hire_job_mail: switchEmergencyHireEmail,
  };

  try {
    const response = await client.settings.addNotifications(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};


export const requestEmailChange = async (settingsData: any) => {
  const {
    newEmail,
  } = settingsData;

  const mappedData: any = {
    new_email: newEmail,
  };

  try {
    const response = await client.settings.changeEmail(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const requestPhoneChange = async (settingsData: any) => {
  const {
    newPhone,
  } = settingsData;

  const mappedData: any = {
    new_Phone: newPhone,
  };

  try {
    const response = await client.settings.changePhone(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const upgradeToClient = async (settingsData: any) => {
  try {
    const response = await client.settings.upgradeToClient(settingsData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};
