<?php

namespace App\Http\Controllers\Api;

use App\DataTables\ReviewDataTable;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReviewResource;
use App\Models\Contract;
use App\Models\MobileUser;
use App\Models\Review;
use Illuminate\Http\Request;

class ReviewController extends Controller
{
    public function index(Request $request, $id) 
    {
        $query = Review::query()
            ->where('operative_id', $id)
            ->where('author_id', '!=', $id);
        
        if ($query->count() == 0) {
            return response()->json([
                'error' => true,
                'message' => 'No reviews!',
            ]);
        }

        $results = $query->cursor();
        $results = ReviewResource::collection($results);
        return $results;
    }

    public function store(Request $request, $contractId)
    {
        $contract = Contract::find($contractId);

        if (!$contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found with this id!'
            ]);
        }

        if ($contract->status != Contract::complete && $contract->status != Contract::canceled) {
            return response()->json([
                'error' => true,
                'message' => 'Contract is neither completed or canceled to review!'
            ]);
        }

        if (auth()->id() != $contract->operative_id && auth()->id() != $contract->client_id) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot submit review! You are not part of this contract!'
            ]);
        }

        $query = Review::query()
            ->where('author_id', auth()->id())
            ->where('contract_id', $contract->id)
            ->first();

        if ($query) {
            if (auth()->user()->account_type == MobileUser::freelancer) {
                return response()->json([
                    'error' => true,
                    'message' => 'You have submitted your review for this job!',
                ]);
            } elseif (auth()->user()->account_type == MobileUser::business) {
                return response()->json([
                    'error' => true,
                    'message' => 'You have submitted your review for the Operative!',
                ]);
            }

        }

        if (auth()->user()->account_type == MobileUser::freelancer && $request->get('comment') == '') {
            return response()->json([
                'error' => true,
                'message' => "Comment shouldn't be an empty string",
            ]);
        }

        if (auth()->user()->account_type == MobileUser::business && $request->has('rating')) {
            $ratings = $request->get('rating');
            $sum = 0;
            foreach ($ratings as $rating) {
                $sum += $rating;
            }
            $data['rating'] = json_encode($ratings);
            $data['rating_avg'] = $sum / count($ratings);
        } elseif (auth()->user()->account_type == MobileUser::business && !$request->has('rating')) {
            return response()->json([
                'error' => true,
                'message' => "Rating should be present in review!",
            ]);
        }

        $data['operative_id'] = $contract->operative_id;
        $data['client_id'] = $contract->client_id;
        $data['author_id'] = auth()->id();
        $data['contract_id'] = $contractId;
        $data['comment'] = $request->get('comment');
        $data['share_with_surely'] = $request->get('share_with_surely') ?? false;
        $data['share_with_client'] = $request->get('share_with_client') ?? false;

        if (!Review::create($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot submit review!'
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Review submitted successfully!'
        ]);
    }

    public function table(ReviewDataTable $dataTable) {
        return $dataTable->render('reviews.index');
    }
}
