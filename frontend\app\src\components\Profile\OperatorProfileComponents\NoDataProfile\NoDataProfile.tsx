// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';

import surelyiconprofile from '../../../../assets/icons/surleyicon/surelyiconprofile.png';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

const NoDataProfile: React.FC = () => {
  return (
    <View className='flex flex-col justify-center items-center w-[234px] mx-auto mt-[40px] mb-[40px]'>
      {/* <Image
        src={surelyiconprofile}
        className='w-[40px] h-[40px] flex-shrink-0'
      /> */}
      <SurelyIconNoData/>
      <Text className='text-[#444B5F] text-center font-normal leading-[24px] rubik !text-[#383838] mt-[10px]'>
        You can be sure that all details are important.
      </Text>
    </View>
  );
};

export default NoDataProfile;
