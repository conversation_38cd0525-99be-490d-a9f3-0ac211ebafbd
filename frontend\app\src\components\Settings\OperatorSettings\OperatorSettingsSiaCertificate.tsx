// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Text, View, Button, Tabs, Divider, Badge, Image, useToast, Breadcrumbs } from 'reshaped';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import check from '../../../assets/icons/sialicenceicon/check.png';
import uncheck from '../../../assets/icons/sialicenceicon/uncheck.png';
import { addSiaLicence, getSiaLicence } from 'src/services/settings';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { getValidateOperatorProfile } from 'src/services/user';
import { AppContext } from 'src/context/AppContext';

const OperatorSettingsSiaCertificate: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { fetchAppData } = useContext(AppContext);

  const [activeTab, setActiveTab] = useState(location?.state?.activeTab || '0');
  const toast = useToast();

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const [siaLicence, setSiaLicence] = useState(0);
  const [idCheck, setIdCheck] = useState(0);
  const [proofOfAddress, setProofOfAddress] = useState(0);
  const [employmentHistory, setEmploymentHistory] = useState(0);
  const [creditCheck, setCreditCheck] = useState(0);
  const [noCriminalRecord, setNoCriminalRecord] = useState(0);

  const [addressVerificationDocument, setAddressVerificationDocument] = useState();
  const [documentType, setDocumentType] = useState();
  const [idBackDocument, setIdBackDocument] = useState();
  const [idFrontDocument, setIdFrontDocument] = useState();
  const [selfieVerificationDocument, setSelfieVerificationDocument] = useState();
  const [siaLicenceCardPhoto, setSiaLicenceCardPhoto] = useState();
  const [siaLicenceExpiryDate, setSiaLicenceExpiryDate] = useState();
  const [siaLicenceNumber, setSiaLicenceNumber] = useState();

  const [isSaving, setIsSaving] = useState(false);

  const isValidateUnavailable =
    addressVerificationDocument === null ||
    documentType === null ||
    idBackDocument === null ||
    idFrontDocument === null ||
    selfieVerificationDocument === null ||
    siaLicenceCardPhoto === null ||
    siaLicenceExpiryDate === null ||
    siaLicenceNumber === null ||
    addressVerificationDocument === undefined ||
    documentType === undefined ||
    idBackDocument === undefined ||
    idFrontDocument === undefined ||
    selfieVerificationDocument === undefined ||
    siaLicenceCardPhoto === undefined ||
    siaLicenceExpiryDate === undefined ||
    siaLicenceNumber === undefined;

  const submitFirstTab = async () => {
    setIsSaving(true);
    const firstTabSettings: any = {
      siaLicence: siaLicence,
      idCheck: idCheck,
      proofOfAddress: proofOfAddress,
      employmentHistory: employmentHistory === 1 ? 1 : 0,
      creditCheck: creditCheck === 1 ? 1 : 0,
      noCriminalRecord: noCriminalRecord === 1 ? 1 : 0,
    };

    await addSiaLicence(firstTabSettings)
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
        if (
          firstTabSettings.siaLicence === 1 &&
          firstTabSettings.idCheck === 1 &&
          firstTabSettings.proofOfAddress === 1 &&
          firstTabSettings.employmentHistory === 1 &&
          firstTabSettings.creditCheck === 1 &&
          firstTabSettings.noCriminalRecord === 1
        ) {
          toast.show({
            title: 'Congratulations!',
            text: 'Your BS7858 checks have been successfully completed.',
            startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
          });
        }
      });
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  useEffect(() => {
    getSiaLicence().then((data: any) => {
      setSiaLicence(data.data.sia_licence);
      setIdCheck(data.data.id_check);
      setProofOfAddress(data.data.proof_of_address);
      setEmploymentHistory(data.data.employment_history);
      setCreditCheck(data.data.credit_check);
      setNoCriminalRecord(data.data.no_criminal_record);
    });
  }, []);

  useEffect(() => {
    getValidateOperatorProfile().then((data: any) => {
      setAddressVerificationDocument(data.addressVerificationDocument);
      setDocumentType(data.documentType);
      setIdBackDocument(data.idBackDocument);
      setIdFrontDocument(data.idFrontDocument);
      setSelfieVerificationDocument(data.selfieVerificationDocument);
      setSiaLicenceCardPhoto(data.siaLicenceCardPhoto);
      setSiaLicenceExpiryDate(data.siaLicenceExpiryDate);
      setSiaLicenceNumber(data.siaLicenceNumber);
    });
  }, []);

  return (
    <View className='  mx-auto overflow-hidden px-[12px] sm:w-auto'>
      <Breadcrumbs className='mb-[28px]'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='rubik text-[16px] font-medium text-[#1A1A1A]'>SIA licence</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='mb-[16px] flex items-center p-0 lg:mb-5'>
        <Text className='text-foreground-neutral font-rufina-stencil leading-10 text-[#323C58] lg:text-[32px] xl:leading-10'>SIA licence</Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          <Tabs.Item value='0'>
            <span className='rubik text-[#14171F]'>SIA licence</span>
          </Tabs.Item>
          <Tabs.Item value='1'>
            <span className='rubik text-[#14171F]'>BS7858</span>
          </Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col justify-between sm:flex-row'>
          <View className='mt-6 flex flex-col gap-3'>
            <Text className='text-neutral rubik text-base font-medium leading-4'>SIA licence validation status</Text>

            {/* <Badge
              rounded={false}
              className='flex flex-col items-center px-1 bg-[#EDEDED] w-[89px] h-[24px]'
            >
              <Text className='text-xs rubik font-normal text-[#6A6A6A] uppercase'>
                SIA Pending
              </Text>
            </Badge> */}
            {siaLicence ? (
              <span className='rubik h-[24px] w-[89px] rounded-md !bg-[#E6FEF3]  p-[4px] text-xs font-normal leading-4 text-[#05751F]'>
                SIA CERTIFIED
              </span>
            ) : (
              <span className='rubik h-[24px] w-[89px] rounded-md !bg-[#EDEDED] p-1 text-xs font-normal leading-4 text-[#6A6A6A]'>SIA PENDING</span>
            )}

            <Link
              to='/first-step-validation-operator'
              className='rubik border-none bg-transparent text-base font-medium leading-4 text-[#323C58] underline outline-none'
            >
              Provide new document
            </Link>
            <Divider className='mt-4 h-[1px] w-full'></Divider>

            <View className='mt-1 flex flex-row justify-between gap-4'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitFirstTab().then(() => {
                    // toast.show({
                    //   title: 'Done!',
                    //   text: 'You have updated your profile',
                    //   startSlot: (
                    //     <Image src={surleyicon} className='w-[30px] h-[30px]' />
                    //   ),
                    // });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {activeTab === '1' && (
        <View className='flex flex-col justify-between sm:flex-row'>
          <div>
            <div className='mt-6 flex flex-col gap-3'>
              <div className='flex flex-row items-center justify-between'>
                <div className='flex w-full max-w-[348px] flex-col gap-1'>
                  <div className='flex flex-row items-center gap-1'>
                    {siaLicence === 1 ? (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#05751F]'>check_circle</span>
                    ) : (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#323C58]'>cancel</span>
                    )}
                    <span className={`rubik text-[15px] font-medium text-[#444B5F]`}>SIA licence</span>
                  </div>
                  <span className={`rubik text-[14px] text-[#444B5F]`}>Your SIA licence has been verified.</span>
                </div>
                <label className='relative flex h-fit cursor-not-allowed items-center'>
                  <input
                    type='checkbox'
                    id='siaLicence'
                    name='siaLicence'
                    checked={siaLicence === 1}
                    onChange={() => setSiaLicence(siaLicence === 1 ? 0 : 1)}
                    className='sr-only !border-0'
                    disabled
                  />
                  <span className={`flex items-center justify-center rounded bg-[#EBEDF2] ${siaLicence === 1 ? 'bg-[#EBEDF2]' : ''}`}>
                    {/* {siaLicence === 1 && ( */}
                    <span className='material-icons-outlined inline-block p-0.5 text-base leading-4 text-[#C7CDDB]'>done</span>
                    {/* )} */}
                  </span>
                </label>
              </div>

              <div className='flex flex-row items-center justify-between'>
                <div className='flex w-full max-w-[348px] flex-col gap-1'>
                  <div className='flex flex-row items-center gap-1'>
                    {idCheck === 1 ? (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#05751F]'>check_circle</span>
                    ) : (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#323C58]'>cancel</span>
                    )}
                    <span className={`rubik text-[15px] font-medium text-[#444B5F]`}>ID check</span>
                  </div>
                  <span className={`rubik text-[14px] font-normal text-[#444B5F]`}>Your ID has been verified.</span>
                </div>
                <label className='relative flex h-fit cursor-not-allowed items-center'>
                  <input
                    type='checkbox'
                    id='idCheck'
                    name='idCheck'
                    checked={idCheck === 1}
                    onChange={() => setIdCheck(idCheck === 1 ? 0 : 1)}
                    className='sr-only !border-0'
                    disabled
                  />
                  <span className={`flex items-center justify-center rounded bg-[#EBEDF2] ${idCheck === 1 ? 'bg-[#EBEDF2]' : ''}`}>
                    {/* {idCheck === 1 && ( */}
                    <span className='material-icons-outlined inline-block p-0.5 text-base leading-4 text-[#C7CDDB]'>done</span>
                    {/* )} */}
                  </span>
                </label>
              </div>

              <div className='flex flex-row items-center justify-between'>
                <div className='flex w-full max-w-[348px] flex-col gap-1'>
                  <div className='flex flex-row items-center gap-1'>
                    {proofOfAddress === 1 ? (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#05751F]'>check_circle</span>
                    ) : (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#323C58]'>cancel</span>
                    )}
                    <span className={`rubik text-[15px] font-medium text-[#444B5F]`}>Proof of address</span>
                  </div>
                  <span className={`rubik text-[14px] font-normal text-[#444B5F]`}>Your address has been verified.</span>
                </div>
                <label className='relative flex h-fit cursor-not-allowed items-center'>
                  <input
                    type='checkbox'
                    id='proofOfAddress'
                    name='proofOfAddress'
                    checked={proofOfAddress === 1}
                    onChange={() => setProofOfAddress(proofOfAddress === 1 ? 0 : 1)}
                    className='sr-only !border-0'
                    disabled
                  />
                  <span className={`flex items-center justify-center rounded bg-[#EBEDF2] ${proofOfAddress === 1 ? 'bg-[#EBEDF2]' : ''}`}>
                    {/* {proofOfAddress === 1 && ( */}
                    <span className='material-icons-outlined inline-block p-0.5 text-base leading-4 text-[#C7CDDB]'>done</span>
                    {/* )} */}
                  </span>
                </label>
              </div>

              <div className='flex flex-row items-center justify-between'>
                <div className='flex w-full max-w-[348px] flex-col gap-1'>
                  <div className='flex flex-row items-center gap-1'>
                    {employmentHistory === 1 ? (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#05751F]'>check_circle</span>
                    ) : (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#323C58]'>cancel</span>
                    )}
                    <span className={`rubik text-[15px] font-medium text-[#444B5F]`}>Employment history</span>
                  </div>
                  <span className={`rubik text-[14px] font-normal text-[#444B5F]`}>I am able to provide up to 5 years of employment history.</span>
                </div>
                <label className='relative flex cursor-pointer items-center'>
                  <input
                    type='checkbox'
                    id='employmentHistory'
                    name='employmentHistory'
                    checked={employmentHistory === 1}
                    onChange={() => setEmploymentHistory(employmentHistory === 1 ? 0 : 1)}
                    className='sr-only !border-0'
                  />
                  <span
                    className={`flex items-center justify-center rounded ${
                      employmentHistory === 1 ? 'bg-[#0B80E7]' : 'border-neutral h-5 w-5 border-2 bg-white'
                    }`}
                  >
                    {employmentHistory === 1 && (
                      <span className={`material-icons-outlined inline-block p-0.5 text-base leading-4 text-white`}>done</span>
                    )}
                  </span>
                </label>
              </div>

              <div className='flex flex-row items-center justify-between'>
                <div className='flex w-full max-w-[348px] flex-col gap-1'>
                  <div className='flex flex-row items-center gap-1'>
                    {creditCheck === 1 ? (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#05751F]'>check_circle</span>
                    ) : (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#323C58]'>cancel</span>
                    )}
                    <span className={`rubik text-[15px] font-medium text-[#444B5F]`}>Credit check</span>
                  </div>
                  <span className={`rubik text-[14px] font-normal text-[#444B5F]`}>I confirm that I have no county court judgments.</span>
                </div>
                <label className='relative flex cursor-pointer items-center'>
                  <input
                    type='checkbox'
                    id='creditCheck'
                    name='creditCheck'
                    checked={creditCheck === 1}
                    onChange={() => setCreditCheck(creditCheck === 1 ? 0 : 1)}
                    className='sr-only !border-0'
                  />
                  <span
                    className={`flex items-center justify-center rounded ${
                      creditCheck === 1 ? 'bg-[#0B80E7]' : 'border-neutral h-5 w-5 border-2 bg-white'
                    }`}
                  >
                    {creditCheck === 1 && <span className={`material-icons-outlined inline-block p-0.5 text-base leading-4 text-white`}>done</span>}
                  </span>
                </label>
              </div>

              <div className='flex flex-row items-center justify-between'>
                <div className='flex w-full max-w-[348px] flex-col gap-1'>
                  <div className='flex flex-row items-center gap-1'>
                    {noCriminalRecord === 1 ? (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#05751F]'>check_circle</span>
                    ) : (
                      <span className='material-icons-outlined flex-shrink-0 text-base text-[#323C58]'>cancel</span>
                    )}
                    <span className={`rubik text-[15px] font-medium text-[#444B5F]`}>No criminal record</span>
                  </div>
                  <span className={`rubik text-[14px] font-normal text-[#444B5F]`}>I confirm that I have a clean criminal record.</span>
                </div>
                <label className='relative flex cursor-pointer items-center'>
                  <input
                    type='checkbox'
                    id='noCriminalRecord'
                    name='noCriminalRecord'
                    checked={noCriminalRecord === 1}
                    onChange={() => setNoCriminalRecord(noCriminalRecord === 1 ? 0 : 1)}
                    className='sr-only !border-0'
                  />
                  <span
                    className={`flex items-center justify-center rounded ${
                      noCriminalRecord === 1 ? 'bg-[#0B80E7]' : 'border-neutral h-5 w-5 border-2 bg-white'
                    }`}
                  >
                    {noCriminalRecord === 1 && (
                      <span className={`material-icons-outlined inline-block p-0.5 text-base leading-4 text-white`}>done</span>
                    )}
                  </span>
                </label>
              </div>
            </div>
            <Divider className='mt-6 h-[1px] w-full'></Divider>

            <View className='mt-[16px] flex flex-row justify-between gap-4'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitFirstTab().then(() => {
                    // toast.show({
                    //   title: 'Done!',
                    //   text: 'You have updated your profile',
                    //   startSlot: (
                    //     <Image src={surleyicon} className='w-[30px] h-[30px]' />
                    //   ),
                    // });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </div>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}

      {/* <Divider className='w-full h-[1px] mt-[16px]'></Divider>

      <View className='flex flex-row justify-between mt-[16px]'>
        <Button
          variant='outline'
          icon={() => <span className='material-icons -mt-1'>clear</span>}
          onClick={() => navigate('/operator-settings')}
          className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded bg-background-base w-[260px] h-[48px] mr-[10px]'
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            submitFirstTab();
            toast.show({
              title: 'Done!',
              text: 'You have updated your profile',
              startSlot: (
                <Image src={surleyicon} className='w-[30px] h-[30px]' />
              ),
            });
          }}
          className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded bg-background-base !text-white !bg-[#0B80E7] w-[260px] h-[48px]'
        >
          Save settings
        </Button>
      </View> */}
    </View>
  );
};

export default OperatorSettingsSiaCertificate;
