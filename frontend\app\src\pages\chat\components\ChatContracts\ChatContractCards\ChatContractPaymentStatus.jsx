import { Tooltip, Actionable } from 'reshaped';

const ChatContractPaymentStatus = ({ contract }) => {
  const { starts, ends, escrow_amount, pay_amount, escrow_rate } = contract;

  const daysUntilPayment = Math.abs(Math.floor((new Date(ends) - new Date()) / (1000 * 60 * 60 * 24)));
  const fees = contract?.operator_vat_amount + contract?.application_fee_amount + contract?.application_vat_amount

  const PaymentTooltip = () => {
    const subTotal = parseFloat(contract?.sub_total);
    const operatorVat = parseFloat(contract?.operator_vat_amount);
    const surelyFee = parseFloat(contract?.application_fee_amount);
    const surelyVat = parseFloat(contract?.application_vat_amount || (surelyFee * 0.2));
    const transactionFee = parseFloat(contract?.payment_transaction_fee_amount);
    const surelyVatRate = contract?.application_vat_rate;
    const total = parseFloat(contract?.total_amount);
    const emergencyHireFee = parseFloat(contract?.emergency_hire_fee_amount);
    const emergencyHireFeeRate = parseFloat(contract?.emergency_hire_fee_rate);
    // Calculate total Surley fees
    const totalSurelyFees = (!!transactionFee ? transactionFee : (surelyFee + surelyVat));

    const paymentDetails = [
      { label: 'Subtotal', value: subTotal },
      ...(operatorVat > 0 ? [{ label: 'VAT', value: operatorVat }] : []),
      ...(emergencyHireFee > 0 ? [{ label: 'Emergency hire', value: emergencyHireFee }] : []),
      // { label: 'Service fees', value: (!!transactionFee ? transactionFee : (surelyFee + surelyVat)) },
      // { label: 'Service fees VAT', value: surelyVat },
      { label: 'Surely Fees', value: totalSurelyFees },
      { label: 'Total', value: total },
    ];

    const filteredPaymentDetails = !emergencyHireFee ? paymentDetails.filter(({ label }) => label !== 'Emergency hire') : paymentDetails

    return (
      <div className='w-full p-1'>
        <ul className='flex w-full flex-col'>
          {filteredPaymentDetails?.map(({ label, value }) => (
            <li 
              key={label} 
              className={`flex w-full justify-between gap-2 border-b border-[#383838] py-2 last:border-b-0
                ${label === 'Total' ? 'mt-2 pt-3' : ''}`}
            >
              <div className={`${label === 'Total' ? 'font-bold text-[15px]' : ''}`}>{label}</div>
              <div className={`${label === 'Total' ? 'font-bold text-[15px]' : ''}`}>{'£' + +value?.toFixed(2)}</div>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  //subtotal : sub_total 100
  //operator vat : operator_vat_amount (20%)
  //surley fee : application_fee_amount 24 (%20)
  //surley vat : 4.2 (20%)
  //total : total_amount

  //escrow amount : escrow_amount (40%)
  //outstanding amount : total_amount - escrow_amount

  return (
    <div className='flex w-full flex-col gap-3 rounded-lg bg-[#F4F5F7] p-4'>
      <h3 className='rubik flex gap-2 text-left text-[14px] font-medium leading-5 text-[#1A1A1A]'>
        Job payment
        <Tooltip
          className='w-[200px] bg-[#1C212B] !text-[#EFF0F1] lg:w-full'
          // text='Escrow is an account where funds are held until a specific task or agreement has been satisfied.'
          text='Escrow is an account where funds are held until a specific task or agreement has been satisfied.'
        >
          {(attributes) => (
            <Actionable attributes={attributes} as='div'>
                  <div className='flex h-[17px] w-[17px] items-center justify-center self-center rounded-full border border-[#8996B8] bg-[#C7CDDB]'>
                    <span className='material-icons-outlined !text-[12px] !leading-none !font-normal text-[#323C58]' style={{fontSize: '12px', lineHeight: '1'}}>question_mark</span>
                  </div>
            </Actionable>
          )}
        </Tooltip>
      </h3>
      {escrow_amount ? (
        <>
          <div className={`rounded text-[13px] text-[#323C58] ${contract?.escrow_status === 'paid' ? 'bg-[#CDEDD5]' : 'bg-[#DBDFEA]'} py-[9px]`}>
            Escrow: <span className='font-medium'>{`£${+escrow_amount?.toFixed(2)} (${escrow_rate}%)`}</span>
          </div>
          <div className={`rounded py-[9px] text-[13px] text-[#323C58] ${((escrow_rate === 100 && contract.escrow_status === 'paid') || contract?.payment_status === 'paid') ? 'bg-[#CDEDD5]' : 'bg-[#DBDFEA]'}`}>
            Outstanding: <span className='font-medium'>{+contract?.pay_amount?.toFixed(2)}</span>
            <Tooltip className='w-[200px] bg-[#1C212B] !text-[#EFF0F1] lg:w-full' text={PaymentTooltip()}>
              {(attributes) => (
                <Actionable attributes={attributes} as='div'>
                  <div className='ml-2 flex h-[17px] w-[17px] items-center justify-center rounded-full border border-[#8996B8] bg-[#C7CDDB]'>
                    <span className='material-icons-outlined !text-[12px] !leading-none !font-normal text-[#323C58]' style={{fontSize: '12px', lineHeight: '1'}}>question_mark</span>
                  </div>
                </Actionable>
              )}
            </Tooltip>
          </div>
        </>
      ) : (
        <div className='rounded bg-[#EBEDF2] py-[9px] text-[13px] font-medium text-[#323C58]'>
          No money in escrow <span className=''>&#128577;</span>
        </div>
      )}
    </div>
  );
};

export default ChatContractPaymentStatus;
