import { Text, View, Divider } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';

const GuestUpgradeModal = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const { closeModal } = useModalAction();

  const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
  const isGuest = accountType === '5';

  if (!isGuest) {
    return null;
  }

  const handleUpgradeClick = () => {
    navigate('/client-settings-general');
    closeModal();
  };

  return (
    <View className='flex flex-col items-start p-6'>
      <View className='flex w-full items-center justify-between'>
        <Text className='font-rufina-stencil text-[32px] text-[#323C58]'>
          Become a Client
        </Text>
        <button
          onClick={() => closeModal()}
          className='btn-no-hover flex items-center justify-end'
        >
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
      </View>

      <Divider className='mt-4 mb-6 h-[1px] w-full' />
      
      <View className='w-full mb-6'>
        <Text className='rubik text-[16px] font-medium text-[#323C58] mb-2'>
          Complete Your Profile
        </Text>
        <Text className='rubik text-[14px] text-[#6B7280] mb-6'>
          Complete your profile details to become a client and get full access to all client features.
        </Text>
        
        <Text className='rubik text-[14px] text-[#6B7280] mb-4'>
          As a Client, you'll be able to:
          <ul className='list-disc pl-5 mt-2 space-y-1'>
            <li>Post jobs and hire security operatives</li>
            <li>Communicate directly with operatives</li>
            <li>Access advanced features to manage your security needs</li>
          </ul>
        </Text>

        <View className='flex gap-3'>
          <button
            onClick={() => closeModal()}
            className='flex-1 px-4 py-2 border border-[#BBC1D3] rounded-lg text-[#323C58] bg-gray-50'
          >
            <Text className='rubik text-[14px] font-medium'>Later</Text>
          </button>
          <button
            onClick={handleUpgradeClick}
            className='flex-1 px-4 py-2 bg-[#0B80E7] text-white rounded-lg hover:bg-[#0960AA] flex items-center justify-center gap-2'
          >
            <Text className='rubik text-[14px] font-medium'>Become Client</Text>
          </button>
        </View>
      </View>
    </View>
  );
};

export default GuestUpgradeModal;