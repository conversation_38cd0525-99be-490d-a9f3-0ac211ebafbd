<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UserExperienceRequest;
use App\Models\MobileUserExperience;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExperienceController extends Controller
{
    public function experiences()
    {
        $experiences = Auth::guard('api')->user()->experiences;
        $data = array();
        foreach ($experiences as $experience){
                $obj = [
                    'id'=>$experience->id,
                    'expertise_id'=>$experience->expertise_id,
                    'expertise_name'=>$experience->expertise->name,
                    'years'=>$experience->years,
                    'employer_name'=>$experience->employer_name,
                    'gig_id'=>$experience->gig_id,
                    'gig_name'=> $experience->gig ? $experience->gig->name : '',
                    'start_date'=>date('Y-m-d',strtotime($experience->start_date)),
                    'end_date'=>$experience->end_date ? date('Y-m-d',strtotime($experience->end_date)) : null,

                ];
                array_push($data,$obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data,
        ]);
    }

    public function add_experience(UserExperienceRequest $request)
    {
        $request['mobile_user_id'] = Auth::guard('api')->user()->id;
        MobileUserExperience::create($request->all());

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    public function edit_experience(UserExperienceRequest $request, $id)
    {
        MobileUserExperience::find($id)->update($request->all());

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    public function remove_experience($id)
    {
        MobileUserExperience::find($id)->delete($id);
        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

}
