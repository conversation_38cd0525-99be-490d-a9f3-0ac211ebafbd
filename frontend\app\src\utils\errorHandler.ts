const SUPPORT_MESSAGE = "Please contact support for assistance.";

export const getErrorMessage = (error: any) => {
  // Payment/Client/Operator specific errors that should be shown directly
  const directErrors = [
    'no_bank_account_linked',
    'insufficient_funds',
    'invalid_payment_method',
    'card_declined',
    'payment_failed',
    'card_expired',
    'invalid_card_number',
    'invalid_cvc',
    'processing_error'
  ];

  if (!error) return SUPPORT_MESSAGE;

  // Check if it's a payment-related error that should be shown directly
  const shouldShowDirectly = directErrors.some(errType => 
    error.message?.toLowerCase().includes(errType) || 
    error.type?.toLowerCase().includes(errType)
  );

  // If it's a payment error containing sensitive info (like API keys)
  const containsSensitiveInfo = error.message?.includes('sk_') || 
                               error.message?.includes('pk_') ||
                               error.message?.includes('API Key');

  if (shouldShowDirectly && !containsSensitiveInfo) {
    return error.message || error.toString();
  }

  // For payment errors with sensitive info, return a generic payment error
  if (containsSensitiveInfo) {
    return "Payment processing failed. Please try again or contact support.";
  }

  return SUPPORT_MESSAGE;
};