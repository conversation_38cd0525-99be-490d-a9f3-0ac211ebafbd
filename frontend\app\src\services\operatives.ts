import { HttpClient } from 'src/client/http-client';
import client from '../client';

export default class Operative {
  static async list(queryParams: string) {
    const url = 'users/operatives?' + queryParams;
    try {
      const response = await HttpClient.get<any>(url);
      if (!response.error) {
        return response;
      }
    } catch (error) {
      console.error(error);
    }
  }

  create() {}
}

export const getMapOperatives = async (filterString = '') => {
  try {
    const fullFilterString = filterString + (filterString ? '&' : '') + 'per_page=100000';
    const response = await client.operatives.getAllOperatives(fullFilterString);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getOperative = async (id: number, clientId?: number | undefined) => {
  try {
    const response = await client.operatives.getOperative(id, clientId);

    if (!response.error) {
      return response;
    }
  } catch (error) {
    // console.error(error);
  }
};

export const removeOperatorsFavorite = async (id: any) => {
  try {
    const response = await client.favourites.removeOperatorsFavorite(id);
    if (!response.error) {
      return response;
    }
  } catch (error) {}
};

export const addOperatorsFavorite = async (id: any) => {
  try {
    const response = await client.favourites.addOperatorsFavorite(id);
    if (!response.error) {
      return response;
    }
  } catch (error) {}
};

export const applyForJob = async (applyData: { id: any; proposal: any; payRate: any; positionId: any; chat_id: any; type: string }) => {
  const { id, proposal, payRate, positionId, chat_id, type } = applyData;

  const finalData = {
    id,
    notes: proposal,
    pay_rate: payRate,
    position_id: positionId,
    chat_id,
    type,
  };

  try {
    const response = await client.operatives.applyForJob(id, finalData);
    if (!response.error) {
      return response;
    }
  } catch (error: any) {
    if (error.response && error.response.data && error.response.data.message) {
      return error.response.data.message;
    } else {
      console.error(error);
    }
  }
};

export const reportOperative = async (applyData: { id: any; details: any; document: any }) => {
  const { id, details, document } = applyData;

  const finalData = {
    user_id: id,
    comment: details,
    files: document,
  };

  try {
    const response = await client.operatives.reportOperative(finalData);
    if (!response.error) {
      return response;
    } else {
      throw new Error(response.message);
    }
  } catch (error) {
    throw error;
  }
};
