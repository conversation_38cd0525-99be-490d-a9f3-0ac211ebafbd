// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion, Card } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import codeofconduct from '../../assets/images/codeofconduct/codeofconduct.png';
import sign from '../../assets/images/codeofconduct/sign.svg';
import one from '../../assets/images/codeofconduct/one.svg';
import two from '../../assets/images/codeofconduct/two.svg';
import three from '../../assets/images/codeofconduct/three.svg';
import four from '../../assets/images/codeofconduct/four.svg';
import five from '../../assets/images/codeofconduct/five.svg';
import six from '../../assets/images/codeofconduct/six.svg';
import seven from '../../assets/images/codeofconduct/seven.svg';

const CodeofConduct: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex lg:flex-row flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <img
            src={codeofconduct}
            className='w-full lg:w-auto h-[300px] lg:h-[849px] mt-[64px] mt-[64px] sm:mb-[64px]'
          />
          <View className='flex flex-col sm:w-[675px] lg:h-[849px] sm:p-[50px] px-[12px] py-[50px] bg-[#FFFF] lg:mt-[64px] mb-[64px]'>
            <Text className='text-left font-rufina-stencil text-[48px] font-normal leading-[56px] text-[#323C58]'>
              Code of Conduct
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[12px]'>
              Surely’s Code of Conduct sets out the behaviour we expect of security operatives in the delivery of job
              posts listed by clients on our platform. At its heart, the aim of our Code of Conduct is a combination of
              three core values – on-the-job professionalism, respectful communications and personal integrity.
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[25px]'>
              All security operatives must agree with our code of conduct when signing up to the platform. It is part of
              the barometer by which we measure our success as an organisation in achieving our primary goal - to
              transform the security operative marketplace for the better in every possible way.
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[25px]'>
              We do not believe that we are asking security operatives for anything onerous or complicated, although we
              do accept that it might take a little time for some individuals to get used to a standard of behaviour
              that we all aspire to, but that we perhaps fall short of as individuals, from time to time.
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[25px]'>
              In general, we ask you to be kind and considerate in the way that you interact with others. By being
              polite and kind in the way that we communicate with other stakeholders, both on the platform and on the
              job, we believe we will encourage them to behave in a similar way towards us and others too.
            </Text>
            <div className='items-start bg-[#388DD8] w-[160px] h-[4px]  mt-[28px]' />
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[28px]'>
              With warm regards,
            </Text>
            <img src={sign} className='w-[242.356px] h-[42.756px] mt-[11px]' />
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[11px]'>
              Aliona & Aurela, Founders
            </Text>
          </View>
        </View>
      </View>

      <View className='w-full bg-[#FFFF]'>
        <View className=' flex flex-col max-w-[1100px] mx-auto px-[12px] lg:px-[30px]   lg:items-center '>
          <Text className='!text-[#383838] font-rufina-stencil sm:text-center text-[32px] font-normal leading-[40px] mt-[64px] sm:w-[601px]  mx-auto'>
            Our magnificent seven core commitments are as follows:
          </Text>
          <View className='flex sm:flex-row flex-col sm:gap-[70px] gap-[50px] mt-[39px]  '>
            <View className='flex flex-row gap-[9px] items-center'>
              <img src={one} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium'>Acting professionally</Text>
            </View>
            <View className='flex flex-row gap-[9px] items-center '>
              <img src={two} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium'>Being inclusive</Text>
            </View>
            <View className='flex flex-row gap-[9px] items-center '>
              <img src={three} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium'>Being trustworthy</Text>
            </View>
          </View>
          <View className='flex sm:flex-row flex-col sm:gap-[70px] gap-[50px] sm:mt-[14px] mt-[50px]  '>
            <View className='flex flex-row gap-[9px] items-center'>
              <img src={four} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium '>Communicating clearly</Text>
            </View>
            <View className='flex flex-row gap-[9px] items-center '>
              <img src={five} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium whitespace-nowrap'>Being accountable</Text>
            </View>
            <View className='flex flex-row gap-[9px] items-center '>
              <img src={six} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium whitespace-nowrap'>Acting fairly</Text>
            </View>
            <View className='flex flex-row gap-[9px] items-center '>
              <img src={seven} />
              <Text className='rubik text-[24px] leading-[32px] font-mendium whitespace-nowrap'>Being positive</Text>
            </View>
          </View>
          <div className='bg-[#388DD8] sm:w-[1203px] h-[4px]  mt-[28px]' />
          <Text className='sm:w-[701px] sm:text-center mx-auto rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[53.9px] mb-[64.5px]'>
            Each of these commitments should be applied in an appropriate manner to all other stakeholders that’s not
            just to clients, other security operatives and the general public; it also includes local authorities,
            emergency services and the SIA, as well as any third parties who have a vested interested in the positive
            transformation of the security operative marketplace for the benefit of all concerned.
          </Text>
        </View>
      </View>

      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col  w-full max-w-[1320px] mx-auto  text-center '>
          <View className='flex sm:flex-row flex-col gap-[24px] mt-[64.5px] px-[12px] xl:px-0  '>
            <Card className='flex flex-col items-start justify-center gap-[12px] sm:w-[648px] sm:p-[50px] py-[30px]  bg-[#FFF] border-none'>
              <img src={one} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Acting professionally
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will do our utmost to act professionally in the delivery of our work, protecting people and property
                to the best of our ability. We will commit to a process of continuing professional development (CPD), to
                ensure that our qualifications grow alongside our experience on an ongoing basis. We will turn up for
                work on time, dress appropriately, and deliver excellent customer service.
              </Text>
            </Card>
            <Card className='flex flex-col items-start justify-center gap-[12px]  sm:w-[648px] sm:p-[50px] py-[30px] bg-[#FFF] border-none'>
              <img src={two} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Being inclusive
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will always the nine protected characteristics within the Equality Act 2010 - not just in a legal
                sense, but in a genuine personal way too. We will do our best to overcome internal prejudices and
                unconscious biases as we become aware of them, to ensure that everyone feels safe and welcome. We will
                make a determined effort to accept diversity and promote equality in all their various forms.
              </Text>
            </Card>
          </View>
          <View className='flex sm:flex-row flex-col gap-[24px] mt-[24px] px-[12px] xl:px-0  '>
            <Card className='flex flex-col items-start justify-center gap-[12px] sm:w-[648px] sm:p-[50px] py-[30px] bg-[#FFF] border-none'>
              <img src={three} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Being trustworthy
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will commit ourselves to being authentic and honest in the way that we conduct ourselves. We will
                ensure that our profiles are a true reflection of our experience and knowledge as well as our
                qualifications and certifications. We will be reliable, turning up on time when booked, and do what we
                say we will do. We will be authentic, genuine and consistent in the way we conduct ourselves.
              </Text>
            </Card>
            <Card className='flex flex-col items-start justify-center gap-[12px] sm:w-[648px] sm:p-[50px] py-[30px] bg-[#FFF] border-none'>
              <img src={four} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Communicating clearly
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will avoid ambiguity in the way we communicate with others, and in a manner which implicitly
                demonstrates our professionalism, confidence and authority. We will seek clarity if we need to do so,
                and make sure that we understand any given situation as fully as possible. We will focus on our tone of
                voice and body language as well as the words we use, to ensure we are communicating clearly.
              </Text>
            </Card>
          </View>
          <View className='flex sm:flex-row flex-col  gap-[24px] mt-[24px] px-[12px] xl:px-0 '>
            <Card className='flex flex-col items-start justify-center gap-[12px] sm:w-[648px] sm:p-[50px] py-[30px] bg-[#FFF] border-none'>
              <img src={five} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Being accountable
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will take responsibility for our actions and monitor the consequences of our decision-making process,
                and finetune our approach dynamically in any given situation to deliver the best possible outcome. We
                will avoid reacting in an inappropriate and emotional way to provocation from third parties, no matter
                how reasonable it might be to do so; instead, we will make good, rational choices.
              </Text>
            </Card>
            <Card className='flex flex-col items-start justify-center gap-[12px] sm:w-[648px] sm:p-[50px] py-[30px] bg-[#FFF] border-none'>
              <img src={six} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Acting fairly
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will be decent and honourable in our approach and treat each incident on its own merits. We will be
                tolerant and impartial in the way that we conduct ourselves, keeping calm and in control of our
                emotions. We will make decisions based upon a thorough review of relevant information available at the
                time, ask questions to seek clarity if we need to do so, and communicate our decisions clearly.
              </Text>
            </Card>
          </View>
          <View className='flex flex-row gap-[24px] mt-[24px] px-[12px] xl:px-0'>
            <Card className='flex flex-col items-start justify-center gap-[12px] xl:w-[648px] sm:p-[50px] py-[30px] bg-[#FFF] border-none'>
              <img src={seven} />
              <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58] mt-[12px]'>
                Being positive
              </Text>
              <Text className='rubik text-[16px] font-normal leading-[24px] text-[#383838] mt-[12px]'>
                We will foster a positive attitude in the way that we conduct ourselves, differentiating between the
                good person and the bad act. We will encourage others to rise to our level rather than us descending to
                theirs. We will seek win-win solutions that deliver a beneficial outcome to all parties concerned, in a
                tactful and considerate manner. Our approach will open doors to better future behaviour by others.
              </Text>
            </Card>
          </View>
        </View>
        <Text className='xl:text-center rubik text-[16px] font-normal text-[#383838] leading-[24px] mt-[64.3px] mx-auto px-[12px] xl:px-0 xl:w-[663px] mb-[125px]'>
          We see our Code of Conduct as being dynamic in nature, and we expect that it will evolve naturally over time.
          We welcome your input if you feel we need to make any changes. If so, please email us at&nbsp;
          <a className='cursor-pointer' href='mailto:<EMAIL>'>
            <EMAIL>
          </a>
          &nbsp;– we will respond to all suggestions and let you know if we introduce them.
        </Text>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <Footer />
    </View>
  );
};

export default CodeofConduct;
