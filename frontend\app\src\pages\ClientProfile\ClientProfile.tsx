// @ts-nocheck
import { useParams } from 'react-router-dom';

import ClientBio from 'src/components/Profile/ClientProfileComponents/ClientBio/ClientBio';
import ClientEmploymentHistory
  from 'src/components/Profile/ClientProfileComponents/ClientEmploymentHistory/ClientEmploymentHistory';
import ClientOverallRating
  from 'src/components/Profile/ClientProfileComponents/ClientOverallRating/ClientOverallRating';
import ClientRelevantQualification
  from 'src/components/Profile/ClientProfileComponents/ClientRelevantQualification/ClientRelevantQualification';
import ClientSecurityAchievement
  from 'src/components/Profile/ClientProfileComponents/ClientSecurityAchievement/ClientSecurityAchievement';

interface Props {
  operative?: React.ReactNode;
}

export const ClientProfile = ({operative}: Props): JSX.Element => {
  const { operatorId } = useParams();

  if (!operatorId) {
    return <div>Operator ID not provided</div>;
  }

  return (
    <div className='flex flex-col gap-6 md:mt-[65px]'>
      <div className='flex flex-col gap-6 lg:flex-row'>
        <ClientBio oper={operative || {}} />
        <ClientSecurityAchievement oper={operative || {}} />
      </div>
      {/* <ClientOverallRating oper={operative || {}} />  */}
      <div className='flex flex-col gap-6 lg:flex-row'>
        <ClientRelevantQualification oper={operative || {}} />
        <ClientEmploymentHistory oper={operative || {}} />
      </div>
    </div>
  );
};

export default ClientProfile;
