import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';
import OperatorSearchBar from 'src/components/OperatorSearchBar/OperatorSearchBar';

interface Props {
  children?: React.ReactNode;
}

//TODO: delete this file
export const FavoriteJobsLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container
      padding={0}
      className="w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover"
    >
      <Header />
      <main className=' align-center mt-[80px]  w-full max-w-[1320px]   mx-auto    text-center '>
        <section className='w-full mt-[61px]'>
          <p className='text-center lg:text-start font-rufina-stencil text-2xl font-normal leading-10 text-left mb-[30px]'>
            Favorite Jobs
          </p>
          <div className="lg:sticky lg:top-[90px] lg:z-50 lg:min-h-[56px]">
          <OperatorSearchBar />
          </div>
          <article className='flex flex-row gap-5 flex-wrap justify-center mt-[61px]'>
            {children}
          </article>
        </section>
      </main>
    </Container>
  );
};

export default FavoriteJobsLayout;
