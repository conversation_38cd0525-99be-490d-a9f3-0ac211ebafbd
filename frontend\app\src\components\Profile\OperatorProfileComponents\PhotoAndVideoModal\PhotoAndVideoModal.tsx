import React from 'react';
import { Modal, View, Image } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';

interface PhotoAndVideoModalProps {
  active: boolean;
  deactivate: () => void;
  mediaList: string[] | null;
  currentMediaIndex: number;
  onNext: () => void;
  onPrevious: () => void;
}

const PhotoAndVideoModal: React.FC<PhotoAndVideoModalProps> = ({
  active,
  deactivate,
  mediaList,
  currentMediaIndex,
  onNext,
  onPrevious
}) => {
  const getMediaSrc = (src: string) => {
    const baseUrl = 'https://app.surelysecurity.com/storage/';
    return src.startsWith(baseUrl) ? src : baseUrl + src;
  };

  return (
    <Modal active={active} onClose={deactivate} className='h-auto w-auto sm:h-auto sm:w-autoxx'>
      <View className='mx-auto'>
        <View className='mt-[-8px] flex items-center p-0'>
          <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end bg-transparent'>
            <span className='material-icons text-500 align-middle'>close</span>
          </button>
        </View>
        <View className='mx-auto mt-[10px]'>
          {mediaList && (
            <>
              {mediaList[currentMediaIndex].endsWith('.mp4') ||
              mediaList[currentMediaIndex].endsWith('.webm') ||
              mediaList[currentMediaIndex].endsWith('.mkv') ? (
                <video src={getMediaSrc(mediaList[currentMediaIndex])} controls className='max-h-[430px] w-full' />
              ) : (
                <Image src={getMediaSrc(mediaList[currentMediaIndex])} className='max-h-[430px] w-full' />
              )}
            </>
          )}
        </View>

        {mediaList && mediaList.length > 1 && (
          <View className='mt-4 flex justify-between'>
            {currentMediaIndex > 0 ? (
              <button className='btn-no-hover' onClick={onPrevious}>
                <span className='material-icons text-[40px]'>chevron_left</span>
              </button>
            ) : (
              <div></div>
            )}

            {currentMediaIndex < mediaList.length - 1 ? (
              <button className='btn-no-hover' onClick={onNext}>
                <span className='material-icons text-[40px]'>chevron_right</span>
              </button>
            ) : (
              <div></div>
            )}
          </View>
        )}
      </View>
    </Modal>
  );
};

export default PhotoAndVideoModal;
