<?php

namespace App\DataTables;

use App\Models\Review;
use Carbon\Carbon;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class ReviewDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('action', 'reviews.contract')
            ->editColumn('name', function ($row) {
                return "<div>
                            <p class='m-0 h5'>$row->name</p>
                            <a href='mailTo:$row->email' class='text-primary'> $row->email </a>
                        </div>";
            })
            ->editColumn('phone', function ($row) {
                return "<div>
                            <a href='tel:$row->phone' class='text-primary h5'> $row->phone </a>
                        </div>";
            })
            ->editColumn('job_post', function ($row) {
                return "<div>
                            <p class='text-black'> $row->job_post</p>
                        </div>";
            })
            ->editColumn('review', function ($row) {
                return "<p class='cut-text' style=''>$row->review</p>";
            })
            ->editColumn('rating_avg', function ($row) {
                return "<p class=''>$row->rating_avg</p>";
            })
            ->editColumn('date', function ($row) {
                return Carbon::parse($row->date)->format('d-m-Y');
            })
            ->rawColumns(['action', 'rating_avg', 'review', 'name', 'phone', 'job_post'])
            ->filter(function ($query) {
                $q = request()->get('search')['value'];
                $query->where('user.name', 'like', "%$q%")
                    ->orWhere('user.email', 'like', "%$q%")
                    ->orWhere('user.phone', 'like', "%$q%")
                    ->orWhere('contract_id', 'like', "%$q%")
                    ->orWhere('reviews.created_at', 'like', "%$q%")
                    ->orWhere('job.post_name', 'like', "%$q%");
            });
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Review $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Review $model)
    {
        return $model->query()
            ->leftJoin('mobile_users as user', 'user.id', 'reviews.author_id')
            ->leftJoin('contracts as contract', 'contract.id', 'reviews.contract_id')
            ->leftJoin('mobile_users as operative', 'operative.id', '=', 'contract.operative_id')
            ->leftJoin('mobile_users as client', 'client.id', '=', 'contract.client_id')
            ->leftJoin('jobs as job', 'job.id', 'contract.job_id')
            ->select([
                'reviews.id as id',
                'user.name as name',
                'user.email as email',
                'user.phone as phone',
                'job.post_name as job_post',
                'reviews.rating_avg as rating_avg',
                'contract.id as contract_id',
                'reviews.created_at as date',
                'reviews.comment as review',
                'operative.name as operative_name',
                'operative.email as operative_email',
                'operative.phone as operative_phone',
                'client.name as client_name',
                'client.email as client_email',
                'client.phone as client_phone',
                'contract.hourly_rate as hourly_rate',
                'contract.location as contract_location',
                'contract.start_date as contract_start_date',
                'contract.end_date as contract_end_date'
            ]);
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('review-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->parameters([
                "dom" => "<'dt--top-section'<'row'<'col-sm-12 col-md-6 d-flex justify-content-md-start justify-content-center'B><'col-sm-12 col-md-6 d-flex justify-content-md-end justify-content-center mt-md-0 mt-3'f>>>" .
                    "<'table-responsive'tr>" .
                    "<'dt--bottom-section d-sm-flex justify-content-sm-between text-center'<'dt--pages-count  mb-sm-0 mb-3'i><'dt--pagination'p><'dt--page-length'l>>",
                "lengthMenu" => array(10, 20, 50, 100, 10000),
                'buttons' => [
                    'buttons' => [
                        ['extend' => 'copy', 'className' => 'btn'],
                        ['extend' => 'csv', 'className' => 'btn'],
                        ['extend' => 'excel', 'className' => 'btn'],
                        ['extend' => 'print', 'className' => 'btn'],
                    ]
                ],
                'oLanguage' => [
                    'oPaginate' => [
                        'sPrevious' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                        "sNext" => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                    ],
                    'sInfo' => 'Showing page _PAGE_ of _PAGES_ | Results: _TOTAL_',
                    'sSearch' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                    'sSearchPlaceholder' => 'Search...',
                    "sLengthMenu" => "Results :  _MENU_",
                ],
                'stripeClasses' => [],
            ])
            ->orderBy(1, 'DESC')
            ->ajax([
                'url' => '/reviews',
                'type' => 'GET',
                'scheme' => 'https',
            ]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id'),
            Column::make('name')
                ->title('User')
                ->addClass('col-2'),
            Column::make('phone'),
            Column::make('job_post'),
            Column::make('rating_avg')
                ->addClass('col-2'),
            Column::make('date'),
            Column::make('review')
                ->addClass('col-2'),
            Column::computed('action')
                ->title('Contract')
                ->exportable(false)
                ->printable(false)
                ->width(100)
                ->addClass('col-2')
                ->addClass('text-center'),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'Review_' . date('YmdHis');
    }
}
