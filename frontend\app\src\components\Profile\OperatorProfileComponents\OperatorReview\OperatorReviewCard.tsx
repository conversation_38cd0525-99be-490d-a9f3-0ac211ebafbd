import React from 'react';
import { Card, Text, View } from 'reshaped';
import {Review} from '../../../../pages/OperatorProfilePage/store/OperatorProfilePageDummyData'


const OperatorReviewCard: React.FC<any> = ({ review }: { review: Review }) => {
  return (
    <Card className='flex flex-col'>
      <View className='flex items-center justify-between'>
        {[...Array(5)].map((_, starIndex) => (
          <span
            key={starIndex}
            className={`material-icons ${
              starIndex < Math.round(review.rate)
                ? 'text-yellow-400'
                : 'text-gray-400'
            }`}
          >
            star
          </span>
        ))}
        <Text className='text-neutral text-base font-medium leading-5 mt-[6px]'>
          {review.rate?.toFixed(1)}
        </Text>
        <Text className='text-neutral-faded text-xs font-normal leading-4 mt-[5px]'>
          {review.date}
        </Text>
      </View>
      <Text className='text-neutral text-base font-medium leading-5 mt-[8px]'>
        {review.name}
      </Text>
      <Text className='text-gray-300 text-base italic font-normal leading-5 mt-[8px] !text-[#383838]'>
        {review.comment}
      </Text>
    </Card>
  );
};

export default OperatorReviewCard;
