import React, { useContext } from 'react';
import { Text, View, Button, Modal } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from 'src/context/AuthContext';

interface CloseAccountCreatorModalProps {
  active: boolean;
  deactivate: () => void;
}

const CloseAccountCreatorModal: React.FC<CloseAccountCreatorModalProps> = ({ active, deactivate }) => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const accountType = user?.profile?.account_type;

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[474px]'>
      <View className='flex flex-col'>
        <svg xmlns='http://www.w3.org/2000/svg' width='47' height='47' viewBox='0 0 47 47' fill='none'>
          <path
            d='M23.5 10.4212L39.2445 41.9191H7.75545L23.5 10.4212ZM23.5 0.76123L0.5 46.7612H46.5L23.5 0.76123ZM25.5909 34.656H21.4091V39.4981H25.5909V34.656ZM25.5909 20.1297H21.4091V29.8139H25.5909V20.1297Z'
            fill='#CB101D'
          />
        </svg>
        <Text className='rubik mt-[16px] text-[20px] font-normal leading-[28px] text-[#1A1A1A] '>Closing account creator.</Text>
        <Text className='rubik mt-[8px] text-[14px] font-normal leading-[20px] text-[#323C58]'>
          Are you sure you want to close the account creator? Your progress will be lost.
        </Text>

        <View className='mt-[20px] flex flex-row justify-between'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='border-neutral bg-background-base mr-[10px] flex h-[48px] w-[195px] items-center justify-center gap-2 rounded-[8px] border px-4 py-2'
          >
            <Text className='rubik text-[14px] font-medium leading-[24px] text-[#323C58] lg:text-[16px]'>No, go back</Text>
          </Button>
          <Button
            onClick={() => {
              if (accountType === 1) {
                navigate('/my-profile');
              } else if (accountType === 2) {
                navigate('/client-dashboard');
              } else {
                navigate('/');
              }
            }}
            
            className='border-neutral bg-background-base flex  h-[48px] w-[203px] items-center justify-center rounded-[8px] border !bg-[#CB101D]'
          >
            <Text className='rubik text-[14px] font-medium leading-[24px] text-[#FFFFFF] lg:text-[16px]'>Close account creator</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default CloseAccountCreatorModal;
