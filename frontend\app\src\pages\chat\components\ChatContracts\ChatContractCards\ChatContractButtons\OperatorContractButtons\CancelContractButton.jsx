import { useToggle } from 'reshaped';
import RefuseContractModal from '../../../../ChatModals/RefuseContractModal';

const OperatorCancelContractButton = ({ refuseContract, contract }) => {
  const { active, activate, deactivate } = useToggle(false);
  const buttonLabel = contract?.status === 'Pending' ? 'Refuse contract' : 'Cancel contract';

  return (
    <div className='rubik height-[156px] flex flex-col gap-3 text-[16px] font-medium'>
      <div
        onClick={activate}
        className='flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg border border-[#DFE2EA] py-[10.5px] text-[#CB101D]'
      >
        <span className='material-icons'>close</span>
        {buttonLabel}
      </div>
      <RefuseContractModal
        active={active}
        deactivate={deactivate}
        confirm={() => {
          refuseContract();
          deactivate();
        }}
        contract={contract}
      />
    </div>
  );
};

export default OperatorCancelContractButton;
