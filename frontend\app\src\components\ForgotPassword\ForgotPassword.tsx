// @ts-nocheck
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Text, View, Button, Divider, Modal, TextField, useToast, Image } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

interface ForgotPasswordProps {
  active: boolean;
  deactivate: () => void;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ active, deactivate }) => {
  const toast = useToast();
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [password, setPassword] = useState('');
  const [passwordConfirmation, setPasswordConfirmation] = useState('');
  const [isPasswordVisible1, setPasswordVisible1] = useState(false);
  const [isPasswordVisible2, setPasswordVisible2] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [timer, setTimer] = useState(300);

  useEffect(() => {
    let countdown: NodeJS.Timeout;

    if (step === 2 && timer > 0) {
      countdown = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else if (step === 2 && timer === 0) {
      setTimer(300);
    }
    return () => {
      clearInterval(countdown);
    };
  }, [step, timer]);

  const handleBackClick = () => {
    if (step > 1) {
      if (step === 2) {
        setTimer(300);
      }
      setStep(step - 1);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  const storeVerificationToken = (token: string) => {
    localStorage.setItem('verification_token', token);
  };

  const getVerificationToken = () => {
    return localStorage.getItem('verification_token');
  };

  const togglePasswordVisibility1 = () => {
    setPasswordVisible1(!isPasswordVisible1);
  };
  const togglePasswordVisibility2 = () => {
    setPasswordVisible2(!isPasswordVisible2);
  };

  const handleStep2Error = (error: string) => {
    setErrorMessage(error);
  };

  const isValidEmail = (email: any) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  const handleSubmit = () => {
    setErrorMessage(null);
    if (step === 1) {
      if (!isValidEmail(email)) {
        toast.show({
          title: 'Please enter a valid email.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }
      const data = { email };
      axios
        .post(import.meta.env.VITE_REACT_APP_BASE_URL + '/send_forgot_password', data)
        .then((response) => {
          setStep(2);
        })
        .catch((error) => {
          console.error('Error sending email', error);
        });
    } else if (step === 2) {
      if (timer === 0) {
        toast.show({
          title: 'Verification code expired. Please request a new code.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });

        return;
      }
      if (!email.trim() || !verificationCode.trim()) {
        toast.show({
          title: 'Please enter a valid email and verification code.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }
      const data = { email, code: verificationCode };
      axios
        .post(import.meta.env.VITE_REACT_APP_BASE_URL + '/confirm_forgot_code', data)
        .then((response) => {
          if (response.data.error === true) {
            alert(response.data.message);
          } else storeVerificationToken(response.data.verification_token);
          setStep(3);
        })
        .catch((error) => {
          console.error('Error verifying email and code', error);
          handleStep2Error('Invalid email or verification code.');
        });
    } else if (step === 3) {
      if (!email.trim() || !verificationCode.trim() || !password.trim() || !passwordConfirmation.trim()) {
        alert('Please fill in all fields.');
        return;
      }
      const storedVerificationToken = getVerificationToken();
      if (!storedVerificationToken) {
        toast.show({
          title: 'Verification token not found.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }

      const data = {
        email,
        verification_token: storedVerificationToken,
        password,
        password_confirmation: passwordConfirmation,
      };
      axios
        .post(import.meta.env.VITE_REACT_APP_BASE_URL + '/new_password', data)
        .then((response) => {
          toast.show({
            title: 'Password Changed.',
            startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
          });
          deactivate();
        })
        .catch((error) => {
          console.error('Error setting new password', error);
        });
    }
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px]'>
      <View className='mt-[16px] flex items-center p-0'>
        <Text className='font-rufina-stencil text-[32px] text-[#323C58]'>Forgot Password</Text>
        <span className='material-icons text-500 ml-auto mr-0 flex items-center justify-end' onClick={deactivate}>
          close
        </span>
      </View>

      {step === 1 && (
        <div>
          <View className='mt-[15px] flex flex-col'>
            <Text className='rubik mt-[5px] text-[20px] leading-4 text-[#323C58]'>First Step</Text>
            <Text className='text-neutral rubik mt-[16px] text-base font-medium leading-4'>Email</Text>
            <input
              name='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              // className='w-[376px] h-[48px] mt-[12px]'
              className='rubik mt-[12px] w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D]'
              placeholder='Your email address'
            />
          </View>
          <Divider className='mt-[12px] h-[1px] w-full' />
        </div>
      )}

      {step === 2 && (
        <div>
          <View className='mt-[10px] flex flex-col'>
            <Text className='rubik mt-[5px] text-[20px] leading-4 text-[#323C58]'>Second Step</Text>
            <View className='mt-[16px] flex flex-row justify-between'>
              <Text className='text-neutral rubik text-[17px] text-base leading-4 '>6-Digits verification Code</Text>
              <Text className='text-neutral rubik text-[17px] text-base leading-4 '>Timer: {formatTime(timer)}</Text>
            </View>
            <input
              name='verificationCode'
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              // className='w-[376px] h-[48px] mt-[12px]'
              className='rubik mt-[12px] w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D]'
              placeholder='Verification Code'
            />
          </View>
          {errorMessage && <Text className='text-red-500'>{errorMessage}</Text>}
          <Divider className='mt-[12px] h-[1px] w-full ' />
        </div>
      )}

      {step === 3 && (
        <div>
          <View className='mt-[10px] flex flex-col'>
            <Text className='rubik mt-[5px] text-[20px] leading-4 text-[#323C58]'>Third Step</Text>
            <Text className='text-neutral rubik mt-[16px] text-base font-medium leading-4'>Password</Text>
            <TextField
              name='password'
              value={password}
              onChange={(e) => setPassword(e.value)}
              // className='w-[376px] h-[48px] mt-[12px]'
              className='rubik mt-[12px] w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D]'
              placeholder='. . . . . . . . . .'
              inputAttributes={{
                type: isPasswordVisible1 ? 'text' : 'password',
              }}
              endSlot={
                <Button
                  variant='ghost'
                  className='material-icons-outlined ml-2 cursor-pointer text-gray-500'
                  onClick={togglePasswordVisibility1}
                  icon={() => <span className='material-icons-outlined'>{isPasswordVisible1 ? 'visibility' : 'visibility_off'}</span>}
                />
              }
            />
            <Text className='text-neutral rubik mt-[16px] text-base font-medium leading-4'>Confirm Password</Text>
            <TextField
              name='password_confirmation'
              value={passwordConfirmation}
              onChange={(e) => setPasswordConfirmation(e.value)}
              // className='w-[376px] h-[48px] mt-[12px]'
              className='rubik mt-[12px] w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D]'
              placeholder='. . . . . . . . . .'
              endSlot={
                <Button
                  variant='ghost'
                  className='material-icons-outlined ml-2 cursor-pointer text-gray-500'
                  onClick={togglePasswordVisibility2}
                  icon={() => <span className='material-icons-outlined'>{isPasswordVisible2 ? 'visibility' : 'visibility_off'}</span>}
                />
              }
              inputAttributes={{
                type: isPasswordVisible2 ? 'text' : 'password',
              }}
            />
          </View>
          <Divider className='mt-[8px] h-[1px] w-full' />
        </div>
      )}

      <View className='mt-[16px] flex flex-row justify-end'>
        {step === 1 && (
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutral bg-background-base mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded border px-4 py-2'
          >
            Cancel
          </Button>
        )}
        {step > 1 && (
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>arrow_back_ios</span>}
            onClick={handleBackClick}
            className='border-neutral bg-background-base mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded border px-4 py-2'
          >
            Back
          </Button>
        )}
        <Button
          onClick={() => handleSubmit()}
          className='border-neutral bg-background-base flex h-[48px] w-[180px] items-center justify-center gap-2 rounded border !bg-[#0B80E7] px-4 py-2 !text-white'
        >
          {step === 1 || step === 2 ? 'Next' : 'Submit'}
        </Button>
      </View>
    </Modal>
  );
};

export default ForgotPassword;
