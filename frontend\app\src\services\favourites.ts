// @ts-nocheck
import client from 'src/client';

export const getAllFavouriteJobs = async () => {
  try {
    const response = await client.favourites.getAllFavouriteJobs();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

// export const getAllFavouriteOperators = async () => {
//   try {
//     const response = await client.favourites.getAllFavouriteOperators();
//     if (!response.error) {
//       return response;
//     }
//   } catch (error) {
//   }
// };

export const toggleFavourite = async (id: number) => {
  try {
    const response = await client.favourites.toggleFavourite(id);

    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};
