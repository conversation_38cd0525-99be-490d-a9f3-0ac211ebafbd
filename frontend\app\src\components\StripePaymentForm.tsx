// @ts-nocheck
import React, { useState } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { Button, View, Text, useToast } from 'reshaped';
import { addBankCardDetails } from 'src/services/settings';

const StripePaymentForm = ({ cardHolderName }: { cardHolderName: string }) => {
  const stripe = useStripe();
  const elements = useElements();
  const toast = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: elements.getElement(CardElement)!,
        billing_details: {
          name: cardHolderName,
        }
      });

      if (error) {
        toast.show({
          title: 'Error',
          text: error.message,
          type: 'error'
        });
        return;
      }

      const response = await addBankCardDetails({
        payment_method_id: paymentMethod.id,
        holder_name: cardHolderName
      });

      if (response?.error) {
        toast.show({
          title: 'Error',
          text: response.message || 'Failed to save card',
          type: 'error'
        });
      } else {
        toast.show({
          title: 'Success',
          text: 'Card saved successfully',
          type: 'success'
        });
      }
    } catch (err) {
      toast.show({
        title: 'Error',
        text: 'Failed to process payment method',
        type: 'error'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <CardElement
        options={{
          style: {
            base: {
              fontSize: '16px',
              color: '#424770',
              '::placeholder': {
                color: '#aab7c4',
              },
            },
            invalid: {
              color: '#9e2146',
            },
          },
        }}
      />
      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isProcessing ? 'Processing...' : 'Save Card'}
      </button>
    </form>
  );
};

export default StripePaymentForm;