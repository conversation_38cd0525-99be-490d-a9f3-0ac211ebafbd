import { Button, Text } from 'reshaped';

const CertifiedButton: React.FC = () => {
  return (
    <Button
      size='small'
      rounded={true}
      elevated={false}
      className='px-3 border border-[#05751F] !bg-[#E6FEF3]       '
    >
      <Text color='positive' className='flex items-center text-medium text-xs gap-2'>
        <span className='material-icons '>star</span>Close Protection
      </Text>
    </Button>
  );
};
export default CertifiedButton;
