// @ts-nocheck
import React, { useState, useContext } from 'react';
import { Button, Text, View, Image, useToggle } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import { headerLogo } from '../../assets/images';
import { conflictManagementQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/ConflictManagementQuestions';
import { customerServiceQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/CustumerServiceQuestions';
import { disabilityFocusQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/DisabilityFocusQuestions';
import { substanceAwarenessQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/SubstanceAwarenessQuestions';
import { useOfEquipmentsQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/UseOfEquipmentQuestions';
import { vulnerablePeopleQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/VulnerablePeopleQuestions';
import { inclusivityPledgeQuestions } from '../../store/dummydata/TrainingQuestionsDummyData/InclusivityPledgeQuestions';
import CancelTestModal from './CancelTestModal/CancelTestModal';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import { AppContext } from 'src/context/AppContext';

const SurelyProTraningCategories: React.FC = () => {
  const navigate = useNavigate();
  const { surelyProBadges } = useContext(AppContext);
  const [selectedType, setSelectedType] = useState<string | null>(null);

  const handleTypeSelection = (type: string, text: string) => {
    setSelectedType(type);

    const questionSet = getQuestionSet(type);
    const document = getDocument(type);
    const downloadDocument = getDownloadDocument(type);
    const video = getVideo(type);
    const minutes = getMinutes(type);

    navigate('/surelypro-traning-start-course', {
      state: { type, text, questionSet, document, downloadDocument, video, minutes },
    });
  };
  const getQuestionSet = (type: string) => {
    switch (type) {
      case 'CustomerService':
        return customerServiceQuestions;
      case 'UseOfEquipment':
        return useOfEquipmentsQuestions;
      case 'DisabilityFocus':
        return disabilityFocusQuestions;
      case 'SubstanceAwareness':
        return substanceAwarenessQuestions;
      case 'VulnerablePeople':
        return vulnerablePeopleQuestions;
      case 'ConflictManagament':
        return conflictManagementQuestions;
      case 'InclusivityPledge':
        return inclusivityPledgeQuestions;
      default:
        return [];
    }
  };

  const getDocument = (type: string) => {
    switch (type) {
      case 'CustomerService':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Customer%20Service%20Presentation%20v5.pdf';
      case 'UseOfEquipment':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Use%20of%20Equipment%20Presentation%20v3.pdf';
      case 'DisabilityFocus':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Disability%20Focus%20Presentation%20v4.pdf';
      case 'SubstanceAwareness':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Substance%20Awareness%20Presentation%20v11.pdf';
      case 'VulnerablePeople':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Vulnerable%20People%20Presentation%20v5.pdf';
      case 'ConflictManagament':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Conflict%20Management%20Presentation%20v7.pdf';
      case 'InclusivityPledge':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Inclusivity%20Pledge%20%207.pdf';
      default:
        return [];
    }
  };
  const getDownloadDocument = (type: string) => {
    switch (type) {
      case 'CustomerService':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Customer%20Service%20Presentation%20v5.pdf';
      case 'UseOfEquipment':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Use%20of%20Equipment%20Presentation%20v3.pdf';
      case 'DisabilityFocus':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Disability%20Focus%20Presentation%20v4.pdf';
      case 'SubstanceAwareness':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Substance%20Awareness%20Presentation%20v11.pdf';
      case 'VulnerablePeople':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Vulnerable%20People%20Presentation%20v5.pdf';
      case 'ConflictManagament':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Conflict%20Management%20Presentation%20v7.pdf';
      case 'InclusivityPledge':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/other/Surely%20Inclusivity%20Pledge%20%207.pdf';
      default:
        return [];
    }
  };

  const getVideo = (type: string) => {
    switch (type) {
      case 'CustomerService':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/Customer%20Service%20(16_9)%204k.mp4';
      case 'UseOfEquipment':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/Use%20of%20Equipment%20(16_9)%204k.mp4';
      case 'DisabilityFocus':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/Disability%20Focus%20(16_9)%204k.mp4';
      case 'SubstanceAwareness':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/Substance%20Awareness%20(16_9)%204k.mp4';
      case 'VulnerablePeople':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/VulnerablePeople%20(16_9)%204k.mp4';
      case 'ConflictManagament':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/ConflictManagement%20(16_9)%204k.mp4';
      case 'InclusivityPledge':
        return 'https://surely.ams3.cdn.digitaloceanspaces.com/video/James%20-%20Inclusivity%20Pledge_3.mp4';
      default:
        return [];
    }
  };

  const getMinutes = (type: string) => {
    switch (type) {
      case 'CustomerService':
        return 5;
      case 'UseOfEquipment':
        return 7;
      case 'DisabilityFocus':
        return 4;
      case 'SubstanceAwareness':
        return 5;
      case 'VulnerablePeople':
        return 6;
      case 'ConflictManagament':
        return 5;
      case 'InclusivityPledge':
        return 8;
      default:
        return [];
    }
  };

  const { active: active, activate: activate, deactivate: deactivate } = useToggle(false);

  const courses = [
    {
      type: 'InclusivityPledge',
      title: 'Inclusivity Pledge',
      description:
        'Complete our Inclusivity Pledge course to show your willingness in promoting workplace diversity, addressing biases, fostering effective communication, confronting discrimination, and understanding legal frameworks like the Equality Act 2010.',
      duration: '8 minutes',
    },
    {
      type: 'CustomerService',
      title: 'Customer Service',
      description:
        'Complete our Customer Service course to master communication, exceed customer expectations, and navigate various behaviours encountered in the security industry.',
      duration: '5 minutes',
    },
    {
      type: 'UseOfEquipment',
      title: 'Use of Equipment',
      description:
        'Enroll in our Radio Communication Course to elevate your skills and excel in delivering exceptional service while ensuring coordination and safety. ',
      duration: '7 minutes ',
    },
    {
      type: 'DisabilityFocus',
      title: 'Disability Focus',
      description:
        'Complete our Disability Focus course to understand disabilities, including invisible ones, and make reasonable adjustments. Learn to provide excellent customer service to people with disabilities and access valuable agency resources.',
      duration: '4 minutes',
    },
    {
      type: 'SubstanceAwareness',
      title: 'Substance Awareness',
      description:
        'Complete our Substance Awareness course to understand the reasons behind substance abuse, recognise various types of substances, and identify signs and symptoms. ',
      duration: '5 minutes ',
    },
    {
      type: 'VulnerablePeople',
      title: 'Vulnerable People',
      description:
        ' Complete our Vulnerable People course to grasp the definition and types of vulnerability, as well as essential considerations for interactions with such individuals.',
      duration: '6 minutes ',
    },
    {
      type: 'ConflictManagament',
      title: 'Conflict Managament',
      description:
        'Complete our Conflict Management course to learn essential communication skills, understand human responses in conflict, and master strategies for diffusing conflicts. ',
      duration: '5 minutes ',
    },
  ];

  const sortedCourses = courses.sort((a, b) => {
    const aHasBadge = surelyProBadges.some((badge: any) => badge.type === a.type);
    const bHasBadge = surelyProBadges.some((badge: any) => badge.type === b.type);

    return aHasBadge - bHasBadge;
  });

  return (
    <View className='flex flex-col gap-4 p-4 sm:p-8 md:p-12 lg:p-16 xl:p-20 2xl:p-24'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button onClick={activate} icon={() => <span className='material-icons mt-[-3px]'>close</span>} className='btn-no-hover !bg-transparent' />
      </View>

      <CancelTestModal active={active} deactivate={deactivate} />

      <View className='flex flex-col items-center justify-center gap-4 sm:gap-8'>
        <View className='flex flex-col sm:w-[80%] sm:flex-col md:w-[70%] lg:w-[60%] xl:w-[50%]'>
          <Text className='font-rufina-stencil text-center text-[32px] font-normal leading-[40px] text-[#1A1A1A]'>Let’s start your course</Text>
          <Text className='rubik mt-[4px] text-center text-base font-normal leading-[24px] text-[#323C58]'>
            Welcome to SurelyPro Courses! Our comprehensive range of courses empowers and equips you with the knowledge and skills to thrive in the
            security industry.
          </Text>
        </View>

        <View className='flex w-full flex-col sm:w-[80%] md:w-[70%] lg:w-[60%] xl:w-[50%]'>
          <View className='flex h-[80px] items-center gap-[16px] self-stretch rounded-t-2xl border border-[#DFE2EA] bg-[#FFF] p-6'>
            <Text className='rubik text-[16px] font-medium leading-[20px]'>SurelyPro badges</Text>
          </View>
          <View className='flex flex-col items-start  self-stretch overflow-auto rounded-b-lg border border-[#DFE2EA] bg-[#FFF] p-6 lg:h-[600px] '>
            <div>
              {sortedCourses.map((course) => (
                <div
                  key={course.type}
                  className={`mt-[16px] flex cursor-pointer flex-col items-start justify-center gap-[12px] self-stretch rounded-lg border p-4 ${
                    surelyProBadges.some((badge: any) => badge.type === course.type)
                      ? 'border-[#0B80E7] bg-[#DBDFEA]'
                      : 'border-[#DFE2EA] bg-[#F4F5F7]'
                  }`}
                  onClick={() => handleTypeSelection(course.type, course.title)}
                >
                  <Text className='rubik text-[16px] font-medium leading-[20px] text-[#000]'>{course.title}</Text>
                  <Text className='rubik text-[14px] font-normal leading-[20px] text-[#323C58]'>{course.description}</Text>
                  <View className='flex items-center justify-between self-stretch'>
                    <Text className='rubik text-[13px] font-normal leading-[16px] text-[#0B80E7]'>
                      {surelyProBadges.some((badge: any) => badge.type === course.type) ? 'Completed' : 'View details'}
                    </Text>
                    <Text className='rubik text-[13px] font-medium leading-[16px] text-[#323C58]'>{course.duration}</Text>
                  </View>
                </div>
              ))}
            </div>
          </View>
        </View>

        <View className='flex w-full flex-col gap-[12px] md:w-[70%]  lg:w-[60%] xl:w-[1320px] xl:gap-0'>
          <View className='mt-[16px] flex  flex-row justify-between'>
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined p-0 pb-1 text-[20px]'>close</span>}
              onClick={() => navigate('/my-profile')}
              className='rubik border-neutral bg-background-base flex  h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[white] px-4 py-2 text-[15px] !text-[#000000] xl:w-[180px]'
            >
              Close
            </Button>
          </View>
          <div className='flex items-center justify-center'>
            <Image src={headerLogo} className='flex-shrink-0 sm:h-[41.274px] sm:w-[109.76px]' />
          </div>
        </View>
      </View>
    </View>
  );
};

export default SurelyProTraningCategories;
