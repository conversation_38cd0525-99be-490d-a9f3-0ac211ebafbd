// @ts-nocheck
import { PaginatorInfo, QueryOptions } from './general';

// ----------- OUR TYPES -----------
export interface User {
  FirstName: string;
  SecondName: string;
  Shenime: string;
  Status_user: boolean;
  UserId: number;
  UserName: string;
  exp_date?: string;
  district_name: string;
  region_code: string;
  region_name: string;
  usr_birthdate: string;
  usr_city: string;
  usr_diploma: string;
  usr_email: string;
  usr_fax: string;
  usr_gender: string;
  usr_mobile: string;
  usr_phone?: string;
  usr_postcode: string;
  usr_short: string;
  usr_street: string;
  usr_titile: string;
  permissions: [];
  role_ids: number[];
  role_names: string[];
  email_verified: boolean;
}

export interface BaseBackendRegisterDataType {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  login_type: number;
  platform: number;
  app_version: string;
  firebase_token: string;
  account_type: number;
  phone: string;
  account_name: string;
  referal_code:any
}

export interface OperatorBackendRegisterDataType
  extends BaseBackendRegisterDataType {
    sia_licence_number: string;
  sia_licence_expiry_date: string;
  sia_licence_card_photo: string;
  document_type: string;
  id_front_document: string;
  id_back_document: string;
  address_verification_document: string;
  selfie_verification_document: string;
  account_name: string;
  postal_code:any;
  city :any;
  address_2: any;
  sia_licence_types:any;
  google_token:any;
  linkedin_token:any;
  redirect_uri: any;
}

export interface OperatorBackendValidateProfileDataType
  extends OperatorBackendValidateDataType {
    sia_licence_number: string;
  sia_licence_expiry_date: string;
  sia_licence_card_photo: string;
  document_type: string;
  id_front_document: string;
  id_back_document: string;
  address_verification_document: string;
  selfie_verification_document: string;
}

export interface ClientBackendRegisterDataType
  extends BaseBackendRegisterDataType {
  client_represent_business_as: string;
  company_registered_number: string;
  industry_sectors: string;
  selected_industry: string;
  account_name: string;
}

export interface LoginUserInput {
  email: string;
  password: string;
  loginType: number;
  platform: number;
  appVersion: string;
  firebaseToken: string;
  ref:any;
}

export type BackendLoginDataType = {
  email: string;
  password: string;
  password_confirmation: string;
  login_type: number;
  platform: number;
  app_version: string;
  firebase_token: string;
  phone?: string;
  account_type?: number;
  account_name?: string;
  google_token?: string;
  linkedin_token?: string;
  redirect_uri?: string;
};

export type Settings = {
  map(
    arg0: (setting: {

      companyName: any;
      companyWebsite: any;
      jobTitle: any;
      jobDescription: any;
      isCurrent: any;
      startDate: any;
      endDate: any;
      isReferenceLetter: any;
      referenceLetter: any;

      qualificationTitle: any;
      qualificationDescription: any;
      expiryDate: any;
      qualificationDocument: any;

      curiculumVitae:any;
    }) => {
      company_name: any;
      company_website: any;
      job_title: any;
      job_description: any;
      is_current: any;
      start_date: any;
      end_date: any;
      is_reference_letter: any;
      reference_letter: any;

      qualification_title?: any;
      qualification_description?: any;
      expiry_date?: any;
      qualification_document?: any;

      positionArray?: any[];
      qualificationArray?: any[];

      // cv:any;

    },
  ): unknown;
  name?: string;
  email?: string;
  phone?: string;
  postalCode?: string;
  postalTown?: string;
  lat?: string;
  lng?: string;
  addressLine?: string;
  postalCity?:string;
  town?: string;
  county?: string;
  siaLicenseType?: string;
  industrySector?: string;
  profilePhoto?: string;
  additionalPictures?: string[];
  profileVideo?: string;
  profileTitle?: string;
  profileDescription?: string;
  languages?: any;
  profileAditionalPhoto?: any;
  locationRange?: any;
  accountName?: string;
  sortCode?: string;
  accountNumber?: string;
  utrNumber?: string;
  vatNumber?: string;
  companyNumber?: string;
  oldPassword?: string;
  password?: string;

  companyName?: string;
  companyWebside?: string;
  jobTitle?: string;
  jobDescription?: string;
  isCurrent?: boolean;
  startDate?: string;
  endDate?: string;
  isReferenceLetter?: boolean;
  referenceLetter?: string;

  positionArray?: any;
  employments?: any;
  employmentData?: any;
  selectedFiles?: any;


  curiculumVitae?:any;

  instantBook?: any;

  passwordConfirmation?:any;

  siaLicence?:any;
  idCheck?:any;
  proofOfAddress?:any;
  employmentHistory?:any;
  creditCheck?:any;
  noCriminalRecord?:any;

  text?:any;
  type?:any;
};

export type BackendSettings = {
  name?: string;
  email?: string;
  phone?: string;
  postal_code?: string;
  city_id?: string;
  lat?: string;
  lng?: string;
  address?: string;
  address_1?: string;
  address_2?: string;
  address_3?:string;
  industrySector?: string;
  location_range?: any;
  sia_license_type?: string;
  industry_sector?: string;
  profile_photo?: string;
  additional_pictures?: string[];
  profile_video?: string;
  profile_title?: string;
  profile_description?: string;
  languages?: any;
  profile_aditional_photo?: any;

  account_name?: string;
  sort_code?: string;
  account_number?: string;
  utr_number?: string;
  vat_number?: string;
  company_number?: string;

  old_password?: string;
  password?: string;

  company_name?: string;
  company_website?: string;
  job_title?: string;
  job_description?: string;
  is_current?: boolean;
  start_date?: string;
  end_date?: string;
  is_reference_letter?: boolean;
  reference_letter?: string;

  positionArray?: any;

  employments?: any;

  qualification_title?: any;
  qualification_description?: any;
  expiry_date?: any;
  qualification_document?: any;

  cv?: any;

  instant_book?: any;
  password_confirmation?:any

  sia_licence?:any
  id_check?:any
  proof_of_address?:any
  employment_history?:any
  credit_check?:any
  no_criminal_record?:any;

  text?:any;
  type?:any;
};

// ----------- OUR TYPES END -----------

export interface UpdateUserInput extends Partial<User> {
  id: string;
}

export type SocialLoginInputType = {
  provider: string;
  access_token: string;
};

export type SendOtpCodeInputType = {
  phone: string;
};

export interface ForgotPasswordUserInput {
  email: string;
}

export interface ResetPasswordUserInput {
  email: string;
  token: string;
  password: string;
}

export interface VerifyForgotPasswordUserInput {
  token: string;
  email: string;
}

export interface ChangePasswordUserInput {
  oldPassword: string;
  newPassword: string;
}

export interface PasswordChangeResponse {
  success: boolean;
  message: string;
}

export interface AuthResponse {
  data: any;
  error: boolean;
  message: string;
  token: string;
}

export interface OTPResponse {
  message: string;
  success: boolean;
  provider: string;
  id: string;
  phone_number: string;
  is_contact_exist: boolean;
}

export interface VerifyOtpInputType {
  phone_number: string;
  code: string;
  otp_id: string;
}

export interface OtpLoginInputType {
  phone_number: string;
  code: string;
  otp_id: string;
  name?: string;
  email?: string;
}

export interface OTPVerifyResponse {
  success: string;
  message: string;
}

export interface CreateContactUsInput {
  name: string;
  email: string;
  subject: string;
  description: string;
}

export interface RegisterQueryOptions extends QueryOptions {
  name: string;
  orderBy: string;
  'page-size': number;
}

export type RegisterPaginator = PaginatorInfo<User>;
