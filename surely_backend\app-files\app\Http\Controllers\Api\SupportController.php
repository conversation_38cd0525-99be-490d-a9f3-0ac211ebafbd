<?php

namespace App\Http\Controllers\Api;

use App\DataTables\SupportDataTable;
use App\Http\Controllers\Controller;
use App\Models\Contract;
use App\Models\Support;
use App\Models\User;
use App\Notifications\AskForSupportNotification;
use Exception;
use Illuminate\Http\Request;
use SendGrid\Mail\Mail;

class SupportController extends Controller
{
    use \App\Traits\Helpers;

    public function store(Request $request, $contractId)
    {
        $contract = Contract::find($contractId);

        if (!$contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found with this id!'
            ]);
        }

        if ($contract->status != Contract::complete && $contract->status != Contract::canceled) {
            return response()->json([
                'error' => true,
                'message' => 'Contract is neither completed or canceled to ask for support!'
            ]);
        }

        if (auth()->id() != $contract->operative_id && auth()->id() != $contract->client_id) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot ask for support! You are not part of this contract!'
            ]);
        }

        if ($request->get('comment') == '') {
            return response()->json([
                'error' => true,
                'message' => "Comment shouldn't be an empty string",
            ]);
        }

        if ($request->has('files')) {
            $documents = [];
            if (count($request->get('files')) > 3) {
                return response()->json([
                    'error' => true,
                    'message' => "Cannot upload more than three files!",
                ]);
            }

            foreach ($request->get('files') as $file) {
                $fileSizeInBytes = mb_strlen($file, '8bit');
                $fileSizeInMB = $fileSizeInBytes / (1024 * 1024);
                if ($fileSizeInMB > 3) {
                    return response()->json([
                        'error' => true,
                        'message' => "Size of the files shouldn't be more than 3mb",
                    ]);
                }
                $documents[] = $this->base64Upload('support_files', $file);
            }

            $data['documents'] = json_encode($documents);
        }

        $data['author_id'] = auth()->id();
        $data['contract_id'] = $contract->id;
        $data['comment'] = $request->get('comment');

        if (!Support::create($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Ask for support cannot be submitted!'
            ]);
        }
        
        $infoUser = new User(['email' => '<EMAIL>']);
        $infoUser->notify(new AskForSupportNotification(auth()->user()->name, auth()->user()->email));

        return response()->json([
            'error' => false,
            'message' => 'Ask for support submitted successfully!'
        ]);
    }

    public function table(SupportDataTable $dataTable) {
        return $dataTable->render('support.index');
    }

}
