// @ts-nocheck
import {
  Text<PERSON><PERSON>,
  View,
  Text,
  Button,
  Popover,
  useToggle,
  Avatar,
} from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { useContext } from 'react';
import { AppContext } from 'src/context/AppContext';

const ClientSearchBarTwo: React.FC = () => {
  const navigate = useNavigate();
  const { name } = useContext(AppContext);

  const lastname =
    name?.split(' ').pop()?.charAt(0).toUpperCase() +
    name?.split(' ').pop()?.slice(1);
  const firstname =
    name?.split(' ').shift()?.charAt(0).toUpperCase() +
    name?.split(' ').shift()?.slice(1);
  const initialName =
    (firstname?.[0] || '') + (lastname?.[0] || '');

  return (
    <View
      width={'100%'}
      direction={'row'}
      align={'center'}
      className='w-full max-w-[1320px] bg-white bg-opacity-96 shadow-md justify-between p-6 rounded-[100px] '
    >
      <View  direction={'row'} justify={'start'} align={'center'} gap={3} className='hidden md:flex'>
        <View className='flex flex-row justify-center items-center gap-[8px] px-[12px] py-[8px]'>
          <Avatar
            size={8}
            initials={initialName}
            color='neutral'
            variant='faded'
            className='rubik font-medium text-[14px]  leading-[20px] text-[#323C58]'
          ></Avatar>
          <Text className='rubik font-medium lg:text-[14px] text-[13px] leading-[20px] text-[#323C58]'>
            {firstname + ' ' + lastname}
          </Text>
        </View>
      </View>

      <View>
        <View
          className='lg:flex gap-[16px]'
          direction={'row'}
          justify={'center'}
          align={'center'}
        >
          <View className='flex gap-[8px] '>
            <Button
              className='!p-[4px] flex '
              variant='ghost'
              onClick={() => navigate('/post-job')}
            >
              <View
                direction={'row'}
                justify={'center'}
                align={'center'}
                className='gap-[4px]'
              >
                <span className='material-icons-outlined text-[#0B80E7]'>
                  add
                </span>

                <Text className='w-99 h-23 rubik font-medium lg:text-base leading-6 text-[#323C58]'>
                  Post a job
                </Text>
              </View>
            </Button>
            {/* <Button className='!p-[4px]' variant='ghost'>
              <View
                direction={'row'}
                justify={'center'}
                align={'center'}
                gap={1}
              >
                <span className='material-icons-outlined text-[#0B80E7]'>
                  notifications_active
                </span>

                <Text className='w-99 h-23 rubik font-medium lg:text-base leading-6 text-[#323C58]'>
                  Instant Book
                </Text>
              </View>
            </Button> */}
          </View>

          <Button
            className='px-[12px] py-[8px]'
            variant='outline'
            rounded={true}
            onClick={() => navigate('/search-operator')}
          >
            <View direction={'row'} justify={'center'} align={'center'} gap={1}>
              <span className='material-icons-outlined'>search</span>
              <Text className='w-99 h-23 rubik font-medium lg:text-base leading-6 text-[#323C58]'>
                Find Security
              </Text>
            </View>
          </Button>
          <Button
            color='black'
            rounded={true}
            className='!bg-[#323c58] px-[12px] py-[8px]'
            onClick={() => navigate('/client-settings')}
          >
            <View direction={'row'} justify={'center'} align={'center'} gap={1}>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                width='15'
                height='16'
                viewBox='0 0 15 16'
                fill='none'
              >
                <path
                  d='M12.8009 8.64691C12.8305 8.41004 12.8527 8.17318 12.8527 7.92151C12.8527 7.66985 12.8305 7.43298 12.8009 7.19612L14.3627 5.97479C14.5034 5.86376 14.5404 5.66391 14.4516 5.50107L12.9712 2.93998C12.9045 2.82155 12.7787 2.75493 12.6455 2.75493C12.6011 2.75493 12.5567 2.76233 12.5196 2.77714L10.6765 3.51733C10.2916 3.22126 9.87713 2.97699 9.42561 2.79194L9.14434 0.830415C9.12213 0.652767 8.96669 0.519531 8.78164 0.519531H5.82085C5.6358 0.519531 5.48036 0.652767 5.45815 0.830415L5.17687 2.79194C4.72535 2.97699 4.31084 3.22866 3.92594 3.51733L2.08285 2.77714C2.03843 2.76233 1.99402 2.75493 1.94961 2.75493C1.82378 2.75493 1.69794 2.82155 1.63133 2.93998L0.150928 5.50107C0.0547027 5.66391 0.0991147 5.86376 0.239752 5.97479L1.80157 7.19612C1.77196 7.43298 1.74976 7.67725 1.74976 7.92151C1.74976 8.16578 1.77196 8.41004 1.80157 8.64691L0.239752 9.86824C0.0991147 9.97927 0.0621047 10.1791 0.150928 10.342L1.63133 12.903C1.69794 13.0215 1.82378 13.0881 1.95701 13.0881C2.00142 13.0881 2.04584 13.0807 2.08285 13.0659L3.92594 12.3257C4.31084 12.6218 4.72535 12.866 5.17687 13.0511L5.45815 15.0126C5.48036 15.1903 5.6358 15.3235 5.82085 15.3235H8.78164C8.96669 15.3235 9.12213 15.1903 9.14434 15.0126L9.42561 13.0511C9.87713 12.866 10.2916 12.6144 10.6765 12.3257L12.5196 13.0659C12.5641 13.0807 12.6085 13.0881 12.6529 13.0881C12.7787 13.0881 12.9045 13.0215 12.9712 12.903L14.4516 10.342C14.5404 10.1791 14.5034 9.97927 14.3627 9.86824L12.8009 8.64691ZM11.3353 7.38117C11.3649 7.61063 11.3723 7.76607 11.3723 7.92151C11.3723 8.07696 11.3575 8.2398 11.3353 8.46186L11.2317 9.29828L11.8905 9.81642L12.6899 10.4382L12.1717 11.3338L11.2317 10.9563L10.4619 10.6454L9.79571 11.1488C9.47743 11.3856 9.17395 11.5633 8.87046 11.6891L8.08585 12.0074L7.96742 12.8438L7.81938 13.8431H6.78311L6.64247 12.8438L6.52404 12.0074L5.73943 11.6891C5.42114 11.5559 5.12506 11.3856 4.82898 11.1636L4.1554 10.6454L3.37079 10.9637L2.43074 11.3412L1.9126 10.4456L2.71201 9.82382L3.37079 9.30568L3.26716 8.46926C3.24496 8.2398 3.23015 8.06955 3.23015 7.92151C3.23015 7.77347 3.24496 7.60323 3.26716 7.38117L3.37079 6.54475L2.71201 6.02661L1.9126 5.40484L2.43074 4.5092L3.37079 4.8867L4.1406 5.19758L4.80678 4.69425C5.12506 4.45739 5.42854 4.27974 5.73202 4.1539L6.51663 3.83562L6.63507 2.9992L6.78311 1.99993H7.81198L7.95262 2.9992L8.07105 3.83562L8.85566 4.1539C9.17395 4.28714 9.47003 4.45739 9.7661 4.67945L10.4397 5.19758L11.2243 4.8793L12.1643 4.5018L12.6825 5.39744L11.8905 6.02661L11.2317 6.54475L11.3353 7.38117ZM7.30124 4.96072C5.66541 4.96072 4.34045 6.28568 4.34045 7.92151C4.34045 9.55735 5.66541 10.8823 7.30124 10.8823C8.93708 10.8823 10.262 9.55735 10.262 7.92151C10.262 6.28568 8.93708 4.96072 7.30124 4.96072ZM7.30124 9.40191C6.48703 9.40191 5.82085 8.73573 5.82085 7.92151C5.82085 7.1073 6.48703 6.44112 7.30124 6.44112C8.11546 6.44112 8.78164 7.1073 8.78164 7.92151C8.78164 8.73573 8.11546 9.40191 7.30124 9.40191Z'
                  fill='white'
                />
              </svg>
              <Text className='w-99 h-23 rubik font-medium lg:text-base leading-6 text-[#FFF] '>
                Account Settings
              </Text>
            </View>
          </Button>
        </View>

      </View>
    </View>
  );
};
export default ClientSearchBarTwo;
