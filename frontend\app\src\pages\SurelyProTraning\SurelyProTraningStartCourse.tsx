// @ts-nocheck
import React, { useState } from 'react';
import { Button, Text, View, Image, useToggle } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { headerLogo } from '../../assets/images';
import CancelTestModal from './CancelTestModal/CancelTestModal';
import DocumentModal from './DocumentModal/DocumentModal';

const SurelyProTraningStartCourse: React.FC = () => {
  const navigate = useNavigate();
  const {
    active: active1,
    activate: activate1,
    deactivate: deactivate1,
  } = useToggle(false);
  const {
    active: active2,
    activate: activate2,
    deactivate: deactivate2,
  } = useToggle(false);
  const {
    active: active3,
    activate: activate3,
    deactivate: deactivate3,
  } = useToggle(false);

  const { state } = useLocation();

  const { type, text, questionSet, document, downloadDocument, video, minutes } =
    state || {
      type: '',
      text: '',
      questionSet: [],
      document: '',
      downloadDocument: '',
      video: '',
      minutes: ''
    };

  const [isVideoModalOpen, setVideoModalOpen] = useState(false);

  const openVideoModal = () => {
    setVideoModalOpen(true);
  };

  const closeVideoModal = () => {
    setVideoModalOpen(false);
  };

  return (
    <View className='flex flex-col gap-4 p-4 md:p-8 lg:p-12 xl:p-16 2xl:p-20'>
      <View className='flex items-end w-full justify-end bg-[C85E1D]'>
        <Button
          onClick={activate1}
          icon={() => <span className='material-icons mt-[-3px] '>close</span>}
          className='btn-no-hover !bg-transparent'
        ></Button>
      </View>

      <CancelTestModal active={active1} deactivate={deactivate1} />

      <Text className='text-left text-[#323C58] rubik text-[24px] font-normal leading-[32px]'>
        {text}
      </Text>
      <View className='flex flex-col lg:flex-row justify-between items-center self-stretch gap-8 xl:gap-0 '>
        <View className='flex flex-col mt-[21px]'>
          <View className='flex flex-col w-full xl:w-[760px] xl:h-[438px] items-start gap-3 flex-shrink-0 '>
            <video controls width='100%' height='100%'>
              <source src={video} type='video/mp4' />
              Your browser does not support the video tag.
            </video>
          </View>
          {isVideoModalOpen && (
            <div className='modal-overlay'>
              <div className='video-modal'>
                <video controls width='100%' height='100%'>
                  <source src={video} type='video/mp4' />
                  Your browser does not support the video tag.
                </video>
                <button onClick={closeVideoModal}>Close Video</button>
              </div>
            </div>
          )}
        </View>

        <View className='flex flex-col w-full lg:w-[388px] xl:w-[488px] pb-0 flex-shrink-0 items-start gap-[12px]'>
          <Text className=' text-[#1A1A1A] text-left font-rufina-stencil text-[32px] font-normal leading-40'>
            Let’s start your course
          </Text>
          <Text className='text-[#323C58] text-left rubik text-base font-normal leading-6 mt-[16px]'>
            Before you proceed, please watch the training video attentively and
            feel free to take notes. Once you feel prepared, you can begin the
            course by clicking the "start test" button. Following that, we will
            evaluate your knowledge with 10 multiple choice questions.
          </Text>
          <a
            className='text-[#323C58] rubik text-[14px] font-medium leading-[20px] underline'
            onClick={activate3}
          >
            View training document
          </a>
          <DocumentModal
            active={active3}
            deactivate={deactivate3}
            text={text}
            document={document}
            downloadDocument={downloadDocument}
            type={type}
          />
        </View>
      </View>

      <View className='flex flex-col w-full gap-[20px] xl:gap-0  xl:w-[1320px]'>
        <View className='flex flex-row justify-between gap-[16px] lg:gap-0 mt-[16px]'>
          <Button
            variant='outline'
            icon={() => (
              <span className='material-icons-outlined p-0 pb-1 text-[20px]'>
                close
              </span>
            )}
            onClick={activate2}
            className='flex justify-center rubik items-center self-stretch px-4 py-2 gap-2 self-stretch border border-neutral rounded-[8px] bg-background-base text-[15px] !text-[#000000] !bg-[white] w-full  lg:w-[180px] h-[48px]'
          >
            Close
          </Button>
          <CancelTestModal active={active2} deactivate={deactivate2} />
          <Button
            endIcon={() => (
              <span className='material-icons-outlined p-0 pb-1 text-[20px]'>
                arrow_forward_ios
              </span>
            )}
            onClick={() =>
              navigate('/surelypro-traning-questions', {
                state: { type, text, questionSet, document, downloadDocument, video, minutes  },
              })
            }
            className='flex justify-center rubik items-center self-stretch px-4 py-2 gap-2 self-stretch border border-neutral rounded-[8px] bg-background-base text-[15px] !text-white !bg-[#0B80E7] w-full lg:w-[180px] h-[48px] '
          >
            Start test
          </Button>
        </View>
        <div className='flex items-center justify-center'>
          <Image
            src={headerLogo}
            className='w-[109.76px] h-[41.274px] flex-shrink-0'
          />
        </div>
      </View>
    </View>
  );
};

export default SurelyProTraningStartCourse;
