<?php

namespace App\Console\Commands;

use App\Models\MobileUser;
use App\Notifications\InstantBookOperatorNotification;
use Illuminate\Console\Command;

class InstantBookNotifyOperator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instant-book-notify-operator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Instant Book Notify Operator';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $operatives = MobileUser::where('account_type', MobileUser::freelancer)->get();
        foreach ($operatives as $operative) {
            try {
                $operative->notify(new InstantBookOperatorNotification());
            } catch (\Exception $e) {
                // Log the error
                $this->error('Error sending notification to the email: ' . $operative->email . ' | '.$e->getMessage());
            }
        }

        $this->info('Executed');
    }
}
