<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        if (count($user->notifications) == 0) {
            return response()->json([
                'error' => false,
                'data' => [
                    'contract_status_alert' => false,
                    'surelypro_badges_alert' => false,
                    'instant_book_reminder_alert' => false,
                    'contract_start_date_reminder_alert' => false,
                    'new_chat_message_alert' => false,
                    'payments_alert' => false,
                    'new_emergency_hire_job_alert' => false,
                    'contract_status_mail' => false,
                    'surelypro_badges_mail' => false,
                    'instant_book_reminder_mail' => false,
                    'contract_start_date_reminder_mail' => false,
                    'new_chat_message_mail' => false,
                    'payments_mail' => false,
                    'new_emergency_hire_job_mail' => false,
                ]
            ]);
        }

        return response()->json([
            'error' => false,
            'data' => $user->notifications->first(),
        ]);
    }
    public function store(Request $request)
    {
        $notifications = Notification::where('mobile_user_id', auth()->id())->first();

        if (!$notifications) {
            $request->merge(['mobile_user_id' => auth()->id()]);
            Notification::create($request->all());
        } else {
            if (!$notifications->update($request->all())) {
                return response()->json([
                    'error' => true,
                    'message' => 'Cannot update notifications!',
                ]);
            }
        }

        return response()->json([
            'error' => false,
            'message' => 'Notifications updated successfully!',
        ]);
    }
}
