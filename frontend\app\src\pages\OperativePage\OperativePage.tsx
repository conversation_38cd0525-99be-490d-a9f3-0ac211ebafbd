// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion } from 'reshaped';
import operativepage1 from '../../assets/images/operativepage/operativepage1.png';
import operativepage2 from '../../assets/images/operativepage/operativepage2.svg';
import operativepage3 from '../../assets/images/operativepage/operativepage3.svg';
import operativepage4 from '../../assets/images/operativepage/operativepage4.svg';
import operativepage5 from '../../assets/images/operativepage/operativepage5.svg';
import operativepage6 from '../../assets/images/operativepage/operativepage6.svg';
import operativepage7 from '../../assets/images/operativepage/operativepage7.svg';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import operatorQuestions, { OperatorQuestionsType } from '../../store/dummydata/OperatorQuestionsHomePage';
import Footer from '../Footer/Footer';
import Subscribe from '../Subscribe/Subscribe';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';

const OperativePage: React.FC = () => {
  const [activeId, setActiveId] = useState<number | null>(null);
  const [showAllOperators, setShowAllOperators] = useState(false);
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;
  const toggleShowAllOperators: any = () => {
    setShowAllOperators(!showAllOperators);
  };

  return (
    <View className='mt-[-90px] w-full overflow-x-hidden'>
      <View className='mx-auto mt-[66px] flex w-full max-w-[1320px] flex-col  items-center text-center'>
        <View className='flex w-full flex-col !bg-[#F4F5F7] '>
          <Image src={operativepage1} className='h-[auto] w-[1320px] sm:h-[580px]' />
          <View className='flex max-w-[1320px] flex-col justify-between px-[12px] sm:mt-[70px] sm:gap-[89px] lg:flex-row xl:mx-auto xl:px-0 '>
            <View className='mt-[52px] flex flex-col items-start sm:mt-0 lg:w-[420px] xl:w-[520px]'>
              <Text className='rubik ml-[0px] text-left text-[16px] font-medium leading-[20px] text-[#388DD8] lg:ml-[30px]'>Security Operatives</Text>
              <Text className='font-rufina-stencil ml-[0px] mt-[10px] text-left text-[35px] font-normal leading-[46px] text-[#323C58] sm:text-[48px] sm:leading-[56px] lg:ml-[30px]'>
                Book the right security jobs for you.
              </Text>
            </View>
            <View className='mx-auto mt-[52px] flex flex-col items-start  gap-[24px] sm:mt-0 lg:w-[529px]'>
              <Text className='rubik mr-[0px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mr-0 '>
                Are you frustrated by how much you get paid to be a security operative? Do you feel you’re not getting either recognised or rewarded
                for the continual investment you’re making in acquiring relevant qualifications? Does it seem unfair to you that every security
                operative is pretty much considered to be the same, just because they have the same SIA licence, regardless of their relative levels
                of experience, knowledge and passion?
              </Text>
              <Text className='rubik mr-[0px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mr-[0px] '>
                If you’ve answered yes to one or more of the above questions, then perhaps you need to take a closer look at Surely. We believe it’s
                well past time for a major change to the way the industry works. We want to transform the way the security operative marketplace works
                by providing an online environment that highlights the skills, experience and knowledge of professional security operatives.
              </Text>
              <Text className='rubik mr-[0px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mr-[0px]'>
                Over the last six months, we’ve spoken to lots of security professionals like you to ask them what they think – they’ve told us that
                the rates of pay they’re being offered don’t often reflect their expertise. They told us that they’re sometimes being part in harm’s
                way without the necessary backup being in place. They told us that the current system is causing dangerous situations to arise.
              </Text>
              <Text className='rubik mr-[0px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mr-[0px]'>
                That’s where Surely comes in. Our app addresses all of these issues - and many, many more. It is by far the most comprehensive
                marketplace app for security jobs in the UK, perhaps even in the world. It safeguards the best interests of both security operatives
                and clients at the same, and to the mutual benefit of both. We have taken incredible care to create the best solution to meet your
                needs.
              </Text>
              <Text className='rubik mr-[0px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mr-[0px]'>
                But don’t just take our word for it – it’s free to sign up and free to use. Create your profile today and start seeing the benefits
                tomorrow.
              </Text>
              <Text className='rubik mr-[0px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mr-[0x]'>
                It’s simple, it’s secure, it’s Surely.
              </Text>
              <div id='work' />
              {isAuthenticated ? (
                <div></div>
              ) : (
                <Button
                  variant='outline'
                  onClick={() => {
                    openModal('REGISTER');
                  }}
                  className='mb-[46px] mt-[10px]  h-[56px] w-[140px] rounded-[8px] !bg-[#0B80E7]'
                >
                  <Text className='rubik text-[17px] font-medium leading-[24px] !text-[#ffff]'>Sign up now</Text>
                </Button>
              )}
            </View>
          </View>
        </View>
        <Text className='font-rufina-stencil mb-7 mt-12 text-[45px] leading-[56px] !text-[#323C58] sm:mb-0 sm:mt-[70px] lg:text-[48px]  '>
          How it works?
        </Text>

        <View className=' mx-auto mb-[20px] flex max-w-[1100px] flex-col sm:mb-0'>
          <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between gap-12 sm:mt-[71px] sm:gap-[89px] lg:flex-row'>
            <View className='mx-auto flex flex-col   items-start justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[100%]  xl:px-0'>
              <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Join our community</Text>
              <Text className='font-rufina-stencil text-left text-[45px] font-normal leading-[56px] text-[#323C58] lg:text-[48px]'>
                Sign up today.
              </Text>
              <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                Apply to become an official Surely Security Operative. All you need to sign up to our platform is a valid SIA licence. We also need to
                verify that you are compliant with the most important BS5878 requirements. These will be safely stored on your personal profile for
                easy sharing with clients.
              </Text>
              <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
            </View>
            <img src={operativepage2} className='shrink-1 w-full sm:ml-[0px]  lg:h-[414.77px] lg:w-[469.28px]' />
          </View>

          <View className='mt-[40px] flex max-w-[1100px] flex-col-reverse justify-between gap-12 sm:mt-[80px] sm:gap-[89px] lg:flex-row'>
            <img src={operativepage3} className=' shrink-1 w-full sm:ml-[0px] lg:h-[414.77px] lg:w-[472.28px]' />
            <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[534px] xl:px-0'>
              <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Create your own unique ProFile</Text>
              <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>Show off your skills.</Text>
              <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                We know there’s more to you than an SIA badge. That’s why Surely gives you the opportunity to upload all your qualifications to
                attract the best clients. You can even add a bio, photos and a video introducing yourself too, as well as uploading your CV and
                letters of recommendation, making sure you stand out from the crowd.
              </Text>
              <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
            </View>
          </View>

          <View className='mt-[40px] flex max-w-[1100px] flex-col justify-between gap-12 sm:mt-[80px] sm:gap-[89px] lg:flex-row'>
            <View className='mx-auto flex flex-col  items-start  justify-center gap-3 px-[12px]  sm:ml-[0px] xl:w-[537px] xl:px-0'>
              <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Find your perfect job today</Text>
              <Text className='font-rufina-stencil text-left text-[45px] font-normal leading-[56px] text-[#323C58] lg:text-[48px]'>
                Choose client job tasks that suit you.
              </Text>
              <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                Clients post job tasks whenever they need a security operative - we’ll notify you when a new job post is added that meets your needs.
                Clients will contact you directly if they like your profile to discuss the details. You can also use Instant Book to show that you are
                available to work today.
              </Text>
              <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
            </View>
            <img src={operativepage4} className='shrink-1 mr-[-10px] w-full  sm:ml-[0px] sm:h-auto lg:w-[469.28px]' />
          </View>
        </View>
      </View>

      <div
        className='mx-0 mt-[50px] flex h-[480px] w-full justify-between bg-cover bg-center bg-left-top bg-no-repeat sm:mt-[101px] md:mx-auto md:h-[315px] md:w-full'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='mx-auto my-auto flex flex-col'>
          <Text className='font-rufina-stencil text-center text-[48px] font-normal leading-[56px] text-[#FFF]'>Don’t miss the opportunity.</Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='mx-auto my-auto mt-[32px] flex flex-col sm:flex-row'>
              <Button
                className='rubik mr-[24px] flex h-[56px] w-full items-center justify-center gap-2 rounded-[8px]  !bg-[#fff] p-4 text-[17px] sm:w-[271px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
            </View>
          )}
        </View>
      </div>

      <View className=' mx-auto mb-[20px] flex max-w-[1100px] flex-col items-center sm:mb-[0px] sm:items-start'>
        <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between gap-12 sm:mt-[100px] sm:gap-[89px] lg:flex-row'>
          <img src={operativepage5} className='shrink-1 w-full sm:ml-[-20px]  lg:h-[414.77px] lg:w-[496px]' />
          <View className='mx-auto flex flex-col items-start  justify-center  gap-3 px-[12px] sm:ml-[0px] xl:w-[506px]  xl:px-0'>
            <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Fast and secure payments</Text>
            <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>A better way to be paid.</Text>
            <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
              Our innovative diary, messaging and payment system means you’ll always know exactly where you stand. We prequalify all clients to make
              sure they’re trustworthy. A deposit is paid out to you as soon as the task is completed. You’ll get the balance paid in full, normally
              within 10 working days.
            </Text>
            <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
          </View>
        </View>

        <View className='mt-[40px] flex max-w-[1100px] flex-col-reverse justify-between gap-12 sm:mt-[81px] sm:gap-[89px] lg:flex-row'>
          <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0] lg:w-[436px] xl:px-0'>
            <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Stand out from the crowd</Text>
            <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>
              Develop the skills clients value.
            </Text>
            <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
              We’ve focused on six key areas that we believe a good Security Operative needs to know – Vulnerable People, Disability Focus, Use of
              Equipment, Customer Service, Conflict Management and Substance Awareness. You’ll receive a SurelyPro badge for each one you pass,
              highlighting your commitment to professionalism and passion for excellence.
            </Text>
            <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
          </View>
          <img src={operativepage6} className='h-auto w-full lg:h-[372px] lg:w-[572px]  ' />
        </View>

        <View className='mt-[40px] flex max-w-[1100px] flex-col justify-between gap-12 sm:mt-[81px] sm:gap-[89px] lg:flex-row'>
          <img src={operativepage7} className='shrink-1  w-full sm:ml-[0px] sm:mr-0  lg:h-[372px] lg:w-[503px]' />
          <View className='mx-auto flex flex-col items-start   justify-center  gap-3 px-[12px]  sm:ml-[0px] xl:w-[508px] xl:px-0'>
            <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Show the world how great you are</Text>
            <Text className='font-rufina-stencil text-left text-[45px] font-normal leading-[56px] text-[#323C58] lg:text-[48px]'>
              Get rated and reviewed by clients.
            </Text>
            <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
              Clients can rate you according to a number of important factors - Punctuality, Professionalism, Helpfulness, Dress Code, Positivity and
              Communication. The better your reviews, the more you’ll stand out from the crowd. We’ve got an arbitration service in place to deal with
              unfair feedback, and you can even submit client reviews too.
            </Text>
            <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
          </View>
        </View>
      </View>
      <div id='faq'></div>
      <View className='mx-auto mx-auto mt-12 flex w-full max-w-[1320px] flex-col items-center sm:mt-0'>
        <Text className='font-rufina-stencil text-center text-[48px] leading-[56px] !text-[#323C58] sm:mt-[100px]'>Frequently asked questions.</Text>
        <div className='w-full xl:w-auto '>
          <div className='mt-[44.3px] flex w-full flex-col gap-[20px] px-[12px] sm:w-auto sm:flex-row xl:px-0'>
            <div className='mt-[15px] flex w-full flex-col gap-4'>
              {operatorQuestions.slice(0, showAllOperators ? undefined : 8).map((item: OperatorQuestionsType) => {
                if (item.id % 2 === 1) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                            endIcon={() => (
                              <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>{item.question}</p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' flex flex-row xl:w-[600px] '>
                          <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]  '>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
            <div className='mt-[15px] flex w-full flex-col gap-4'>
              {operatorQuestions.slice(0, showAllOperators ? undefined : 8).map((item: OperatorQuestionsType) => {
                if (item.id % 2 === 0) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                            endIcon={() => (
                              <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>{item.question}</p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' xl:w-[600px] '>
                          <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]'>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
          </div>

          <div className='mb-[45px] mt-[24px] flex justify-center sm:mb-[101px]'>
            <Button className='border-5 rubik h-[40px] w-[140px] border-[#000] !bg-[#fff] ' onClick={toggleShowAllOperators}>
              {showAllOperators ? 'Load Less' : 'Load More'}
            </Button>
          </div>
        </div>
      </View>

      <Subscribe />
      <Footer />
    </View>
  );
};

export default OperativePage;
