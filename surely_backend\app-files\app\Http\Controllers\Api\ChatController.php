<?php

namespace App\Http\Controllers\Api;

use App\Events\ChatSent;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ChatMessageRequest;
use App\Http\Resources\ChatResource;
use App\Models\Chat;
use App\Models\ChatMessage;
use App\Models\Job;
use App\Models\MobileUser;
use App\Notifications\ChatNotification;
use App\Notifications\EmergencyHireMessageNotification;
use App\Traits\Helpers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserCollection;
use App\Http\Resources\UserResource;
use App\Http\Requests\ChatRequest;
use App\Events\NewMessageEvent;
use App\Events\ReadMessageEvent;
use App\Http\Resources\MessageResource;

use Illuminate\Support\Facades\Cache;
use App\Models\Contract;

class ChatController extends Controller
{
    use Helpers;

    public function index()
    {
        $chats = Chat::where('receiver_id', auth()->id())
            ->orWhere('sender_id', auth()->id())
            ->with(['contract'])
            ->orderBy('updated_at', 'DESC')
            ->get();

        $chats = ChatResource::collection($chats);
        return response()->json([
            'data' => $chats,
        ]);
    }

    public function show(MobileUser $user)
    {
        if ($user->id === auth()->id()) {
            return redirect()->route('chat.index');
        }

        UserResource::withoutWrapping();

        $chats = $user->messages()->where('receiver_id', auth()->id())->whereNull('seen_at')->get();
        $result = $chats->each->update(['seen_at' => now()]);
        $result = $chats->each->update(['read' => true]);

        if ($result->count()) {
            $message = $result->last()->load('sender');
            broadcast(new ReadMessageEvent($message))->toOthers();
        }

        return response()->json([
            'users' => UserCollection::make($this->getChatWithUser()),
            'chat_with' => UserResource::make($user),
            'messages' => $this->loadMessages($user),
        ]);
    }

    public function chat($userId, ChatRequest $request)
    {
        $message = auth()->user()->messages()->create([
            'receiver_id' => $userId,
            'message' => $request->message,
            'reply_id' => $request->reply_id,
        ]);

        broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    public function destroy(Chat $chat)
    {
        if ($chat->sender_id !== auth()->id()) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized access!'
            ]);
        }

        $message = tap($chat)->update([
            'message_deleted_at' => now(),
        ]);

        broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    private function getChatWithUser()
    {
        return MobileUser::query()
            ->whereHas('receiveMessages', function ($query) {
                $query->where('sender_id', auth()->id());
            })
            ->orWhereHas('sendMessages', function ($query) {
                $query->where('receiver_id', auth()->id());
            })
            ->withCount(['messages' => fn($query) => $query->where('receiver_id', auth()->id())->whereNull('seen_at')])
            ->with([
                'sendMessages' => function ($query) {
                    $query->whereIn('id', function ($query) {
                        $query->selectRaw('max(id)')
                            ->from('chats')
                            ->where('receiver_id', auth()->id())
                            ->groupBy('sender_id');
                    });
                },
                'receiveMessages' => function ($query) {
                    $query->whereIn('id', function ($query) {
                        $query->selectRaw('max(id)')
                            ->from('chats')
                            ->where('sender_id', auth()->id())
                            ->groupBy('receiver_id');
                    });
                },
            ])
            ->orderByDesc(function ($query) {
                $query->select('created_at')
                    ->from('chats')
                    ->whereColumn('sender_id', 'mobile_users.id')
                    ->orWhereColumn('receiver_id', 'mobile_users.id')
                    ->orderByDesc('created_at')
                    ->limit(1);
            })
            ->get();
    }

    private function loadMessages($user)
    {
        return Chat::query()
            ->where(fn($query) => $query->where('sender_id', auth()->id())->where('receiver_id', $user->id))
            ->orWhere(fn($query) => $query->where('sender_id', $user->id)->where('receiver_id', auth()->id()))
            ->with(['receiver', 'sender'])
            ->orderBy('created_at')
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender_id' => $message->sender_id,
                    'receiver_id' => $message->receiver_id,
                    'created_at' => $message->created_at,
                    'type' => $message->type,
                    'metadata' => $message->metadata,
                    'sender' => [
                        'id' => $message->sender->id,
                        'name' => $message->sender->name,
                        'profile_photo' => $message->sender->profile_photo
                    ],
                    'receiver' => [
                        'id' => $message->receiver->id,
                        'name' => $message->receiver->name,
                        'profile_photo' => $message->receiver->profile_photo
                    ]
                ];
            });
    }

    public function getMessages($chatId) {

        $chat = Chat::find($chatId);

        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Please provide a valid chat id!',
            ]);
        }

        if ($chat->sender_id != auth()->id() && $chat->receiver_id != auth()->id()) {
            return response()->json([
                'error' => true,
                'message' => 'Please provide a valid chat id!',
            ]);
        }

        $chatMessages = ChatMessage::where('chat_id', $chatId)->with(['sender' => function ($query) {
            $query->select('id', 'name', 'profile_photo');
        },
            'receiver' => function ($query) {
                $query->select('id', 'name', 'profile_photo');
            }
        ])->get();

        if ($chatMessages->last()->read == null || $chatMessages->last()->read == false) {
            broadcast(new ReadMessageEvent($chatMessages->last()))->toOthers();
        }

        foreach ($chatMessages as $message) {
            $message->read = true;
            $message->save();
        }

        return response()->json([
            'data' => $chatMessages,
        ]);
    }


    public function getMessagesByUser($userId) {
        $chatMessages = ChatMessage::where(function ($query) use ($userId) {
            $query->where(function ($q) use ($userId) {
                $q->where('sender_id', $userId)->where('receiver_id', auth()->id());

            });
            $query->orWhere(function ($q) use ($userId) {
                $q->where('sender_id', auth()->id())->where('receiver_id', $userId);
            });
        })->with(['sender', 'receiver'])->get();

        return response()->json([
            'data' => $chatMessages,
        ]);
    }

    public function sendMessage(Request $request, $chatId) {

        $request['sender_id'] = auth()->id();

        if($chatId == Chat::NEW_CHAT){
            if (! $request->filled('receiver_id')) {
                return response()->json([
                    'error' => true,
                    'message' => 'Please provide a receiver_id!',
                ],422);
            }

            $chat = Chat::whereNull('job_id')->whereNull('contract_id')->where(function ($query) use ($request){
                $query->where(function ($q) use ($request)  {
                    $q->where('sender_id', auth()->id())->where('receiver_id', $request->receiver_id);
                });

                $query->orWhere(function ($q) use ($request) {
                    $q->where('sender_id', $request->reciver_id)->where('receiver_id', auth()->id());
                });
            })->first();

            if(!$chat){
                $chat = Chat::create($request->all());

                $request['receiver_id'] = $chat->receiver_id != auth()->id() ? $chat->receiver_id : $chat->sender_id;
                $message = $chat->writeMessage($request);

                return response()->json([
                    'data' => [
                        'chat' => $chat,
                        'message' => $message,
                    ],
                ]);
            }
            if($chat){
                return response()->json([
                    'data' => [
                        'chat' => $chat,
                        'message' => null,
                    ],
                ]);
            }
        } else{
            $chat = Chat::find($chatId);
        }

        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Please provide a valid chat id!',
            ]);
        }

        if ($chat->sender_id != auth()->id() && $chat->receiver_id != auth()->id()) {
            return response()->json([
                'error' => true,
                'message' => 'Please provide a valid chat id!',
            ]);
        }

        $request['receiver_id'] = $chat->receiver_id != auth()->id() ? $chat->receiver_id : $chat->sender_id;

        $message = $chat->writeMessage($request);
        $operator = MobileUser::find($request['receiver_id']);

        // Check if this chat is related to an emergency hire job
        $isEmergencyHire = $chat->isEmergencyHireJob();

        if ($isEmergencyHire) {
            // For emergency hire jobs, always send an email notification
            // Get the job associated with this chat
            $job = null;
            if ($chat->job_id) {
                $job = $chat->job()->first();
            } elseif ($chat->contract_id) {
                $contract = $chat->contract()->first();
                if ($contract && $contract->job_id) {
                    $job = Job::find($contract->job_id);
                }
            }

            if ($job) {
                // Send emergency hire message notification
                $operator->notify(new EmergencyHireMessageNotification($message, $job));

                // Log the emergency hire message notification
                Log::info("Sent emergency hire message notification to {$operator->email} for job #{$job->id}");
            }
        } else if ($operator->notification('new_chat_message_mail')) {
            // For regular jobs, only send if the user has enabled chat notifications
            $operator->notify(new ChatNotification($operator->name));
        }

        // Always broadcast the message event for real-time updates
        broadcast(new NewMessageEvent($message->load('receiver', 'sender')))->toOthers();

        return response()->json([
            'data' => [
                'chat' => $chat,
                'message' => $message,
            ],
        ]);
    }
    public function getChatDetails($chatId)
    {
        $chat = Chat::where('id', $chatId)
            ->with(['receiver', 'sender', 'job', 'contract'])
            ->firstOrFail();

        if ($chat->sender_id !== auth()->id() && $chat->receiver_id !== auth()->id()) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized access to chat'
            ], 403);
        }

        return response()->json([
            'data' => new ChatResource($chat)
        ]);
    }

    public function getChatList()
    {
        $chats = Chat::where('receiver_id', auth()->id())
            ->orWhere('sender_id', auth()->id())
            ->with([
                'receiver:id,name,profile_photo',
                'sender:id,name,profile_photo',
                'contract' => function($query) {
                    $query->with(['job', 'client', 'operative']);
                }
            ])
            ->orderBy('updated_at', 'DESC')
            ->get();

        return response()->json([
            'data' => ChatResource::collection($chats)
        ]);
    }

    public function getChatMessages($chatId)
    {
        $chat = Chat::findOrFail($chatId);

        if ($chat->sender_id !== auth()->id() && $chat->receiver_id !== auth()->id()) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized access to messages'
            ], 403);
        }

        $messages = $chat->messages()
            ->with(['sender:id,name,profile_photo', 'receiver:id,name,profile_photo'])
            ->orderBy('created_at', 'ASC')
            ->get();

        return response()->json([
            'data' => MessageResource::collection($messages)
        ]);
    }
}
