// @ts-nocheck
import React from 'react';
import { Text, View, Button, Modal } from 'reshaped';
import { useNavigate, useLocation } from 'react-router-dom';
import cancelicon from '../../../assets/icons/cancelicon/cancelicon.svg';

interface CancelTestModalProps {
  active: boolean;
  deactivate: () => void;
}

const CancelTestModal: React.FC<CancelTestModalProps> = ({
  active,
  deactivate,
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleConfirm = () => {
    switch (location.pathname) {
      case '/surelypro-traning':
        navigate('/my-profile');
        break;
      case '/surelypro-traning-start-course':
        navigate('/surelypro-traning');
        break;
      case '/surelypro-traning-questions':
        navigate('/surelypro-traning');
        break;
      default:
        navigate('/default-destination');
    }
  };

  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='!w-[424px] !h-[auto]'
    >
      <View className='flex flex-col'>
        <View className=''>
          {/* <span className='material-icons-outlined text-[#CB101D] text-[70px]'>
            report_problem
          </span> */}
          {/* <img src={cancelicon}/> */}
        </View>
        <Text className='text-left text-[#1A1A1A] rubik text-[20px] font-normal mt-[16px] leading-[28px]'>
          Cancel your test
        </Text>
        <Text className='text-left text-[#323C58] rubik text-[14px] font-normal leading-5 mt-[8px]'>
          Are you sure you want to cancel your test?
        </Text>
        <Text className='text-left text-[#323C58] rubik text-[14px] font-normal leading-5 '>
          Your progress will be lost but you can retake the test any time.
        </Text>
        <View className='flex flex-row justify-between mt-[20px]'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] w-[195px] h-[48px] mr-[10px] rubik'
          >
            <Text className='rubik text-[#323C58] font-medium leading-[24px] text-[16px]'>
              No, go back
            </Text>
          </Button>
          <Button
            onClick={handleConfirm}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] !text-white !bg-[#0B80E7] w-[195px] h-[48px] rubik'
          >
            <Text className='rubik text-[16px] text-[#FFFFFF] leading-[24px] font-medium'>
              {location.pathname === '/surelypro-traning'
                ? 'Confirm'
                : 'Exit course'}
            </Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default CancelTestModal;
