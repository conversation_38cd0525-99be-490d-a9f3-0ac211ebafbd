// @ts-nocheck

import { View } from 'reshaped';
import React from 'react';
import classnames from 'classnames';

// This is a simple wrapper around reshaped's View component. for identifying the page
// the page component is used in the router. and defines full width and height
//every page has a margin top of -90px because of the header of the main component

function Page(props: any) {
  const combinedClasses = classnames('w-full overflow-x-hidden mt-[-90px] bg-[#fff]', props.className);

  return <View {...props} className={combinedClasses}  />;
}

export default Page;
