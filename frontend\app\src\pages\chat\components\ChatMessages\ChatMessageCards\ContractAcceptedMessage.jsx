import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useChatContext } from 'src/context/ChatContext';

const ContractAcceptedMessage = ({ message }) => {
  const { contract } = useChatContext()
  const nameShown = contract?.operative?.name
  const contractId = message?.contract_id
  
  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Contract accepted</h2>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          <span className='text-[14px] leading-5 text-[#323C58] font-medium'>{nameShown}</span> accepted contract{' '}
          <span className='text-[14px] leading-5 text-[#323C58] font-medium'>#{contractId}</span>
        </p>
      </div>
    </div>
  );
};

export default ContractAcceptedMessage;
