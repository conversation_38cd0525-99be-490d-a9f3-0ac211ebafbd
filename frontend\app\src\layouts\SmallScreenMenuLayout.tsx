import React from 'react';
import { Container } from 'reshaped';


interface Props {
  children?: React.ReactNode;
}

export const SmallScreenMenuLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container
      padding={0}
      className="w-full h-screen bg-[url('src/assets/altBg.jpg')] bg-cover "
    >
      <main className='flex flex-wrap items-center justify-center gap-24 '>
        {children}
      </main>
    </Container>
  );
};

export default SmallScreenMenuLayout;
