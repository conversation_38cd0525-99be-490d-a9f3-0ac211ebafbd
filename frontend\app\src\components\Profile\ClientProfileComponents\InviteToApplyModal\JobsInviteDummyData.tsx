interface Job {
  id: number;
  date: string;
  title: string;
  proposals: number;
  selected: boolean;
}

const JobsInviteDummyData: Job[] = [
  {
    id: 1,
    date: 'Wed 7 June 2023',
    title: 'Goodwood Festival',
    proposals: 12,
    selected: false,
  },
  {
    id: 2,
    date: 'Mon 6 June 2023',
    title: 'Office building. I need someone for this simple job.',
    proposals: 8,
    selected: true,
  },
  {
    id: 3,
    date: 'Wed 7 June 2023',
    title:
      'Zoo security staff. Good payment, flexible hours. Professionals only.',
    proposals: 10,
    selected: false,
  },
  {
    id: 4,
    date: 'Wed 7 June 2023',
    title: 'Zoo security staff.',
    proposals: 10,
    selected: false,
  },
  {
    id: 5,
    date: 'Mon 8 May 2023',
    title: 'Zoo security staff.',
    proposals: 10,
    selected: false,
  },
];

export default JobsInviteDummyData;
