// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Text, View, Switch, Divider, Button, Tabs, Breadcrumbs, Image, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { addNotifications, getNotifications } from 'src/services/settings';
import { AppContext } from 'src/context/AppContext';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorSettingsNotification: React.FC = () => {
  const navigate = useNavigate();
  const { fetchAppData } = useContext(AppContext);
  const toast = useToast();
  const [activeTab, setActiveTab] = useState('0');
  const [switchContractStatusWeb, setSwitchContractStatusWeb] = useState<boolean>(true);
  const [switchSurelyProBadgesWeb, setSwitchSurelyProBadgesWeb] = useState<boolean>(true);
  const [switchInstantBookReminderWeb, setSwitchInstantBookReminderWeb] = useState<boolean>(true);
  const [switchContractStartDateWeb, setSwitchContractStartDateWeb] = useState<boolean>(true);
  const [switchNewChatMessageWeb, setSwitchNewChatMessageWeb] = useState<boolean>(true);
  const [switchPaymentsWeb, setSwitchPaymentsWeb] = useState<boolean>(true);
  const [switchEmergencyHireWeb, setSwitchEmergencyHireWeb] = useState<boolean>(true);

  const [switchContractStatusEmail, setSwitchContractStatusEmail] = useState<boolean>(false);
  const [switchSurelyProBadgesEmail, setSwitchSurelyProBadgesEmail] = useState<boolean>(false);
  const [switchInstantBookReminderEmail, setSwitchInstantBookReminderEmail] = useState<boolean>(false);
  const [switchContractStartDateEmail, setSwitchContractStartDateEmail] = useState<boolean>(false);
  const [switchNewChatMessageEmail, setSwitchNewChatMessageEmail] = useState<boolean>(false);
  const [switchPaymentsEmail, setSwitchPaymentsEmail] = useState<boolean>(false);
  const [switchEmergencyHireEmail, setSwitchEmergencyHireEmail] = useState<boolean>(false);

  const [isSaving, setIsSaving] = useState(false);

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const submitNotification = () => {
    setIsSaving(true);
    const firstTabSettings = {
      switchContractStatusWeb: switchContractStatusWeb ? 1 : 0,
      switchSurelyProBadgesWeb: switchSurelyProBadgesWeb ? 1 : 0,
      switchInstantBookReminderWeb: switchInstantBookReminderWeb ? 1 : 0,
      switchContractStartDateWeb: switchContractStartDateWeb ? 1 : 0,
      switchNewChatMessageWeb: switchNewChatMessageWeb ? 1 : 0,
      switchPaymentsWeb: switchPaymentsWeb ? 1 : 0,
      switchEmergencyHireWeb: switchEmergencyHireWeb ? 1 : 0,

      switchContractStatusEmail: switchContractStatusEmail ? 1 : 0,
      switchSurelyProBadgesEmail: switchSurelyProBadgesEmail ? 1 : 0,
      switchInstantBookReminderEmail: switchInstantBookReminderEmail ? 1 : 0,
      switchContractStartDateEmail: switchContractStartDateEmail ? 1 : 0,
      switchNewChatMessageEmail: switchNewChatMessageEmail ? 1 : 0,
      switchPaymentsEmail: switchPaymentsEmail ? 1 : 0,
      switchEmergencyHireEmail: switchEmergencyHireEmail ? 1 : 0,
    };

    addNotifications(firstTabSettings)
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
      });
  };

  useEffect(() => {
    getNotifications().then((data: any) => {
      setSwitchContractStatusWeb(!!data.data?.contract_status_alert);
      setSwitchSurelyProBadgesWeb(!!data.data?.surelypro_badges_alert);
      setSwitchInstantBookReminderWeb(!!data.data?.instant_book_reminder_alert);
      setSwitchContractStartDateWeb(!!data.data?.contract_start_date_reminder_alert);
      setSwitchNewChatMessageWeb(!!data.data?.new_chat_message_alert);
      setSwitchPaymentsWeb(!!data.data?.payments_alert);
      setSwitchEmergencyHireWeb(!!data.data?.new_emergency_hire_job_alert);

      setSwitchContractStatusEmail(!!data.data?.contract_status_mail);
      setSwitchSurelyProBadgesEmail(!!data.data?.surelypro_badges_mail);
      setSwitchInstantBookReminderEmail(!!data.data?.instant_book_reminder_mail);
      setSwitchContractStartDateEmail(!!data.data?.contract_status_mail);
      setSwitchNewChatMessageEmail(!!data.data?.new_chat_message_mail);
      setSwitchPaymentsEmail(!!data.data?.payments_mail);
      setSwitchEmergencyHireEmail(!!data.data?.new_emergency_hire_job_mail);
    });
  }, []);

  return (
    <View className=' mx-auto w-[300px] sm:w-auto'>
      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='text-[#3C455D] rubik text-[16px]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='text-[#3C455D] rubik text-[16px]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='font-medium rubik text-[#1A1A1A] text-[16px]'>Notifications</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='mb-[16px] flex items-center p-0'>
        <Text className='text-foreground-neutral font-rufina-stencil font-normal leading-10 text-[#323C58] xl:text-4xl xl:font-normal xl:leading-10'>
          Notifications
        </Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          <Tabs.Item value='0'>Web app</Tabs.Item>
          <Tabs.Item value='2'>Email</Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col lg:flex-row lg:justify-between'>
          <View className='mt-[16px] flex flex-col space-y-4'>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>Contract status alert</Text>
              <Switch name='switch' checked={switchContractStatusWeb} onChange={(event) => setSwitchContractStatusWeb(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>SurelyPro badges alert</Text>
              <Switch name='switch' checked={switchSurelyProBadgesWeb} onChange={(event) => setSwitchSurelyProBadgesWeb(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>Instant book reminder alert</Text>
              <Switch name='switch' checked={switchInstantBookReminderWeb} onChange={(event) => setSwitchInstantBookReminderWeb(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>Contract start date reminder alert</Text>
              <Switch name='switch' checked={switchContractStartDateWeb} onChange={(event) => setSwitchContractStartDateWeb(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>New chat message alert</Text>
              <Switch name='switch' checked={switchNewChatMessageWeb} onChange={(event) => setSwitchNewChatMessageWeb(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>Payments alert</Text>
              <Switch name='switch' checked={switchPaymentsWeb} onChange={(event) => setSwitchPaymentsWeb(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4'>New Emergency Hire job alert</Text>
              <Switch name='switch' checked={switchEmergencyHireWeb} onChange={(event) => setSwitchEmergencyHireWeb(event.checked)} />
            </View>
            <Divider className='mt-8 h-[1px] w-full'></Divider>

            <View className='mt-8 flex flex-row justify-between gap-4'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitNotification();
                  toast.show({
                    title: 'Done!',
                    text: 'You have updated your profile',
                    startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex flex-col sm:ml-[135px] sm:mt-[0px] lg:w-[313px]'></View>
        </View>
      )}
      {activeTab === '2' && (
        <View className='flex flex-col lg:flex-row lg:justify-between'>
          <View className='mt-[16px] flex flex-col space-y-4'>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>Contract status mail</Text>
              <Switch
                name='switch'
                checked={switchContractStatusEmail}
                onChange={(event) => setSwitchContractStatusEmail(event.checked)}
                className=''
              />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>SurelyPro badges mail</Text>
              <Switch name='switch' checked={switchSurelyProBadgesEmail} onChange={(event) => setSwitchSurelyProBadgesEmail(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>Instant book reminder mail</Text>
              <Switch name='switch' checked={switchInstantBookReminderEmail} onChange={(event) => setSwitchInstantBookReminderEmail(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>Contract start date reminder mail</Text>
              <Switch name='switch' checked={switchContractStartDateEmail} onChange={(event) => setSwitchContractStartDateEmail(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>New chat message mail</Text>
              <Switch name='switch' checked={switchNewChatMessageEmail} onChange={(event) => setSwitchNewChatMessageEmail(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>Payments mail</Text>
              <Switch name='switch' checked={switchPaymentsEmail} onChange={(event) => setSwitchPaymentsEmail(event.checked)} />
            </View>
            <View className='flex flex-row items-center justify-between'>
              <Text className='text-neutral rubik py-1 text-base font-medium leading-4 '>New Emergency Hire job mail</Text>
              <Switch name='switch' checked={switchEmergencyHireEmail} onChange={(event) => setSwitchEmergencyHireEmail(event.checked)} />
            </View>
            <Divider className='mt-8 h-[1px] w-full'></Divider>

            <View className='mt-8 flex flex-row justify-between gap-4'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitNotification();
                  toast.show({
                    title: 'Done!',
                    text: 'You have updated your profile',
                    startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
    </View>
  );
};

export default OperatorSettingsNotification;
