// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';
import SurelyIcon from 'src/pages/LandingPage/SurelyIcon';
import SurelyIconNoData from './SurelyIconNoData';

const NoOperatorFavorite: React.FC = ({ favourite }: { favourite: boolean }) => {
  if (favourite) {
    return (
      <View className='mx-auto mb-[40px] mt-[40px] flex w-[234px] flex-col items-center justify-center'>
        <SurelyIconNoData />
        <Text className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>
          You don't have any favorite operator.
        </Text>
      </View>
    );
  } else {
    return (
      <View className='mx-auto mb-[40px] mt-[40px] flex w-[234px] flex-col items-center justify-center'>
        <SurelyIconNoData />
        <Text className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>No operators found.</Text>
      </View>
    );
  }
};

export default NoOperatorFavorite;
