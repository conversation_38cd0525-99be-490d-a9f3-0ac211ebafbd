import getNameInitials from '../../../../../utils/getNameInitials';
import MessageStatus from './MessageStatus';
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useAuthContext } from 'src/context/AuthContext';

const NoContractMessage = ({ message }) => {
  const { user, isClient } = useAuthContext();
  //TODO: WORK
  let isReceiver;

  if (user?.profile?.id == message?.receiver_id) {
    isReceiver = true;
  } else {
    isReceiver = false;
  }

  const nameShown = isReceiver ? message.sender.name : message.receiver.name;

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#388DD8] bg-[#F4F5F7] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Start yout chat</h2>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          Start your conversation with <span className='font-medium'>{nameShown}</span>
        </p>
      </div>
    </div>
  );
};

export default NoContractMessage;
