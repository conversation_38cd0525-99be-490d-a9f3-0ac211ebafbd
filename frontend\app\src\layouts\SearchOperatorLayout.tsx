import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';
import ClientSearchBarOne from 'src/components/ClientSearchBar/ClientSearchBarOne';

interface Props {
  children?: React.ReactNode;
}

export const SearchSecurityOperatorLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container padding={0} className="relative min-h-screen w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />
      <main className='align-center mt-[80px] h-full w-full max-w-[1320px]   mx-auto    text-center '>
        <section className='h-full w-full mt-[61px]'>
          <p className='text-center xl:text-start font-rufina-stencil text-2xl font-normal leading-10 text-left mb-[30px]'>
            Find a Security Operative
          </p>
          <div className="lg:sticky lg:top-[90px] lg:z-50 lg:min-h-[56px]">
          <ClientSearchBarOne />
          </div>
          <article className='h-full flex flex-row gap-5 flex-wrap xl:justify-center px-[12px] xl:px-0 mt-[61px]'>
            {children}
          </article>
        </section>
      </main>
    </Container>
  );
};

export default SearchSecurityOperatorLayout;
