// @ts-nocheck
import React from 'react';
import { Button, Modal, View, Text } from 'reshaped';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';

interface YoutubeVideoModalProps {
  active: boolean;
  deactivate: () => void;
  videoId: string | null;
}

const YoutubeVideoModal: React.FC<YoutubeVideoModalProps> = ({
  active,
  deactivate,
  videoId,
}) => {
  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='w-auto h-auto sm:w-auto sm:h-auto'
    >
      <View className='mx-auto'>
        <View className='flex items-center p-0 mt-[-8px]'>
          <button
            onClick={deactivate}
            className='flex items-center justify-end ml-auto btn-no-hover'
          >
            <span className='material-icons align-middle text-500 '>close</span>
          </button>
        </View>
        <View className='mt-[10px] mx-auto'>
          {videoId && (
            <iframe
              title='YouTube Video'
              width='560'
              height='315'
              src={`https://www.youtube.com/embed/${videoId}`}
              frameBorder={0}
              allowFullScreen
              className='w-full h-full sm:w-[560px] sm:h-[315px]'
            ></iframe>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default YoutubeVideoModal;
