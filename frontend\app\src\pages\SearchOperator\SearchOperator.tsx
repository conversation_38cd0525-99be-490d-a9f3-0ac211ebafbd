// @ts-nocheck
import React, { useEffect, useContext, useRef } from 'react';
import { View, Button, Loader } from 'reshaped';
import { OperativesContext, initialOperativeFilters } from 'src/context/OperativesContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import SearchOperatorCard from './SearchOperatorCard';
import { useLocation } from 'react-router-dom';
import Loading from 'src/components/Loading/Loading';
import SurelyIcon from '../LandingPage/SurelyIcon';
import NoOperatorFavorite from 'src/components/NoData/NoOperatorFavorite';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';

export const NoOperatorFound = () => (
  <View className='mx-auto mb-[40px] mt-[40px] flex w-[234px] flex-col items-center justify-center'>
    <SurelyIcon />
    <div className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>No operator found</div>
  </View>
);

const SearchOperator: React.FC = () => {
  const { isAuthenticated, user } = useContext(AuthContext);
  const { openModal } = useModalAction();
  
  const {
    allOperatives,
    favourites,
    fetchAllOperative,
    isLoadingOperatives,
    isLoadingMoreOperatives,
    handleLoadMore,
    addFavorite,
    removeFavorite,
    setFilters,
    page,
    setPage,
    isLastPage,
    from,
  } = useContext(OperativesContext);

  const bottomElement = useRef(null);
  const loadMoreButton = useRef(null);

  const location = useLocation();
  const currentPath = location.pathname;
  const isFavoritePage = currentPath.includes('favorite-operator');

  const isEmpty = allOperatives?.length === 0;

  const aggregatedOperators = isFavoritePage ? favourites : allOperatives;

  const loadMore = () => {
    if (!isAuthenticated) {
      openModal('GUEST_VIEW');
      return;
    }
    
    const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
    const isGuest = accountType === '5';

    if (isGuest) {
      openModal('GUEST_UPGRADE');
      return;
    }
    
    handleLoadMore();
    loadMoreButton.current?.scrollIntoView(false, { behavior: 'smooth' });
  };

  useEffect(() => {
    setPage(1);
    setFilters({ ...initialOperativeFilters, is_favorite: isFavoritePage });
  }, [isFavoritePage]);

  useEffect(() => {
    if (page !== 1) {
      if (bottomElement.current) {
        bottomElement.current.scrollIntoView(true, { behavior: 'smooth' });
      }
    }
  }, [aggregatedOperators]);


  return (
    <View className='mb-10 flex min-h-screen w-full flex-col'>
      {isLoadingOperatives ? (
        <div className='shrink'>
          <Loading />
        </div>
      ) : (
        <>
          <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
            {aggregatedOperators?.length !== 0 ? (
              aggregatedOperators?.map((operator: any, index: number) => {
                if (index === from - 4) {
                  return (
                    <div ref={bottomElement} key={operator.id + '_' + index} className='scroll-mt-[82px]'>
                      <SearchOperatorCard
                        operator={operator}
                        addFavorite={addFavorite}
                        removeFavorite={removeFavorite}
                        fetchAllOperative={fetchAllOperative}
                      />
                    </div>
                  );
                } else
                  return (
                    <SearchOperatorCard
                      key={operator.id + '_' + index}
                      operator={operator}
                      addFavorite={addFavorite}
                      removeFavorite={removeFavorite}
                      fetchAllOperative={fetchAllOperative}
                    />
                  );
              })
            ) : (
              <div className='col-span-4'>
                <NoOperatorFavorite favourite={isFavoritePage} />
              </div>
            )}
          </div>
          {isLoadingMoreOperatives && !isEmpty && (
            <div ref={loadMoreButton} className='mb-10 mt-4 h-[42.19px] w-full py-2'>
              <Loader size='medium' className='mx-auto' />
            </div>
          )}
          {!isLastPage && !isEmpty && !isLoadingMoreOperatives && (
            <div>
              <Button
                variant='outline'
                onClick={loadMore}
                className='rubik mx-auto mb-10 mt-4 rounded-lg bg-[#D1E1FF] px-4 py-2 text-[16px] font-medium leading-6 !text-[#1A1A1A] hover:bg-[#0D2F87] hover:text-[#D1E1FF]'
              >
                Load More
              </Button>
            </div>
          )}
        </>
      )}
    </View>
  );
};

export default SearchOperator;
