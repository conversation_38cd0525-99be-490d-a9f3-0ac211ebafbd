import { useToggle } from 'reshaped';
import OperatorReviewModal from '../../../../ChatModals/OperatorReviewModal';

const ClientPostReviewButton = ({ chat }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div
        onClick={activate}
        className='flex cursor-pointer items-center justify-center gap-2 rounded-[8px] border border-[#DFE2EA] py-[10.5px]'
      >
        <p className='rubik flex items-center gap-2 text-[16px] font-medium leading-[24px] text-[#05751F]'>
          <span className='material-icons-outlined w-6'>work_outlined</span> Post a review
        </p>
      </div>
      <OperatorReviewModal active={active} deactivate={deactivate} chat={chat} />
    </>
  );
};

export default ClientPostReviewButton;
