import moment from 'moment';
import React, { useState } from 'react';
import { Text, View, Modal, Divider, Card } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';

interface OperatorReviewModalProps {
  active: boolean;
  deactivate: () => void;
  allReviewAndComent: any;
}

const OperatorReviewModal: React.FC<OperatorReviewModalProps> = ({ active, deactivate, allReviewAndComent }) => {
  const [expandedIndexes, setExpandedIndexes] = useState<number[]>([]);

  const toggleExpand = (index: number) => {
    if (expandedIndexes.includes(index)) {
      setExpandedIndexes(expandedIndexes.filter((i) => i !== index));
    } else {
      setExpandedIndexes([...expandedIndexes, index]);
    }
  };

  const isExpanded = (index: number) => {
    return expandedIndexes.includes(index);
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] overflow-hidden p-[24px] lg:w-[700px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>All reviews</Text>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <div className='mt-[24px] overflow-y-auto pr-2 lg:h-[400px]'>
          <View className=' grid grid-cols-2 gap-4 '>
            {allReviewAndComent?.map((review: any, index: number) => {
              const makeRatingAvgNumber = +review?.rating_avg;
              return (
                <Card className='flex w-auto flex-col' key={index}>
                  <View className='flex flex-col items-center justify-between gap-2 lg:flex-row'>
                    {[...Array(5)].map((_, starIndex) => (
                      <span key={starIndex} className={`material-icons ${starIndex < review?.rating_avg ? 'text-[#F4BF00]' : 'text-[#C7CDDB]'}`}>
                        star
                      </span>
                    ))}
                    <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323C58]'>{makeRatingAvgNumber.toFixed(1)}</Text>
                    <Text className='rubik text-[13px] font-normal leading-[16px] text-[#444B5F]'>{moment(review?.date).format('ddd D MMM')}</Text>
                  </View>
                  <Text className={`rubik mt-[8px] text-[14px] font-medium leading-[20px] text-[#14171F] ${isExpanded(index) ? '' : 'truncate'}`}>
                    {review.name}
                  </Text>
                  <p
                    style={{
                      wordWrap: 'break-word',
                      overflowWrap: 'break-word',
                      resize: 'none',
                    }}
                    className={`rubik mt-[4px] text-[14px] font-normal italic leading-[20px] text-[#383838] ${isExpanded(index) ? '' : 'truncate'}`}
                  >
                    "{review?.comment}"
                  </p>
                  {review.comment.length > 70 && (
                    <div className='flex w-full justify-start'>
                      <button onClick={() => toggleExpand(index)} className='btn-no-hover bg-transparent'>
                        {isExpanded(index) ? 'Read Less' : 'Read More'}
                      </button>
                    </div>
                  )}
                </Card>
              );
            })}
          </View>
        </div>
      </View>
    </Modal>
  );
};

export default OperatorReviewModal;
