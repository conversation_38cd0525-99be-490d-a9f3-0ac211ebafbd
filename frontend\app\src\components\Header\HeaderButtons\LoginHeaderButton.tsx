import { Button, Text, View } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';

const LoginButton = () => {
  const { openModal } = useModalAction();

  return (
    <Button
      type='submit'
      rounded={true}
      className='!bg-[#F4F5F7] border-2 border-[#323c58]'
      onClick={() => openModal('LOGIN')}
    >
      <View className='flex flex-row justify-center items-center gap-2'>
        <div className='rounded-full w-[32px] h-[32px] bg-[#323c58]'>
          <span className='material-icons-outlined text-[18px] mt-[8px] ml-[8px] text-[#F4F5F7]'>
            logout
          </span>
        </div>
        <Text className='rubik font-medium text-[14px] leading-5 text-[#323c58]'>
          Log in
        </Text>
      </View>
    </Button>
  );
};

export default LoginButton;
