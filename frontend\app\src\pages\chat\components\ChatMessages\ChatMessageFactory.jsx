import TextMessage from './ChatMessageCards/TextMessage';
import ContractMessage from './ChatMessageCards/ContractMessage';
import PaymentMessage from './ChatMessageCards/PaymentMessage';
import JobInvitationMessage from './ChatMessageCards/JobInvitationMessage';
import ClientAcceptedApplicantMessage from './ChatMessageCards/ClientAcceptedApplicantMessage';
import NoContractMessage from './ChatMessageCards/NoContractMessage';
import JobApplicationMessage from './ChatMessageCards/JobApplicationMessage';
import ContractClientCancelledMessage from './ChatMessageCards/ContractClientCancelledMessage';
import JobApplicationDeclined from './ChatMessageCards/JobApplicationDeclined';
import IndividualContractMessage from './ChatMessageCards/IndividualContractMessage.tsx';
import EscrowPaidMessage from './ChatMessageCards/EscrowPaidMessage.jsx';
import ContractAcceptedMessage from './ChatMessageCards/ContractAcceptedMessage.jsx';
import ShiftsAcceptedMessage from './ChatMessageCards/ShiftsAcceptedMessage.jsx';
import PaymentStatusMessage from './ChatMessageCards/PaymentStatusMessage.tsx';

const DateChange = ({ dateChange }) => (
  <div className='mx-auto mt-2 w-fit rounded-lg bg-[#F4F5F7] px-2 py-1 text-xs text-[#323C58]'>{dateChange}</div>
);

export const MessageTimestamp = ({ timestamp }) => <div className='absolute bottom-2 right-4 text-[10px] text-[#888888] lg:text-xs'>{timestamp}</div>;

const ChatMessagesFactory = ({ message }) => {
  const { type, ...rest } = message;

  switch (type) {
    case 'dayChange':
      return <DateChange dateChange={message?.dateChange} />;
    case 'text':
      return (
        <div className='relative'>
          <TextMessage message={message} tested />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'contract':
      return (
        <div className='relative'>
          <ContractMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'no_contract':
      return (
        <div className='relative'>
          <NoContractMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'payment':
      return (
        <div className='relative'>
          <PaymentMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'invite_to_apply':
      return (
        <div className='relative'>
          <JobInvitationMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'has_applied_to_invitation':
      return (
        <div className='relative'>
          <JobInvitationMessage message={message} hasApplied={true} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'application':
      return (
        <div className='relative'>
          <JobApplicationMessage message={message} notNeeded />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'accepted':
      return (
        <div className='relative'>
          <ContractAcceptedMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'client_accepted_applicant':
      return (
        <div className='relative'>
          <ClientAcceptedApplicantMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'applied_through_invitation':
      return (
        <div className='relative'>
          <ClientAcceptedApplicantMessage message={message} fromInvitation={true} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'job_application_declined':
      return (
        <div className='relative'>
          <JobApplicationDeclined message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'client_cancelled_contract':
      return (
        <div className='relative'>
          <ContractClientCancelledMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'cancel':
      return (
        <div className='relative'>
          <ContractClientCancelledMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'reject':
      return (
        <div className='relative'>
          <ContractClientCancelledMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'operator_refused_contract':
      return (
        <div className='relative'>
          <ContractClientCancelledMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'operator_accepted_contract':
      return (
        <div className='relative'>
          <ContractMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'individual_contract':
      return (
        <div className='relative'>
          <IndividualContractMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'escrow_payment':
      return (
        <div className='relative'>
          <EscrowPaidMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'outstanding_payment':
      return (
        <div className='relative'>
          <EscrowPaidMessage message={message} outstanding={true} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'accept-shifts':
      return (
        <div className='relative'>
          <ShiftsAcceptedMessage message={message} shiftStatus='accepted' />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'confirm-shifts':
      return (
        <div className='relative'>
          <ShiftsAcceptedMessage message={message} shiftStatus='confirmed' />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'shift_update_request':
      return (
        <div className='relative'>
          <ShiftsAcceptedMessage message={message} shiftStatus='changed' />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'complete-shifts':
      return (
        <div className='relative'>
          <ShiftsAcceptedMessage message={message} shiftStatus='reviewed' />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'payment_terms_changed':
      return (
        <div className='relative'>
          <ShiftsAcceptedMessage message={message} shiftChange={true} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'hourly_rate_changed':
      return (
        <div className='relative'>
          <ShiftsAcceptedMessage message={message} shiftChange={true} hourly={true} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    case 'payment_status':
      return (
        <div className='relative'>
          <PaymentStatusMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    default: {
      return (
        <div className='relative'>
          <TextMessage message={message} />
          <MessageTimestamp timestamp={message?.timestamp} />
        </div>
      );
    }
  }
};

export default ChatMessagesFactory;
