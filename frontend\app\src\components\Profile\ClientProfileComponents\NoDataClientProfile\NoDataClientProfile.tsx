// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

const NoDataClientProfile: React.FC = () => {
  return (
    <View className='flex flex-col justify-center items-center w-[234px] mx-auto mt-[40px] mb-[40px]'>
      <SurelyIconNoData/>
      <Text className='text-[#444B5F] text-center font-normal leading-[24px] rubik !text-[#383838] mt-[10px]'>
      The Operative has yet to give this information. 
      </Text>
    </View>
  );
};

export default NoDataClientProfile;
