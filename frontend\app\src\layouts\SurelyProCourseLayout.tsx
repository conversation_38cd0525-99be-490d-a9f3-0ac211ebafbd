// @ts-nocheck
import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';

interface Props {
  children?: React.ReactNode;
}

export const SurelyProCourseLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container padding={0} className="w-[100vw] min-h-[100vh] bg-[#FAFBFF] bg-cover">
      {/* <Header /> */}
      <main className='flex flex-wrap items-center justify-center gap-24 '>{children}</main>
    </Container>
  );
};

export default SurelyProCourseLayout;

