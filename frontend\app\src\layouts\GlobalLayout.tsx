import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';

interface Props {
  children?: React.ReactNode;
}

export const GlobalLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container padding={0} className="min-h-[100vh] w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />
        <main className='pt-[80px] sm:pt-[90px]'>{children}</main>
    </Container>
  );
};

export default GlobalLayout;
