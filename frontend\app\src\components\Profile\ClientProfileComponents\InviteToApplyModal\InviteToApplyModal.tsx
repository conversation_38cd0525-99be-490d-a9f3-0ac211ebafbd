// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Text, View, Button, Divider, Modal } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import moment from 'moment';
import { JobContext } from 'src/context/JobContext';
import { ChatService } from 'src/services/chat';

interface InviteToApplyModalProps {
  active: boolean;
  deactivate: () => void;
  operatorId: number | string;
}

const InviteToApplyModal: React.FC<InviteToApplyModalProps> = ({ allJobs, active, deactivate, operatorId }) => {
  const navigate = useNavigate();
  const [selectedJobs, setSelectedJobs] = useState<any>([]);

  const alreadyInvitedd = allJobs?.map((job:any) =>
    job.applicants?.find((op:any) => op.provider_id === operatorId) ? { ...job, hasApplied: true } : { ...job, hasApplied: false },
  );

  const toggleSelectionJob = (id: number) => () => {
    if (selectedJobs.includes(id)) {
      setSelectedJobs(selectedJobs.filter((job: any) => job !== id));
      return;
    }
    setSelectedJobs([...selectedJobs, id]);
  };

  const sendInvites = () => {
    ChatService.inviteToApply(operatorId, selectedJobs).then((data) => {
      if (data) {
        deactivate();
        navigate(`/chat/${data}`);
      }
    });
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px]'>
      <View className='flex flex-col items-start '>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Invite for</Text>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <button onClick={() => navigate('/post-job')} className='btn-no-hover mt-[10px] bg-transparent'>
          <span className='material-icons -mt-[3px] align-middle text-[#0B80E7]'>add</span>
          <span className='rubik text-[16px] font-medium leading-[24px] text-[#323C58]'>Post a job</span>
        </button>
        <View className='flex h-[361px] flex-col gap-[16px] overflow-auto pr-[10px] w-full '>
          {alreadyInvitedd?.map((job: any, index: any) => {
            let formattedDate = moment(job.created_at).format('ddd D MMM YYYY');
            const isOperInvited = !!job?.hasApplied;

            return (
              <View key={job.id} className='flex flex-col items-start justify-center gap-[20px] self-stretch rounded-[8px] !bg-[#F4F5F7] p-[16px] '>
                <View className='flex flex-col items-start gap-[8px] '>
                  <Text className='rubik text-[13px] font-normal leading-[16px] text-[#444B5F] '>{formattedDate}</Text>
                  <Text className='rubik text-left text-[16px] font-medium leading-[20px] text-[#000] '>{job?.post_name}</Text>
                </View>
                {isOperInvited ? (
                  <button
                    onClick={toggleSelectionJob(job.id)}
                    className='flex items-center justify-center rounded-[4px] border-[#DFE2EA] bg-[#ffffff] px-[8px] py-[4px]'
                  >
                    <span className='rubik text-[14px] font-[400] leading-[20px] text-[#05751F]'>Already invited</span>
                  </button>
                ) : !isOperInvited && !selectedJobs.includes(job.id) ? (
                  <button
                    onClick={toggleSelectionJob(job.id)}
                    className='flex items-center justify-center rounded-[4px] bg-[#323C58] px-[8px] py-[4px]'
                  >
                    <span className='rubik text-[14px] font-[400] leading-[20px] text-[#fff]'>Select this job</span>
                  </button>
                ) : (
                  <button
                    onClick={toggleSelectionJob(job.id)}
                    className='flex items-center justify-center rounded-[4px] bg-[#ffffff] px-[8px] py-[4px]'
                  >
                    <span className='material-icons p-[1.5px]'>close</span>
                    <span className='rubik text-[14px] font-[400] leading-[20px] text-[#323C58]'>Unselect</span>
                  </button>
                )}
                {/* {!alreadyInvited.includes(job.id) && !selectedJobs.includes(job.id) && (
                  <button
                    onClick={toggleSelectionJob(job.id)}
                    className='flex py-[4px] px-[8px] justify-center items-center rounded-[4px] bg-[#323C58]'
                  >
                    <span className='rubik text-[14px] font-[400] leading-[20px] text-[#fff]'>Select this job</span>
                  </button>
                )}
                {!alreadyInvited.includes(job.id) && selectedJobs.includes(job.id) && (
                  <button
                    onClick={toggleSelectionJob(job.id)}
                    className='flex py-[4px] px-[8px] justify-center items-center rounded-[4px] bg-[#ffffff]'
                  >
                    <span className='material-icons p-[1.5px] text-[#323C58]'>close</span>
                    <span className='rubik text-[14px] font-normal leading-[20px] text-[#323C58]'>Unselect</span>
                  </button>
                )}
                {alreadyInvited.includes(job.id) && (
                  <button
                    onClick={toggleSelectionJob(job.id)}
                    className='flex py-[4px] px-[8px] justify-center items-center rounded-[4px] bg-[#ffffff] border-[#DFE2EA]'
                  >
                    <span className='rubik text-[14px] font-[400] leading-[20px] text-[#05751F]'>Already invited</span>
                  </button>
                )} */}
              </View>
            );
          })}
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}

            className='flex justify-center items-center rubik font-medium text-[16px] leading-[24px] px-4 py-2 gap-2 border border-neutral rounded bg-background-base w-[180px] h-[48px] mr-[10px]'
          >
            Close
          </Button>
          <Button
            onClick={sendInvites}

            className='flex justify-center items-center rubik font-medium text-[16px] leading-[24px] px-4 py-2 gap-2 border border-neutral rounded bg-background-base !text-white !bg-[#0B80E7] w-[180px] h-[48px]'

          >
            Send
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default InviteToApplyModal;
