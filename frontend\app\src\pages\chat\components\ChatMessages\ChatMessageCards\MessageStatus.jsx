const statuses = [
  { type: 'sent', icon: 'check', color: '#BBC1D3' },
  { type: 'recieved', icon: 'done_all', color: '#BBC1D3' },
  { type: 'read', icon: 'done_all', color: '#0B80E7' },
  { type: 'notSent', icon: 'schedule', color: '#BBC1D3' },
];

const MessageStatus = ({ status }) => {
  switch (status) {
    case null:
      return <span className='material-icons-outlined -mr-10 -mt-0.5 ml-auto h-[20px] overflow-hidden text-xl text-[#BBC1D3]'>check</span>;
    case 'recieved':
      return <span className='material-icons-outlined -mt-0.5 h-[20px] overflow-hidden text-xl text-[#BBC1D3]'>done_all</span>;
    case true:
      return <span className='material-icons-outlined -mr-10 -mt-0.5 ml-auto h-[20px] overflow-hidden text-xl text-[#0B80E7]'>done_all</span>;
    case 'notSent':
      return <span className='material-icons-outlined -mt-0.5 h-[20px] overflow-hidden text-xl text-[#BBC1D3]'>notSent</span>;
    default:
      break;
  }
};

export default MessageStatus;
