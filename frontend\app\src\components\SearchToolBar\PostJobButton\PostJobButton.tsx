import { Button, Text, View } from 'reshaped';
import { useNavigate } from 'react-router-dom';

const PostJobButton = () => {
  const navigate = useNavigate();

  return (
    <Button variant='ghost' onClick={() => navigate('/post-job')}>
      <View direction={'row'} justify={'center'} align={'center'}>
        <span className='material-icons-outlined text-[#0B80E7]'>add</span>

        <Text className='w-99 h-23 rubik font-medium text-base leading-6'>
          Post a Job
        </Text>
      </View>
    </Button>
  );
};

export default PostJobButton;
