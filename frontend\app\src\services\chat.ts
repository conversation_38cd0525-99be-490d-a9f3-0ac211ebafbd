import { HttpClient } from 'src/client/http-client';
import client from '../client';

export class ChatService {
  static async getChats() {
    try {
      const response = await HttpClient.get<any>('chats');
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error(error);
    }
  }

  static async getMessages(chatId: number) {
    try {
      const response = await HttpClient.get<any>(`chats/${chatId}/messages`);
      
      // Check for error responses
      if (response?.error) {
        throw new Error(response.error);
      }
      
      if (!response?.data) {
        throw new Error('No data received');
      }
      
      return response.data;
    } catch (error: any) {
      // Log detailed error info
      console.error('Chat error:', {
        message: error?.response?.data?.message || error.message,
        status: error?.response?.status,
        data: error?.response?.data
      });
      throw error;
    }
  }
  createChat() {}

  public static async inviteToApply(operatorId: number | string, jobIds: number[]) {
    const payload = {
      operator_id: operatorId,
      job_ids: jobIds,
      message: 'Invite to apply',
      type: 'invite_to_apply',
    };
    try {
      const response = await HttpClient.post<any>(`chats/0/invite/${operatorId}/for-jobs`, payload);
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      return null;
    }

    return null;
  }

  public static async sendMessage(chatId: number, payload: any) {
    try {
      const response = await HttpClient.post<any>(`chats/${chatId}/messages`, payload);
      if (response?.error) {
        throw new Error(response.error);
      }
      return response;
    } catch (error) {
      console.error('Send message error:', error);
      throw error;
    }
  }
  
  static async initializeChat(chatId: number) {
    try {
      const response = await HttpClient.get<any>(`chats/${chatId}/initialize`);
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      throw error;
    }
  }

  static async getChatsList() {
    try {
      const response = await HttpClient.get<any>('chats/list');
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error('Failed to get chats list:', error);
      throw error;
    }
  }
}

export const acceptJobInvitation = async (chatId: string, contractId: string) => {
  try {
    const response = await client.chat.acceptJobInvitation(chatId, contractId);
    if (!response.error) return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const createChat = async (receiver_id?: number, message?: string, type?: string) => {
  try {
    if (receiver_id && message && type) {
      const response = await client.chat.createNoContractChat(receiver_id, message, type);
      if (!response.error) return response.data;
    }
    const response = await client.chat.createChat();
    if (!response.error) {
      return response.data;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getChats = async (id: number) => {
  try {
    const response = await client.chat.getChats(id);
    if (!response.error) {
      return response.data;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getMessages = async (id: number) => {
  try {
    const response = await client.chat.getMessages(id);
    if (!response.error) {
      return response.data;
    }
  } catch (error) {
    console.error(error);
  }
};

export const deleteChat = async (id: string) => {
  try {
    const response = await client.chat.deleteChat(id);
    if (!response.error) {
      return response.data;
    }
  } catch (error) {
    console.error(error);
  }
}
