import React from 'react';
import Select from 'react-select';

const generateStars = (count, key) => {
  const goldStars = [...Array(count)].map((_, index) => (
    <span
      key={`gold-${key}-${index}`}
      className='material-icons text-[16px] text-[#F4BF00] px-[1.5px] py-[2.5px]'
    >
      star
    </span>
  ));

  const silverStars = [...Array(5 - count)].map((_, index) => (
    <span
      key={`silver-${key}-${index}`}
      className='material-icons text-[16px] text-[#C7CDDB] px-[1.5px] py-[2.5px]'
    >
      star
    </span>
  ));

  return [...goldStars, ...silverStars];
};

const StarSelect = ({ ratings, className, onChange }) => {
  const customStyles = {
    menu: (provided) => ({
      ...provided,
      padding: '12px',
      display: 'flex',
      flexDirection: 'column',
    }),
    option: (provided) => ({
      ...provided,
      height: '28px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-start',
    }),
    singleValue: (provided) => ({
      ...provided,
      display: 'flex',
      alignItems: 'center',
      height: '100%',
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    placeholder: () => ({
      fontSize: '14px',
      fontFamily: 'Rubik',
      color: '#3C455D',
    }),
  };

  const PreIcon = () => {
    ratings && generateStars(ratings);
  };

  const ValueContainer = ({ children, ...props }) => (
    <div
      style={{
        height: '25px',
        display: 'flex',
        alignItems: 'center',
        paddingTop: 23,
        paddingBottom: 23,
        paddingLeft: 12,
        paddingRight: 12,
      }}
    >
      <PreIcon /> {children}
    </div>
  );

  const components = {
    Option: ({ innerProps, label, value }) => (
      <div {...innerProps} className='flex items-center gap-2'>
        <input defaultChecked={ratings === value} type='radio' />
        {label}
      </div>
    ),
    SingleValue: ({ children, ...props }) => <div {...props}>{children}</div>,
    ValueContainer,
  };

  const options = [
    {
      value: 5,
      label: (
        <div className='flex items-center gap-0.5'>
          {generateStars(5, '5')}
          <span className='text-[14px] rubik text-[#323C58] font-medium ml-1'>
            5
          </span>
        </div>
      ),
    },
    {
      value: 4,
      label: (
        <div className='flex items-center gap-0.5'>
          {generateStars(4, '4')}
          <span className='text-[14px] rubik text-[#323C58] font-medium ml-1'>
            4
          </span>
        </div>
      ),
    },
    {
      value: 3,
      label: (
        <div className='flex items-center gap-0.5'>
          {generateStars(3, '3')}
          <span className='text-[14px] rubik text-[#323C58] font-medium ml-1'>
            3
          </span>
        </div>
      ),
    },
    {
      value: 2,
      label: (
        <div className='flex items-center gap-0.5'>
          {generateStars(2, '2')}
          <span className='text-[14px] rubik text-[#323C58] font-medium ml-1'>
            2
          </span>
        </div>
      ),
    },
    {
      value: 1,
      label: (
        <div className='flex items-center gap-0.5'>
          {generateStars(1, '1')}
          <span className='text-[14px] rubik text-[#323C58] font-medium ml-1'>
            1
          </span>
        </div>
      ),
    },
  ];

  return (
    <Select
      options={options}
      styles={customStyles}
      value={ratings}
      className={className}
      onChange={({ value }) => onChange(value)}
      components={components}
      placeholder='Select a rating'
    />
  );
};

export default StarSelect;
