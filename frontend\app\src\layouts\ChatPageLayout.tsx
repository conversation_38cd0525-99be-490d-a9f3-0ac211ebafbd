import React from 'react';

import Header from '../components/Header/Header';

interface Props {
  children?: React.ReactNode;
}

export const ChatPageLayout = ({ children }: Props): JSX.Element => {
  return (
    <div className="bg-[url('src/assets/altBg.jpg')] bg-cover lg:pb-20">
      <div className='relative h-screen w-full grow overflow-hidden lg:overflow-visible'>
        <Header />
        <main className='mx-auto mt-4 w-full max-w-[1320px] text-center lg:mb-[92px] lg:mt-[62px]'>{children}</main>
      </div>
    </div>
  );
};

export default ChatPageLayout;
