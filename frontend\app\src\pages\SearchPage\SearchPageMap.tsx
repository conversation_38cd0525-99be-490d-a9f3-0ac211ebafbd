// @ts-nocheck
import React, { useContext } from 'react';
import { Container } from 'reshaped';
import Header from '../../components/Header/Header';
import OperatorSearchBar from 'src/components/OperatorSearchBar/OperatorSearchBar';
import { OperativesContext } from 'src/context/OperativesContext.tsx';
import MapContainerSearchJob from './MapContainerSearchJob';
import Loading from 'src/components/Loading/Loading';
import { AppContext } from 'src/context/AppContext';

interface Props {
  children?: React.ReactNode;
  title?: string;
}

export const SearchPageMap = ({ children, title }: Props): JSX.Element => {
  const { latMap, lngMap } = useContext(AppContext);

  return (
    <Container padding={0} className="w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />
      <main className=' align-center mx-auto mt-[32px]  w-full max-w-[1320px]   text-center    sm:mt-[80px] '>
        {latMap && lngMap && <MapContainerSearchJob />}
        <section className='flex w-full flex-col gap-[16px] px-[12px] sm:mt-[61px]  xl:px-0'>
          <p className='font-rufina-stencil mb-0 text-left text-2xl font-normal leading-10 sm:mb-[38px] lg:text-start '>{title}</p>

          <OperatorSearchBar />
          {!latMap && !lngMap && (
            <div className='mt-40'>
              <Loading />
            </div>
          )}
          <article className=' mt-0 flex flex-row flex-wrap justify-center gap-5 sm:mt-[64px]'>{children}</article>
        </section>
      </main>
    </Container>
  );
};

export default SearchPageMap;
