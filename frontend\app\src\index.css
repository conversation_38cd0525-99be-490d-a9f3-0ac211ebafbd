/** Tailwind **/

@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "RufinaStencil";
  src: local("RufinaStencil"), 
  url("./assets/fonts/font.woff")  format("woff"),
  url("./assets/fonts/font.woff2")  format("woff2");
  font-display: swap;
  /* font-weight: normal; */
 }

@layer utilities {
  .border-gradient {
    border-radius: 50%;
    border: 2px solid transparent;
    border-width: 2px;
    background: linear-gradient(
      180deg,
      rgba(50, 60, 88, 0.855) -38.91%,
      rgba(113, 225, 248, 0.9025) 75.55%
    );
  }
}

:root {
  font-family: 'Rubik', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif !important;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

/* button:hover {
  border-color: #646cff;
} */

.rufina {
  font-family: "Rufina" !important;
}

.font-rufina {
  font-family: "Rufina" !important;
}

.rufina-stencil {
  font-family: "RufinaStencil" !important;
}

.font-rufina-stencil {
  font-family: "RufinaStencil" !important;
}

.rubik {
  font-family: "Rubik" !important;
}

.rubik {
  font-family: "Rubik" !important;
}

.rubik-italic{
    font-style: italic !important;
    font-family: "Rubik" !important;
}

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */

/* Scrollbar */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #88888860;
  border-radius: 12px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.invited-highlight {
  @apply shadow-[0px_1px_7px_8px_rgba(232,251,248,1)];
  -webkit-box-shadow: 0px 1px 7px 8px rgba(232, 251, 248, 1);
  -moz-box-shadow: 0px 1px 7px 8px rgba(232, 251, 248, 1);
}