// @ts-nocheck
import client from '../client';

import {
  LoginUserInput,
  BackendLoginDataType,
  ClientBackendRegisterDataType,
  OperatorBackendRegisterDataType,
  SendOtpCodeInputType,
} from '../types/user';
import { ClientJoinedRegisterDataType, OperatorJoinedRegisterDataType, BaseRegisterDataType, OperatorValidateDataType } from '../types/register';

export const registerOperator = async (registerData: BaseRegisterDataType | BaseRegisterDataType) => {
  const {
    firstName,
    lastName,
    email,
    password,
    passwordConfirmation,
    loginType,
    platform,
    appVersion,
    firebaseToken,
    accountType,
    phone,
    siaLicenceNumber,
    expiryDate,
    capturedImage,
    documentType,
    frontImage,
    backImage,
    addressVerificationDocument,
    capturedImageSelfie,
    postCode,
    city,
    siaLicenceTypes,
    ref,
    referFriend,
  } = registerData;
  
  const mappedData: OperatorBackendRegisterDataType = {
    name: firstName + ' ' + lastName,
    email: email,
    password: password,
    password_confirmation: passwordConfirmation,
    login_type: loginType,
    platform: platform,
    app_version: appVersion,
    firebase_token: firebaseToken,
    account_type: accountType,
    phone: phone,
    sia_licence_number: siaLicenceNumber,
    sia_licence_expiry_date: expiryDate,
    sia_licence_card_photo: capturedImage,
    document_type: documentType,
    id_front_document: frontImage,
    id_back_document: backImage,
    address_verification_document: addressVerificationDocument,
    selfie_verification_document: capturedImageSelfie,
    account_name: '',
    postal_code: postCode,
    city: city,
    address_2: city,
    sia_licence_types: siaLicenceTypes,
    google_token: localStorage.getItem('google_token'),
    linkedin_token: localStorage.getItem('linkedIn_code'),
    redirect_uri: import.meta.env.VITE_REACT_APP_LINKEDIN_REGISTER_REDIRECT_URL,
    ref: ref,
    referal_code: referFriend
  };

  try {
    const response = await client.users.register(mappedData);
    if (response && !response.error) {
      return response;
    } else {
      throw new Error(response.message || 'An error occurred during registration.');
    }
  } catch (error: any) {
    throw new Error(error.message || 'An error occurred during registration.');
  }
};

export const registerClient = async (registerData: ClientJoinedRegisterDataType) => {
  const {
    firstName,
    lastName,
    email,
    password,
    passwordConfirmation,
    loginType,
    platform,
    appVersion,
    firebaseToken,
    accountType,
    phone,
    clientRepresentBusinessAs,
    companyRegisteredNumber,
    industrySectors,
    selectedIndustry,
    accountName,
    ref,
    referFriend
  } = registerData;
  

  const mappedData: ClientBackendRegisterDataType = {
    name: firstName + ' ' + lastName,
    email: email,
    password: password,
    password_confirmation: passwordConfirmation,
    login_type: loginType,
    platform: platform,
    app_version: appVersion,
    firebase_token: firebaseToken,
    account_type: accountType,
    phone: phone,
    client_represent_business_as: clientRepresentBusinessAs,
    company_registered_number: companyRegisteredNumber,
    industry_sectors: industrySectors,
    selected_industry: selectedIndustry,
    account_name: accountName,
    ref: ref,
    referal_code: referFriend
  };

  try {
    const response = await client.users.register(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const loginUser = async (loginData: LoginUserInput) => {
  const {
    email,
    password,
    loginType,
    platform,
    appVersion,
    firebaseToken = 'test',
    google_token = '',
    linkedin_token = '',
    redirect_uri,
  } = loginData;

  const mappedData: BackendLoginDataType = {
    email,
    password,
    password_confirmation: password,
    login_type: loginType,
    platform,
    app_version: appVersion,
    firebase_token: firebaseToken,
    // phone: '***********',
    account_type: 1,
    google_token,
    linkedin_token,
    redirect_uri,
    ref: 'login',
  };

  try {
    const response = await client.users.login(mappedData);
    if (!response.error) {
      return { success: true, data: response?.data, token: response.token };
    } else {
      return {
        success: false,
        error: response.message,
        status: response.status,
      };
    }
  } catch (error) {
    if (error.response) {
      const { message, status } = error.response.data;
      return {
        success: false,
        error: message,
        status: status,
      };
    } else {
      return { success: false, error: 'An error occurred during login.' };
    }
  }
};

export const validateOperatorProfile = async (validateOperatorData: OperatorValidateDataType) => {
  const {
    siaLicenceNumber,
    expiryDate,
    capturedImage,
    documentType,
    frontImage,
    backImage,
    addressVerificationDocument,
    capturedImageSelfie,
    siaLicenceTypes,
  } = validateOperatorData;

  const mappedData: OperatorBackendRegisterDataType = {
    sia_licence_number: siaLicenceNumber,
    sia_licence_expiry_date: expiryDate,
    sia_licence_card_photo: capturedImage,
    document_type: documentType,
    id_front_document: frontImage,
    id_back_document: backImage,
    address_verification_document: addressVerificationDocument,
    selfie_verification_document: capturedImageSelfie,
    sia_licence_types: siaLicenceTypes,
  };

  try {
    const response = await client.users.validateOperatorProfile(mappedData);
    if (response && !response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const validateClientProfile = async (validateClientData: ClientBackendRegisterDataType) => {
  try {
    const response = await client.users.validateClientProfile(validateClientData);
    if (response && !response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getValidateOperatorProfile: any = async () => {
  try {
    const response = await client.users.getValidateOperatorProfile();
    const data = response.data;
    const mappedUser = {
      addressVerificationDocument: data.user.address_verification_document,
      documentType: data.user.document_type,
      idBackDocument: data.user.id_back_document,
      idFrontDocument: data.user.id_front_document,
      selfieVerificationDocument: data.user.selfie_verification_document,
      siaLicenceCardPhoto: data.user.sia_licence_card_photo,
      siaLicenceExpiryDate: data.user.sia_licence_expiry_date,
      siaLicenceNumber: data.user.sia_licence_number,
    };
    return mappedUser;
  } catch (error) {
    console.error(error);
  }
};

export const getProfileOperatorData: any = async () => {
  try {
    const response = await client.users.getOperatorProfileData();
    const data = response.data;

    const mappedUser = {
      jobs: data?.user?.jobs,
      cv: data?.ccv,
      addressVerificationDocument: data.user.address_verification_document,
      documentType: data.user.document_type,
      idBackDocument: data.user.id_back_document,
      idFrontDocument: data.user.id_front_document,
      selfieVerificationDocument: data.user.selfie_verification_document,
      siaLicenceCardPhoto: data.user.sia_licence_card_photo,
      siaLicenceExpiryDate: data.user.sia_licence_expiry_date,
      siaLicenceNumber: data.user.sia_licence_number,
      profileTitle: data.user.profile_title,
      profileDescription: data.user.profile_description,
      profilePhoto: data.user.profile_photo,
      profileVideo: data.user.profile_video,
      industrySectors: data.user.industry_sectors,
      siaLicense: data.user.sia_licence_types,
      additionalPictures: data.user.additional_pictures,
      languages: data.user.languages,
      firstName: data.user.name,
      lastName: data.user.name,
      name: data.user.name,
      // firstName: data.data.name.split(' ', 1).pop(),
      // lastName: data.user.name.split(' ').pop(),
      email: data.user.email,
      phoneNumber: data.user.phone,
      postalCode: data.user.postal_code,
      addressLine: data.user.address,
      townCity: data.user.address_1,
      county: data.user.address_3,
      postalCity: data.user.address_2,
      locationRange: data.user.location_range,
      qualificationArray: data.qualifications,
      positionArray: data.employments,
      testlang: data.languages,
      instantBook: data.user.instant_book,
      siaLicence: data.sia_certificates[0]?.sia_licence,
      idCheck: data.user.sia_certificates[0]?.id_check,
      proofOfAddress: data.user.sia_certificates[0]?.proof_of_address,
      employmentHistory: data.user.sia_certificates[0]?.employment_history,
      creditCheck: data.user.sia_certificates[0]?.credit_check,
      noCriminalRecord: data.user.sia_certificates[0]?.no_criminal_record,
      clientRepresentBusinessAs: data.user.client_represent_business_as,
      companyRegisteredNumber: data.user.company_registered_number,
      otherReview: data?.reviews?.other_ratings,
      overallReview: data?.reviews?.overall_rating,
      id: data?.user?.id,
      reportCount: data?.user?.reports_count,
      lat: data?.user?.lat,
      lng: data?.user?.lng,
    };

    return mappedUser;
  } catch (error) {
    console.error(error);
  }
};

export const sendCode = async (phone: SendOtpCodeInputType) => {
  try {
    const response = await client.users.sendOtpCode(phone);
    if (!response.error) {
      return { success: true, data: { user_id: response?.user_id }, token: response.token };
    } else {
      return { success: false, error: response.message };
    }
  } catch (error) {
    return { success: false, error: 'An error occurred during login.' };
  }
};

export const verifyOptCode = async (data: any) => {
  try {
    const response = await client.users.verifyOtpCode(data);

    if (!response.error) {
      return { success: true, data: response?.data, token: response.token };
    } else {
      return { success: false, error: response.message };
    }
  } catch (error) {
    return { success: false, error: 'An error occurred during login.' };
  }
};

export const verifyEmail = async (settingsData: any) => {
  const { token } = settingsData;

  const mappedData: any = {
    token: token,
  };

  try {
    const response = await client.users.validateEmail(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const loginVerifyEmail = async (loginVerifyData: any) => {
  const { email, ref } = loginVerifyData;

  const mappedData: any = {
    email: email,
    ref: 'login',
  };

  try {
    const response = await client.users.loginVerifyEmail(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const addSubscribe = async (subscribeNewsletter: any) => {
  const { name, lastName, email, type, subscribe } = subscribeNewsletter;

  const mappedData: any = {
    first_name: name,
    last_name: lastName,
    email: email,
    user_type: type,
    subscribe: subscribe,
  };

  try {
    const response = await client.users.addSubscribe(mappedData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};


export const getReferFriend = async () => {
  try {
    const response = await client.users.getReferFriend();
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const checkTakenEmail = async (takenEmail:any) => {
  const { email } = takenEmail;

  const mappedData = {
    email: email,
  };

  try {
    const response = await client.users.emailTaken(mappedData);
    if (!response.error) {
      return response;
    } else {
      // If the response has an error flag, return it
      return { error: true, message: response.message };
    }
  } catch (error) {
    console.error(error);
    // If an error occurs, return an object indicating the error
    return { error: true, message: error.message };
  }
};

export const getBasicProfileData = async () => {
  try {
    const response = await client.users.getOperatorProfileData();
    const user = response.data?.user;

    return {
      id: user?.id,
      name: user?.name,
      email: user?.email,
      email_verified: user?.profile.email_verified, 
      profile_photo: user?.profile_photo,
      account_type: user?.account_type
    };
  } catch (error) {
    console.error(error);
  }
};

export const requestVerificationEmail = async (data: { email: string, user_id: number }) => {
  const mappedData = {
    email: data.email,
    ref: 'verify',
    user_id: data.user_id
  };

  try {
    const response = await loginVerifyEmail(mappedData);
    if (response && !response.error) {
      return { success: true };
    }
    return { success: false, error: response?.message || 'Failed to send verification email' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to send verification email' 
    };
  }
};

export const getTopProfiles = async () => {
  try {
    const response = await client.users.getTopProfiles();
    return response;
  } catch (error) {
    console.error(error);
  }
};