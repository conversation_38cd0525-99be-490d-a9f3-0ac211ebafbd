// @ts-nocheck
import React, { useState, useContext } from 'react';
import { Link } from 'react-router-dom';
import { Card, View, Divider, Text, Button } from 'reshaped';
import BadgeEmergencyHire from '../../components/cards/JobCard/BadgeEmergencyHire/BadgeEmergencyHire';
import inclusivitypledgeicon from '../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import { JobContext } from 'src/context/JobContext';
import moment from 'moment';
import SearchJobCard from '../SearchPage/SearchJobCard';

//TODO: TO BE DELETED

const FavoriteJobs: React.FC = () => {
  const { jobs, handleLoadMore, handleSelectedJob, favourites, addFavorite, removeFavorite, fetchAllJobs } =
    useContext(JobContext);

  return (
    <View className='flex flex-col mb-10'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 '>
        {favourites?.map((job: any, index: any) => (
          <SearchJobCard
            job={job}
            addFavorite={addFavorite}
            removeFavorite={removeFavorite}
            handleSelectedJob={handleSelectedJob}
            fetchAllJobs={fetchAllJobs}
          />
        ))}
      </div>
      {/* <Button
        variant='outline'
        onClick={handleLoadMore}
        className='mt-4 mx-auto bg-[#D1E1FF] text-[#0D2F87] hover:bg-[#0D2F87] hover:text-[#D1E1FF] rounded-lg py-2 px-4 text-sm font-medium'
      >
        Load More
      </Button> */}
    </View>
  );
};

export default FavoriteJobs;
