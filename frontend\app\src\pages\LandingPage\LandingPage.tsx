// @ts-nocheck
import React, { useState, useContext, useEffect, useRef } from 'react';
import { But<PERSON>, Text, Tabs, Image, View, Accordion, Divider, Card, Icon, Carousel, Loader } from 'reshaped';
import { useSearchParams, useNavigate } from 'react-router-dom';
import homepage1 from '../../assets/images/homepage/homepage1.svg';
import homepage2 from '../../assets/images/homepage/homepage2.png';
import homepage3 from '../../assets/images/homepage/homepage3.png';
import homepage4 from '../../assets/images/homepage/homepage4.png';
import homepage51 from '../../assets/images/homepage/homepage51.png';
import homepage52 from '../../assets/images/homepage/homepage52.png';
import homepage6 from '../../assets/images/homepage/homepage6.png';
import homepage8 from '../../assets/images/homepage/homepage8.png';
import homepage10 from '../../assets/images/homepage/homepage10.png';
import homepage121 from '../../assets/images/homepage/homepage121.png';
import homepage122 from '../../assets/images/homepage/homepage122.png';
import homepage14 from '../../assets/images/homepage/homepage14.png';
import homepage15 from '../../assets/images/homepage/homepage15.png';
import homepage16 from '../../assets/images/homepage/homepage16.png';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import homepage19 from '../../assets/images/homepage/homepage19.svg';
import homepage190 from '../../assets/images/homepage/homepage190.png';
import homepage20 from '../../assets/images/homepage/homepage20.png';
import homepage21 from '../../assets/images/homepage/homepage21.png';
import homepage22 from '../../assets/images/homepage/homepage22.png';
import homepage23 from '../../assets/images/homepage/homepage23.png';
import homepage24 from '../../assets/images/homepage/homepage24.png';
import homepage25 from '../../assets/images/homepage/homepage25.png';
import homepage26 from '../../assets/images/homepage/homepage26.png';
import homepage27 from '../../assets/images/homepage/homepage27.png';
import homepage28 from '../../assets/images/homepage/homepage28.png';
import homepage29 from '../../assets/images/homepage/homepage29.png';
import BS7858 from '../../assets/images/homepage/BS7858.png';
import check from '../../assets/images/homepage/check.png';
import homepage30 from '../../assets/images/homepage/homepage30.jpg';
import homepage31 from '../../assets/images/homepage/homepage31.jpg';
import homepage32 from '../../assets/images/homepage/homepage32.jpg';
import client from '../../assets/images/homepage/client.jpg';
import security from '../../assets/images/homepage/security.jpg';
import homepage71 from '../../assets/images/homepage/homepage71.svg';
import homepage72 from '../../assets/images/homepage/homepage72.svg';
import homepage73 from '../../assets/images/homepage/homepage73.svg';
import homepage91 from '../../assets/images/homepage/homepage91.svg';
import homepage92 from '../../assets/images/homepage/homepage92.svg';
import homepage11 from '../../assets/images/homepage/homepage11.svg';
import homepage13 from '../../assets/images/homepage/homepage13.svg';
import homepage18 from '../../assets/images/homepage/homepage18.svg';
import homepage123 from '../../assets/images/homepage/homepage123.svg';
// import homepage711 from '../../assets/images/homepage/homepage711.svg';
import homepage711 from '../../assets/images/homepage/homepage711.webp';
import homepage712 from '../../assets/images/homepage/homepage712.svg';
import homepage713 from '../../assets/images/homepage/homepage713.svg';
import referfrend from '../../assets/images/referfrend/referfrend.svg';
import operatorQuestions, { OperatorQuestionsType } from '../../store/dummydata/OperatorQuestionsHomePage';
import clientQuestions, { ClientQuestionsType } from '../../store/dummydata/ClientQuestionsHomePage';
import dummyOperator from 'src/store/dummydata/OperatorHomePageDummy';
import { IdVerified } from 'src/assets/icons';
import CertifiedButton from 'src/components/SCard/CertifiedButton/CertifiedButton';
import BadgeEmergencyHire from 'src/components/cards/JobCard/BadgeEmergencyHire/BadgeEmergencyHire';
import jobs from '../../store/dummydata/JobsHomePageDummy';
import { useModalAction } from 'src/context/ModalContext';
import Footer from '../Footer/Footer';
import Subscribe from '../Subscribe/Subscribe';
import './costum.css';
import YoutubeVideoModal from './YouTubeModal/YouTubeModal';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Signature from './Signature';
import SurelyIcon from './SurelyIcon';
import GreenCheck from './GreenCheck';
import group from '../../assets/images/homepage/Group.svg';
import ButtonCarousel from '../../components/BuuttonCarousel/ButtonCarousel';
import OperatorHomePageCarousel from 'src/components/HomePageCarousels/OperatorHomePageCarousel';
import JobsHomePageCarousel from 'src/components/HomePageCarousels/JobsHomePageCarousel';
import { GuestUpgradeBanner } from 'src/components/GuestUpgradeBanner';
import { getTopProfiles } from 'src/services/user';
import Client from 'src/client';
import SearchOperatorCard from '../SearchOperator/SearchOperatorCard';
import Loading from 'src/components/Loading/Loading';
import { OperativesContext } from 'src/context/OperativesContext';
import axios from 'axios';
import NoOperatorFavorite from 'src/components/NoData/NoOperatorFavorite';
import { useLocation } from 'react-router-dom';
import { AuthContext } from 'src/context/AuthContext';
import SearchOperatorLandingPage from 'src/layouts/SearchOperatorLandingPage';

const buttons = [
  {
    title: 'Door Supervisor',
    available: 786,
  },
  {
    title: 'Close Protection',
    available: 456,
  },
  {
    title: 'CCTV',
    available: 118,
  },
  {
    title: 'Security Guard',
    available: 245,
  },
  {
    title: 'Public Space Surveillance',
    available: 345,
  },
];

const VideoProps = [
  { id: 'MTf9N31lsvY', title: 'Video 1' },
  { id: 'cVvLYtufyKM', title: 'Video 2' },
  { id: 'nq2gyrcnCBc', title: 'Video 3' },
];

const gradientStyle = {
  background: 'linear-gradient(180deg, #0080D3 55.61%, #0064A5 90.19%)',
};
const LandingPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { user: userInfo, isAuthenticated } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState('1');
  const [activeTabQuestions, setActiveTabQuestions] = useState('0');
  const [activeId, setActiveId] = useState<number | null>(null);
  const [showAllOperators, setShowAllOperators] = useState(false);
  const [showAllClients, setShowAllClients] = useState<boolean>(false);                     
  const { openModal } = useModalAction();
  const navigate = useNavigate();
  const [currentVideo, setCurrentVideo] = useState<string | null>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [referralToken, setReferralToken] = useState('');
  const [topProfiles, setTopProfiles] = useState([]);
  const {
    allOperatives,
    favourites,
    fetchAllOperative,
    isLoadingOperatives,
    isLoadingMoreOperatives,
    handleLoadMore,
    addFavorite,
    removeFavorite,
    setFilters,
    page,
    setPage,
    isLastPage,
    from,
  } = useContext(OperativesContext);
  const user = userInfo?.user;
  const loadMoreButton = useRef(null);
  const isEmpty = allOperatives?.length === 0;
  const location = useLocation();
  const currentPath = location.pathname;
  const isFavoritePage = currentPath.includes('favorite-operator');

  const aggregatedOperators = isFavoritePage ? favourites : allOperatives;

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;
  const openVideoModal = (videoId: string) => {
    setCurrentVideo(videoId);
    setIsVideoModalOpen(true);
  };

  const closeVideoModal = () => {
    setCurrentVideo(null);
    setIsVideoModalOpen(false);
  };

  const getBackgroundImage = () => {
    if (!isAuthenticated) {
      return `url(${security})`;
    } else if (user?.account_type == 2) {
      return `url(${client})`;
    } else {
      return `url(${security})`;
    }
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const action = queryParams.get('action');
    const referalToken = queryParams.get('referal_token');
    if (action === 'sign_up' && referalToken) {
      openModal('REGISTER');
      setReferralToken(referalToken);
    } else if (action ==='login-redirect') {
      navigate('/', { replace: true })
      localStorage.setItem('wpRedirect', 'true');
      openModal('LOGIN');
    } else if (action ==='register-redirect') {
      navigate('/', { replace: true })
      localStorage.setItem('wpRedirect', 'true');
      openModal('REGISTER');
    }else if (action === 'guest-redirect') {
      localStorage.setItem('wpRedirect', 'true');
      openModal('GUEST_VIEW');
    }
  }, []);

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };
  const handleTabChangeQuestions = (args: { value: string; name?: string }) => {
    setActiveTabQuestions(args.value);
  };
  const toggleShowAllOperators = () => {
    setShowAllOperators(!showAllOperators);
  };

  const toggleShowAllClients = () => {
    setShowAllClients(!showAllClients);
  };
  const clientFaq = () => {
    navigate('/clients');
    setTimeout(() => {
      const faqSection = document.getElementById('faq');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };
  const operatorFaq = () => {
    navigate('/security-operatives');
    setTimeout(() => {
      const faqSection = document.getElementById('faq');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };

  const findSurelyCharter = () => {
    navigate('/about-us');
    setTimeout(() => {
      const faqSection = document.getElementById('surelycharter');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };
  const scrollToSurelyCharter = () => {
    setTimeout(() => {
      const surelyCharterSection = document.getElementById('surelycharter');
      if (surelyCharterSection) {
        surelyCharterSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };
  const loadMore = () => {
    if (!isAuthenticated) {
      openModal('GUEST_VIEW');
      return;
    }
    handleLoadMore();
    loadMoreButton.current?.scrollIntoView(false, { behavior: 'smooth' });
  };

  return (
    <View className='mt-[-85px] w-full overflow-x-hidden '>
      <GuestUpgradeBanner />
      <View className='mx-auto flex w-full flex-col items-center gap-8 bg-[#F4F5F7] text-center '>
        <View className='mt-10 flex w-screen max-w-[1320px] flex-col md:w-full lg:mx-auto lg:mt-[100px]'>
          <View className='mx-auto ml-[0px] mt-[22px] flex flex-col sm:flex-row sm:justify-between xl:ml-[20px]'>
            <View className='flex flex-col px-[12px] sm:mx-[0px] sm:mx-auto sm:mt-[22px] xl:w-[760px] xl:px-0'>
              <Text className='font-rufina-stencil mx-0  text-left text-[40px] font-normal leading-[55px] text-[#323C58] sm:mx-0 sm:text-left sm:text-[64px] sm:leading-[72px] '>
                The security sector is changing, be a part of it...
              </Text>
              <View className=' ml-0 mt-[32px] sm:ml-[0px] xl:w-[520px]'>
                <Text className='text-[ #383838] rubik mt-[10px] text-left text-[16px] font-medium leading-[24px]'>
                  We're putting the most discerning clients in touch with the most professional security operatives.
                </Text>
              </View>
              <SearchOperatorLandingPage></SearchOperatorLandingPage>
              {isAuthenticated ? (
                <div></div>
              ) : (
                <View className='mt-[32px] flex flex-col sm:flex-row '>
                  <Button
                    className='rubik ml-0 flex h-[56px] w-full items-center justify-center gap-2 rounded-[8px] border-[2px] !border-[#323C58] !bg-[#fff] p-4 text-[17px] !text-[#323C58] sm:ml-[0px] sm:mr-[24px] sm:w-[271px]'
                    onClick={() => {
                      if (!isAuthenticated) {
                        openModal('REGISTER', { userType: 'operative' });
                      }
                    }}
                  >
                    Sign up - Security Operative
                  </Button>
                  <Button
                    className='rubik ml-0 mt-[10px] flex h-[56px] w-full items-center justify-center gap-2  rounded-[8px] !bg-[#0B80E7] p-4 text-[17px]  !text-[#ffff] sm:ml-[0px] sm:mt-0 sm:w-[166px] '
                    onClick={() => {
                      if (!isAuthenticated) {
                        openModal('REGISTER', { userType: 'client' });
                      }
                    }}
                  >
                    Sign up - Client
                  </Button>

                  <Button
                    className='rubik ml-0 mt-[10px] flex h-[56px] w-full items-center justify-center gap-2 rounded-[8px] border-[2px] !bg-[#323c58] p-4 text-[17px] !text-[#F4F5F7] sm:ml-[24px] sm:mt-0 sm:w-[200px]'
                    onClick={() => {
                      if (!isAuthenticated) {
                        openModal('GUEST_VIEW');
                      }
                    }}
                  >
                    Continue as Guest
                  </Button>
                </View>
              )}
            </View>
            {!isSmallScreen && <Image src={homepage1} className='-mt-[28px] ml-[24px] h-[380px] w-[410px]' />}
          </View>

          <View className='mt-[22px] flex w-full flex-col  '>
            {/* <SearchOperatorLandingPage></SearchOperatorLandingPage> */}
            <View className='flex items-center justify-center'>
              <View className='mt-[20px] w-[240px]'>
                <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
                  <Tabs.List> 
                    <Tabs.Item value='0'>
                      <div className='flex items-center gap-[4px]'>
                        <span className='material-icons-outlined text-[20px] '>person</span>
                        <span className='rubik p-[1.5px] text-[15px] font-normal leading-[20px] text-[#14171F]'>Find work</span>
                      </div>
                    </Tabs.Item>
                    <Tabs.Item value='1'>
                      <div className='flex items-center gap-[4px]'>
                        <span className='material-icons-outlined text-[20px]'>security</span>
                        <span className='rubik p-[1.5px] text-[15px] font-normal leading-[20px] text-[#14171F]'>Hire</span>
                      </div>
                    </Tabs.Item>
                  </Tabs.List>
                </Tabs>
              </View>
            </View>

            {activeTab === '0' && (
              <>
                <div className='  mt-[28px] sm:mx-[0px] '>
                  <div className='mt-[20px] hidden w-full grid-cols-2 gap-2 gap-[24px] px-[12px] sm:grid lg:flex lg:justify-between xl:px-0'>
                    {jobs.map((job: any) => (
                      <div
                        key={job?.title + job?.jobsId}
                        onClick={() => {
                          if (!isAuthenticated) {
                            openModal('REGISTER');
                          }
                        }}
                      >
                        <Card className='h-[700px] w-full cursor-pointer rounded-lg border border-[#dfe2ea]  p-6  drop-shadow-md xl:w-[300px]'>
                          <View className='flex flex-col items-center gap-5'>
                            <View className='flex w-full items-center justify-between'>
                              <View className='flex items-center'>{job.emergency ? <BadgeEmergencyHire /> : null}</View>

                              <Image src={job.is_favorite ? homepage28 : homepage29} className='' />
                            </View>

                            <div>
                              <View>
                                <Image className='w-fill h-[176px] rounded-xl bg-contain' src={job.photo} />
                              </View>
                              <View className='flex w-full flex-col justify-start gap-1'>
                                <View className='mt-[14px]'>
                                  <Text className='rubik text-base font-medium leading-5'>{job.title}</Text>
                                </View>
                                <View>
                                  <Text className='rubik my-3 text-xl font-medium leading-8'>£{job.pay_hour}/h</Text>
                                </View>
                                <View direction='row' gap={1}>
                                  <Text className='rubik text-sm font-medium leading-5'>I need:</Text>
                                  <Text className='rubik text-sm font-medium leading-5 text-[#0B80E7]'>{job.operator_type}</Text>
                                </View>
                                <View direction={'row'} align={'center'} className='my-1'>
                                  <span className='material-icons-outlined  w-1/12 text-sm'>calendar_today</span>

                                  <View className='w-11/12'>
                                    <Text>
                                      From {job.start_date} to {job.end_date}
                                    </Text>
                                  </View>
                                  <View className={'my-2'}>
                                    <Text className='rubik text-sm font-normal leading-5 '>Published 2 days ago</Text>
                                  </View>
                                </View>
                                <View gap={1} className='rounded bg-[#F4F5F7]'>
                                  <View direction={'row'} gap={2} className='ml-[10px] mt-[10px]'>
                                    <View direction={'row'} gap={2}>
                                      <span className='material-icons-outlined text-base'>place</span>
                                      <Text className='rubik text-sm font-normal leading-5'>{job.place}</Text>
                                    </View>
                                    <View direction={'row'} align={'center'} gap={2}>
                                      <span className='material-icons text-base'>person</span>
                                      <Text className='rubik text-sm font-normal leading-5'>{job.number_operator} People</Text>
                                    </View>
                                  </View>
                                  <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                                    <span className='material-icons-outlined text-base'>credit_card</span>
                                    <View direction={'row'} gap={1}>
                                      <Text className='rubik text-sm font-normal leading-5'>Earnings:</Text>
                                      <Text className='rubik text-sm font-medium leading-5'>£{job.earnings}</Text>
                                    </View>
                                  </View>
                                  <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                                    <span className='material-icons-outlined text-base'>lock</span>
                                    <View direction={'row'} gap={1}>
                                      <Text className='rubik text-sm font-normal leading-5'>Escrow deposit:</Text>
                                      <Text className='rubik text-sm font-medium leading-5'>{job.escrow_deposit}%</Text>
                                    </View>
                                  </View>
                                  <View direction={'row'} align={'center'} gap={2} className='mb-[10px] ml-[10px]'>
                                    <span className='material-icons-outlined text-base'>payments</span>
                                    <View direction={'row'} gap={1}>
                                      <Text className='rubik text-sm font-normal leading-5'>Payment terms:</Text>
                                      <Text className='rubik text-sm font-medium leading-5'>{job.payment_terms} days</Text>
                                    </View>
                                  </View>
                                </View>
                              </View>
                              <Divider className='h-[1px] w-full'></Divider>
                              <View className='mt-[10px] flex flex-wrap items-center justify-start gap-2'>
                                <Button
                                  size='small'
                                  rounded={true}
                                  elevated={false}
                                  className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                                >
                                  <Text color='positive' className='rubik flex items-center gap-1'>
                                    <span className='material-icons text-[16px]'>star</span>
                                    Close Protection
                                  </Text>
                                </Button>
                                <Button
                                  size='small'
                                  rounded={true}
                                  elevated={false}
                                  className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                                >
                                  <Text color='positive' className='rubik flex items-center gap-1'>
                                    <span className='material-icons text-[16px]'>star</span>
                                    Security Guard
                                  </Text>
                                </Button>
                              </View>
                            </div>
                          </View>
                        </Card>
                      </div>
                    ))}
                  </div>
                </div>
                <div className='block sm:hidden '>
                  <JobsHomePageCarousel />
                </div>
              </>
            )}
            {activeTab === '1' && (
            <div className='mt-[28px] sm:mx-[0px]'>
                  <div className='flex flex-col w-full'>
                    {isLoadingOperatives && allOperatives.length === 0 ? (
                      <div className='mt-40'>
                        <Loading />
                      </div>
                    ) : (
                      <>
                        <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
                          {aggregatedOperators?.length !== 0 ? (
                            aggregatedOperators?.map((operator: any, index: number) => {
                              if (index === from - 4) {
                                return (
                                  <div ref={bottomElement} key={operator.id + '_' + index} className='scroll-mt-[82px]'>
                                    <SearchOperatorCard
                                      operator={operator}
                                      addFavorite={addFavorite}
                                      removeFavorite={removeFavorite}
                                      fetchAllOperative={fetchAllOperative}
                                    />
                                  </div>
                                );
                              } else
                                return (
                                  <SearchOperatorCard
                                    key={operator.id + '_' + index}
                                    operator={operator}
                                    addFavorite={addFavorite}
                                    removeFavorite={removeFavorite}
                                    fetchAllOperative={fetchAllOperative}
                                  />
                                );
                            })
                          ) : (
                            <div className='col-span-4'>
                              <NoOperatorFavorite favourite={isFavoritePage} />
                            </div>
                          )}
                        </div>
                        {isLoadingMoreOperatives && !isEmpty && (
                          <div ref={loadMoreButton} className='mb-10 mt-4 h-[42.19px] w-full py-2'>
                            <Loader size='medium' className='mx-auto' />
                          </div>
                        )}
                        {!isLastPage && !isEmpty && !isLoadingMoreOperatives && (
                          <div>
                            <Button
                              variant='outline'
                              onClick={loadMore}
                              className='rubik mx-auto mb-10 mt-4 rounded-lg bg-[#D1E1FF] px-4 py-2 text-[16px] font-medium leading-6 !text-[#1A1A1A] hover:bg-[#0D2F87] hover:text-[#D1E1FF]'
                            >
                              Load More
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  <div className='block sm:hidden'>
                    <OperatorHomePageCarousel />
                  </div>
            </div>
          )}
          </View>
        </View>
        <View className='mx-auto  flex w-full flex-col items-center gap-8 bg-[#FFFF] text-center sm:mt-[42px]'>
          <View className='mx-auto mt-10 flex w-full flex-col justify-between sm:mt-[74px] xl:w-[1320px] xl:flex-row'>
            <View className='mx-auto flex  flex-col  justify-center px-[12px] sm:mx-[0px] sm:mr-[35px] xl:w-[488px] xl:px-0'>
              <Text className='font-rufina-stencil text-left text-[48px] font-[400] leading-[56px] text-[#323C58]'>
                Why choose <span> </span>
                <span className='font-rufina-stencil text-left text-[48px] font-[400] leading-[56px] text-[#0B80E7]'>Surely?</span>
              </Text>
              <Text className='rubik mt-[4px] text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                We're transforming the UK security operative marketplace so that it's fairer, better and simpler than ever before. 
              </Text>

              <a
                className='btn-no-hover mt-[12px] flex h-[48px] w-[147px] items-center gap-[8px] py-4'
                rel='noopener noreferrer'
                href='/about-us'
                onClick={(event) => {
                  event.preventDefault();
                  navigate('/about-us');
                  scrollToSurelyCharter();
                }}
              >
                <Text className='rubik text-[17px] font-medium leading-5 !text-[#0B80E7]'>Find out more</Text>
                <span className='material-icons mt-[2px] flex h-[24px] w-[24px] items-center text-[18px] !text-[#0B80E7]'>arrow_forward</span>
              </a>
            </View>
            <Image src={homepage2} className='h-auto w-auto   flex-shrink-0 sm:mx-[0px] sm:mt-[22px] xl:h-[378.767px] xl:w-[747.179px] ' />
          </View>

          <View className='flex flex-col items-center justify-between px-[12px]   sm:mx-[0px] sm:flex-row  xl:mt-[62px] xl:w-[1310px] xl:px-0'>
            <View className='mt-[15px] flex  flex-col items-start sm:ml-[0px] sm:mt-[0px]  xl:w-[424px]'>
              <svg xmlns='http://www.w3.org/2000/svg' width='37' height='37' viewBox='0 0 37 37' fill='none'>
                <path
                  d='M29.8158 21.7331V8.80062C29.8158 7.02241 28.3609 5.5675 26.5827 5.5675H3.95089C2.17268 5.5675 0.717773 7.02241 0.717773 8.80062V21.7331C0.717773 23.5113 2.17268 24.9662 3.95089 24.9662H26.5827C28.3609 24.9662 29.8158 23.5113 29.8158 21.7331ZM26.5827 21.7331H3.95089V8.80062H26.5827V21.7331ZM15.2668 10.4172C12.5833 10.4172 10.4171 12.5834 10.4171 15.2669C10.4171 17.9503 12.5833 20.1165 15.2668 20.1165C17.9503 20.1165 20.1165 17.9503 20.1165 15.2669C20.1165 12.5834 17.9503 10.4172 15.2668 10.4172ZM36.2821 10.4172V28.1993C36.2821 29.9775 34.8272 31.4324 33.049 31.4324H5.56745C5.56745 29.8159 5.56745 29.9775 5.56745 28.1993H33.049V10.4172C34.8272 10.4172 34.6655 10.4172 36.2821 10.4172Z'
                  fill='#0B80E7'
                />
              </svg>
              <Text className='rubik mt-[12px] text-left text-[24px] font-normal leading-8 text-[1A1A1A]'>A fairer place</Text>
              <Text className='rubik mt-[4px] items-start text-left text-[16px] font-normal leading-[24px] text-[#383838] '>
                Without all the middlemen taking a cut, clients pay less, and security operatives earn more.
              </Text>
            </View>
            <View className='mt-[20px] flex  flex-col items-start sm:ml-[0px] sm:mt-[0px]  xl:w-[424px]'>
              <svg xmlns='http://www.w3.org/2000/svg' width='39' height='39' viewBox='0 0 39 39' fill='none'>
                <g clipPath='url(#clip0_6196_2174)'>
                  <path
                    d='M25.5901 17.76C27.0316 17.76 28.2002 16.5914 28.2002 15.1499C28.2002 13.7084 27.0316 12.5398 25.5901 12.5398C24.1486 12.5398 22.98 13.7084 22.98 15.1499C22.98 16.5914 24.1486 17.76 25.5901 17.76Z'
                    fill='#0B80E7'
                  />
                  <path
                    d='M13.4097 17.76C14.8512 17.76 16.0198 16.5914 16.0198 15.1499C16.0198 13.7084 14.8512 12.5398 13.4097 12.5398C11.9681 12.5398 10.7996 13.7084 10.7996 15.1499C10.7996 16.5914 11.9681 17.76 13.4097 17.76Z'
                    fill='#0B80E7'
                  />
                  <path
                    d='M19.4999 26.4607C16.9246 26.4607 14.7147 25.0512 13.4966 22.9805H10.5907C11.9828 26.5477 15.4455 29.0708 19.4999 29.0708C23.5543 29.0708 27.017 26.5477 28.4091 22.9805H25.5031C24.3025 25.0512 22.0752 26.4607 19.4999 26.4607ZM19.4825 2.09961C9.87726 2.09961 2.09912 9.89515 2.09912 19.5004C2.09912 29.1056 9.87726 36.9011 19.4825 36.9011C29.1051 36.9011 36.9007 29.1056 36.9007 19.5004C36.9007 9.89515 29.1051 2.09961 19.4825 2.09961ZM19.4999 33.421C11.8087 33.421 5.57927 27.1915 5.57927 19.5004C5.57927 11.8092 11.8087 5.57976 19.4999 5.57976C27.191 5.57976 33.4205 11.8092 33.4205 19.5004C33.4205 27.1915 27.191 33.421 19.4999 33.421Z'
                    fill='#0B80E7'
                  />
                </g>
                <defs>
                  <clipPath id='clip0_6196_2174'>
                    <rect width='39' height='39' fill='white' />
                  </clipPath>
                </defs>
              </svg>
              <Text className='rubik mt-[12px] text-left text-[24px] font-normal leading-8 text-[1A1A1A]'>A better place</Text>
              <Text className='rubik mt-[4px] items-start text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                Clients get the best person for the job every time, by checking out security operative profiles. 
              </Text>
            </View>
            <View className='mt-[20px] flex  flex-col items-start sm:ml-[0px] sm:mt-[0px] xl:w-[424px]'>
              <svg xmlns='http://www.w3.org/2000/svg' width='35' height='35' viewBox='0 0 35 35' fill='none'>
                <path
                  d='M20.9991 7.87775V4.37876H14.0011V7.87775H20.9991ZM3.50412 11.3767V30.6212H31.4961V11.3767H3.50412ZM31.4961 7.87775C33.438 7.87775 34.9951 9.43481 34.9951 11.3767V30.6212C34.9951 32.5632 33.438 34.1202 31.4961 34.1202H3.50412C1.56218 34.1202 0.00512695 32.5632 0.00512695 30.6212L0.0226219 11.3767C0.0226219 9.43481 1.56218 7.87775 3.50412 7.87775H10.5021V4.37876C10.5021 2.43681 12.0592 0.879761 14.0011 0.879761H20.9991C22.941 0.879761 24.4981 2.43681 24.4981 4.37876V7.87775H31.4961Z'
                  fill='#0B80E7'
                />
              </svg>
              <Text className='rubik mt-[12px] text-left text-[24px] font-normal leading-8 text-[1A1A1A]'>A simpler place </Text>
              <Text className='rubik mt-[4px] items-start text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                Clients and security operatives can chat in a safe place to agree all details before signing contracts. 
              </Text>
            </View>
          </View>

          <div
            className='mx-4 mt-[62px] flex h-auto w-full justify-between bg-cover bg-center bg-left-top bg-no-repeat md:mx-auto md:h-[713px] xl:w-[1320px]'
            style={{
              backgroundImage: `url(${homepage4})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <View className='flex flex-col items-start  px-[12px] md:items-start xl:mx-auto xl:w-full  xl:pl-[32px] '>
              <Text className='rubik mt-[20px] text-left text-center text-[24px] font-normal !text-[#7CDBEF] md:mt-[20px] md:text-left'>
                Security Operatives
              </Text>
              <View className='mt-[142.5px] w-full max-w-[534px]'>
                <Text className='font-rufina-stencil text-left text-start text-[48px] font-normal leading-[56px] text-[#ffff] md:text-left md:text-[48px] '>
                  {/* mt-[40px] md:mt-[170px] */}
                  Book security jobs directly with the people who need your skills.
                </Text>
                <Text className='rubik mt-[20px] text-left text-start text-[16px] font-normal leading-[24px] text-[#F4F5F7] md:mt-[15px] md:text-left'>
                  You get to work when and where you want, and we have minimum rates of pay in place too.
                </Text>
              </View>
              <View className='mx-auto mb-[20px] mt-[20px] flex w-full flex-col items-center sm:mx-0 sm:mb-[0px] sm:w-auto  sm:flex-row md:mt-[90px] md:items-start'>
                <Button
                  onClick={() => {
                    if (user?.account_type == 2) {
                      navigate('/');
                    } else if (user?.account_type == 1) {
                      navigate('/search-jobs');
                    } else if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className='flex h-[auto] w-full flex-row justify-center rounded-[8px] !bg-[#7CDBEF] p-[10px] shadow-md  sm:h-[161px] sm:flex-col sm:justify-between sm:p-[20px] xl:w-[277px] '
                >
                  <View className='flex w-full flex-row justify-between sm:flex-col sm:justify-start '>
                    <Text className='rubik text-left text-[18px] font-normal leading-[32px] text-[#323C58] sm:text-[24px]  '>
                      Search client job posts now
                    </Text>
                    <View className='flex items-center justify-end gap-2 py-0 sm:py-4'>
                      <span className='material-icons-outlined ml-4 py-0 text-[17px] sm:ml-0 sm:py-[4px] sm:text-[24px]   '>arrow_forward_ios</span>
                    </View>
                  </View>
                </Button>
                <Button
                  onClick={() => {
                    if (user?.account_type == 2) {
                      navigate('/');
                    } else if (user?.account_type == 1) {
                      navigate('/my-profile');
                    } else if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className='ml-[0px] mt-[10px] flex  h-auto w-full  flex-row justify-center rounded-[8px] !bg-[#7CDBEF] p-[10px] shadow-md sm:ml-[20px] sm:mt-[0px] sm:h-[161px] sm:flex-col sm:justify-between sm:p-[20px] xl:w-[277px]'
                >
                  <View className='flex w-full flex-row justify-between sm:flex-col sm:justify-start  '>
                    <Text className='rubik text-left text-[18px] font-normal leading-[32px] text-[#323C58] sm:text-[24px] sm:leading-[32px]  '>
                      Your personal Surely ProFile
                    </Text>
                    <View className='flex items-center justify-end gap-2 py-0 sm:py-4'>
                      <span className='material-icons-outlined ml-4 py-0 text-[17px] sm:ml-0 sm:py-[4px] sm:text-[24px] '>arrow_forward_ios</span>
                    </View>
                  </View>
                </Button>

                <Button
                  onClick={() => operatorFaq()}
                  className='ml-0 mt-[10px] flex h-auto w-full flex-row justify-center rounded-[8px] !bg-[#7CDBEF] p-[10px] shadow-md sm:ml-[20px] sm:mt-[0px] sm:h-[161px] sm:flex-col sm:justify-between sm:p-[20px] xl:w-[277px]'
                >
                  <View className='flex w-full flex-row justify-between sm:flex-col sm:justify-start '>
                    <Text className='rubik text-left text-[18px] font-normal leading-[32px] text-[#323C58] sm:text-[24px] sm:leading-[32px]  '>
                      Check out our FAQs
                    </Text>
                    <View className='flex items-center justify-end gap-2 py-0 sm:py-4 lg:mt-[35px] '>
                      <span className='material-icons-outlined ml-4 py-0 text-[17px] sm:ml-0 sm:py-[4px] sm:text-[24px]'>arrow_forward_ios</span>
                    </View>
                  </View>
                </Button>
              </View>
            </View>
          </div>

          <View className='flex w-auto flex-col  bg-[#F4F5F7] xl:mt-[28px] xl:w-[1320px] xl:flex-row'>
            <Image src={homepage51} className='xl:mx-auto' />

            <View className='flex w-full flex-1 flex-col justify-end gap-[12px] px-[12px] py-[32px]  xl:w-[660px] xl:px-[48px] '>
              <View className='flex flex-col gap-[12px] xl:w-[529px]'>
                <Text className='font-rufina-stencil mt-[15px] text-left text-left text-[24px] font-normal leading-[24px] text-[#323C58] sm:mt-[0] sm:text-left sm:text-[48px] sm:leading-[56px]'>
                  Our Mission.
                </Text>

                <Text className=' rubik text-left  text-[16px] font-normal leading-[24px] text-[#383838] sm:text-left sm:text-[16px]'>
                  We're on a mission to transform the UK security operative marketplace. We've listened to all your frustrations and made a list of
                  what you want - and we've added them all into Surely. It's time to put more money into the hands of security operatives, and to give
                  clients a better deal too. We're using innovative technologies to put professional security operatives directly in touch with the
                  most discerning clients.
                  <br />
                  <br />
                  Simple. Secure. Surely.
                </Text>
              </View>
              <View className=''>
                <a
                  href='/about-us'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='btn-no-hover mt-[2px] flex h-[48px] w-[147px] items-center gap-[8px]'
                  onClick={(event) => {
                    event.preventDefault();
                    navigate('/about-us');
                  }}
                >
                  <Text className='rubik text-[17px] font-medium leading-5 !text-[#0B80E7]'>Find out more</Text>
                  <span className='material-icons mt-[2px] flex h-[24px] w-[24px] items-center text-[18px] !text-[#0B80E7]'>arrow_forward</span>
                </a>

                <View className='mb-[16px] mt-[60px] sm:mt-[101px]'>
                  <Signature />
                </View>
              </View>
            </View>
          </View>
          <div
            className='mx-4 flex h-auto w-full justify-between bg-cover bg-center bg-left-top bg-no-repeat sm:mt-[32px] md:mx-auto md:h-[713px] lg:w-[1320px]'
            style={{
              backgroundImage: `url(${homepage6})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <View className='flex flex-col items-start  px-[12px]  md:items-start xl:mx-auto xl:w-full xl:pl-[32px]'>
              <Text className='rubik mt-[20px] text-left text-center text-[24px] font-normal !text-[#7CDBEF] md:mt-[20px] md:text-left'>Clients</Text>
              <View className='mt-[142.5px] w-full max-w-[534px]'>
                <Text className='font-rufina-stencil text-left text-start text-[48px] font-normal leading-[56px] text-[#ffff] md:text-left md:text-[48px] '>
                  {/* mt-[40px] md:mt-[170px] */}
                  Find the perfect security operative every single time.
                </Text>
                <Text className='rubik mt-[20px] text-left text-start text-[16px] font-normal leading-[24px] text-[#F4F5F7] md:mt-[15px] md:text-left'>
                  With Surely, you get to choose the right person for the job, every time – and you'll pay less too.
                </Text>
              </View>
              <View className='mx-auto mb-[20px] mt-[20px] flex w-full flex-col items-center sm:mx-0 sm:mb-[0px] sm:flex-row md:mt-[90px] md:items-start'>
                <Button
                  onClick={() => {
                    if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className='flex h-[auto] w-full flex-row justify-center rounded-[8px] !bg-[#7CDBEF] p-[10px] shadow-md  sm:h-[161px] sm:flex-col sm:justify-between sm:p-[20px] lg:w-[277px] '
                >
                  <View className='flex w-full flex-row justify-between sm:flex-col sm:justify-start '>
                    <Text className='rubik text-left text-[18px] font-normal leading-[32px] text-[#323C58] sm:text-[24px] sm:leading-[32px]  '>
                      Search now for security operatives
                    </Text>
                    <View className='flex items-center justify-end gap-2 py-0 sm:py-4 md:mt-[-30px] lg:mt-[0px]'>
                      <span className='material-icons-outlined ml-4 py-0 text-[17px] sm:ml-0 sm:py-[4px] sm:text-[24px]'>arrow_forward_ios</span>
                    </View>
                  </View>
                </Button>
                <Button
                  onClick={() => {
                    if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className='ml-[0px] mt-[10px] flex h-auto w-full  flex-row justify-center rounded-[8px] !bg-[#7CDBEF] p-[10px] shadow-md sm:ml-[20px] sm:mt-[0px] sm:h-[161px] sm:flex-col sm:justify-between sm:p-[20px] lg:w-[277px]'
                >
                  <View className='flex w-full flex-row justify-between sm:flex-col sm:justify-start'>
                    <Text className='rubik text-left text-[18px] font-normal leading-[32px] text-[#323C58] sm:text-[24px] sm:leading-[32px]  '>
                      Create your first job post today
                    </Text>
                    <View className='flex items-center justify-end gap-2 py-0 sm:py-4'>
                      <span className='material-icons-outlined ml-4 py-0 text-[17px] sm:ml-0 sm:py-[4px] sm:text-[24px]'>arrow_forward_ios</span>
                    </View>
                  </View>
                </Button>

                <Button
                  onClick={() => clientFaq()}
                  className='ml-[0px] mt-[10px] flex  h-auto w-full  flex-row justify-center rounded-[8px] !bg-[#7CDBEF] p-[10px] shadow-md sm:ml-[20px] sm:mt-[0px] sm:h-[161px] sm:flex-col sm:justify-between sm:p-[20px] lg:w-[277px]'
                >
                  <View className='flex w-full flex-row justify-between sm:flex-col sm:justify-start  '>
                    <Text className='rubik text-left text-[18px] font-normal leading-[32px] text-[#323C58] sm:text-[24px] sm:leading-[32px]  '>
                      Check out our FAQs
                    </Text>
                    <View className='flex items-center justify-end gap-2 py-0 sm:py-4 lg:mt-[35px]'>
                      <span className='material-icons-outlined ml-4 py-0 text-[17px] sm:ml-0 sm:py-[4px] sm:text-[24px]'>arrow_forward_ios</span>
                    </View>
                  </View>
                </Button>
              </View>
            </View>
          </div>
          <Text className='font-rufina-stencil mb-2 mt-[8px] text-[48px] leading-[56px] !text-[#323C58] sm:mb-0 sm:mt-[54px]'>How it works?</Text>

          <View className='flex w-full  flex-col bg-[#F4F5F7] sm:mt-[8px]'>
            <View className='flex items-center justify-center'>
              <View className='mt-[20px] w-[300px]'>
                <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
                  <Tabs.List>
                    <Tabs.Item value='0'>
                      <div className='flex items-center space-x-2'>
                        <span className='material-icons-outlined text-[20px]'>person</span>
                        <span className='rubik text-[15px] font-normal leading-[20px] text-[#14171F]'>Security Operatives</span>
                      </div>
                    </Tabs.Item>
                    <Tabs.Item value='1'>
                      <div className='flex items-center space-x-2'>
                        <span className='material-icons-outlined text-[20px]'>security</span>
                        <span className='rubik text-[15px] font-normal leading-[20px] text-[#14171F]'>Clients</span>
                      </div>
                    </Tabs.Item>
                  </Tabs.List>
                </Tabs>
              </View>
            </View>

            {activeTab === '0' && (
              <View className=' mx-auto mb-[70px] flex max-w-[1100px] flex-col sm:mb-[103px]'>
                <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between gap-0 sm:mt-[51.5px] sm:gap-[89px] lg:flex-row'>
                  <View className='mx-auto flex flex-col items-start justify-center  gap-3 px-[12px] sm:ml-[0px] xl:w-[540px]   xl:p-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Join our community</Text>
                    <Text className='font-rufina-stencil text-left text-[45px] font-normal leading-[56px] text-[#323C58] sm:text-[48px]'>
                      Sign up today.
                    </Text>
                    <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838] lg:h-[150px]'>
                      Apply to become an official Surely Security Operative. All you need to sign up to our platform is a valid SIA licence. We also
                      need to verify that you are compliant with the most important BS5878 requirements. These will be safely stored on your personal
                      profile for easy sharing with clients.
                    </Text>
                    <div className='mb-[16px] mt-[28px] h-[4px] w-[160px] items-start bg-[#388DD8] sm:mb-0 lg:mt-[-16px]' />
                  </View>

                  <img src={homepage71} className=' shrink-1 mt-[20px] w-full sm:ml-[0px] sm:mt-[0px]  xl:h-[414.77px] xl:w-[469px]' />
                </View>

                <View className='mt-[81.3px] flex max-w-[1100px] flex-col-reverse  gap-0 sm:gap-[89px] lg:flex-row'>
                  <img src={homepage72} className='  mt-[20px] w-full sm:ml-[0px] sm:mt-0 lg:h-[414.77px]  lg:w-[477px] ' />
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] sm:w-full xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Create your own unique ProFile</Text>
                    <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>Show off your skills.</Text>
                    <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                      We know there's more to you than an SIA badge. That's why Surely gives you the opportunity to upload all your qualifications to
                      attract the best clients. You can even add a bio, photos and a video introducing yourself too, as well as uploading your CV and
                      letters of recommendation, making sure you stand out from the crowd.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                </View>
              </View>
            )}
            {activeTab === '1' && (
              <View className=' mx-auto mb-[70px] flex max-w-[1100px] flex-col sm:mb-[103px]'>
                <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between sm:mt-[51.5px] sm:gap-[89px] lg:flex-row'>
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:mx-0 xl:w-[517px] xl:px-0 '>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Professionalising the marketplace</Text>
                    <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>Sign up today.</Text>
                    <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]  sm:h-[150px]'>
                      There's never been an easier, better way to find the right security operatives for your next job, whether you're looking for
                      individuals or teams. Apart from confirming they have a valid SIA licence, we also verify they're compliant with BS5878
                      requirements – you can ask them to share their documents before booking them if you wish to do so.
                    </Text>
                    <div className='my-4 h-[4px]  w-[160px] items-start bg-[#388DD8] sm:my-4 sm:mb-0' />
                  </View>
                  <img src={homepage71} className=' shrink-1 mt-[20px] w-full sm:ml-[0px] sm:mt-[0px]  lg:h-[414.77px] lg:w-[469px]' />
                </View>

                <View className='mt-[81.3px] flex max-w-[1100px] flex-col-reverse justify-between gap-0 sm:gap-[89px] lg:flex-row'>
                  <img src={homepage72} className='shrink-1 mt-[20px] w-full sm:ml-[0px] sm:mt-0 lg:h-[414.77px] lg:w-[477px]' />
                  <View className='mx-auto flex flex-col  items-start  justify-center gap-3 px-[12px] sm:ml-[0px] xl:w-[534px]  xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Better qualified security operatives</Text>
                    <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>
                      Find your perfect match.
                    </Text>
                    <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                      We all know that an SIA licence is the bare minimum when it comes to evaluating the quality of an individual security operative.
                      That's why Surely security operatives upload any relevant qualifications that demonstrate their knowledge and experience. Their
                      personal profile includes photos, video and bio. You can also review their CV and any letters of recommendation too.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                </View>

                <View className='mt-[81.3px] flex max-w-[1100px] flex-col justify-between gap-0 sm:mt-[51.5px] sm:gap-[89px]  lg:flex-row'>
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[536px]  xl:p-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Post a task today</Text>
                    <Text className='font-rufina-stencil text-left text-[48px] font-normal leading-[56px] text-[#323C58]'>
                      Attract the best talent.
                    </Text>
                    <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]'>
                      Create and post a task whenever you need a security operative – you can communicate directly via in-app chat, audio and video to
                      discuss details. You can also search our community and contact security operatives directly. You can even find security
                      operatives for last minute work through our Emergency Hire function.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                  <img src={homepage73} className=' shrink-1 mt-[20px] w-full sm:ml-[0px] lg:h-[414.77px] lg:w-[490px]' />
                </View>
              </View>
            )}
          </View>

          <div className='mt-[-30px] flex w-full' style={gradientStyle}>
            <View className='mx-auto flex max-w-[1320px] flex-col gap-[25px]'>
              <View className='mx-auto  mt-[15px] flex w-full flex-col justify-between gap-[20px] sm:mt-[74.4px] sm:flex-row'>
                <View className='mt-[10px] flex h-auto w-[370px] flex-col rounded-[12px] bg-[#7CDBEF] p-[32px] sm:mt-[0px] sm:h-[312px] sm:w-[648px]'>
                  <Text className='rubik ml-[32px] mt-[10px] text-left text-[16px] font-semibold text-[#323C58] sm:ml-[0px] sm:mt-[0px]'>
                    TOTAL BILLINGS YEAR TO DATE
                  </Text>
                  <Text className='rubik ml-[32px] mt-[13px] text-left text-[35px] font-light leading-normal text-[#323C58] sm:ml-[0px] sm:mt-[0px] sm:text-[96px]'>
                    £2,653,984
                  </Text>
                </View>
                <Image src={homepage22} className='mt-[10px] h-[200px] w-[370px] rounded-[12px] sm:mt-[0px] sm:h-[312px] sm:w-[312px]' />
                <View className='mt-[10px] flex h-auto w-[370px] flex-col rounded-[12px] bg-[#7CDBEF] p-[32px] sm:mt-[0px] sm:h-[312px] sm:w-[312px]'>
                  <Text className='rubik ml-[32px] mt-[32px] text-left text-[16px] font-semibold text-[#323C58] sm:ml-[0px] sm:mt-[0px]'>
                    TOTAL JOBS POSTED
                  </Text>
                  <Text className='rubik ml-[32px] mt-[13px] text-left text-[35px] font-light leading-normal text-[#323C58] sm:ml-[0px] sm:mt-[0px] sm:text-[96px]'>
                    3,689
                  </Text>
                </View>
              </View>

              <View className='flex- mx-auto mt-[0px] flex w-full flex-col justify-between gap-[20px] sm:mt-[0] sm:flex-row'>
                <View className='mt-[0px] flex h-auto w-[370px] flex-col rounded-[12px] bg-[#323C58] p-[32px] sm:mt-[0px] sm:h-[312px] sm:w-[312px]'>
                  <Text className='rubik ml-[32px] mt-[32px]  text-left text-[16px] font-semibold leading-[20px] text-[#7CDBEF] sm:ml-[0px] sm:mt-[0px]'>
                    NEW MEMBERS JOINED THIS <br></br>WEEK
                  </Text>
                  <Text className='rubik ml-[32px] mt-[13px] text-left text-[35px] font-light leading-normal text-[#7CDBEF] sm:ml-[0px] sm:mt-[0px] sm:text-[96px]'>
                    2,037
                  </Text>
                </View>
                <Image src={homepage23} className='mt-[10px] h-[312px] w-[370px] rounded-[12px] sm:mt-[0px] sm:w-[312px]' />
                <View className='mt-[10px] flex h-[312px] w-[370px] rounded-[12px] bg-[#323C58] p-[32px] sm:mt-[0px] sm:w-[312px]'>
                  <View className='w-[248px] '>
                    <Text className='font-rufina-stencil ml-[32px] text-left text-[32px] font-normal leading-[40px] text-[#7CDBEF]  sm:ml-0'>
                      Join our community of professional security operatives today.
                    </Text>
                  </View>
                </View>
                <View className='custom-bg mt-[10px] flex h-auto w-[370px] items-center justify-center rounded-[12px] bg-[#323C58] sm:mt-[0px] sm:h-[312px] sm:w-[312px]'>
                  <svg xmlns='http://www.w3.org/2000/svg' width='134' height='142' viewBox='0 0 134 142' fill='none'>
                    <path
                      d='M43.24 85.0488L57.42 134.458C58.79 139.948 63.3699 141.779 67.9399 141.779H73.89V76.8188C56.96 70.4188 26.77 62.1787 8.01001 62.1787C2.98001 62.1787 0.22998 66.2987 0.22998 70.4087C0.22998 71.7787 0.690024 74.0685 1.15002 74.9785C17.16 74.9785 36.38 80.4688 43.24 85.0488Z'
                      fill='#E4E2DA'
                    />
                    <path
                      d='M90.7599 57.3286L76.5798 7.91846C75.2098 2.42846 70.6298 0.598633 66.0598 0.598633H60.1099V65.5586C77.0399 71.9586 107.23 80.1987 125.99 80.1987C131.02 80.1987 133.77 76.0788 133.77 71.9688C133.77 70.5987 133.31 68.3089 132.85 67.3989C116.84 67.3989 97.6199 61.9086 90.7599 57.3286Z'
                      fill='#71D8EF'
                    />
                  </svg>
                </View>
              </View>

              <View className='flex- mx-auto mb-[45px] mt-[0px] flex w-full flex-col justify-between gap-[20px] sm:mb-[74.4px] sm:mt-[0px] sm:flex-row'>
                <View className='custom-bg mt-[10px] flex h-auto w-[370px] flex-col rounded-[12px] p-[32px] sm:mt-0 sm:h-[312px] sm:w-[312px]'>
                  <View className='256px'>
                    <Text className='rubik ml-[32px] mt-[32px] text-left text-[16px] font-semibold leading-[20px] text-[#7CDBEF] sm:ml-[0px] sm:mt-[0px]'>
                      NEW JOB TASKS ADDED THIS<br></br> WEEK
                    </Text>
                  </View>
                  <Text className='rubik ml-[32px] mt-[13px] text-left text-[35px] font-light leading-normal text-[#7CDBEF] sm:ml-[0px] sm:mt-[0px] sm:text-[96px]'>
                    167
                  </Text>
                </View>
                <View className='mt-[10px] flex h-auto w-[370px] flex-col rounded-[12px] bg-[#7CDBEF] p-[32px] sm:mt-[0px]  sm:h-[312px] sm:w-[312px] sm:w-[648px]'>
                  <Text className='font-rufina-stencil ml-[32px] mt-[32px] text-left text-[48px] font-semibold leading-[40px] text-[#323C58] sm:ml-[0px] sm:mt-[0px] sm:leading-normal'>
                    Centred around you
                  </Text>
                  <View className='mb-[20px] w-[280px] sm:w-[522px]'>
                    <Text className='rubik ml-[32px] mt-[20px] text-left text-[20px] font-normal leading-[28px] text-[#323C58] sm:ml-[0px] sm:mt-[0px]'>
                      Decide when and where you work, and how much you're willing to do it for. You can filter tasks according to location, industry
                      sector, SIA category, rate of pay, date and time, and so on. It's always on your terms.
                    </Text>
                  </View>
                </View>
                <View className='mt-[10px] flex h-auto w-[370px] flex-col rounded-[12px] border border-[2px] border-[#7CDBEF] p-[32px] sm:mt-[0px]  sm:h-[312px] sm:w-[312px]'>
                  <Text className='rubik ml-[30px] mt-[32px] text-left text-[16px] font-semibold leading-normal text-[#7CDBEF] sm:ml-[0px] sm:mt-[0px]'>
                    TOTAL UK SECURITY <br></br>OPERATIVES
                  </Text>
                  <Text className='rubik ml-[30px] mt-[13px] text-left text-[35px] font-light leading-normal text-[#7CDBEF] sm:ml-[0px] sm:mt-[0px] sm:text-[85px]'>
                    47,345
                  </Text>
                </View>
              </View>
            </View>
          </div>

          <Text className='font-rufina-stencil mb-2 mt-[8px] text-[48px] leading-[56px]  !text-[#323C58] sm:mb-0 sm:mt-[48px]'>How it works?</Text>

          <View className='flex w-full flex-col bg-[#F4F5F7] sm:mt-[8px] '>
            <View className='flex items-center justify-center'>
              <View className='mt-[40px] w-[300px]'>
                <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
                  <Tabs.List>
                    <Tabs.Item value='0'>
                      <div className='flex items-center space-x-2'>
                        <span className='material-icons-outlined text-[20px]'>person</span>
                        <span className='rubik text-[15px] font-normal leading-[20px] text-[#14171F]'>Security Operatives</span>
                      </div>
                    </Tabs.Item>
                    <Tabs.Item value='1'>
                      <div className='flex items-center space-x-2'>
                        <span className='material-icons-outlined text-[20px]'>security</span>
                        <span className='rubik text-[15px] font-normal leading-[20px] text-[#14171F]'>Clients</span>
                      </div>
                    </Tabs.Item>
                  </Tabs.List>
                </Tabs>
              </View>
            </View>

            {activeTab === '0' && (
              <View className=' mx-auto mb-[20px] flex max-w-[1100px] flex-col sm:mb-[81.2px]'>
                <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between gap-0 sm:mt-[80px] sm:gap-[89px] lg:flex-row'>
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[534px]  xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Find your perfect job today</Text>
                    <Text className='font-rufina-stencil text-left text-[47px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px]  lg:text-[48px]'>
                      Choose client job tasks that suit you.
                    </Text>
                    <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838]  sm:mt-[0px] '>
                      Clients post job tasks whenever they need a security operative - we'll notify you when a new job post is added that meets your
                      needs. Clients will contact you directly if they like your profile to discuss the details. You can also use Instant Book to show
                      that you are available to work today.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                  <img src={homepage91} className=' shrink-1 mt-[10px] w-full sm:ml-[0px] lg:mt-0  lg:h-[414.77px] lg:w-[472px]' />
                </View>

                <View className='mt-[20px] mt-[50px] flex max-w-[1100px] flex-col-reverse justify-between gap-0 sm:mt-[81.3px] sm:gap-[89px] lg:flex-row '>
                  <img src={homepage711} className=' shrink-1  mt-[20px] w-full sm:mr-[0px] sm:mt-0 lg:h-auto  lg:w-[472px]' />
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[506px] xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Fast and secure payments</Text>
                    <Text className='font-rufina-stencil mt-[12px] text-left text-[48px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px]'>
                      A better way to be paid.
                    </Text>
                    <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-[0px]'>
                      Our innovative diary, messaging and payment system means you'll always know exactly where you stand. We prequalify all clients
                      to make sure they're trustworthy. A deposit is paid out to you as soon as the task is completed. You'll get the balance paid in
                      full, normally within 10 working days.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                </View>
              </View>
            )}
            {activeTab === '1' && (
              <View className=' mx-auto mb-[20px] flex max-w-[1100px] flex-col sm:mb-[81.2px]'>
                <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between gap-[50px] sm:mt-[80px] sm:gap-[89px] lg:flex-row'>
                  <img src={homepage711} className=' shrink-1 w-full sm:ml-[-20px]  sm:h-auto lg:w-[472px]' />
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[507px]  xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>All your financials in one place</Text>
                    <Text className='font-rufina-stencil mt-[12px] text-left text-[48px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px]'>
                      A better way to be paid.
                    </Text>
                    <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-[0px]'>
                      We've built a sophisticated payment system, so you'll always know exactly where you stand for every task you create. Place a
                      deposit in escrow to book a security operative, which is released when they have completed the job to your satisfaction and the
                      total fee is confirmed. The balance is then paid within the agreed payment terms.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                </View>

                <View className='mt-[20px] flex max-w-[1100px] flex-col-reverse justify-between gap-[50px] sm:mt-[92.5px] sm:gap-[89px] lg:flex-row'>
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[506px] xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Commitment to excellence</Text>
                    <Text className='font-rufina-stencil mt-[12px] text-left text-[48px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px]'>
                      Developing the key skills we know you value.
                    </Text>
                    <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-[0px]'>
                      We've created six SurelyPro badges that focus on the most important things we believe that a security operative needs to know to
                      do their job better – dealing with vulnerable people, use of equipment, customer service, conflict management, disability focus
                      and substance awareness. They need to pass an online exam to be awarded each badge.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                  <img src={homepage712} className=' shrink-1  w-full sm:ml-[0px] sm:mr-[-60px] sm:mr-[0px] sm:h-auto lg:w-[505px]' />
                </View>

                <View className='mt-[20px] flex max-w-[1100px] flex-col justify-between gap-[50px] sm:mt-[55.1px] sm:gap-[89px] lg:flex-row'>
                  <img src={homepage713} className='  shrink-1 w-full sm:ml-[0px]  lg:h-[414.77px] lg:w-[472px]' />
                  <View className='mx-auto flex flex-col items-start  justify-center gap-3 px-[12px] sm:ml-[0px] lg:w-[510px]  xl:px-0'>
                    <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Reputations have to be earned</Text>
                    <Text className='font-rufina-stencil mt-[12px] text-left text-[48px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px]'>
                      Check out the reviews left by other clients.
                    </Text>
                    <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-[0px]'>
                      Clients have the option to rate security operatives for every single job they deliver, according to a number of important
                      factors – punctuality, communication, helpfulness, professionalism, positivity, and dress code. Also, any same-day cancellations
                      are listed on their profile, so you can determine which security operatives are reliable before you get in touch.
                    </Text>
                    <div className='my-4 h-[4px] w-[160px] items-start bg-[#388DD8]' />
                  </View>
                </View>
              </View>
            )}
          </View>

          <div className='mt-[48px] flex w-full flex-col gap-[24px] sm:flex-row md:px-[12px]  xl:w-[1300px] xl:px-0 '>
            <div
              className='flex h-[480px] w-full flex-col justify-between rounded-bl-[12px] rounded-tl-[12px] bg-cover  bg-center bg-left-top bg-no-repeat sm:mx-[1.2%] md:mx-auto lg:h-[690px] lg:w-[424px]'
              style={{ backgroundImage: `url(${homepage30})` }}
            >
              <div className='flex h-full flex-col justify-end gap-[32px] p-[12px] lg:p-[32px] '>
                <View className='flex flex-col gap-[10px]'>
                  <Text className='rubik text-left text-[16px] font-medium leading-[24px] text-[#F4F5F7]'>James – SurelyPro #1</Text>
                  <Text className='font-rufina-stencil text-left text-[24px] font-medium leading-[40px] text-[#F4F5F7] xl:text-[32px] '>
                    "It's time to transform the marketplace."
                  </Text>
                </View>
                <Button
                  onClick={() => openVideoModal(VideoProps[0].id)}
                  className='flex items-center justify-center gap-2 self-stretch  rounded-[8px] !bg-[#0B80E7] p-4 !text-[#F4F5F7] '
                >
                  <Text className='rubik text-[17px] font-medium leading-[24px] text-[#FFF]'>Play video</Text>
                </Button>
              </div>
            </div>
            <div
              style={{ backgroundImage: `url(${homepage32})` }}
              className='mx-0 mt-[20px] flex h-[480px] w-full flex-col justify-between bg-cover  bg-center bg-left-top bg-no-repeat sm:mx-[1.2%] sm:mt-[0px] md:mx-auto lg:h-[690px] lg:w-[424px]'
            >
              <div className='flex h-full flex-col justify-end gap-[32px]  p-[12px] lg:p-[32px]'>
                <View className='flex flex-col gap-[10px]'>
                  <Text className='rubik text-left text-[16px] font-medium leading-[24px] text-[#F4F5F7]'>Christian – SurelyPro #2</Text>
                  <Text className='font-rufina-stencil text-left text-[24px] font-medium leading-[40px] text-[#F4F5F7] xl:text-[32px]'>
                    "We need to address key challenges today."
                  </Text>
                </View>
                <Button
                  onClick={() => openVideoModal(VideoProps[1].id)}
                  className='flex items-center justify-center gap-2 self-stretch  rounded-[8px] !bg-[#0B80E7] p-4 !text-[#F4F5F7] '
                >
                  <Text className='rubik text-[17px] font-medium leading-[24px] text-[#FFF]'>Play video</Text>
                </Button>
              </div>
            </div>
            <div
              className='mt-[20px] flex h-[480px] w-full flex-col justify-between rounded-br-[12px] rounded-tr-[12px] bg-cover bg-center bg-left-top bg-no-repeat sm:mx-[1.2%] sm:mt-[0px] md:mx-auto lg:h-[690px] lg:w-[424px]'
              style={{ backgroundImage: `url(${homepage31})` }}
            >
              <div className='flex h-full flex-col justify-end gap-[32px] p-[12px]  lg:p-[32px]'>
                <View className='flex flex-col gap-[10px]'>
                  <Text className='rubik text-left text-[16px] font-medium leading-[24px] text-[#F4F5F7]'>Dan – SurelyPro #3</Text>
                  <Text className='font-rufina-stencil text-left text-[22px] font-[400] leading-[40px] text-[#F4F5F7] xl:text-[31px]'>
                    "Professional operatives must be rewarded."
                  </Text>
                </View>
                <Button
                  onClick={() => openVideoModal(VideoProps[2].id)}
                  className='flex items-center justify-center gap-2 self-stretch  rounded-[8px] !bg-[#0B80E7] p-4 !text-[#F4F5F7] '
                >
                  <Text className='rubik text-[17px] font-medium leading-[24px] text-[#FFF]'>Play video</Text>
                </Button>
              </div>
            </div>
          </div>
          <YoutubeVideoModal active={isVideoModalOpen} deactivate={closeVideoModal} videoId={currentVideo} />

          <View className='mx-auto flex max-w-[1100px] flex-col justify-between gap-[0px]  sm:mt-[82px] sm:gap-[89px] lg:flex-row'>
            <View className='mx-auto mt-[70px] flex flex-col items-start gap-[12px] px-[12px] sm:ml-[0px] sm:mt-[0px] lg:w-[370px] lg:w-[436px] xl:px-0'>
              <Text className='rubik text-left text-[16px] font-medium leading-5 !text-[#388DD8]'>Stand out from the crowd</Text>
              <Text className='font-rufina-stencil mt-[12px] text-left text-[48px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px]'>
                Develop the skills clients value.
              </Text>
              <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-[0px]'>
                We've focused on six key areas that we believe a good Security Operative needs to know – Vulnerable People, Disability Focus, Use of
                Equipment, Customer Service, Conflict Management and Substance Awareness. You'll receive a SurelyPro badge for each one you pass,
                highlighting your commitment to professionalism and passion for excellence.
              </Text>

              <div className='mt-[28px] h-[4px] w-[160px] items-start bg-[#388DD8]' />
            </View>

            <img src={homepage11} className=' mt-[30px] w-full sm:ml-[0px] sm:mt-[0px]  lg:h-[372px] lg:w-[505px] xl:w-[570px] ' />
          </View>

          <div
            className='mx-4 mt-[87px] flex h-auto w-full justify-between rounded-bl-[12px] rounded-tl-[12px] bg-cover bg-center bg-left-top bg-no-repeat sm:h-[300px] md:mx-auto md:h-auto xl:w-[1320px]'
            style={{
              backgroundImage: `url(${homepage121})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <View className='flex w-full flex-row justify-between sm:w-auto   xl:ml-[64px]'>
              <View className='mx-auto mt-[30px] flex w-full flex-col items-start p-6 lg:w-[495px] xl:mt-[200px]'>
                <Text className='font-rufina-stencil mb-3 mt-[10px] text-left text-[32px] font-normal leading-[22px] text-[#7CDBEF] sm:mb-0 sm:text-[48px] sm:leading-[56px]  '>
                  SurelyPro Badges
                </Text>
                <Text className='font-rufina-stencil mt-[10px] text-left text-[24px] font-normal leading-[22px] text-[#ffff] sm:text-[48px] sm:leading-[56px] '>
                  Taking standards to a new level
                </Text>
                <Text className='rubik mt-[12px] text-left text-[16px] font-normal text-[#FFF]  sm:text-[16px] sm:leading-[24px]'>
                  Our SurelyPro badges are a first step in demonstrating our commitment to professionalising the security operative marketplace. It's
                  just one more way in which we're helping to match up security operatives with clients to their mutual benefit.
                </Text>

                <Button
                  className='mt-[32px] flex h-[56px] w-[170px] items-center justify-center gap-2 rounded-md !bg-[#323C58] px-[20px]  py-[16px] shadow-md'
                  onClick={() => navigate('/surely-pro?type=surleypro')}
                >
                  <Text className='rubik text-[17px] leading-[12px] !text-[#ffff] sm:leading-[24px]'>Tell me more</Text>
                </Button>
              </View>
            </View>
            <View className='mr-[48.3px] mt-[35.87px] flex hidden flex-row lg:flex'>
              <Image src={homepage123} />
              <View className='mt-[12.7px]'>
                <SurelyIcon />
              </View>
            </View>
          </div>

          <View className='mx-auto mt-[22px] flex max-w-[1100px] flex-col justify-between sm:mt-[98px] sm:flex-row'>
            <View className='mx-auto flex w-full flex-col justify-between gap-[50px]   sm:gap-[89px] lg:flex-row'>
              <img src={homepage13} className='h-auto  w-full sm:ml-[0] lg:h-[340px]  lg:w-[498px]' />
              <View className='mx-auto mt-[30px] flex flex-col  items-start gap-[12px] px-[12px] sm:mt-[0px]  lg:w-[508px] xl:px-0'>
                <Text className='rubik text-left text-[16px] font-medium leading-5 text-[#388DD8]'>Show the world how great you are</Text>
                <Text className='font-rufina-stencil mt-[12px] text-left text-[48px] font-normal leading-[56px] text-[#323C58] sm:mt-[0px] '>
                  Get rated and reviewed by clients
                </Text>
                <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#383838] sm:mt-[0px] '>
                  Clients can rate you according to a number of important factors - Punctuality, Professionalism, Helpfulness, Dress Code, Positivity
                  and Communication. The better your reviews, the more you'll stand out from the crowd. We've got an arbitration service in place to
                  deal with unfair feedback, and you can even submit client reviews too.
                </Text>
                <View className='py-[16px]'>
                  <div className='h-[4px] w-[160px] items-start bg-[#388DD8] ' />
                </View>
              </View>
            </View>
          </View>

          <View className='mx-[300px] flex w-[100%] flex-col-reverse justify-between bg-[#323C58] sm:mt-[78px] sm:flex-row xl:w-[1320px]'>
            <View className='flex flex-col  justify-center px-[12px] py-[20px] lg:w-[494px] xl:ml-[64px] xl:justify-start xl:px-0 xl:py-0'>
              <Text className=' rubik mt-[4px] text-left text-[24px] leading-[32px] text-[#7CDBEF] sm:text-[24px] xl:mt-[60px]'>
                Comprehensive validation process
              </Text>
              <Text className=' font-rufina-stencil mt-[14px] text-left text-[36px] font-normal leading-[40px] text-[#fff] sm:text-[48px] lg:leading-[56px] xl:mt-[65px]'>
                Complete peace of mind.
              </Text>
              <Text className=' rubik mt-[14px] text-left text-[16px] leading-[22px] text-[#fff] sm:mt-[12px] sm:text-[16px] lg:leading-[24px]'>
                All security operatives need to pass a strict vetting process that we have put in place before being eligible to apply for client
                jobs.
              </Text>
              <Image src={BS7858} className='mt-[24px] h-[31px] w-[148px] sm:mt-[32px] xl:ml-[2px]' />
              <div className='mt-[24px] grid  items-start sm:mt-[20px] lg:w-[392px] lg:grid-cols-2 xl:pl-[15px]'>
                <View className='mt-[4px] flex flex-row items-center gap-[10px] sm:mt-[12px]'>
                  <GreenCheck />
                  <Text className='rubik mt-[1px] text-left text-[16px] font-medium leading-[20px]  text-[#ffff]'>SIA licence</Text>
                </View>
                <View className='mt-[10px] flex flex-row items-center gap-[10px] sm:mt-[12px]'>
                  <GreenCheck />
                  <Text className='rubik mt-[1px] text-left text-[16px] font-medium leading-[20px] text-[#ffff]'>Employment history</Text>
                </View>
                <View className='mt-[10px] flex flex-row items-center gap-[10px] sm:mt-[12px]'>
                  <GreenCheck />
                  <Text className='rubik mt-[1px] text-left text-[16px] font-medium leading-[20px]  text-[#ffff]'>ID check</Text>
                </View>
                <View className='mt-[10px] flex flex-row items-center gap-[10px] sm:mt-[12px]'>
                  <GreenCheck />
                  <Text className='rubik mt-[1px] text-left text-[16px] font-medium leading-[20px]  text-[#ffff]'>Credit check</Text>
                </View>
                <View className='mt-[10px] flex flex-row items-center gap-[10px] sm:mt-[12px]'>
                  <GreenCheck />
                  <Text className='rubik mt-[1px] text-left text-[16px] font-medium leading-[20px]  text-[#ffff]'>Proof of address</Text>
                </View>
                <View className='mt-[10px] flex flex-row items-center gap-[10px] sm:mt-[12px]'>
                  <GreenCheck />
                  <Text className='rubik mt-[1px] text-left text-[16px] font-medium leading-[20px]  text-[#ffff]'>No criminal record</Text>
                </View>
              </div>
              {isAuthenticated ? (
                <div></div>
              ) : (
                <Button
                  onClick={() => {
                    if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className='mt-[15px] flex h-[56px] w-[140px] items-center justify-center gap-2 rounded-[8px]  !bg-[#0B80E7] sm:mt-[60px]'
                >
                  <Text className='font-mendium rubik text-[17px] !text-[#ffff]'>Sign up now</Text>
                </Button>
              )}
            </View>
            <Image src={homepage14} className='h-[auto] w-[100%] sm:mt-[0px] xl:h-[713px]  xl:w-[660px]' />
          </View>

          <div className='mt-[34px] px-[12px] xl:px-0'>
            <Text className=' font-rufina-stencil text-[48px] leading-[56px] !text-[#323C58]'>Browse opportunities by skillset.</Text>
            <Text className=' rubik mt-[10px] mt-[8px] text-[16px] font-normal leading-[24px] text-[#383838]'>
              Looking for people?
              {isAuthenticated ? (
                <div></div>
              ) : (
                <button
                  onClick={() => {
                    if (!isAuthenticated) {
                      openModal('REGISTER');
                    }
                  }}
                  className=' btn-no-hover  rubik ml-2 mt-[10px] mt-[8px] text-[16px] font-normal leading-[24px] text-[#0B80E7] underline'
                >
                  Sign up now
                </button>
              )}
            </Text>
          </div>

          <View className='mx-auto mt-[20px] flex w-full flex-col  justify-between  gap-2 px-[12px] sm:mt-[12px] lg:flex-row xl:w-[1320px] xl:px-0'>
            <div className='hidden w-full grid-cols-2 gap-2 sm:grid lg:flex lg:justify-between '>
              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  mt-[15px] h-[174px] w-full items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md sm:mt-[0px] xl:w-[244px]'
              >
                <View className='mt-[18px] flex  flex-col xl:w-[190px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Door Supervisor
                      </Text>
                    </Button>
                  </View>
                  <Divider className='mt-[20px] h-[1px] w-full'></Divider>
                  <View className='mt-[20px] flex flex-row justify-between'>
                    <Text className='rubik text-[17px] font-medium leading-[24px] text-[#323C58]'>+786 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>

              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  mt-[15px] h-[174px] w-full items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md sm:mt-[0px] xl:w-[244px]'
              >
                <View className='mt-[18px] flex  flex-col xl:w-[190px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Close Protection
                      </Text>
                    </Button>
                  </View>
                  <Divider className='mt-[20px] h-[1px] w-full'></Divider>
                  <View className='mt-[20px] flex flex-row justify-between'>
                    <Text className='rubik text-[17px] font-medium leading-[24px] text-[#323C58]'>+456 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>

              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  mt-[15px] h-[174px] w-full items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md sm:mt-[0px] xl:w-[244px]'
              >
                <View className='mt-[18px] flex  flex-col xl:w-[190px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        CCTV
                      </Text>
                    </Button>
                  </View>
                  <Divider className='mt-[20px] h-[1px] w-full'></Divider>
                  <View className='mt-[20px] flex flex-row justify-between'>
                    <Text className='rubik text-[17px] font-medium leading-[24px] text-[#323C58]'>+118 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>

              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  mt-[15px] h-[174px] w-full items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md sm:mt-[0px] xl:w-[244px]'
              >
                <View className='mt-[18px] flex  flex-col xl:w-[190px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Security Guard
                      </Text>
                    </Button>
                  </View>
                  <Divider className='mt-[20px] h-[1px] w-full'></Divider>
                  <View className='mt-[20px] flex flex-row justify-between'>
                    <Text className='rubik text-[17px] font-medium leading-[24px] text-[#323C58]'>+245 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>
              <Button
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
                variant='outline'
                className='  mt-[15px] h-[174px] w-full items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md sm:mt-[0px] xl:w-[244px]'
              >
                <View className='mt-[18px] flex  flex-col xl:w-[190px]'>
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                    >
                      <Text color='positive' className='rubik flex items-center gap-1'>
                        <span className='material-icons text-[16px]'>star</span>
                        Public Space Surveillance
                      </Text>
                    </Button>
                  </View>
                  <Divider className='mt-[20px] h-[1px] w-full'></Divider>
                  <View className='mt-[20px] flex flex-row justify-between'>
                    <Text className='rubik text-[17px] font-medium leading-[24px] text-[#323C58]'>+345 available</Text>
                    <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                  </View>
                </View>
              </Button>
            </div>

            <div className='block sm:hidden'>
              <ButtonCarousel buttons={buttons} isAuthenticated={false} />
            </div>
          </View>

          <View className='mb-0 flex w-full flex-col items-center bg-[#F4F5F7] sm:mb-[118px] lg:mt-[70px] lg:w-[1320px] lg:flex-row lg:items-start'>
            <Image src={homepage16} className='h-[auto] w-full lg:w-[660px] xl:h-[490px]' />
            <View className='ml-[0xp] flex h-auto  flex-col justify-center px-[12px]   py-[32px] lg:h-[490px] xl:p-[32px]   '>
              <View className='mx-auto flex w-full flex-col  gap-[32px] sm:ml-[0px] sm:mr-0 lg:w-[485px] xl:w-[560px] '>
                <Button
                  variant='outline'
                  className='border-dark-gradient flex h-[44px] w-[195px] items-center justify-center gap-[4px] self-stretch rounded-full border !bg-[#ffff] p-[12px] '
                  icon={() => (
                    <svg xmlns='http://www.w3.org/2000/svg' width='28' height='14' viewBox='0 0 28 14' fill='none'>
                      <path
                        d='M26.1555 2.43677C24.9572 1.19375 23.4567 0.55542 21.7097 0.55542C21.0266 0.55542 20.3547 0.678633 19.694 0.902601C19.0333 1.13777 18.4398 1.50729 17.9247 2.00002L9.08915 10.3428C8.7644 10.634 8.39485 10.8467 7.99171 10.9923C7.58857 11.1267 7.16303 11.2051 6.72629 11.2051C5.58406 11.2051 4.66579 10.7907 3.8931 9.95087C3.13162 9.11099 2.75087 8.10314 2.75087 6.94971C2.75087 5.79628 3.13162 4.84442 3.9043 4.01575C4.67699 3.19826 5.59526 2.8063 6.72629 2.8063C7.17423 2.8063 7.61097 2.8735 8.0253 3.01907C8.41725 3.15345 8.77559 3.37742 9.07795 3.67977L11.7656 6.3114L13.3333 4.73242L10.5225 2.01123C10.0074 1.5185 9.4139 1.14894 8.7532 0.913776C8.10369 0.67861 7.42059 0.566629 6.71509 0.566629C4.97935 0.566629 3.50116 1.19373 2.30294 2.44795C1.10471 3.69097 0.5 5.20277 0.5 6.94971C0.5 8.69666 1.10471 10.2532 2.28054 11.5186C3.46757 12.7952 4.95695 13.4448 6.71509 13.4448C7.39819 13.4448 8.0701 13.3328 8.742 13.1088C9.4139 12.8848 10.0074 12.5265 10.5225 12.0562L19.3693 3.71337C19.6716 3.42221 20.0412 3.19824 20.4443 3.04146C20.8474 2.88468 21.273 2.8063 21.6985 2.8063C22.8183 2.8063 23.7478 3.19826 24.5317 4.01575C25.3156 4.83323 25.6963 5.79628 25.6963 6.94971C25.6963 8.10314 25.3044 9.11099 24.5429 9.95087C23.7702 10.7907 22.8407 11.2051 21.6985 11.2051C21.2618 11.2051 20.8362 11.1379 20.4107 10.9923C20.0076 10.8579 19.6604 10.6452 19.3693 10.3652L16.7376 7.73358L15.1699 9.30138L17.9135 12.0226C18.4062 12.5153 18.9997 12.8848 19.6716 13.1088C20.3323 13.3328 21.0154 13.4448 21.6873 13.4448C23.4343 13.4448 24.9348 12.7952 26.1331 11.5186C27.3201 10.2532 27.9248 8.70786 27.9248 6.94971C27.9248 5.19157 27.3201 3.69097 26.1219 2.44795L26.1555 2.43677Z'
                        fill='url(#paint0_linear_3409_20400)'
                      />
                      <defs>
                        <linearGradient
                          id='paint0_linear_3409_20400'
                          x1='14.2124'
                          y1='-4.46004'
                          x2='14.2124'
                          y2='18.3844'
                          gradientUnits='userSpaceOnUse'
                        >
                          <stop offset='0.536458' stopColor='#323C58' stopOpacity='0.95' />
                          <stop offset='0.921875' stopColor='#6B789C' stopOpacity='0.96' />
                        </linearGradient>
                      </defs>
                    </svg>
                  )}
                >
                  <Text className='rubik text-[16px] font-normal leading-[20px] '>Inclusivity Pledge</Text>
                </Button>
                <Text className='rubik text-left text-[16px] font-normal leading-[24px] text-[#383838] '>
                  We've created an Inclusivity Pledge that every Security Operative is invited to sign up for. It's aligned with the Equality Act
                  2010, demonstrating the commitment of individual security operatives to treat all individuals with the respect and dignity they
                  deserve, regardless of age, gender, ability, race, sexual identity, language, and so on.
                </Text>

                <a
                  href='/inclusivity-pledge'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='btn-no-hover flex h-[48px] w-[147px] items-center gap-[8px] py-[16px]'
                  onClick={(event) => {
                    event.preventDefault();
                    navigate('/inclusivity-pledge');
                  }}
                >
                  <Text className='rubik text-[17px] font-medium leading-5 !text-[#0B80E7]'>Find out more</Text>
                  <span className='material-icons mt-[2px] flex h-[24px] w-[24px] items-center text-[18px] !text-[#0B80E7]'>arrow_forward</span>
                </a>
              </View>
            </View>
          </View>
        </View>
      </View>

      <div
        className='mt-[62px] flex h-[480px] w-full justify-between bg-cover bg-center bg-left-top  bg-no-repeat sm:mt-[0] md:mx-auto md:h-[315px] md:w-full'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='mx-auto my-auto flex flex-col'>
          <Text className='font-rufina-stencil text-center text-[48px] font-normal leading-[56px] text-[#FFF]'>Don't miss the opportunity.</Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='mx-auto my-auto mt-[32px] flex flex-col sm:flex-row'>
              <Button
                className='rubik mr-[24px] flex h-[56px] w-full items-center justify-center gap-2 rounded-md  !bg-[#fff] p-4 text-[17px] sm:w-[271px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                variant='outline'
                className='rubik mt-[10px] flex h-[56px] w-full items-center justify-center gap-2 rounded-md border-[0px] !bg-[#0B80E7] p-4 text-[17px]  !text-[#ffff] sm:mt-0 sm:w-[166px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'client' });
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <View className=' mx-auto mt-[50px] flex max-w-[1100px]  flex-col text-center xl:mt-[108px]'>
        <View className='mt-[22px] flex w-full flex-col sm:gap-[89px] lg:flex-row'>
          <View className='smlml-[0px] flex flex-col gap-[12px] px-[12px]  sm:w-[auto] xl:px-0'>
            <Text className='rubik text-left text-[16px] font-medium not-italic leading-5 !text-[#388DD8] sm:ml-0 '>Our commitment to you</Text>
            <Text className='font-rufina-stencil text-left text-[48px] leading-[56px]  text-[#323C58] sm:ml-0  '>We're in your corner.</Text>
            <Text className='rubik text-left text-[16px] leading-[24px]  text-[#383838]  sm:ml-0 '>
              It's time to change the way security operatives are hired, and we're here to support that goal in every possible way. Our work is
              founded on five core values, collectively referred to as Our Promise.
            </Text>
            <View className='align-start   flex flex-col gap-[8px] py-1 sm:ml-0 '>
              <View className='flex flex-row items-center gap-[8px] lg:px-[4px] '>
                <Image src={check} className='flex h-[20px] w-[20px] flex-shrink-0 items-center justify-center p-[1.5px] ' />
                <Text className='rubik my-[6px] text-left text-[16px] font-medium leading-[24px] text-[#383838]'>We're always transparent</Text>
              </View>

              <View className='flex flex-row items-center gap-[8px] lg:px-[4px]'>
                <Image src={check} className='flex h-[20px] w-[20px] flex-shrink-0 items-center justify-center p-[1.5px] ' />
                <Text className='rubik my-[6px] text-left text-[16px] font-medium leading-[24px] text-[#383838]'>We give great customer service</Text>
              </View>

              <View className='flex flex-row items-center gap-[8px] lg:px-[4px]'>
                <Image src={check} className='flex h-[20px] w-[20px] flex-shrink-0 items-center justify-center p-[1.5px] ' />
                <Text className='rubik my-[6px] text-left text-[16px] font-medium leading-[24px] text-[#383838]'>We promote excellence</Text>
              </View>

              <View className='flex flex-row items-center gap-[8px] lg:px-[4px]'>
                <Image src={check} className='flex h-[20px] w-[20px] flex-shrink-0 items-center justify-center p-[1.5px] ' />
                <Text className='rubik my-[6px] text-left text-[16px] font-medium leading-[24px] text-[#383838]'>We use fair processes</Text>
              </View>

              <View className='flex flex-row items-center gap-[8px] lg:px-[4px]'>
                <Image src={check} className='flex h-[20px] w-[20px] flex-shrink-0 items-center justify-center p-[1.5px] ' />
                <Text className='rubik my-[6px] text-left text-[16px] font-medium leading-[24px] text-[#383838]'>
                  We drive positive change in the security sector
                </Text>
              </View>
            </View>
            <a
              href='/about-us'
              target='_blank'
              rel='noopener noreferrer'
              className='btn-no-hover flex h-[56px] w-[147px] items-center gap-2 py-4'
              onClick={(event) => {
                event.preventDefault();
                navigate('/about-us');
              }}
            >
              <Text className='rubik text-[17px] font-medium leading-6 !text-[#0B80E7]'>Find out more</Text>
              <span className='material-icons mt-[2px] flex h-[24px] w-[24px] items-center text-[18px] !text-[#0B80E7]'>arrow_forward</span>
            </a>
          </View>
          <Image src={homepage18} className=' mt-[30px] sm:mr-0 sm:mt-[50px]  lg:h-[400px]   ' />
        </View>
      </View>

      <View className='mx-auto sm:mt-[106px] xl:w-[1320px]'>
        <div
          className='mx-0 mt-[62px] flex h-[350px] w-full justify-between bg-cover bg-center bg-left-top bg-no-repeat sm:mt-[0px] sm:h-[500px] md:mx-auto xl:h-[713px] xl:w-[1320px]'
          style={{
            backgroundImage: `url(${homepage19})`,

            // backgroundImage: `url(${isSmallScreen ? homepage190 : homepage19})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          <View className='flex flex-col items-start px-[12px] lg:ml-[64px]  lg:px-0 xl:w-[534px]'>
            <Text className='rubik mt-[15px] text-left text-[24px] !text-[#7CDBEF] sm:mt-[64px]'>Innovation</Text>
            <Text className='font-rufina-stencil mt-[60px] text-left text-[22px] leading-[26px] text-[#7CDBEF] sm:text-[48px] sm:leading-[56px] xl:mt-[300px] '>
              Using technology for your benefit.
            </Text>
            <Text className='rubik mt-[12px] text-left text-[16px] font-normal leading-[24px] text-[#F4F5F7]'>
              We're committed to creating a marketplace that is easier, faster and better. That's why we're committed to investing in the latest
              technologies, to make sure Surely works as well as you do.
            </Text>
          </View>
        </div>

        <View className='mx-auto mt-[50px] flex w-full max-w-[1320px] flex-col justify-between rounded-[12px] bg-[#fff] shadow-md sm:flex-row xl:mt-[128px]'>
          <View className='flex flex-col px-[12px] lg:ml-[83px] lg:mt-[72px] lg:w-[486px] lg:px-0'>
            <Text className='rufina-stencil text-left text-[48px] font-normal leading-[56px]  lg:w-[440px]'>
              {' '}
              Refer a friend, get a free month for both!
            </Text>
            <a
              href='/refer-a-friend'
              target='_blank'
              rel='noopener noreferrer'
              className='btn-no-hover mt-[20px] flex h-[48px] w-[147px] items-center'
              onClick={(event) => {
                event.preventDefault();
                navigate('/refer-a-friend');
              }}
            >
              <Text className='rubik text-[17px] font-medium leading-5 !text-[#0B80E7]'>Refer a friend</Text>
              <span className='material-icons ml-[5px] mt-[2px] text-[18px] !text-[#0B80E7]'>arrow_forward</span>
            </a>
          </View>
          <img src={referfrend} className='lg:mr-[96px] lg:mt-[36.4px]' />
        </View>

        {!isAuthenticated && (
          <div
            className='mt-[110px] flex h-[480px] w-[1320px] flex-shrink-0 flex-col items-start gap-10 rounded-2xl'
            style={{
              backgroundImage: getBackgroundImage(),
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <View className='top-[43.167px] ml-[20px] w-[300px] sm:ml-[64px] sm:w-[486px]'>
              <Text className='rubik  absolute text-[24px] font-normal leading-[32px] text-[#7CDBEF] '>Security Operatives</Text>
              <Text className='xl:text-display-3 font-rufina-stencil mt-[75px] text-[48px] font-normal leading-[56px] text-[#ffff]'>
                Ready to find your next opportunity?
              </Text>
              <Button
                className='mt-[31px] flex h-[56px] w-[166px] items-center justify-center gap-2 rounded-md !bg-[#323C58] p-4 shadow-md'
                endIcon={() => <span className='material-icons-outlined mt-[2px] text-[17px] !text-[#ffff]'>arrow_forward</span>}
                onClick={() => {
                  openModal('REGISTER');
                }}
              >
                <Text className='rubik text-[17px] font-normal leading-[24px] !text-[#ffff] '>Get started</Text>
              </Button>
            </View>
          </div>
        )}
        {isAuthenticated && user?.account_type == '1' && (
          <div
            className='mt-[110px] flex h-[480px] w-[1320px] flex-shrink-0 flex-col items-start gap-10 rounded-2xl'
            style={{
              backgroundImage: getBackgroundImage(),
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <View className='top-[43.167px] ml-[20px] w-[300px] sm:ml-[64px] sm:w-[486px]'>
              <Text className='rubik  absolute text-[24px] font-normal leading-[32px] text-[#7CDBEF] '>Security Operatives</Text>
              <Text className='xl:text-display-3 font-rufina-stencil mt-[75px] text-[48px] font-normal leading-[56px] text-[#ffff]'>
                Ready to find your next opportunity?
              </Text>
              <Button
                className='mt-[31px] flex h-[56px] w-[166px] items-center justify-center gap-2 rounded-md !bg-[#323C58] p-4 shadow-md'
                endIcon={() => <span className='material-icons-outlined mt-[2px] text-[17px] !text-[#ffff]'>arrow_forward</span>}
                onClick={() => navigate('/search-jobs')}
              >
                <Text className='rubik text-[17px] font-normal leading-[24px] !text-[#ffff] '>Get started</Text>
              </Button>
            </View>
          </div>
        )}

        {isAuthenticated && user?.account_type == '2' && (
          <div
            className='mt-[110px] flex h-[480px] w-[1320px] flex-shrink-0 flex-col items-start gap-10 rounded-2xl'
            style={{
              backgroundImage: getBackgroundImage(),
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <View className='top-[43.167px] ml-[20px] w-[300px] sm:ml-[64px] sm:w-[486px]'>
              <Text className='rubik  absolute text-[24px] font-normal leading-[32px] text-[#7CDBEF] '>Clients</Text>
              <Text className='xl:text-display-3 font-rufina-stencil mt-[75px] text-[48px] font-normal leading-[56px] text-[#ffff]'>
                Ready to find your next Security Operative?
              </Text>
              <Button
                className='mt-[31px] flex h-[56px] w-[166px] items-center justify-center gap-2 rounded-md !bg-[#323C58] p-4 shadow-md'
                endIcon={() => <span className='material-icons-outlined mt-[2px] text-[17px] !text-[#ffff]'>arrow_forward</span>}
                onClick={() => navigate('/search-operator')}
              >
                <Text className='rubik text-[17px] font-normal leading-[24px] !text-[#ffff] '>Get started</Text>
              </Button>
            </View>
          </div>
        )}
      </View>
      <Text className='font-rufina-stencil ml-[3%] mt-[83px] items-center text-center text-[48px] font-normal leading-[40px] !text-[#323C58] sm:ml-[0px]'>
        Frequently asked questions
      </Text>

      <View className='mx-auto mt-[15px]  flex w-full max-w-[1320px] flex-col items-center gap-8 text-center'>
        <View className='mt-[20px]'>
          <Tabs value={activeTabQuestions} onChange={handleTabChangeQuestions} variant='borderless'>
            <Tabs.List>
              <Tabs.Item value='0'>
                <div className='flex items-center space-x-2'>
                  <span className='material-icons-outlined text-[20px]'>person</span>
                  <span className='rubik text-[15px] font-normal leading-[20px] text-[#14171F]'>Security Operatives</span>
                </div>
              </Tabs.Item>
              <Tabs.Item value='1'>
                <div className='flex items-center space-x-2'>
                  <span className='material-icons-outlined text-[20px]'>security</span>
                  <span className='rubik text-[15px] font-normal leading-[20px] text-[#14171F]'>Clients</span>
                </div>
              </Tabs.Item>
            </Tabs.List>
          </Tabs>
        </View>
        {activeTabQuestions === '0' && (
          <div className='w-full px-[12px] xl:w-auto xl:px-0 '>
            <div className='flex w-full flex-col gap-[20px] sm:w-auto sm:flex-row'>
              <div className='mt-[15px] flex w-full flex-col gap-4 '>
                {operatorQuestions.slice(0, showAllOperators ? undefined : 20).map((item: OperatorQuestionsType) => {
                  if (item.id % 2 === 1) {
                    return (
                      <Accordion key={item?.question} defaultActive={item.id === activeId}>
                        <Accordion.Trigger>
                          {(attributes, { active }) => (
                            <Button
                              attributes={attributes}
                              highlighted={active}
                              variant='outline'
                              className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                              endIcon={() => (
                                <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                    fill='#323C58'
                                  />
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                    fill='#323C58'
                                  />
                                </svg>
                              )}
                            >
                              <div className='flex flex-row '>
                                <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>
                                  {item.question}
                                </p>
                              </div>
                            </Button>
                          )}
                        </Accordion.Trigger>
                        <Accordion.Content>
                          <div className=' flex flex-row xl:w-[600px] '>
                            <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]  '>{item.answer}</p>
                          </div>
                        </Accordion.Content>
                      </Accordion>
                    );
                  }
                  return null;
                })}
              </div>
              <div className='mt-[15px] flex w-full flex-col gap-4  '>
                {operatorQuestions.slice(0, showAllOperators ? undefined : 20).map((item: OperatorQuestionsType) => {
                  if (item.id % 2 === 0) {
                    return (
                      <Accordion key={item.answer} defaultActive={item.id === activeId}>
                        <Accordion.Trigger>
                          {(attributes, { active }) => (
                            <Button
                              attributes={attributes}
                              highlighted={active}
                              variant='outline'
                              className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                              endIcon={() => (
                                <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                    fill='#323C58'
                                  />
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                    fill='#323C58'
                                  />
                                </svg>
                              )}
                            >
                              <div className='flex flex-row '>
                                <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>
                                  {item.question}
                                </p>
                              </div>
                            </Button>
                          )}
                        </Accordion.Trigger>
                        <Accordion.Content>
                          <div className=' xl:w-[600px] '>
                            <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]'>{item.answer}</p>
                          </div>
                        </Accordion.Content>
                      </Accordion>
                    );
                  }

                  return null;
                })}
              </div>
            </div>

            <div className='mb-[50px] mt-[32px] flex  justify-center lg:mb-[116px]'>
              <Button className='border-5 h-[40px] w-[140px] border-[#000] !bg-[#fff]' onClick={toggleShowAllOperators}>
                {showAllOperators ? 'Load Less' : 'Load More'}
              </Button>
            </div>
          </div>
        )}
        {activeTabQuestions === '1' && (
          <div className='w-full px-[12px] xl:w-auto xl:px-0 '>
            <div className='flex w-full flex-col gap-[20px] sm:w-auto sm:flex-row '>
              <div className='mt-[15px] flex w-full flex-col gap-4'>
                {clientQuestions.slice(0, showAllClients ? undefined : 20).map((item: ClientQuestionsType) => {
                  if (item.id % 2 === 1) {
                    return (
                      <Accordion key={item.question} defaultActive={item.id === activeId}>
                        <Accordion.Trigger>
                          {(attributes, { active }) => (
                            <Button
                              attributes={attributes}
                              highlighted={active}
                              variant='outline'
                              className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                              endIcon={() => (
                                <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                    fill='#323C58'
                                  />
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                    fill='#323C58'
                                  />
                                </svg>
                              )}
                            >
                              <div className='flex flex-row '>
                                <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>
                                  {item.question}
                                </p>
                              </div>
                            </Button>
                          )}
                        </Accordion.Trigger>
                        <Accordion.Content>
                          <div className=' flex flex-row xl:w-[600px] '>
                            <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]  '>{item.answer}</p>
                          </div>
                        </Accordion.Content>
                      </Accordion>
                    );
                  }

                  return null;
                })}
              </div>
              <div className='mt-[15px] flex w-full flex-col gap-4'>
                {clientQuestions.slice(0, showAllClients ? undefined : 20).map((item: ClientQuestionsType) => {
                  if (item.id % 2 === 0) {
                    return (
                      <Accordion key={item.answer} defaultActive={item.id === activeId}>
                        <Accordion.Trigger>
                          {(attributes, { active }) => (
                            <Button
                              attributes={attributes}
                              highlighted={active}
                              variant='outline'
                              className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                              endIcon={() => (
                                <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                    fill='#323C58'
                                  />
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                    fill='#323C58'
                                  />
                                </svg>
                              )}
                            >
                              <div className='flex flex-row '>
                                <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>
                                  {item.question}
                                </p>
                              </div>
                            </Button>
                          )}
                        </Accordion.Trigger>
                        <Accordion.Content>
                          <div className=' xl:w-[600px] '>
                            <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]'>{item.answer}</p>
                          </div>
                        </Accordion.Content>
                      </Accordion>
                    );
                  }

                  return null;
                })}
              </div>
            </div>

            <div className='mb-[50px] mt-[32px] flex  justify-center lg:mb-[116px]'>
              <Button className='border-5 h-[40px] w-[140px] border-[#000] !bg-[#fff]' onClick={toggleShowAllClients}>
                {showAllClients ? 'Load Less' : 'Load More'}
              </Button>
            </div>
          </div>
        )}
      </View>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default LandingPage;
