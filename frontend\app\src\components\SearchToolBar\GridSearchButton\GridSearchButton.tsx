import { Button, Text, View } from 'reshaped';


const GridSearchButton = () => {
  return (
    <Button variant='outline' rounded={true}>
      <View direction={'row'} justify={'center'} align={'center'} gap={1}>
        <span className='material-icons-outlined'>grid_on</span>

        <Text className='w-99 h-23 rubik font-medium text-base leading-6'>
          Grid Search
        </Text>
      </View>
    </Button>
  );
};

export default GridSearchButton;
