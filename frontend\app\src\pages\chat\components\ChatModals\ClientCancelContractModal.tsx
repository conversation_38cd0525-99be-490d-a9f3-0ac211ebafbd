// @ts-nocheck
import React, { useState } from 'react';
import { Text, View, But<PERSON>, Modal } from 'reshaped';

interface ClientCancelContractModalProps {
  active: boolean;
  deactivate: () => void;
  contactId: string;
  confirm: () => void;
}

const ClientCancelContractModal: React.FC<ClientCancelContractModalProps> = ({ active, deactivate, contactId, confirm }) => {
  const [cancelReason, setCancelReason] = useState();

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px]'>
      <View className='flex flex-col'>
        <svg xmlns='http://www.w3.org/2000/svg' width='47' height='47' viewBox='0 0 47 47' fill='none'>
          <path
            d='M23.5195 10.2098L39.2641 41.7077H7.77498L23.5195 10.2098ZM23.5195 0.549805L0.519531 46.5498H46.5195L23.5195 0.549805ZM25.6104 34.4445H21.4286V39.2866H25.6104V34.4445ZM25.6104 19.9182H21.4286V29.6024H25.6104V19.9182Z'
            fill='#CB101D'
          />
        </svg>
        <Text className='rubik mt-3 text-[20px] font-normal text-[#1A1A1A]'>
          Do you want to cancel &nbsp;
          <span className='rubik text-[20px] font-normal text-[#323C58] '>#{contactId}</span>
          &nbsp;contract?
        </Text>
        <Text className='rubik mt-3 text-[15px] font-normal leading-5 text-[#323C58]'>Please provide a reason for cancelling.</Text>
        <textarea
          name='cancel-reason'
          placeholder='Reason..'
          className='rubik mt-3 rounded-lg border border-[#BBC1D3] p-4'
          onChange={(e) => setCancelReason(e.target.value)}
          maxLength={500}
          rows={9}
          style={{
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
            resize: 'none',
          }}
        />
        <Text className='rubik mt-3 text-[15px] font-normal leading-5 text-[#323C58]'>Are you sure you want to cancel this contract?</Text>
        <View className='mt-[20px] flex flex-row justify-between'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='border-neutral bg-background-base mr-[10px] flex h-[48px] w-[173px] items-center justify-center gap-2 rounded-[8px] border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#323C58]'>No, go back</Text>
          </Button>
          <Button
            onClick={() => {
              confirm(cancelReason);
              deactivate();
            }}
            className='border-neutral bg-background-base flex h-[48px] w-[173px] items-center justify-center gap-2 rounded-[8px] border !bg-[#CB101D] px-4 py-2 !text-white'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Cancel contract</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ClientCancelContractModal;
