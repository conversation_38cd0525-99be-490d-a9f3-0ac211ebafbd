<?php

namespace App\Http\Controllers\Api;

use App\DataTables\ReportsDataTable;
use App\Http\Controllers\Controller;
use App\Models\Contract;
use App\Models\MobileUser;
use App\Models\Report;
use App\Notifications\AccountBannedStrikeNotification;
use App\Traits\Helpers;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    use Helpers;
    public function store(Request $request)
    {
        $data = $request->all();
        $data['author_id'] = auth()->id();

        if (!$request->has('user_id')) {
            return response()->json([
                'error' => true,
                'message' => 'User id is not provided in request!',
            ]);
        }

        $user = MobileUser::find($data['user_id']);
        if (!$user) {
            return response()->json([
                'error' => true,
                'message' => 'User not found with this id!',
            ]);
        }

        $data['reported_id'] = $data['user_id'];

        $contract = Contract::where(function ($q) use ($data) {
            if (auth()->user()->account_type == MobileUser::business) {
                $q->where('client_id', auth()->id())
                    ->where('operative_id', $data['user_id']);
            } elseif (auth()->user()->account_type == MobileUser::freelancer) {
                $q->where('operative_id', auth()->id())
                    ->where('client_id', $data['user_id']);
            }
        })->first();

        if (!$contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found with this operative and client!',
            ]);
        }

        $report = Report::where('reported_id', $data['reported_id'])
            ->where('author_id', $data['author_id'])
            ->first();

        if ($report) {
            return response()->json([
                'error' => true,
                'message' => 'You have submitted a report once for this user!',
            ]);
        }

        if ($request->has('files')) {
            foreach ($request->get('files') as $file) {
                $tmp = $this->base64Upload('reports', $file);
                if ($tmp) {
                    $files[] = $tmp;
                }
            }

            if (isset($files)) {
                $data['files'] = json_encode($files);
            } else {
                $data['files'] = null;
            }
        }

        if (!Report::create($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot submit report!',
            ]);
        }

        $reportsCount = Report::query()
            ->where('reported_id', $user->id)
            ->count();

        
        if ($reportsCount >= 3) {
            $user->banned = true;
            $banned_timestamp = [
                'start_date' => Carbon::now(),
                'end_date' => Carbon::now()->addDays(30),
            ];
            $user->banned_timestamp = json_encode($banned_timestamp);
            $user->save();
            $user->notify(new AccountBannedStrikeNotification($user->name));
        }

        return response()->json([
            'error' => false,
            'message' => 'Report submitted successfully!',
        ]);
    }

    public function table(ReportsDataTable $dataTable)
    {
        return $dataTable->render('reports.index');
    }
}
