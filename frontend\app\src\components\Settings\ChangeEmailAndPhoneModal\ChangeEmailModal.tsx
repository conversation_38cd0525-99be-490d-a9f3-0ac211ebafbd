// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Text, View, Button, Divider, Modal, Select, useToast, Image } from 'reshaped';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { requestEmailChange } from 'src/services/settings';

interface ChangeEmailModalProps {
  active: boolean;
  deactivate: () => void;
  email: any;
}

const ChangeEmailModal: React.FC<ChangeEmailModalProps> = ({ active, deactivate, email }) => {
  const toast = useToast();
  const [newEmail, setNewEmail] = useState<string>();

  const submitEmailChangeRequest = async () => {
    const request: any = {
      newEmail,
    };
    await requestEmailChange(request).then((res) => {
      toast.show({
        title: '',
        text: 'Request for email change is sent successfuly!',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    });
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Change Email</Text>
        </View>
        <View className='mt-5 flex flex-col gap-1'>
          <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Current email</Text>
          <input
            name='text'
            placeholder={email}
            value={email}
            className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-full'
          />
        </View>

        <View className='mt-4 flex flex-col gap-1'>
          <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>New Email</Text>
          <input
            name='text'
            placeholder={newEmail}
            value={newEmail}
            onChange={(e) => setNewEmail(e.target.value)}
            className='rubik w-auto rounded border border-[#BBC1D3] !bg-[#ffff] px-3 py-[14px] text-[14px] !text-[#3C455D] sm:w-full'
          />
        </View>

        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutralrounded-[8px] mr-[10px] flex h-[48px] w-[180px] items-center justify-center gap-2 border border-[#DFE2EA] !bg-transparent px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            className='flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] !bg-[#0B80E7] px-4 py-2'
            onClick={() => {
              submitEmailChangeRequest();
              deactivate();
            }}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ChangeEmailModal;
