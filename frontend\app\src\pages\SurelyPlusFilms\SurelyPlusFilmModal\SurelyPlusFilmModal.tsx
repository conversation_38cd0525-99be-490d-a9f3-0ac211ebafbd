// @ts-nocheck
import React from 'react';
import { Modal, View, Button, Text } from 'reshaped';

interface SurelyPlusFilmModalProps {
  active: boolean;
  deactivate: () => void;
  videoLink: string | null;
  description: string | null;
}

const extractVideoId = (url: string): string | null => {
  const match = url.match(
    /(?:youtu\.be\/|youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/|y\/|\/v\/|\/e\/|watch\?.*v=)([^"&?\/\s]{11})/,
  );
  return match ? match[1] : null;
};

const SurelyPlusFilmModal: React.FC<SurelyPlusFilmModalProps> = ({
  active,
  deactivate,
  videoLink,
  description,
}) => {
  if (videoLink === null || videoLink === undefined) {
    return null;
  }

  const embedUrl = `https://www.youtube.com/embed/${extractVideoId(videoLink)}`;

  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='w-auto h-auto sm:w-auto sm:h-auto'
    >
      <View className='mx-auto'>
        <View className='flex items-center p-0 mt-[-8px]'>
          <button
            onClick={deactivate}
            className='flex items-center justify-end ml-auto btn-no-hover'
          >
            <span className='material-icons align-middle text-500'>close</span>
          </button>
        </View>
        <View className='mt-[10px] mx-auto sm:h-[315px]'>
          {embedUrl && (
            <iframe
              width='100%'
              height='100%'
              src={embedUrl}
              title='YouTube video player'
              frameBorder='0'
              allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
              allowFullScreen
            ></iframe>
          )}
        </View>
        <View className='mt-[15px] mx-auto lg:w-[550px] '>
          <Text className='rubik text-[14px] leading-[20px] font-normal text-[#383838] mt-[12px] text-left'>
            {description}
          </Text>
        </View>
      </View>
    </Modal>
  );
};

export default SurelyPlusFilmModal;
