import React from 'react';
import { Card, Text, Button, View } from 'reshaped';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';

interface ClientIndustrySelectorsProps {
  oper: any; 
}

const ClientIndustrySectors:  React.FC<ClientIndustrySelectorsProps> = ({ oper }) => {
  const { industry_sectors } = oper;
  return (
    <Card className='xl:w-[424px] h-[auto] lg:mx-auto p-6'>
      <View className='flex flex-col gap-5'>
        <View className='flex items-center justify-between'>
          <Text className='text-center text-[#1A1A1A] rubik text-[16px] xl:font-medium xl:leading-5'>
            Industry sectors
          </Text>
        </View>

        <View className='flex gap-2'>
          {industry_sectors?.length === 0 ? (
            <NoDataClientProfile />
          ) : (
            <div className=' flex flex-wrap gap-2'>
              {industry_sectors?.map((industrySector: any, index: any) => (
                <Button
                  key={index}
                  size='small'
                  rounded={true}
                  elevated={false}
                  className='px-[8px] py-[4px] border !bg-[#323C58] max-w-xs overflow-hidden truncate '
                >
                  <Text className='text-[#FFFFFF] rubik font-normal text-[12px]  leading-[20px]'>
                    {industrySector}
                  </Text>
                </Button>
              ))}
            </div>
          )}
        </View>
      </View>
    </Card>
  );
};

export default ClientIndustrySectors;
