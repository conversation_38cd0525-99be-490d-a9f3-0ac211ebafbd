const SurelyIcon = () => (
<svg xmlns="http://www.w3.org/2000/svg" width="94" height="98" viewBox="0 0 94 98" fill="none">
  <g filter="url(#filter0_d_3382_19047)">
    <path d="M32.1909 55.9335L41.2005 87.3303C42.071 90.8123 44.9726 91.9874 47.8887 91.9874H51.661V50.7111C40.9104 46.6488 21.7159 41.4111 9.80461 41.4111C6.61279 41.4111 4.85742 44.0231 4.85742 46.6491C4.85742 47.5196 5.14745 48.9704 5.43761 49.5508C15.6079 49.5508 27.8239 53.033 32.1764 55.9491L32.1909 55.9335Z" fill="#DFDDD5"/>
    <path d="M62.397 38.321L53.3874 6.92564C52.5169 3.44367 49.6153 2.26855 46.6991 2.26855H42.927V43.5434C53.6776 47.6057 72.872 52.8434 84.7833 52.8434C87.9751 52.8434 89.7306 50.2328 89.7306 47.6068C89.7306 46.7363 89.4404 45.2855 89.1502 44.7052C78.98 44.7052 66.7639 41.223 62.4115 38.3068L62.397 38.321Z" fill="#71D3EA"/>
  </g>
  <defs>
    <filter id="filter0_d_3382_19047" x="0.857422" y="0.268555" width="92.8733" height="97.7188" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
      <feFlood floodOpacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3382_19047"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3382_19047" result="shape"/>
    </filter>
  </defs>
</svg>
);

export default SurelyIcon;
