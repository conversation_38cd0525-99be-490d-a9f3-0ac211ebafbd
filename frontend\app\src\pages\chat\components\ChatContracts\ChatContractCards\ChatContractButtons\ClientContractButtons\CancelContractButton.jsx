import { useToggle } from 'reshaped';
import ClientCancelContractModal from "src/pages/chat/components/ChatModals/ClientCancelContractModal";

const ClientCancelContractButton = ({ contractId, handleCancelContract={handleCancelContract} }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <div className='w-full rubik height-[156px] flex flex-col gap-3 text-[16px] font-medium'>
      <div
        onClick={activate}
        className='flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg border border-[#DFE2EA] py-[10.5px] text-[#CB101D]'
      >
        <span className='material-icons'>close</span>Cancel contract
      </div>
      <ClientCancelContractModal
        active={active}
        contactId={contractId}
        deactivate={deactivate}
        confirm={handleCancelContract}
      />
    </div>
  );
};

export default ClientCancelContractButton