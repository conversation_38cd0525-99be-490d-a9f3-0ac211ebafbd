import { useAuthContext } from "src/context/AuthContext"
import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';


const JobApplicationDeclined = ({ message }) => {
  const { user, isClient } = useAuthContext()
  const nameShown = message?.receiver_id === user?.profile?.id ? message?.sender?.name : message?.receiver?.name

  return (
    <div className='flex items-start lg:items-center gap-4 p-4 pr-[60px] border border-[#CE0E0E] rounded-lg bg-[#FBD0D4]'>
        <div className='w-8 h-8 lg:w-12 lg:h-12 shrink-0'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='w-full flex flex-col text-left gap-[3px]'>
          <h2 className='rubik text-[#1A1A1A] text-[16px] font-medium leading-5 overflow-ellipsis'>
            Job application declined
          </h2>
          <p className='rubik text-[14px] text-[#383838] leading-5 overflow-ellipsis'>
            {`${isClient ? `You declined the application from ` : 'Your invitation was declined by '}`} <span className="font-medium">{nameShown}</span>
          </p>
        </div>
      </div>
  )
}

export default JobApplicationDeclined
