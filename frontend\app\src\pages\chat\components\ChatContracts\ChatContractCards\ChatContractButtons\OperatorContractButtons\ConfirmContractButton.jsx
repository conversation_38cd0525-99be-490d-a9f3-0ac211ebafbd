import { useToggle } from 'reshaped';
import ConfirmContractModal from 'src/pages/chat/components/ChatModals/ConfirmContractModal';

const OperatorConfirmContractButton = ({ handleAcceptContract, contract }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div
        onClick={activate}
        className='rubik flex cursor-pointer items-center justify-center gap-2 rounded-lg border border-[#DFE2EA] p-3 text-[16px] font-medium !text-[#0B80E7]'
      >
        <p className='flex items-center gap-2'>
          <span className='material-icons-outlined'>check_circle</span>
          Confirm contract
        </p>
      </div>
      <ConfirmContractModal active={active} deactivate={deactivate} handleAcceptContract={handleAcceptContract} contract={contract} />
    </>
  );
};

export default OperatorConfirmContractButton;
