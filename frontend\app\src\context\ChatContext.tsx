// @ts-nocheck
import { FunctionComponent, ReactNode, createContext, useCallback, useContext, useEffect, useState } from 'react';
import { Image, Button, useToast } from 'reshaped';
import Contract from 'src/services/contracts';
import { ChatService, getMessages } from 'src/services/chat';
import { useAuthContext } from './AuthContext';
import { AppContext } from './AppContext';
import surleyicon from 'src/assets/icons/surleyicon/surleyicon.png';
import { useNavigate } from 'react-router-dom';
import Job from 'src/services/jobs';
import { JobContext } from './JobContext';
import moment from 'moment';

interface ChatProviderProps {
  children: ReactNode;
}

interface ChatContextType {
  chats?: [any];
  handleGetChats: () => void;
  handleGetContracts: () => void;
  currentMessages?: [{}];
  handleGetMessages: () => void;
  currentChat: {} | null;
  setCurrentChat: (chat: {}) => void;
  contract: any;
  setContract: (contract: any) => void;
  contracts: [];
  setContracts: (contracts: any) => void;
  loadContract: (id: any) => void;
  messages: any[];
  setMessages: React.Dispatch<React.SetStateAction<any[]>>;
  userData: any;
  loadingContracts: boolean;
  setLoadingContracts: React.Dispatch<React.SetStateAction<boolean>>;
}

const initialState: ChatContextType = {
  chats: [{}],
  handleGetChats: () => {},
  handleGetContracts: () => {},
  currentMessages: [{}],
  handleGetMessages: () => {},
  currentChat: null,
  setCurrentChat: () => {},
  contract: null,
  setContract: () => {},
  contracts: [],
  setContracts: () => {},
  loadContract: () => {},
  messages: [],
  setMessages: () => {},
  userData: null,
  loadingContracts: false,
  setLoadingContracts: () => {},
};

const ChatContext = createContext(initialState);

const ChatProvider: FunctionComponent<ChatProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user, isClient } = useAuthContext();
  const { notificationSettings, jobs } = useContext(AppContext);
  const { fetchAllJobs } = useContext(JobContext);
  const toast = useToast();

  const [chats, setChats] = useState(initialState.chats);
  const [currentChat, setCurrentChat] = useState(initialState.currentChat);
  const [currentMessages, setCurrentMessages] = useState(initialState.currentMessages);
  const [contract, setContract] = useState(initialState.contract);
  const [contracts, setContracts] = useState(initialState.contracts);
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);
  const [userData, setUserData] = useState(() => {
    const stored = localStorage.getItem('userData');
    return stored ? JSON.parse(stored) : null;
  });
  const [loadingContracts, setLoadingContracts] = useState(false);

  const getCurrentContract = () => contract;

  const getCurrentChat = (id: number) => {
    setCurrentChat(chats?.find((chat) => chat.id === id));
  };

  const handleGetChats = async () => {
    let chatList;
    await ChatService.getChats().then((res) => {
      setChats(res || []);
      chatList = res || [];
    });
    return chatList;
  };

  const handleGetMessages = async () => {
    if (!currentChat?.id) {
      console.warn('No chat ID available for fetching messages');
      return [];
    }
    
    try {
      const messages = await getMessages(currentChat.id);
      if (messages) {
        setMessages(messages);
      }
      return messages;
    } catch (error) {
      console.error('Error in handleGetMessages:', error);
      return [];
    }
  };

  const handleGetContracts = async () => {
    Contract.list().then((res) => {
      if (!res?.length) return;
      setContracts(res);
      return;
    });
  };

  const getChatContracts = async (chatId: any) => {
    Contract.get(chatId).then((res) => {
      if (!res?.length) return;
      return res;
    });
  };

  const loadContract = async (id: string | number) => {
    if (!id) return null;
    
    try {
      const response = await Contract.get(id);
      if (response) {
        setContract(response);
      }
      return response;
    } catch (error) {
      if (error?.response?.status !== 429) {
        console.error('Error loading contract:', error);
      }
      return null;
    }
  };

  const handleMessageUpdate = (newMessage: any) => {
    setMessages((prev) => [...prev, newMessage]);
  };

  const handleNewMessage = (message: any) => {
    const currentUserId = user?.profile?.id;
    const isRelevantMessage = message.receiver_id === currentUserId || message.sender_id === currentUserId;

    if (!isRelevantMessage) return;

    // Handle ALL contract-related messages consistently
    if (message.contract_id) {
      setTimeout(async () => {
        const updatedContract = await loadContract(message.contract_id);
        setContract(updatedContract);

        if (message.chat_id === currentChat?.id) {
          setMessages((prev) => {
            if (prev.some((m) => m.id === message.id)) {
              return prev;
            }
            return [...prev, message];
          });
        }
        
        // Ensure chat list is updated
        handleGetChats();
      }, 500);
    } else if (message.chat_id === currentChat?.id) {
      setMessages((prev) => {
        if (prev.some((m) => m.id === message.id)) {
          return prev;
        }
        return [...prev, message];
      });
    }

    // Update chats list
    setChats((prev) => {
      const updatedChats = prev.map((chat) => {
        if (chat.id === message.chat_id) {
          return {
            ...chat,
            last_message: message.message,
            updated_at: message.created_at,
          };
        }
        return chat;
      });

      return updatedChats.sort((a, b) => moment(b.updated_at).diff(moment(a.updated_at)));
    });
  };

  const handleReadMessage = (message: any) => {
    setMessages((prev) => prev.map((msg) => (msg.id === message.id ? { ...msg, read: true } : msg)));
  };

  useEffect(() => {
    if (!isAuthenticated || !window.Echo) return;

    window.Echo.channel('surely-development')
        .listen('NewMessageEvent', async (e) => {
            const isRelevantMessage = e.message.receiver_id === user?.profile?.id || 
                                    e.message.sender_id === user?.profile?.id;

            if (isRelevantMessage) {
                // Update messages if we're in the current chat
                if (e.message.chat_id === currentChat?.id) {
                    setMessages(prev => [...prev, e.message]);
                }
                
                // Always refresh the chat list for new messages
                await handleGetChats();
            }
        });

    return () => {
        window.Echo.leave('surely-development');
    };
}, [isAuthenticated, currentChat?.id, user?.profile?.id]);

  useEffect(() => {
    if (!isAuthenticated && !window.Echo) return;
    window.Echo.channel('surely-development')
      .notification((notification: any) => {})
      .listen('NewMessageEvent', (e: any) => {
        const currentUserId = user?.profile?.id;
        const chatId = e.message.chat_id;
        const receiverId = e.message.receiver_id;
        const senderId = e.message.sender_id;
        if (receiverId !== currentUserId && senderId !== currentUserId) {
          return;
        } else if (receiverId === currentUserId) {
          handleGetChats()?.then((res) => {
            if (currentChat?.id === e?.message?.chat_id) {
              return;
            }
            const chatOfMessage = res?.find((chat) => chat.id === e.message.chat_id);
            const senderName = chatOfMessage.sender_id === senderId ? chatOfMessage.sender_name : chatOfMessage.receiver_name;
            const messageText = e.message?.message;
            const jobContractId = e.message.job_id || `#${e.message.contract_id}`;

            const jobName = e.message.job_id && jobs.find((job) => job.id === e.message.job_id)?.post_name;
            if (e.message.type === 'text') {
              if (!!notificationSettings?.new_chat_message_alert) return;
              const toastId = toast.show({
                title: 'New message',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`New Message from: ${senderName}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View message
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'cancel') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: 'Contract cancelled',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`${jobContractId?.includes('#') ? `We regret to inform you that contract ${jobContractId} was cancelled by ${senderName}}` : `We regret to inform you that the job ${jobName} was cancelled by ${senderName}`}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'reject') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: 'Contract rejected',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`${jobContractId?.includes('#') ? `We regret to inform you that contract ${jobContractId} was refused by ${senderName}}` : `We regret to inform you that the job ${jobName} was refused by ${senderName}`}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'client_accepted_applicant') {
              const toastId = toast.show({
                title: 'Application accepted!',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>Your job application for {jobName} was accepted!</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          Go to chat
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'individual_contract') {
              const toastId = toast.show({
                title: 'New contract created',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Contract ${jobContractId} was created by ${senderName}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'accept-shifts') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: messageText,
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Shifts of ${jobContractId} were changed by ${senderName}`}</div>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'hourly_rate_changed') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: messageText,
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Hourly rates of ${jobContractId} were changed by ${senderName}`}</div>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'outstanding_payment') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: messageText,
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Outstanding payment of ${jobContractId} was paid by ${senderName}`}</div>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            // if (e.message.type === 'no_contract') {
            //   if (!!notificationSettings?.contract_status_alert) return;
            //   const toastId = toast.show({
            //     title: messageText,
            //     text: (
            //       <div className='h-max space-y-4 py-4'>
            //         <div className='flex gap-3'>
            //           <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
            //           <div className=''>
            //             <div className=''>{`Shifts of ${jobContractId} were changed by ${senderName}`}</div>
            //             <Button
            //               className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
            //               onClick={() => {
            //                 toast.hide(toastId);
            //                 navigate('/chat/' + chatId, { state: { activeTab: '2' } });
            //               }}
            //             >
            //               View contract
            //             </Button>
            //           </div>
            //         </div>
            //       </div>
            //     ),
            //   });
            // }
            if (e.message.type === 'confirm-shifts') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: 'Shifts were approved',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Shifts of ${jobContractId} were approved by ${senderName}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'shift_update_request') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: messageText,
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Shifts of ${jobContractId} were changed by ${senderName}`}</div>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'escrow_payment') {
              if (!!notificationSettings?.payments_alert) return;
              const toastId = toast.show({
                title: 'Escrow payment',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Escrow amount for contract ${jobContractId} was deposited by ${senderName}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            close(false);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View Message
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'complete-shifts') {
              if (!!notificationSettings?.contract_status_alert) return;
              const toastId = toast.show({
                title: 'Shifts reviewed',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`Shifts reviewed for contract #${jobContractId} by ${from}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
            if (e.message.type === 'invite_to_apply') {
              const toastId = toast.show({
                title: 'Job invitation',
                text: (
                  <div className='h-max space-y-4 py-4'>
                    <div className='flex gap-3'>
                      <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                      <div className=''>
                        <div className=''>{`You were invited to a job by ${from}`}</div>
                        <h2 className=''>{messageText}</h2>
                        <Button
                          className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                          onClick={() => {
                            toast.hide(toastId);
                            navigate('/chat/' + chatId, { state: { activeTab: '2' } });
                            window.location.reload();
                          }}
                        >
                          View contract
                        </Button>
                      </div>
                    </div>
                  </div>
                ),
              });
            }
          });
        } else if (senderId === currentUserId) return;
      })
      .listen('NewApplicationEvent', (e: any) => {
        const currentUserId = user?.profile?.id;
        const receiverId = e.application?.receiver_id;
        const senderId = e.application?.sender_id;
        if (receiverId !== currentUserId && senderId !== currentUserId) {
          return;
        }
        if (receiverId === currentUserId) {
          fetchAllJobs().then((res) => {});
          const senderName = e.application?.operator_name;
          const jobName = e.application?.post_name;
          const jobId = e.application?.job_id;
          const toastId = toast.show({
            title: 'New application',
            text: (
              <div className='h-max space-y-4 py-4'>
                <div className='flex gap-3'>
                  <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                  <div className=''>
                    <div className=''>{`${senderName} applied to ${jobName}`}</div>
                    <Button
                      className='border-neutral bg-background-base  mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                      onClick={() => {
                        toast.hide(toastId);
                        navigate('/manage-jobs/' + jobId, { state: { activeTab: '2' } });
                        window.location.reload();
                      }}
                    >
                      View application
                    </Button>
                  </div>
                </div>
              </div>
            ),
          });
        }
      })
      .listen('EmergencyJobEvent', (e: any) => {
        if (isClient === false && !!notificationSettings?.new_emergency_hire_job_alert) {
          const job = e.job;
          const toastId = toast.show({
            title: 'New emergency job',
            text: (
              <div className='space-y-4 py-4'>
                <div className='flex gap-3'>
                  <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
                  <div className=''>
                    <div className=''>{`New job opportunity: ${job.post_name}`}</div>
                    <Button
                      className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                      onClick={() => {
                        toast.hide(toastId);
                        navigate('/operator-job/' + job.id);
                        window.location.reload();
                      }}
                    >
                      View job
                    </Button>
                  </div>
                </div>
              </div>
            ),
          });
        }
      })
      .listen('ReadMessageEvent', (e: any) => {
        handleReadMessage(e.message);
      })
      .error((e) => {
        console.error(e);
      });
  }, [isAuthenticated]);

  useEffect(() => {
    if (!userData) {
      const stored = localStorage.getItem('userData');
      if (stored) {
        setUserData(JSON.parse(stored));
      }
    }
  }, []);

  const value = {
    chats,
    handleGetChats,
    currentMessages,
    setCurrentMessages,
    handleGetMessages,
    currentChat,
    setCurrentChat,
    getCurrentChat,
    contract,
    getCurrentContract,
    setContract,
    contracts,
    setContracts,
    handleGetContracts,
    getChatContracts,
    loadContract,
    messages,
    setMessages,
    userData,
    setUserData,
    loadingContracts,
    setLoadingContracts,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

const useChatContext = () => {
  const context = useContext(ChatContext);
  return context;
};

export { ChatContext, ChatProvider, useChatContext };
