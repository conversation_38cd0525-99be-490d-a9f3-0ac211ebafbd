import deploymentManager from './deploymentManager';

interface SessionConfig {
    tokenKey: string;
    sessionTimeout: number;
    warningThreshold: number;
  }
  
  class SessionManager {
    private config: SessionConfig = {
      tokenKey: 'token',
      sessionTimeout: 120 * 60 * 1000, // 120 minutes in milliseconds
      warningThreshold: 5 * 60 * 1000  // 5 minutes warning before expiry
    };
  
    private timeoutId: NodeJS.Timeout | null = null;
    private warningId: NodeJS.Timeout | null = null;
  
    constructor() {
      this.setupSessionListeners();
      // Initialize deployment manager
      deploymentManager;
    }
  
    private setupSessionListeners(): void {
      window.addEventListener('storage', (e) => {
        if (e.key === this.config.tokenKey && !e.newValue) {
          this.handleSessionExpired();
        }
      });
    }
  
    public startSessionTimer(): void {
      if (this.timeoutId) clearTimeout(this.timeoutId);
      if (this.warningId) clearTimeout(this.warningId);
  
      // Set warning timer
      this.warningId = setTimeout(() => {
        this.handleSessionWarning();
      }, this.config.sessionTimeout - this.config.warningThreshold);
  
      // Set expiry timer
      this.timeoutId = setTimeout(() => {
        this.handleSessionExpired();
      }, this.config.sessionTimeout);
    }
  
    private handleSessionWarning(): void {
      // Dispatch warning event
      window.dispatchEvent(new CustomEvent('sessionWarning', {
        detail: { 
          timeLeft: this.config.warningThreshold / 1000 
        }
      }));
    }
  
    private handleSessionExpired(): void {
      localStorage.removeItem(this.config.tokenKey);
      window.dispatchEvent(new CustomEvent('sessionExpired'));
    }
  
    public resetSession(): void {
      this.startSessionTimer();
    }
  }
  
  export default new SessionManager();
