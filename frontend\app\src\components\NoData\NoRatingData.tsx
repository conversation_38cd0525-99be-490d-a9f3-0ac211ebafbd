// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';
import SurelyIconNoData from './SurelyIconNoData';

const NoRatingData: React.FC = () => {
  return (
    <View className='mx-auto mb-[40px] mt-[40px] flex w-[234px] flex-col items-center justify-center'>
      <SurelyIconNoData />
      <Text className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>
        This user has yet to receive ratings.
      </Text>
    </View>
  );
};

export default NoRatingData;
