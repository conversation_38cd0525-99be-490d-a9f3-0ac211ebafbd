import React from 'react';
import { Text, View, <PERSON><PERSON>, Modal } from 'reshaped';
import { useNavigate } from 'react-router-dom';

interface ClosePostJobModalProps {
  active: boolean;
  deactivate: () => void;
}

const ClosePostJobModal: React.FC<ClosePostJobModalProps> = ({
  active,
  deactivate,
}) => {

  const navigate = useNavigate();

  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='!w-[474px] !h-[auto]'
    >
      <View className='flex flex-col'>
      <svg xmlns="http://www.w3.org/2000/svg" width="47" height="47" viewBox="0 0 47 47" fill="none">
  <path d="M23.5 10.4212L39.2445 41.9191H7.75545L23.5 10.4212ZM23.5 0.76123L0.5 46.7612H46.5L23.5 0.76123ZM25.5909 34.656H21.4091V39.4981H25.5909V34.656ZM25.5909 20.1297H21.4091V29.8139H25.5909V20.1297Z" fill="#CB101D"/>
</svg>
        <Text className='text-[#1A1A1A] rubik text-[20px] font-normal leading-[28px] mt-[16px] '>You have unsaved changes</Text>
        <Text className='text-[#323C58] rubik text-[14px] font-normal leading-[20px] mt-[8px] '>
        Are you sure you want to close the job creator?<br></br> Your progress will be lost. 
        </Text>

        <View className='flex flex-row justify-between mt-[20px]'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base w-[195px] h-[48px] mr-[10px]'
          >
           <Text className='text-[#323C58] rubik text-[16px] font-medium leading-[24px]'>No, go back</Text> 
          </Button>
          <Button
            onClick={() => navigate('/client-dashboard')}
            className='flex justify-center items-center  border border-neutral rounded-[8px] bg-background-base !bg-[#CB101D] w-[203px] h-[48px]'
          >
           <Text className='text-[#FFFFFF] rubik text-[16px] font-medium leading-[24px]'>Close job creator </Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ClosePostJobModal;
