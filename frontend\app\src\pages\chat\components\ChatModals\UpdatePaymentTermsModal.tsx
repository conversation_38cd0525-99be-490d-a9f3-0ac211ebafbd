// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Text, View, Button, Modal } from 'reshaped';
import { HttpClient } from 'src/client/http-client';
import { useAuthContext } from 'src/context/AuthContext';
import { useChatContext } from 'src/context/ChatContext';
import Contract from 'src/services/contracts';

interface UpdatePaymentTermsModalProps {
  active: boolean;
  deactivate: () => void;
  successAction?: () => void;
  successLabel?: string;
  title?: string;
  description?: string;
  operative: string;
  jobName: string;
  contract: any;
}

const UpdatePaymentTermsModal: React.FC<UpdatePaymentTermsModalProps> = ({
  active,
  deactivate,
  successAction,
  successLabel = 'Change',
  title = 'Delete Account',
  description = 'Are you sure you want to delete your account?',
}) => {
  const { isClient } = useAuthContext();
  const { contract, loadContract } = useChatContext();
  const [paymentTerms, setPaymentTerms] = useState<any>(contract?.payment_terms);

  const updateContractRate = async () => {
    const url = `contracts/${contract?.id}/shifts`;
    try {
      const response = await HttpClient.put(url, { payment_terms: paymentTerms, message: 'Payment terms changed', type: 'payment_terms_changed' });
      if (!response.error) {
        deactivate();
        loadContract(contract?.id);
        return response;
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    setPaymentTerms(contract?.payment_terms);
  }, [contract?.payment_terms]);

  const operative = contract?.operative?.name;
  const jobName = contract?.job?.post_name;

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View className='flex flex-col'>
        <View className=''>
          <span className='material-icons-outlined text-[46px] text-[#323C58]'>payments</span>
        </View>
        <Text className='rubik mt-[3px] font-[normal] text-[20px] leading-5 text-[#323C58]'>Payment terms:</Text>
        <Text className='rubik mt-2 text-[20px] font-medium leading-5 text-[#323C58]'>
          {operative} - {jobName}
        </Text>
        <View className=''>
          <input
            disabled={!isClient}
            defaultValue={paymentTerms}
            onChange={(v) => {
              setPaymentTerms(+v?.target?.value);
            }}
            className='mr-3 mt-[20px] h-[48px] w-[82px] rounded-[8px] border border-[#E5E5E5] px-[16px] text-[20px] text-[#323C58]'
          />
          <span className='rubik text-[20px] font-[500] text-[#323C58]'> days </span>
        </View>
        <View className='mt-[20px] flex flex-row justify-between'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='border-neutral mr-[10px] flex h-[48px] w-full items-center justify-center gap-2 rounded-[8px] border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>{`${isClient ? 'Cancel' : 'Back'}`}</Text>
          </Button>
          {isClient && (
            <Button
              onClick={updateContractRate}
              className='border-neutral flex h-[48px] w-full items-center justify-center gap-2 rounded-[8px] border  !bg-[#0B80E7] px-4 py-2'
            >
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>{successLabel}</Text>
            </Button>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default UpdatePaymentTermsModal;
