// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { View, Text, Button, Actionable, Tooltip, Accordion, Breadcrumbs, TextField } from 'reshaped';
import Footer from '../Footer/Footer';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import referfrend from '../../assets/images/referfrend/referfrend.svg';
import check from '../../assets/images/referfrend/check.svg';
import referFriendQuestions, { ReferFriendQuestionsType } from '../../store/dummydata/ReferFriendQuestionsDummyData/ReferFriendQuestionsDummyData';
import homebreadcrumbsicon from '../../assets/icons/homebreadcrumbsicon/homebreadcrumbsicon.svg';
import { getReferFriend } from 'src/services/user';

const ReferFriend: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const navigate = useNavigate();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [activeId, setActiveId] = useState<number | null>(null);
  const [copyText, setCopyText] = useState('https://surelysecurity.com/refer/john134522');
  const [isCopied, setIsCopied] = useState(false);
  const { openModal } = useModalAction();

  useEffect(() => {
    getReferFriend().then((data: any) => {
      setCopyText(data.data)
    });
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  const handleCopyClick = async () => {
    try {
      await navigator.clipboard.writeText(copyText);
      setIsCopied(true);
    } catch (error) {
      console.error('Error copying text:', error);
    }
  };
  const handleShareClick = async () => {
    try {
      await navigator.share({
        title: 'Surely Security Referral',
        text: 'Check out Surely Security and get a free for both with this referral link!',
        url: copyText,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  return (
    <View className='mt-[-90px] w-full overflow-x-hidden'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='mx-auto flex w-full max-w-[1320px] flex-col  '>
          <Breadcrumbs className='mt-[30px] sm:mt-[60px]'>
            <Breadcrumbs.Item onClick={() => navigate('/')}>
              <div className='flex flex-row gap-[4px]'>
                <img src={homebreadcrumbsicon} />
                <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Homepage</Text>
              </div>
            </Breadcrumbs.Item>
            <Breadcrumbs.Item onClick={() => {}}>
              <Text className='rubik text-[16px] font-medium text-[#1A1A1A]'>Refer a friend</Text>
            </Breadcrumbs.Item>
          </Breadcrumbs>
          <View className='mx-auto mt-[50px] flex w-full max-w-[1100px] flex-col justify-between rounded-[12px] bg-[#fff] shadow-md sm:mt-[71px] sm:flex-row'>
            <View className='flex flex-col lg:ml-[83px] lg:mt-[42px] lg:w-[486px]'>
              <Text className='rufina-stencil text-left text-[48px] font-normal leading-[56px] lg:w-[440px]'>
              Refer a friend, get a free month for both!
              </Text>
              <Text className='rubik mt-[10px] text-left text-[14px] font-medium leading-[20px] sm:mt-[26.5px]'>Share your link</Text>
              <View className='flex flex-row'>
                <TextField
                  name='email'
                  value={copyText}
                  onChange={(e) => setCopyText(e.value)}
                  className='flex h-[48px] flex-1 flex-shrink-0 items-center justify-between gap-[8px] border !border-[#BBC1D3] bg-[#FFF]  p-[14px] pl-[12px]'
                  endSlot={
                    <Button className='flex items-center justify-center gap-2 rounded-full !bg-[#323C58] px-[8px] py-[4px]' onClick={handleCopyClick}>
                      <Text className='text-[14px] font-semibold leading-[20px] text-[#FFFFFF]'>{isCopied ? 'Copied' : 'Copy'}</Text>
                    </Button>
                  }
                />
                <div
                  className='rounded-l-0 flex h-[48px] items-center justify-center gap-[8px] rounded-r-md !bg-[#0B80E7] p-[12px] pl-[16px]'
                  onClick={handleShareClick}
                >
                  <Text className='text-[16px] font-medium leading-[24px] text-[#fff]'>Share</Text>
                </div>
              </View>
            </View>
            <img src={referfrend} className='lg:mr-[96px] lg:mt-[36.4px]' />
          </View>
        </View>
        <View className='mx-auto flex w-full max-w-[1320px] flex-col items-center text-center '>
          <Text className='font-rufina-stencil ml-[3%] mt-[70px] items-center text-center text-[32px] font-normal leading-[40px] !text-[#323C58] sm:ml-[0px]'>
            Frequently asked questions
          </Text>
          <div className='mt-[44.3px] flex w-full flex-col gap-[20px] px-[12px] sm:w-auto sm:flex-row xl:px-0 mb-[50px]'>
            <div className='mt-[15px] flex w-full flex-col gap-4'>
            {referFriendQuestions.map((item: ReferFriendQuestionsType) => {
                if (item.id % 2 === 1) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                            endIcon={() => (
                              <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>{item.question}</p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' flex flex-row xl:w-[600px] '>
                          <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]  '>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
            <div className='mt-[15px] flex w-full flex-col gap-4'>
            {referFriendQuestions.map((item: ReferFriendQuestionsType) => {
                if (item.id % 2 === 0) {
                  return (
                    <Accordion key={item.id} defaultActive={item.id === activeId}>
                      <Accordion.Trigger>
                        {(attributes, { active }) => (
                          <Button
                            attributes={attributes}
                            highlighted={active}
                            variant='outline'
                            className='flex h-[104px] w-full justify-between rounded-lg border-[#DFE2EA] !bg-[#ffff] p-[24px] text-left xl:w-[648px]'
                            endIcon={() => (
                              <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'>
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                  fill='#323C58'
                                />
                                <path
                                  fillRule='evenodd'
                                  clipRule='evenodd'
                                  d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                  fill='#323C58'
                                />
                              </svg>
                            )}
                          >
                            <div className='flex flex-row '>
                              <p className='rubik text-left text-[14px] font-medium leading-[24px] text-[#323C58] sm:text-[17px]'>{item.question}</p>
                            </div>
                          </Button>
                        )}
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className=' xl:w-[600px] '>
                          <p className='rubik mt-[5px] p-[8px] text-left text-[#323C58]'>{item.answer}</p>
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  );
                }

                return null;
              })}
            </div>
          </div>
        </View>
      </View>
      <Footer />
    </View>
  );
};

export default ReferFriend;
