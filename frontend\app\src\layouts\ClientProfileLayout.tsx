// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Container, Breadcrumbs, Text } from 'reshaped';
import { useNavigate, useParams } from 'react-router-dom';

import Header from 'src/components/Header/Header';
import ClientIndustrySelectors from 'src/components/Profile/ClientProfileComponents/ClientIndustrySectors/ClientIndustrySectors';
import ClientLanguageCard from 'src/components/Profile/ClientProfileComponents/ClientLanguageCard/ClientLanguageCard';
import ClientProfileCard from 'src/components/Profile/ClientProfileComponents/ClientProfileCard/ClientProfileCard';
import ClientSurleyProBadges from 'src/components/Profile/ClientProfileComponents/ClientSurleyProBadges/ClientSurleyProBadges';
import dummyOperator from '../store/dummydata/OperatorsDummyData';
import { ClientType } from '../store/dummydata/OperatorsDummyData';
import homebreadcrumbsicon from '../assets/icons/homebreadcrumbsicon/homebreadcrumbsicon.svg';
import { OperativesContext } from 'src/context/OperativesContext';
import { getOperative } from 'src/services/operatives';
import { useAuthContext } from 'src/context/AuthContext';

interface Props {
  children?: React.ReactNode;
}

export const ClientProfileLayout = ({ children }: Props): JSX.Element => {
  const navigate = useNavigate();

  const [operative, setOperative] = useState();
  const { user } = useAuthContext()
  const { operatorId } = useParams();

  const operName = operative?.name;
  const clientId = user?.profile?.id

  const renderChildren = () => {
    return React.Children.map(children, (child) => {
      return React.cloneElement(child, {
        operative,
      });
    });
  };

  if (!operatorId) {
    return <div>Operator ID not provided</div>;
  }

  useEffect(() => {
    getOperative(+operatorId, clientId).then((res: any) => {
      const data = res.data;
      const user = {
        ...data.user,
        languages: [...data.languages],
        employments: [...data.employments],
        reviews: [data.reviews],
        qualifications: [...data.qualifications],
        sia_certificates: [...data.sia_certificates],
        surely_pro_badge: [...data.surely_pro_badge],
        jobs: data?.jobs,
        cv: data.cv,
      };
      setOperative(user);
    });
  }, [operatorId]);

  return (
    <Container padding={0} className="w-[100vw] min-h-[100vh] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />
      <main className='w-full max-w-[1320px] flex flex-col justify-between mx-auto md:flex-row  mt-[60px]  px-[12px] xl:px-0 gap-6 md:gap-3  '>
      <aside className='flex flex-col gap-6'>
          <Breadcrumbs className='mb-[20px]'>
            <Breadcrumbs.Item onClick={() => navigate('/client-dashboard')}>
              <div className='flex flex-row gap-[4px]'>
                <img src={homebreadcrumbsicon} />
                <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Dashboard</Text>
              </div>
            </Breadcrumbs.Item>
            <Breadcrumbs.Item onClick={() => navigate('/search-operator')}>
              <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Search page</Text>
            </Breadcrumbs.Item>
            <Breadcrumbs.Item onClick={() => {}}>
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>{operName}</Text>
            </Breadcrumbs.Item>
          </Breadcrumbs>
          {operative && (
            <>
              <ClientProfileCard oper={operative || {}} />
              <ClientIndustrySelectors oper={operative || {}} />
              <ClientSurleyProBadges oper={operative || {}} />
              <ClientLanguageCard oper={operative || {}} />
            </>
          )}
        </aside>
        <section className=''>{renderChildren()}</section>
      </main>
    </Container>
  );
};

export default ClientProfileLayout;
