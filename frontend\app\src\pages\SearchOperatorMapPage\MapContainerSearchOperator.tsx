// @ts-nocheck
import React, { useContext, useEffect, useState, useMemo } from 'react';
import { OperativesContext } from 'src/context/OperativesContext';
import { useToggle } from 'reshaped';
import { Map, GoogleApiWrapper, Marker, IProvidedProps } from 'google-maps-react';
import { getMapOperatives } from 'src/services/operatives';
import { JobContext } from 'src/context/JobContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import OperatorModalMapSearch from './OperatorModalMapSearch';
import markericon2 from '../../assets/icons/markericon/markericon2.png';
import { AppContext } from 'src/context/AppContext';

interface MapContainerSearchOperatorProps extends IProvidedProps {
  google: any;
}

const MapContainerSearchOperator: React.FC<MapContainerSearchOperatorProps> = ({ google }) => {
  const { latMap, lngMap } = useContext(AppContext);
  const { active: activeOperator, activate: activateOperator, deactivate: deactivateOperator } = useToggle(false);
  const [visibleOperatives, setVisibleOperatives] = useState([]);
  const [selectedOperative, setSelectedOperative] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { filters, search } = useContext(OperativesContext);

  // Create a memoized filter string
  const filterString = useMemo(() => {
    return (
      Object.entries(filters)
        .filter(([_, value]) => value && (typeof value !== 'object' || value.length > 0))
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&') + (search ? `&qs=${encodeURIComponent(search)}` : '')
    );
  }, [filters, search]);

  useEffect(() => {
    // Clear previous markers when filters change
    setVisibleOperatives([]);
    setIsLoading(true);

    // Fetch with filters - don't add pagination limits to show all operators
    getMapOperatives(filterString)
      .then((res) => {
        if (res && res.data) {
          console.log('Total operators received:', res.data.length);

          // Filter out operators without valid coordinates
          const operativesWithCoordinates = res.data.filter((op) => {
            const hasValidCoords = op.lat && op.lng && !isNaN(parseFloat(op.lat)) && !isNaN(parseFloat(op.lng));
            if (!hasValidCoords) {
              // console.log('Operator missing coordinates:', op.id, op.name);
            }
            return hasValidCoords;
          });

          // console.log('Operators with valid coordinates:', operativesWithCoordinates.length);
          setVisibleOperatives(operativesWithCoordinates);
        } else {
          // console.error('No data returned from getMapOperatives');
        }
      })
      .catch((error) => {
        // console.error('Error fetching operators for map:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [filterString]);

  return (
    <div style={{ height: '100vh', width: '100%', position: 'fixed', top: 75, left: 0, bottom: 0, right: 0 }}>
      <Map google={google} zoom={14} initialCenter={{ lat: latMap, lng: lngMap }}>
        {visibleOperatives?.map((operative: any, index: any) => (
          <Marker
            key={index}
            position={{ lat: parseFloat(operative.lat), lng: parseFloat(operative.lng) }}
            onClick={() => {
              setSelectedOperative(operative);
              activateOperator();
            }}
            icon={{
              url: markericon2,
              anchor: new google.maps.Point(16, 16),
              scaledSize: new google.maps.Size(32, 32),
            }}
          />
        ))}
      </Map>

      {isLoading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            }}
          >
            <div
              className='spinner'
              style={{
                width: '40px',
                height: '40px',
                border: '4px solid #f3f3f3',
                borderTop: '4px solid #3498db',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                marginBottom: '10px',
              }}
            ></div>
            <span>Loading operators...</span>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>

      <OperatorModalMapSearch active={activeOperator} deactivate={deactivateOperator} selectedOperator={selectedOperative} />
    </div>
  );
};

export default GoogleApiWrapper({
  apiKey: import.meta.env.VITE_REACT_APP_GMAP_API_KEY,
})(MapContainerSearchOperator);
