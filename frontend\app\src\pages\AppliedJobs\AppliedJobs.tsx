// @ts-nocheck
import React, { useState, useContext, useEffect } from 'react';
import { View} from 'reshaped';
import SurelyIcon from '../LandingPage/SurelyIcon';
import SearchJobCard from '../SearchPage/SearchJobCard';
import { getAppliedJobs } from 'src/services/jobs';
import { JobContext } from 'src/context/JobContext';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

export const NoJobFound = () => (
  <View className='mx-auto mb-[40px] mt-[40px] flex w-[234px] flex-col items-center justify-center'>
    <SurelyIconNoData />
    <div className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>No results</div>
  </View>
);

const AppliedJobs: React.FC = () => {
  const {
    fetchAllJobs,
    handleSelectedJob,
    addFavorite,
    removeFavorite,
    from,
    setAppliedJobsCount
  } = useContext(JobContext);

  const [allAppliedJobs, setAllAppliedJobs] = useState();

  useEffect(() => {
    getAppliedJobs().then((data: any) => {
      setAllAppliedJobs(data?.data);
      setAppliedJobsCount(data?.data?.length)
    });
  }, []);

  return (
    <View className='mb-10 flex min-h-screen w-full flex-col'>
      <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {allAppliedJobs?.length !== 0 ? (
          allAppliedJobs?.map((job: any, index: number) => {
            if (index === from - 4) {
              return (
                <div key={job.id + '_' + index} className='scroll-mt-[206px]'>
                  <SearchJobCard
                    job={job}
                    addFavorite={addFavorite}
                    removeFavorite={removeFavorite}
                    handleSelectedJob={handleSelectedJob}
                    fetchAllJobs={fetchAllJobs}
                  />
                </div>
              );
            } else
              return (
                <SearchJobCard
                  key={job.id + '_' + index}
                  job={job}
                  addFavorite={addFavorite}
                  removeFavorite={removeFavorite}
                  handleSelectedJob={handleSelectedJob}
                  fetchAllJobs={fetchAllJobs}
                />
              );
          })
        ) : (
          <div className='col-span-4'>
            <NoJobFound />
          </div>
        )}
      </div>
    </View>
  );
};

export default AppliedJobs;
