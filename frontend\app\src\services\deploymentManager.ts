const DEPLOYMENT_TIMESTAMP_KEY = 'deployment_timestamp';

class DeploymentManager {
  private deploymentDate = '2025-03-18';  // Your deployment date

  constructor() {
    this.checkDeploymentTimestamp();
  }

  private checkDeploymentTimestamp(): void {
    const storedTimestamp = localStorage.getItem(DEPLOYMENT_TIMESTAMP_KEY);
    
    if (!storedTimestamp || this.isNewDeployment(storedTimestamp)) {
      this.handleNewDeployment();
    }
  }

  private isNewDeployment(storedTimestamp: string): boolean {
    const deploymentTime = new Date(this.deploymentDate).getTime();
    const storedTime = new Date(storedTimestamp).getTime();
    return deploymentTime > storedTime;
  }

  private handleNewDeployment(): void {
    // Clear all auth-related storage
    localStorage.removeItem('authToken');
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
    localStorage.removeItem('linkedIn_code');
    localStorage.removeItem('google_token');
    
    // Update deployment timestamp
    localStorage.setItem(DEPLOYMENT_TIMESTAMP_KEY, this.deploymentDate);
    
    // Trigger session expiry event
    window.dispatchEvent(new CustomEvent('sessionExpired'));
  }
}

export default new DeploymentManager(); 
