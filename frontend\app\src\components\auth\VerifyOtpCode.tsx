// @ts-nocheck
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import { AuthContext } from 'src/context/AuthContext';
import { useContext } from 'react';
import { sendCode, verifyOptCode } from 'src/services/user';
import { useLocation, useNavigate } from 'react-router-dom';
import { View, useToast, Image } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import SurelyHeaderIcon from '../Header/SurelyHeaderIcon/SurelyHeaderIcon';

type FormDataType = {
  email: string;
  password: string;
  login_type: number;
  platform: number;
  app_version: string;
  firebase_token: string;
  phone: number;
  user_id: number;
};

const isDataUnavailable = (userData: any) => {
  return (
    userData?.user?.profile_title === null ||
    userData?.user?.profile_description === null ||
    userData?.user?.profile_description === undefined ||
    userData?.user?.profile_photo === null ||
    userData?.user?.profile_video === null ||
    userData?.languages?.length === 0 ||
    userData?.user?.profile_title === undefined ||
    userData?.user?.profile_photo === undefined ||
    userData?.user?.profile_video === undefined ||
    userData?.employments?.length === 0 ||
    userData?.qualifications?.length === 0
  );
};

const VerifyOtpCode = () => {
  const { authenticateUser } = useContext(AuthContext);
  const { state } = useLocation();
  const toast = useToast();
  const navigate = useNavigate();

  const schema: any = yup.object().shape({
    code: yup.string().required('Please input your phone number.'),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormDataType>({
    resolver: yupResolver<FormDataType>(schema),
    defaultValues: {
      login_type: 1,
      app_version: 'web 1.0',
      firebase_token: 'test',
      user_id: undefined,
      platform: 3,
    },
  });

  const onSubmitHandler = async (data: any) => {
    data.user_id = state?.user_id ?? null;
    const loginResponse = await verifyOptCode(data);
    if (loginResponse?.token) {
      authenticateUser(loginResponse);
      if (+loginResponse?.data?.user.account_type === 2) {
        navigate('/client-dashboard');
      } else {
        if (isDataUnavailable(loginResponse?.data)) {
          navigate('/my-profile');
        } else {
          navigate('/search-jobs');
        }
      }
    } else {
      toast.show({
        title: 'An error occurred during code verification.',
        text: 'Check your code!',
        startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
  };

  return (
    <>
      <View className='mt-[20px] flex flex-col sm:mt-[84.3px]'>
        <View className='mx-auto flex w-[340px] flex-col items-start sm:w-[488px]  '>
          <SurelyHeaderIcon />
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <section className='rubik mb-3 text-[16px] text-black'>Enter verification code sent to your phone</section>
            <input
              {...register('code')}
              placeholder='Enter the code'
              type='text'
              className={` rubik mb-2 h-[48px] w-full rounded border border-[#BBC1D3] bg-transparent px-2 text-[16px] text-[#323C58] sm:mb-0 sm:w-[368px] ${errors?.firstName?.message && 'border-[#CB101D]'}`}
            />
            <View>
              <button className='border-neutral bg-background-base rubik mt-[16px] h-[48px] w-full gap-2 self-stretch self-stretch rounded-[8px] border bg-[#0B80E7] px-4 py-2 text-[16px] text-white sm:w-[368px]'>
                Verify
              </button>
            </View>
          </form>
        </View>
      </View>
    </>
  );
};

export default VerifyOtpCode;
