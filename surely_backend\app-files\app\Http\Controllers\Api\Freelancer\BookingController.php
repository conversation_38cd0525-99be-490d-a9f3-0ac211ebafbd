<?php

namespace App\Http\Controllers\Api\Freelancer;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\EventRequest;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class BookingController extends Controller
{
    public function user_events(){

        $bookings = Booking::where('provider_id', Auth::guard('api')->user()->id)
            ->whereDate('start_date','>=',date('Y-m-d'))
            ->where('status','!=',Booking::declined)
            ->where('type',Booking::booking)
            ->orderBy('start_date','asc')
            ->get();

        $data = array();
        foreach ($bookings as $booking) {
            $obj = [
                'id'=>$booking->id,
                'buyer_id'=>$booking->buyer_id ?? 0,
                'name'=>$booking->type == Booking::booking ? $booking->buyer->name: $booking->title,
                'start_date'=>$booking->start_date,
                'end_date'=>$booking->end_date,
                'shift_start'=>$booking->shift_start,
                'shift_end'=>$booking->shift_end,
                'total_hours'=>$booking->total_hours,
                'total_price'=>$booking->total_price,
                'notes'=>$booking->notes ?? '',
                'status'=>(int)$booking->status,
                'type'=>$booking->type == Booking::booking ? 'Booking' : 'Event'
            ];
            array_push($data,$obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data,
        ]);
    }

    public function change_status($id,$status){

        Booking::findOrFail($id)->update(['status'=>$status]);

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    public function calendar_events(){

        $bookings = Booking::where('provider_id', Auth::guard('api')->user()->id)
            ->whereDate('start_date','>=',date('Y-m-d'))
            ->where('status', Booking::approved)
            ->orderBy('start_date','asc')
            ->get();

        $data = array();
        foreach ($bookings as $booking) {
            $obj = [
                'id'=>$booking->id,
                'buyer_id'=>$booking->buyer_id ?? 0,
                'name'=>$booking->type == Booking::booking ? $booking->buyer->name: $booking->title,
                'start_date'=>$booking->start_date,
                'end_date'=>$booking->end_date,
                'shift_start'=>$booking->shift_start,
                'shift_end'=>$booking->shift_end,
                'total_hours'=>$booking->total_hours,
                'total_price'=>$booking->total_price,
                'notes'=>$booking->notes ?? '',
                'status'=>(int)$booking->status,
                'type'=>$booking->type == Booking::booking ? 'Booking' : 'Event'
            ];
            array_push($data,$obj);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data,
        ]);
    }

    public function  add_event(EventRequest $request){
        $user = Auth::guard('api')->user();

        $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date);
        $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date);

        $diff_in_days = $end_date->diffInDays($start_date)  + 1; //including first day

        $shift_start = Carbon::createFromFormat('H:i', $request->start_time);
        $shift_end = Carbon::createFromFormat('H:i', $request->end_time);

        $diff_in_hours = $shift_end->diffInHours($shift_start);


        $total_hours = $diff_in_hours * $diff_in_days;

        Booking::create([
           // 'buyer_id'=>$user->id,
            'provider_id'=>$user->id,
            'start_date'=>$start_date,
            'end_date'=>$end_date,
            'shift_start'=>$shift_start,
            'shift_end'=>$shift_end,
            'price_per_hour'=>0,
            'total_hours'=>$total_hours,
            'total_price'=>0,
          //  'card_id'=>$request->card_id,
         //   'notes'=>$request->notes,
            'status'=>Booking::approved,
            'type'=>Booking::event,
            'title'=>$request->title,
        ]);

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }
}
