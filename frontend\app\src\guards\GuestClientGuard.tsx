// @ts-nocheck
import {FunctionComponent, ReactNode} from 'react'
import {Navigate, useLocation} from 'react-router-dom'
import {useAuthContext} from "src/context/AuthContext.tsx";

interface GuestClientGuardProps {
    children: ReactNode
}

const GuestClientGuard: FunctionComponent<GuestClientGuardProps> = ({children}) => {
    const location = useLocation()
    const {user} = useAuthContext();

    // Ensure we're comparing strings
    const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type)
    const isClient = accountType === '2'
    const isGuest = accountType === '5'

    if (!isClient && !isGuest) {
        return <Navigate to="/" state={{from: location}}/>
    }

    return <>{children}</>
}

export default GuestClientGuard
