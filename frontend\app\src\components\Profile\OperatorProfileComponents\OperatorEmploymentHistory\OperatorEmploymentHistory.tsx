// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, Button, View, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import { AppContext } from '../../../../context/AppContext';
import moment from 'moment';

const OperatorEmploymentHistory: React.FC = () => {
  const navigate = useNavigate();
  const { positions } = useContext(AppContext);
  return (
    <>
      <Card className='xl:w-full xl:mx-auto p-6 xl:max-w-[312px]'>
        <View className='flex items-center justify-between'>
          <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>
            Employment history
          </Text>
          <Button
            icon={() => (
              <span className='material-icons-outlined text-white text-base flex items-center justify-center'>
                edit
              </span>
            )}
            onClick={() =>
              navigate('/operator-settings-employment', {
                state: {
                  activeTab: '0',
                },
              })
            }
            className='!w-[32px] !h-[32px] !rounded-full border border-[#323C58] !bg-[#323C58] '
          ></Button>
        </View>
        <View className='mt-[20px] flex flex-col gap-2'>
          {positions.length === 0 ? (
            <NoDataProfile />
          ) : (
            positions.map(({ id, jobTitle, startDate, endDate, jobDescription }: any) => {
              let formattedStartDate = moment(startDate).format('DD.MM.YYYY');
              let formattedEndDate;
              endDate ? (formattedEndDate = moment(endDate).format('DD.MM.YYYY')) : (formattedEndDate = null);

              return (
                <View key={id} className='flex  gap-3 px-2'>
                  <View className='flex flex-col py-1'>
                    <span className='material-icons text-xs text-[#323C58]'>fiber_manual_record</span>
                    <div className='flex grow w-[1px] bg-[#BBC1D3] gap-2 ml-[5px]' />
                  </View>
                  <View className='flex flex-col py-1 mt-1'>
                    <Text className='text-[15px] font-normal leading-5 rubik !text-[#1A1A1A]'>
                      {formattedStartDate} - {formattedEndDate || 'Now'}
                    </Text>
                    <Text className='text-[14px] font-normal leading-5 rubik !text-[#323C58]'>{jobTitle}</Text>
                    <Text className='font-normal text-[14px] leading-5 rubik !text-[#383838]'>{jobDescription}</Text>
                  </View>
                </View>
              );
            })
          )}
        </View>
      </Card>
    </>
  );
};

export default OperatorEmploymentHistory;
