<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\JobCollection;
use App\Http\Resources\JobResource;
use App\Http\Resources\OperativeCollection;
use App\Http\Resources\OperativeResource;
use Illuminate\Http\Request;
use App\Models\Favourite;
use App\Http\Controllers\Controller;
class FavouriteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * 
     */
    public function index(Request $request)
    {   
        $query = Favourite::query()->where('favourites.user_id', auth()->id());
        
        $data = [];
        if ($request->get('context') == 'operatives') {
            $query->where('context', 'operatives')
                ->leftJoin('mobile_users', 'mobile_users.id', '=', 'favourites.favourite_id')
                ->select('mobile_users.*');
        
            $data = $query->paginate(); 
            $data = new OperativeCollection($data);
        }

        if ($request->get('context') == 'jobs') {
            $query->where('context', 'jobs')
                ->leftJoin('jobs', 'jobs.id', '=', 'favourites.favourite_id')
                ->select('jobs.*');
            
            $data = $query->paginate();
            $data = new JobCollection($data);
        }
        
        return $data;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = $request->validate([
            'context' => 'required',
            'favourite_id' => 'required',
        ]);

        $data = Favourite::where('user_id', auth()->id())
            ->where('favourite_id', $request->get('favourite_id'))
            ->first();
        
        if ($data) {
            $data->delete();
            return response()->json([
                'error' => false,
                'message' => 'Success! Unliked!',
            ]);
        }

        $validator = array_merge($validator, ['user_id' => auth()->id()]);
        
        if (!Favourite::create($validator)) {
            return response()->json([
                'error' => true,
                'message' => 'Fail! Cannot Like!'
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Success! Liked!'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
