// @ts-nocheck
import { FunctionComponent } from 'react';
import { Loader, View } from 'reshaped';

const Loading: FunctionComponent = () => {
  const isMobile = window.innerWidth < 1024

  if (isMobile) {
   return (
    <div className='flex items-center justify-center h-full w-full'>
      <Loader size='medium' className='h-[100px] w-[100px]' />
    </div>
   )
  } else return (
    <div className='fixed left-[45.4%] top-[45%] h-full w-full items-center '>
      <Loader size='medium' className='h-[100px] w-[100px]' />
    </div>
  );
};

export default Loading;
