// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';
import SurelyIcon from 'src/pages/LandingPage/SurelyIcon';
import SurelyIconNoData from './SurelyIconNoData';

const NoJobPhoto: React.FC = () => {
  return (
    <View className='absolute inset-0 mx-auto flex  h-full w-full flex-col items-center justify-center rounded-xl rounded-xl border border-[#999999]  object-cover p-2'>
      <SurelyIconNoData />
      <Text className='rubik mt-[10px] text-center font-normal leading-[24px] !text-[#383838] text-[#444B5F]'>
        Sorry, this job does not have any images.
      </Text>
    </View>
  );
};

export default NoJobPhoto;
