// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Divider, Modal, Popover, useToast, Image, Switch } from 'reshaped';
import { format, addMonths, subMonths, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameMonth, isSameDay, addDays, isBefore, startOfDay } from 'date-fns';
import moment from 'moment';
import right from '../../assets/icons/calendaricon/right.svg';
import left from '../../assets/icons/calendaricon/left.svg';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { enGB } from 'date-fns/locale';

interface CalendarEditJobProps {
  active: boolean;
  deactivate: () => void;
  onDateRangeSelect: (dates: Date[]) => void;
  shiftData: {
    [key: string]: {
      startTimeHour: string;
      startTimeMinute: string;
      endTimeHour: string;
      endTimeMinute: string;
    };
  };
  setShiftData: React.Dispatch<
    React.SetStateAction<{
      [key: string]: {
        startTimeHour: string;
        startTimeMinute: string;
        endTimeHour: string;
        endTimeMinute: string;
      };
    }>
  >;
  setSelectedDates: () => void;
  onSubmit: () => void;
}
const CalendarEditJob: React.FC<CalendarEditJobProps> = ({
  active,
  deactivate,
  onDateRangeSelect,
  shiftData: initialShiftData,
  setShiftData: setParentShiftData,
  setSelectedDates,
  onSubmit,
}) => {
  const toast = useToast();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [popupDate, setPopupDate] = useState<Date | null>(null);
  const [shiftStartTimeHour, setShiftStartTimeHour] = useState<any>('');
  const [shiftStartTimeMinute, setShiftStartTimeMinute] = useState<any>('');
  const [shiftEndTimeHour, setShiftEndTimeHour] = useState<any>('');
  const [shiftEndTimeMinute, setShiftEndTimeMinute] = useState<any>('');
  const [dateStyles, setDateStyles] = useState<{ [key: string]: string }>({});
  const [shiftData, setShiftData] = useState<{
    [key: string]: any;
  }>(initialShiftData || {});
  const [rangeStart, setRangeStart] = useState(null);
  const [rangeEnd, setRangeEnd] = useState(null);
  const [finalRange, setFinalRange] = useState([]);
  const [isDateRange, setIsDateRange] = useState<boolean>(false);
  const [firstSelectedDate, setFirstSelectedDate] = useState<Date | null>(null);
  const [datePopovers, setDatePopovers] = useState<{ [key: string]: boolean }>({});

  const handleDateStyles = (date: any) => {
    if (date?.start) {
      setDateStyles((prevState) => [...prevState, date]);
    } else if (date?.length > 0) {
      setDateStyles((prevState) => [...prevState, ...date]);
    }
  };
  const handleDateStart = (start: any) => {
    setRangeStart(start);
  };

  const handleDateEnd = (end: any) => {
    const stD = moment(rangeStart, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ [(Central European Standard Time)]');
    const enD = moment(end, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ [(Central European Standard Time)]');
    if (enD < stD) return;
    setRangeEnd(end?._d);
    setRangeEnd(end?._d);

    const selectedRange: any = [];
    for (let dt = moment(stD); dt.diff(enD, 'days') <= 0; dt.add(1, 'days')) {
      selectedRange.push(dt.format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ [(Central European Standard Time)]'));
    }

    setFinalRange(selectedRange);
    setPopupDate(end);
  };

  useEffect(() => {
    setShiftData(shiftData);
    Object.keys(shiftData).forEach((key) => {
      setDateStyles({ [shiftData?.[key]?.start]: '!bg-[#0B80E7] !text-[#ffff]' });
      setShiftStartTimeHour(shiftData?.[key]?.start ? moment(shiftData?.[key]?.start).utc().hours().toString() : '');
      setShiftStartTimeMinute(shiftData?.[key]?.start ? moment(shiftData?.[key]?.start).utc().minutes().toString() : '');
      setShiftEndTimeHour(shiftData?.[key]?.end ? moment(shiftData?.[key]?.end).utc().hours().toString() : '');
      setShiftEndTimeMinute(shiftData?.[key]?.end ? moment(shiftData?.[key]?.end).utc().minutes().toString() : '');
    });
  }, [shiftData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, minValue: number, maxValue: number) => {
    let value = e.target.value;
    value = value.replace(/^0+(?=\d)/, '');
    if (!/^\d*$/.test(value) || parseInt(value) < minValue || parseInt(value) > maxValue) {
      return '';
    }
    return value.length === 1 ? '0' + value : value;
  };

  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleDateClick = (date: Date) => {
    if (!isBefore(date, startOfDay(new Date()))) {
      if (isDateRange && !firstSelectedDate) {
        setFirstSelectedDate(date);
        return;
      }
      setSelectedDate(date);
      setPopupDate(date);
      const formattedDate = moment(date).format('YYYY-MM-DD');
      const data = Object.values(shiftData)?.find((shift: any) => moment(shift.start).format('YYYY-MM-DD') === formattedDate);

      if (data) {
        setShiftStartTimeHour(data?.start ? moment(data?.start).utc().hours().toString() : '');
        setShiftStartTimeMinute(data?.start ? moment(data?.start).utc().minutes().toString() : '');
        setShiftEndTimeHour(data?.end ? moment(data?.end).utc().hours().toString() : '');
        setShiftEndTimeMinute(data?.end ? moment(data?.end).utc().minutes().toString() : '');
      } else {
        setShiftStartTimeHour('');
        setShiftStartTimeMinute('');
        setShiftEndTimeHour('');
        setShiftEndTimeMinute('');
      }
    }
  };

  const handleDateRangeSelection = (formattedFinalRange: any) => {
    setParentShiftData((prevShiftData) => {
      const formatedPrevState = prevShiftData.map((date, index) => {
        if (!date?.start?.includes('+01')) {
          const currentStartDate = new Date(date?.start);
          const currentEndDate = new Date(date?.end);

          const formattedShiftStartTime = new Date(
            currentStartDate?.getFullYear(),
            currentStartDate?.getMonth(),
            currentStartDate?.getDate(),
            parseInt(shiftStartTimeHour),
            parseInt(shiftStartTimeMinute),
            0,
            0,
          );

          const formattedShiftEndTime = new Date(
            currentEndDate?.getFullYear(),
            currentEndDate?.getMonth(),
            currentEndDate?.getDate(),
            parseInt(shiftEndTimeHour),
            parseInt(shiftEndTimeMinute),
            0,
            0,
          );

          const formattedStartEndDates = {
            start: format(formattedShiftStartTime, "yyyy-MM-dd'T'HH:mm:ss.SSSX", {
              locale: enGB,
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            }),
            end: format(formattedShiftEndTime, "yyyy-MM-dd'T'HH:mm:ss.SSSX", {
              locale: enGB,
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            }),
          };

          return formattedStartEndDates;
        } else return date;
      });
      return [...formatedPrevState, ...formattedFinalRange];
    });
  };

  const handleCheckboxChange = () => {
    if (!finalRange || finalRange.length < 2) {
      if (
        popupDate &&
        shiftStartTimeHour &&
        shiftStartTimeMinute &&
        shiftEndTimeHour &&
        shiftEndTimeMinute &&
        parseInt(shiftEndTimeHour) > parseInt(shiftStartTimeHour)
      ) {
        const dateFormatOptions = {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        };

        const formattedShiftStartTime = new Date(
          popupDate.getFullYear(),
          popupDate.getMonth(),
          popupDate.getDate(),
          parseInt(shiftStartTimeHour),
          parseInt(shiftStartTimeMinute),
          0,
          0,
        )
          .toISOString()
          .toLocaleString('en-GB', dateFormatOptions);

        const formattedShiftEndTime = new Date(
          popupDate.getFullYear(),
          popupDate.getMonth(),
          popupDate.getDate(),
          parseInt(shiftEndTimeHour),
          parseInt(shiftEndTimeMinute),
          0,
          0,
        )
          .toISOString()
          .toLocaleString('en-GB', dateFormatOptions);

        handleDateStyles({ [popupDate!.toISOString()]: '!bg-[#0B80E7] !text-[#ffff]' });

        onDateRangeSelect({
          start: formattedShiftStartTime,
          end: formattedShiftEndTime,
        });

        setDatePopovers((prevDatePopovers) => ({
          ...prevDatePopovers,
          [popupDate.toISOString()]: false,
        }));
      } else {
        toast.show({
          title: 'Shift date and time are not valid.',
          // text: 'Check the shifts',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }
    } else {
      if (
        shiftStartTimeHour &&
        shiftStartTimeMinute &&
        shiftEndTimeHour &&
        shiftEndTimeMinute &&
        parseInt(shiftEndTimeHour) > parseInt(shiftStartTimeHour)
      ) {
        const dateFormatOptions = {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        };

        const formattedFinalRange = finalRange.map((date) => {
          const currentDate = new Date(date);

          const formattedShiftStartTime = new Date(
            currentDate?.getFullYear(),
            currentDate?.getMonth(),
            currentDate?.getDate(),
            parseInt(shiftStartTimeHour),
            parseInt(shiftStartTimeMinute),
            0,
            0,
          )
            .toISOString()
            .toLocaleString('en-GB', dateFormatOptions);

          const formattedShiftEndTime = new Date(
            currentDate?.getFullYear(),
            currentDate?.getMonth(),
            currentDate?.getDate(),
            parseInt(shiftEndTimeHour),
            parseInt(shiftEndTimeMinute),
            0,
            0,
          )
            .toISOString()
            .toLocaleString('en-GB', dateFormatOptions);

          const formattedStartEndDates = {
            start: formattedShiftStartTime,
            end: formattedShiftEndTime,
          };

          setDatePopovers((prevDatePopovers) => ({
            ...prevDatePopovers,
            [popupDate.toISOString()]: false,
          }));

          handleDateStyles({ [popupDate!.toISOString()]: '!bg-[#0B80E7] !text-[#ffff]' });

          return formattedStartEndDates;
        });

        onDateRangeSelect(formattedFinalRange);
      } else {
        toast.show({
          title: 'Shift date and time are not valid.',
          // text: 'Check the shifts',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }
    }
  };

  const calculatePopoverPosition = (date: Date) => {
    const calendarDays = getCalendarDays();
    const index = calendarDays.findIndex((d) => isSameDay(d, date));
    if (index >= 14) {
      return 'top';
    } else {
      return 'bottom';
    }
  };

  const getCalendarDays = () => {
    const firstDayOfMonth = startOfMonth(currentMonth);
    const lastDayOfMonth = endOfMonth(currentMonth);
    const startDate = startOfWeek(firstDayOfMonth, { weekStartsOn: 1 });
    const endDate = endOfWeek(lastDayOfMonth, { weekStartsOn: 1 });
    const calendarDays = [];
    let currentDate = startDate;

    while (currentDate <= endDate) {
      calendarDays.push(currentDate);
      currentDate = addDays(currentDate, 1);
    }

    return calendarDays;
  };

  const calendarDays = getCalendarDays();

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[924px]'>
      <div>
        <div className='flex items-center justify-between px-4 py-3'>
          <Text className='rubik text-[24px] font-normal text-[#323C58]'>Job days and shift hours</Text>
          <span className='material-icons-outlined cursor-pointer' onClick={deactivate}>
            close
          </span>
        </div>
        <Divider className='mt-[8px] h-[1px] w-full' />
        <div className='mt-[8px] flex items-center justify-between px-4 py-3'>
          <div className='flex items-center'>
            <Text className='rubik text-[20px] font-normal leading-[28px] text-[#323C58]'>{format(currentMonth, 'MMMM yyyy')}</Text>
            <img src={left} className='ml-[10px] sm:ml-[24px]' onClick={prevMonth} />
            <img src={right} className='ml-[10px] sm:ml-[24px]' onClick={nextMonth} />
          </div>
          <div className='flex flex-col '>
            <div className='flex flex-col items-start lg:flex-row'>
              <div className='mt-2 flex flex-row items-start lg:mt-0'>
                <div className='h-[18px] w-[18px] rounded-md border border-black bg-[#F2F3F7]' />
                <Text className='rubik ml-2 text-[13px] font-normal leading-[20px] text-[#383838]'>Past days</Text>
              </div>
              <div className='mt-2 flex flex-row items-start lg:mt-0'>
                <div className='h-[18px] w-[18px] rounded-md border border-[#0B80E7] bg-[#0B80E7] sm:ml-4' />
                <Text className='rubik ml-2 text-[13px] font-normal leading-[20px] text-[#383838]'>Days selected</Text>
              </div>
              <div className='mt-2 flex flex-row items-start lg:mt-0'>
                <div className='h-[18px] w-[18px] rounded-md border border-black sm:ml-4' />
                <Text className='rubik ml-2 text-[13px] font-normal leading-[20px] text-[#383838]'>Days available</Text>
              </div>
              <div className='mt-2 flex flex-row items-start lg:mt-0'>
                <Switch
                  name='Date Range'
                  className='sm:ml-4'
                  onChange={() => {
                    setIsDateRange((prevState) => !prevState);
                    if (isDateRange === false) {
                      setRangeStart(null);
                      setRangeEnd(null);
                    } else {
                      setDatePopovers({});
                      // setDateStyles([]);
                    }
                  }}
                  checked={isDateRange}
                />

                <Text className='rubik ml-2 text-[13px] font-normal leading-[20px] text-[#383838]'>Date Range</Text>
              </div>
            </div>
            {isDateRange === true && (
              <Text className='rubik text-[13px] font-normal leading-[20px] text-[#383838] lg:mt-2 lg:w-[460px]'>
                Please select the start and end dates for the job. Note that adding shift hours is only applicable to the last date. Changing the date
                range will result in the loss of job data.
              </Text>
            )}
          </div>
        </div>
        <div className='grid grid-cols-7 justify-center gap-4 px-2 pb-3'>
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
            <div key={day} className='text-center font-medium'>
              {day}
            </div>
          ))}
          {calendarDays.map((date) => (
            <div key={date.toISOString()}>
              <Popover
                active={datePopovers[date.toISOString()]}
                onClose={() => {
                  setDatePopovers((prevDatePopovers) => ({
                    ...prevDatePopovers,
                    [date.toISOString()]: false,
                  }));
                }}
                onOpen={() => {
                  setDatePopovers((prevDatePopovers) => ({
                    ...prevDatePopovers,
                    [date.toISOString()]: true,
                  }));
                }}
                position={calculatePopoverPosition(date)}
              >
                <Popover.Trigger>
                  {(attributes) => {
                    const formattedDate = date.toISOString();
                    return (
                      <Button
                        attributes={attributes}
                        variant='outline'
                        className={`flex cursor-pointer items-center justify-center rounded-md border-[#D5D4DF] focus:outline-none lg:h-[70px] lg:w-[105px] ${
                          !isSameMonth(date, currentMonth) ? 'text-gray-400' : ''
                        } ${
                          finalRange?.includes(
                            moment(date, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ [(Central European Standard Time)]').format(
                              'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ [(Central European Standard Time)]',
                            ),
                          )
                            ? '!bg-[#0B80E7] !text-[#ffff]'
                            : dateStyles[date.toISOString()] || ''
                        } ${isBefore(date, startOfDay(new Date())) ? '!bg-[#F2F3F7]' : ''}`}
                        onClick={() => {
                          if (isDateRange) {
                            if (!rangeStart) {
                              handleDateStart(date);
                            }
                            if (rangeStart) {
                              handleDateEnd(date);
                            }
                          } else {
                            handleDateClick(date);
                          }
                        }}
                      >
                        <div>{format(date, 'dd')}</div>
                      </Button>
                    );
                  }}
                </Popover.Trigger>
                {popupDate && isSameDay(date, popupDate) && (
                  <Popover.Content className='border-none !bg-[#323C58] lg:w-[244px]'>
                    <div className='   justify-center rounded text-white '>
                      <div className='w-[212px] rounded-[8px] bg-[#F4F5F7] p-[16px]'>
                        <Text className='rubik font-medium leading-[20px] text-[#1A1A1A] '>Shift start time:</Text>
                        <div className='mt-2 flex items-center'>
                          <input
                            type='number'
                            className='h-[48px] w-[74.5px] rounded-md border border-[#BBC1D3] text-center text-[#1A1A1A]'
                            value={shiftStartTimeHour}
                            onChange={(e) => setShiftStartTimeHour(handleInputChange(e, 0, 23))}
                            min='0'
                            max='23'
                          />
                          <Text className='rubik mx-2 text-[24px] text-[#323C58]'>:</Text>
                          <input
                            type='number'
                            className='h-[48px] w-[74.5px] rounded-md border border-[#BBC1D3] text-center text-[#1A1A1A]'
                            value={shiftStartTimeMinute}
                            onChange={(e) => setShiftStartTimeMinute(handleInputChange(e, 0, 59))}
                            min='0'
                            max='59'
                          />
                        </div>
                      </div>
                      <div className='mt-[8px] w-[212px] rounded-[8px] bg-[#F4F5F7] p-[16px]'>
                        <Text className='rubik font-medium leading-[20px] text-[#1A1A1A] '>Shift end time:</Text>
                        <div className='mt-2 flex items-center'>
                          <input
                            type='number'
                            className='h-[48px] w-[74.5px] rounded-md border border-[#BBC1D3] text-center text-[#1A1A1A]'
                            value={shiftEndTimeHour}
                            onChange={(e) => setShiftEndTimeHour(handleInputChange(e, 0, 23))}
                            min='0'
                            max='23'
                          />
                          <Text className='rubik mx-2 text-[24px] text-[#323C58]'>:</Text>
                          <input
                            type='number'
                            className='h-[48px] w-[74.5px] rounded-md border border-[#BBC1D3] text-center text-[#1A1A1A]'
                            value={shiftEndTimeMinute}
                            onChange={(e) => setShiftEndTimeMinute(handleInputChange(e, 0, 59))}
                            min='0'
                            max='59'
                          />
                        </div>
                      </div>
                      <div className='mt-7 border-t border-[#F4F5F7] pt-3'>
                        <label className='flex items-center'>
                          <input type='checkbox' className='form-checkbox' onChange={handleCheckboxChange} />
                          <Text className='rubik ml-2 text-[14px] font-medium leading-[20px]'>{format(popupDate!, 'dd MMM yyyy')}</Text>
                        </label>
                      </div>
                    </div>
                  </Popover.Content>
                )}
              </Popover>
            </div>
          ))}
        </div>
      </div>
      <div className='mt-[20px] flex flex-row justify-between'>
        <Button
          onClick={() => {
            onSubmit();
            deactivate();
          }}
          className='rubik border-neutral flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[#0B80E7] px-4 py-2 lg:w-[340px]'
        >
          <Text className='rubik text-[16px] font-medium leading-[24px] !text-[#ffff]'> Confirm</Text>
        </Button>
      </div>
    </Modal>
  );
};

export default CalendarEditJob;
