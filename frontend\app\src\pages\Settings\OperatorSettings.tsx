import React from 'react';
import { Button, Text, View } from 'reshaped';
import { useNavigate } from 'react-router-dom';

const OperatorSettings: React.FC = () => {
  const navigate = useNavigate();

  return (
    <View className='flex flex-col'>
      <View className='flex items-center p-0'>
        <Text className='text-foreground-neutral lg:text-4xl font-rufina-stencil font-normal xl:font-normal leading-10 xl:leading-10 text-[#323C58]'>
          Account settings
        </Text>
        <a
          onClick={() => navigate('/my-profile')}
          className='text-light-gray text-base rubik font-normal leading-5 underline text-[#383838] ml-4 cursor-pointer'
        >
          Profile page
        </a>
      </View>
      <View className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-[24px] '>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-general')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              person_pin_circle
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              General
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal whitespace-nowrap leading-5 text-[#383838] mt-[4px]'>
              Name, location, post code, phone number
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-profile-details')}
          >
            <span className='material-icons text-[#1A1A1A]'>person</span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              Profile details
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              SIA skills, Industry sectors, Profile photos, video, description
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-availability')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              notifications_active
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              Availability
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              Instant book
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-sia-certificate')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              security
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              SIA licence
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              Licence status, BS7858
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-employment')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              work_outline
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              Employment
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              Employment history, work experience, relevant qualifications
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-payment')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              receipt_long
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              Payments
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              Bank details, UTR, VAT, credit card and invoices
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-login')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              login
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              Login details
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              Password, security, delete your account
            </Text>
          </Button>
        </div>
        <div className='rounded-lg' style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.12)' }}>
          <Button
            className='block w-[300px] md:w-[312px] h-[161px] border-0 !bg-white p-5 mx-auto'
            variant='outline'
            onClick={() => navigate('/operator-settings-notification')}
          >
            <span className='material-icons-outlined text-[#1A1A1A]'>
              report_problem
            </span>
            <Text className='text-black text-white text-black-900 xl:text-base rubik  font-medium leading-5 xl:leading-6 !text-[#000000] mt-[12px]'>
              Notification
            </Text>
            <Text className='text-light-gray text-sm rubik font-normal leading-5 text-[#383838] mt-[4px]'>
              Global alerts for important activity
            </Text>
          </Button>
        </div>
      </View>
    </View>
  );
};

export default OperatorSettings;
