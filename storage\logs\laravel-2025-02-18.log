[2025-02-18 14:43:31] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 14:44:48] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 14:45:54] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 15:24:56] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 15:35:28] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 15:48:23] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 15:58:34] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 16:02:49] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 16:04:57] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 19:53:38] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
[2025-02-18 19:56:31] production.ERROR: symlink(): No such file or directory {"exception":"[object] (ErrorException(code: 0): symlink(): No such file or directory at /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:332)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(332): symlink()
#2 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/StorageLinkCommand.php(47): Illuminate\\Filesystem\\Filesystem->link()
#3 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\StorageLinkCommand->handle()
#4 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call()
#8 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call()
#9 /var/www/html/surely_backend/app-files/vendor/symfony/console/Command/Command.php(298): Illuminate\\Console\\Command->execute()
#10 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run()
#11 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(1040): Illuminate\\Console\\Command->run()
#12 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand()
#13 /var/www/html/surely_backend/app-files/vendor/symfony/console/Application.php(171): Symfony\\Component\\Console\\Application->doRun()
#14 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run()
#15 /var/www/html/surely_backend/app-files/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run()
#16 /var/www/html/surely_backend/app-files/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
