import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { But<PERSON>, Loader, TextArea, TextField } from 'reshaped';
import moment from 'moment';

import { useChatContext } from 'src/context/ChatContext';
import { useAuthContext } from 'src/context/AuthContext';

import { ChatService, getMessages } from 'src/services/chat';
import ChatCard from '../ChatList/ChatCard';
import TextMessage from './ChatMessageCards/TextMessage';
import ContractMessage from './ChatMessageCards/ContractMessage';
import PaymentMessage from './ChatMessageCards/PaymentMessage';
import JobInvitationMessage from './ChatMessageCards/JobInvitationMessage';
import ChatMessagesFactory from './ChatMessageFactory';
import Loading from 'src/components/Loading/Loading';

const ChatMessages = ({ user }) => {
  const { messages, setMessages, userData, handleGetChats, handleGetMessages, loadContract, setCurrentChat } = useChatContext();
  const [loadingMessages, setLoadingMessages] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const messagesContainerRef = useRef(null);

  const screenWidth = window.innerWidth;
  const { currentChat, getCurrentChat, contract } = useChatContext();

  const params = useParams();
  const id = user?.profile?.id;

  const receiverId = user?.profile?.id;
  const nameShown = user?.isAuthenticated ? (currentChat?.sender_id === id ? currentChat?.receiver_name : currentChat?.sender_name) : '';
  const jobOrContractName =
    currentChat?.type === 'no_contract' ? (currentChat?.contract?.id ? '- #' + currentChat?.contract?.id : '') : ' - ' + currentChat?.job?.post_name;

  let jobName = currentChat?.job?.name;

  if (jobName) {
    jobName = ' - ' + jobName;
  }

  const PAGE_SIZE = 50;
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef(null);
  const observer = useRef();

  const lastMessageRef = useCallback(node => {
    if (loadingMessages) return;
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersible && hasMore) {
        setPage(prev => prev + 1);
      }
    });
    
    if (node) observer.current.observe(node);
  }, [loadingMessages, hasMore]);

  // Infinite scroll implementation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMessages) {
          loadMessages(params.id, page + 1);
          setPage(prev => prev + 1);
        }
      },
      { threshold: 0.5 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loadingMessages, page]);

  // Optimize message loading
  const loadMessages = async (chatId, pageNum) => {
    try {
      setLoadingMessages(true);
      const response = await handleGetMessages(chatId, pageNum);
      
      if (!response?.data) return;
      
      const formattedMessages = formatMessagesWithDates(response.data);
      
      setMessages(prev => pageNum === 1 ? formattedMessages : [...prev, ...formattedMessages]);
      setHasMore(response.meta.current_page < response.meta.last_page);
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoadingMessages(false);
    }
  };

  // Format messages with date separators
  const formatMessagesWithDates = (messages) => {
    return messages?.reduce((formatted, message, index) => {
      const currentDay = moment(message.created_at).format('YYYY, M, D');
      const nextDay = moment(messages[index + 1]?.created_at).format('YYYY, M, D');

      if (index === 0 || currentDay !== nextDay) {
        formatted.push({
          type: 'dayChange',
          dateChange: moment(message.created_at).format('D/M/YYYY'),
        });
      }

      formatted.push(message);
      return formatted;
    }, []);
  };

  // Scroll to bottom on new messages
  useEffect(() => {
    if (!messages?.length) return;
    
    const scrollToBottom = () => {
      if (messagesContainerRef.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
    };

    scrollToBottom();
  }, [messages]);

  const filteredMessages = useMemo(() => 
    messages?.reduce((acc, obj) => {
      if (obj.id !== undefined) {
        if (!acc.idSet.has(obj.id)) {
          acc.idSet.add(obj.id);
          acc.result.push(obj);
        }
      } else {
        acc.result.push(obj);
      }
      return acc;
    }, { result: [], idSet: new Set() }).result,
  [messages]);

  useEffect(() => {
    if (!params?.id) return;
    
    setLoadingMessages(true);
    handleGetMessages(params.id)
      .then((response) => {
        if (!response) return;
        const formattedMessages = formatMessagesWithDates(response);
        setMessages(formattedMessages);
      })
      .finally(() => {
        setLoadingMessages(false);
      });
  }, [params.id]);

  const handleSendMessage = async (message) => {
    if (!currentChat?.id) {
        console.error('No chat ID available');
        return;
    }

    const optimisticMessage = {
        id: `temp-${Date.now()}`,
        chat_id: currentChat.id,
        sender_id: userData?.id,
        sender_name: userData?.name,
        receiver_id: currentChat.receiver_id,
        message,
        type: 'text',
        created_at: new Date().toISOString()
    };
    
    // Add optimistic message
    setMessages(prev => [...prev, optimisticMessage]);
    
    try {
        const response = await ChatService.sendMessage(currentChat.id, { message });
        
        if (response?.data?.message) {
            // Replace optimistic message with real message
            setMessages(prev => prev.map(msg => 
                msg.id === optimisticMessage.id ? response.data.message : msg
            ));
            
            // If this is a new chat, update the current chat with the new chat data
            if (response?.data?.chat && response?.data?.chat?.id !== currentChat?.id) {
                setCurrentChat(response.data.chat);
                navigate(`/chat/${response.data.chat.id}`);
            }
        }
    } catch (error) {
        console.error('Failed to send message:', error);
        // Remove optimistic message on error
        setMessages(prev => prev.filter(msg => msg.id !== optimisticMessage.id));
    }
  };

  useEffect(() => {
    if (!currentChat?.id || !currentChat?.contract?.id) return;
    
    let timeoutId;
    const pollContract = async () => {
      try {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        timeoutId = setTimeout(async () => {
          await loadContract(currentChat.contract.id);
          await handleGetMessages();
        }, 500); // Debounce time
        
      } catch (error) {
        console.error('Error polling contract:', error);
      }
    };
    
    const interval = setInterval(pollContract, 10000); // Increased to 10 seconds
    
    // Initial poll with delay
    setTimeout(pollContract, 1000);
    
    return () => {
      clearInterval(interval);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [currentChat?.id, currentChat?.contract?.id]);

  const navigate = useNavigate();

  const sendMessageHandler = async (messageText) => {
    const message = messageText?.trim();
    if (!message) return;
    
    await handleSendMessage(message);
    inputRef.current.value = '';
  };

  return (
    <div className='w-full grow rounded-lg rounded-b-none border border-[#DFE2EA] bg-white lg:h-[777px] lg:w-[537px]'>
      <div className='border-b border-[#DFE2EA] px-3 py-4 lg:h-[72px] lg:px-6  lg:py-6 '>
        {nameShown && jobOrContractName !== undefined ? (
          <p className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A] lg:text-left'>
            {nameShown}
            <span className='text-[#323C58]'>{jobOrContractName}</span>
          </p>
        ) : filteredMessages?.length !== 0 ? (
          <p className='rubik animate-pulse text-center text-[16px] font-medium leading-5 text-[#1A1A1A] lg:text-left'>Chat messages</p>
        ) : (
          <p className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A] lg:text-left'>Chat messages</p>
        )}
      </div>
      <div className='flex h-full flex-col p-3 lg:h-[705px] lg:p-6'>
        <div 
          ref={messagesContainerRef}
          className='flex flex-col overflow-auto pb-1' 
          style={{ height: 'calc(100vh - 358px)' }}
        >
          {loadingMessages ? (
            <div className='flex h-full w-full items-center justify-center'>
              <Loader size='medium' className='h-10 w-10' />
            </div>
          ) : (
            filteredMessages
              ?.filter((message) => message.message || message.type)
              ?.map((message, index) => {
                const formattedTimestampMessage = { ...message, timestamp: moment(message.created_at).format('LT') };
                return (
                  <div
                    className={`${index === 0 ? 'mt-0' : 'mt-2'} pr-2`}
                    key={(message?.id && message?.id + '-' + message?.sender_id + '-' + index) || index}
                    ref={lastMessageRef}
                  >
                    <ChatMessagesFactory message={formattedTimestampMessage} />
                  </div>
                );
              })
          )}
          <div ref={lastMessageRef}></div>
        </div>
        <div className='relative flex w-full flex-col justify-between gap-5 lg:h-[281px]'>
          <hr className='mt-5 w-full' />
          <textarea
            ref={inputRef}
            className='text-message rubik focus:ring-none w-full rounded border border-[#BBC1D3] bg-[#F8F8F8] px-3 py-[14px] text-left text-[#383838] lg:h-[132px]'
            id='text-message'
            onKeyDown={(e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    sendMessageHandler(inputRef.current.value);
                }
            }}
          />
          <span
            onClick={() => sendMessageHandler(inputRef.current.value)}
            className='material-icons absolute bottom-2.5 right-2 ml-auto h-min w-fit cursor-pointer rounded-lg !bg-[#0B80E7] px-2 py-1.5 text-[20px] text-[#ffffff] lg:static lg:px-4 lg:py-3'
          >
            send
          </span>
        </div>
      </div>
    </div>
  );
};

export default ChatMessages;
