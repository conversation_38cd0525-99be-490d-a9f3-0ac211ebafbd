// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Text, Button } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Subscribe from '../Subscribe/Subscribe';

import '../../components/Header/HeaderMenu/HeaderMenu.css';

const AccessibilityPolicy: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-center mx-auto mt-[100px]'>
            <Text className='text-center font-rufina-stencil text-[48px] font-normal leading-[56px] text-[#323C58]'>
              ACCESSIBILITY POLICY
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Accessibility is the word used to describe whether a product (for
              example, a website, mobile site, digital TV interface or
              application) can be used by people of all abilities. For instance,
              a website is accessible if all people, including disabled and
              elderly people, can use it.
            </Text>
            <div className='items-center mx-auto bg-[#388DD8] w-[200px] h-[4px] my-4 mt-[25px]' />
          </View>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-start mx-auto mt-[20px] mb-[20px]'>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              At Surely, the trading name of 5AT Limited, we aim to ensure that
              our website is informative and entertaining to use, as well as
              being accessible – this is called usability. On a website,
              accessibility depends on how a person’s ability and/or needs
              affect the way they perceive information on a page and how they
              navigate within and between pages.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              Elements that affect accessibility include:
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              • For people who are partially sighted: the colours and the
              contrast between colours; the size of text; the choice of fonts
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              • For people who are blind: how a screen reader interprets the
              elements on a page (for example, alt tags for images, and title
              tags for links); the inclusion of audio description for video
              content
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              • For people who are hearing impaired: how any audio-visual
              content is represented graphically (for example, subtitles and/or
              signing)
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              • For people who find a keyboard or mouse difficult to use: the
              ease with which someone can navigate to various parts of the page
              (for instance, by tabbing)
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              • For people who find reading difficult: the length of sentences
              and paragraphs; the complexity of the vocabulary; the choice of
              fonts and size of text; the opportunity to have text read out loud
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              At Surely, our accessibility policy takes into account these
              factors wherever possible. The internet can be a great enabler and
              source of freedom for users of all abilities. As such, the value
              of Surely (and the wider web) to our audiences depends on its
              accessibility.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              This is an area of importance for Surely. In keeping with our
              obligations under the Equality Act, we are committed to ensuring
              that Surely digital services are as accessible as reasonably
              possible.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              We aim for a consistently high level of usability for our entire
              audience, following best-practice accessibility guidelines. When a
              website is correctly designed, developed and edited, all users
              have equal access to the site’s information, functionality and
              benefits.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Surely aims to make our website accessible and usable for people
              of all abilities, including but not limited to, older audiences,
              and people living with visual, hearing, cognitive or motor
              impairments.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Many people use assistive technologies to allow them, for example,
              to view websites in easier-to-read colours, with larger fonts or
              as spoken text, or to navigate around a site using the keyboard
              only.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              As these assistive technologies become more available and
              sophisticated, Surely wants to ensure that our website continues
              to work well with them to deliver a good experience for all our
              users.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              In instances where the specific accessibility needs of some user
              groups require Surely to create new or repurposed editorial
              content, we will do this, wherever appropriate, so that we become
              more inclusive to a wider online audience.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              The web content accessibility guidelines (WCAG) 2.0 also drive our
              approach to website accessibility at:
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='http://www.w3.org/TR/2008/REC-WCAG20-20081211'
                target='_blank'
                rel='noopener noreferrer'
              >
                http://www.w3.org/TR/2008/REC-WCAG20-20081211
              </a>
              . Other helpful information can be found on the W3C website. WC3
              stands for the World Wide Web Consortium. It is an international
              community that develops open standards to ensure the long-term
              growth of the Web at{' '}
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='http://www.w3.org'
                target='_blank'
                rel='noopener noreferrer'
              >
                http://www.w3.org
              </a>
              .
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              We also recommend that our website users visit the BBC
              accessibility web page. You will find a lot of useful information
              on how to improve your online experience at{' '}
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='http://www.bbc.co.uk/accessibility'
                target='_blank'
                rel='noopener noreferrer'
              >
                http://www.bbc.co.uk/accessibility
              </a>
              .
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              We welcome feedback from our users: please contact{' '}
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='mailto:<EMAIL>'
              >
                <EMAIL>
              </a>{' '}
              with any helpful comments and criticisms. We will use this
              information to help us to improve the accessibility of our website
              to all users.
            </Text>
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default AccessibilityPolicy;
