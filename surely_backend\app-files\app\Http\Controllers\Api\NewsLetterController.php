<?php

namespace App\Http\Controllers\Api;

use App\DataTables\NewsLetterDatatable;
use App\Http\Controllers\Controller;
use App\Http\Requests\SubscribeRequest;
use App\Models\NewsLetter;
use Illuminate\Http\Request;

class NewsLetterController extends Controller
{
    public function subscribe(SubscribeRequest $request)
    {
        $subscriber = NewsLetter::where('email', $request->get('email'))->first();
        
        if ($subscriber) {
            $subscriber->update($request->all());
            
            if (!$request->filled('subscribe') || $request->get('subscribe') == 1) {
                return response()->json([
                    'error' => false,
                    'message' => 'Subscribed successfully to newsletter!',
                ]);
            } elseif ($request->get('subscribe') == 0) {
                return response()->json([
                    'error' => false,
                    'message' => 'Unsubscribed successfully from newsletter!',
                ]);
            }
        }

        if (!NewsLetter::create($request->all())) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot subscribe to newsletter!'
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Subscribed successfully to newsletter!',
        ]);
    }

    public function table(NewsLetterDatatable $dataTable)
    {
        return $dataTable->render('newsletter.newsletter_table');
    }
}
