const GreenCheck = () => (
<svg xmlns="http://www.w3.org/2000/svg" width="110" height="43" viewBox="0 0 110 43" fill="none">
  <path d="M107.74 27.7965C106.56 27.7965 105.6 28.7137 105.6 29.9803C105.6 31.247 106.56 32.1642 107.74 32.1642C108.919 32.1642 109.88 31.247 109.88 29.9803C109.88 28.7137 108.919 27.7965 107.74 27.7965Z" fill="#323C58"/>
  <path d="M22.2204 26.8356C22.2204 21.2013 22.4387 18.1439 22.6135 15.0865L20.4296 14.9555C20.0365 14.9118 19.8618 14.7371 19.8618 14.344V14.0383L27.2869 13.6452C27.1558 17.8819 26.8938 21.7254 26.8938 25.9184C26.8938 29.0631 27.0248 31.1159 29.3397 31.1159C30.7373 31.1159 31.9166 30.1551 32.8775 29.0631C33.1396 28.9758 33.5327 29.1942 33.4016 29.6309C33.0085 30.9412 30.4316 32.4699 28.1167 32.4699C24.2295 32.5136 22.2204 30.6792 22.2204 26.8356ZM34.5372 15.0865L32.135 14.9555C31.7419 14.9118 31.5672 14.7371 31.5672 14.344V14.0383L39.298 13.5579V28.1896C39.298 30.1551 39.429 31.247 40.4336 31.247C42.137 31.247 42.2244 28.9321 42.0933 27.4471L42.8358 27.4034C42.9232 27.6655 43.0105 28.4953 43.0105 28.8884C43.0105 31.5527 41.1324 32.6883 39.2543 32.6883C35.8039 32.6883 34.5372 30.8539 34.5372 27.2287V15.0865Z" fill="#323C58"/>
  <path d="M50.8284 13.6893V31.029L53.6237 31.16C54.0168 31.2037 54.1915 31.3784 54.1915 31.7715L54.1478 32.1209H43.8401V31.8589C43.8401 31.4658 43.9711 31.2911 44.4078 31.2037L46.3296 31.029V15.1307L43.8837 14.9996C43.4906 14.9559 43.3159 14.7812 43.3159 14.3881V14.0824L50.8284 13.6893ZM55.4581 16.7904C56.2443 16.7904 57.1178 17.0961 57.3799 17.4892C58.2971 17.0524 58.6465 16.2662 58.6465 15.4801C58.6465 14.4318 57.9914 13.4273 56.4627 13.4273C53.7547 13.4273 51.6582 15.3927 51.6582 17.5329C51.6582 17.8386 51.7456 18.1007 51.9203 18.1007C52.095 18.057 53.3179 16.7904 55.4581 16.7904Z" fill="#323C58"/>
  <path d="M72.4924 20.3718C72.5798 19.935 72.5797 19.4983 72.5797 19.1052C72.5797 16.3535 71.3568 14.5628 69.6097 14.5628C67.1638 14.5628 65.2857 16.8776 65.2857 21.7258V23.0361C65.2857 28.4083 66.1156 31.422 70.0028 31.422C72.2304 31.422 74.8073 30.068 76.0302 28.1463C76.4233 28.2773 76.8601 28.4083 76.8601 28.9325C76.8601 29.8933 74.6762 32.5576 68.9546 32.5576C63.8444 32.5576 60.1318 28.7577 60.1318 23.0797C60.1318 17.2707 64.1064 13.5145 69.304 13.5145C74.2394 13.5145 77.2968 15.5237 77.2968 19.935C77.2968 20.6775 77.2532 21.7258 76.3796 21.7258H66.7707C66.6397 21.42 66.334 20.3718 67.5132 20.3718H72.4924Z" fill="#323C58"/>
  <path d="M86.6874 3.42542V31.0292L88.5655 31.2039C88.9586 31.2475 89.1333 31.5096 89.1333 31.859V32.0774H79.4807V31.9027C79.4807 31.5096 79.6118 31.2475 80.0486 31.2475L81.9266 31.0728V5.56558L79.2187 5.43454C78.8256 5.39087 78.6509 5.21616 78.6509 4.82307V4.3863L86.6874 3.42542Z" fill="#323C58"/>
  <path d="M100.708 31.3781C98.7866 36.5757 96.4717 42.0353 91.7983 42.0353C89.8765 42.0353 88.7847 40.856 88.7847 39.6331C88.7847 38.4538 89.2214 37.6676 89.8329 36.9688C90.488 37.7113 91.2742 38.3664 92.6282 38.3664C95.1178 38.3664 97.0396 36.4883 98.6556 32.1206L91.4926 15.043L90.1823 14.7809C89.7892 14.6936 89.6145 14.5189 89.6145 14.1258V13.8637H98.2625V14.1258C98.2625 14.5189 98.1315 14.6499 97.6947 14.7373L96.035 14.9993L100.839 27.1852C101.669 28.8886 101.407 29.6311 101.276 29.9805L100.708 31.3781ZM102.761 25.8749C101.975 25.8749 101.407 25.3071 101.931 24.0841L104.945 15.5671L102.543 14.9557C102.15 14.8683 101.975 14.6499 101.975 14.3005V13.9074H109.094V14.3005C109.094 14.6936 108.963 14.912 108.57 14.9993L106.474 15.6108L102.761 25.8749Z" fill="#323C58"/>
  <path d="M6.3659 31.7716C6.45325 31.8153 6.54061 32.0336 6.54061 32.2083C6.54061 32.6451 6.19119 32.8635 5.71074 32.8635C4.0947 32.8198 1.60513 32.0773 0.120117 31.2474V25.3074H1.16837C1.56146 25.3074 1.77984 25.4821 1.86719 25.8752L2.91544 30.2429C3.43957 30.8107 4.88088 31.6405 6.3659 31.7716ZM8.98651 32.9508C8.33135 32.9508 8.15666 32.6014 8.15666 32.1647C8.15666 31.99 8.20033 31.9026 8.28769 31.8152C10.7336 31.6405 13.3979 30.1992 13.3979 26.7487C13.3979 23.8224 11.1267 22.2937 7.85092 20.9397C3.92001 19.28 0.251142 17.4019 0.251142 13.1216C0.251142 7.74932 5.05561 6.0896 7.71989 6.0896C8.15666 6.0896 8.46241 6.35166 8.46241 6.91946C8.46241 7.00682 8.41872 7.18152 8.37505 7.2252C6.40959 7.39991 4.96826 9.19066 4.96826 12.0733C4.96826 14.9123 7.10842 16.2663 10.2095 17.5329C13.7473 19.0179 17.8966 20.765 17.8966 25.5258C17.8529 30.9854 11.9565 32.9508 8.98651 32.9508Z" fill="#323C58"/>
  <path d="M13.747 8.01149L14.9699 12.2481C15.101 12.7286 15.4941 12.8596 15.8872 12.8596H16.4113V7.26898C14.9699 6.70118 12.3493 6.00235 10.7333 6.00235C10.2965 6.00235 10.0781 6.35177 10.0781 6.70118C10.0781 6.83221 10.1218 7.00692 10.1655 7.09427C11.5195 7.13795 13.1792 7.61839 13.747 8.01149Z" fill="#323C58"/>
  <path d="M17.8533 5.60936L16.6303 1.37271C16.4993 0.892265 16.1062 0.76123 15.7131 0.76123H15.189V6.35186C16.6303 6.91966 19.2509 7.61849 20.867 7.61849C21.3037 7.61849 21.5221 7.26908 21.5221 6.91966C21.5221 6.78863 21.4784 6.61392 21.4347 6.52657C20.0808 6.48289 18.4211 6.00245 17.8533 5.60936Z" fill="#71E1F8"/>
</svg>
  );
  
  export default GreenCheck;
  