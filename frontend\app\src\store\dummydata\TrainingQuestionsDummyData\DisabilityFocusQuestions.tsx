export type QuestionDataDisabilityFocus = {
    id: number;
    question: string;
    answer_1: string;
    answer_2: string;
    answer_3: string;
    answer_4: string;
    correct_answer: number;
  };
  
  export const disabilityFocusQuestions: QuestionDataDisabilityFocus[] = [
    {
      id: 1,
      question:
        'Which of the following Acts of Parliament considers the protected rights of a disabled person?',
      answer_1: 'Equality Act 2010',
      answer_2: 'Health and Safety Act 1974',
      answer_3: 'Private Security Industry Act 2001',
      answer_4: 'Licensing Act 2003',
      correct_answer: 1,
    },
    {
      id: 2,
      question:
        'Which of the following four options is classified as being an “invisible” disability?',
      answer_1: 'Cerebral Palsy',
      answer_2: 'Autism',
      answer_3: 'Down’s Syndrome',
      answer_4: 'Vision impairment',
      correct_answer: 2,
    },
    {
      id: 3,
      question:
        'Which of the following actions should we adopt when dealing with a disabled customer?',
      answer_1: 'Talk to one of their family members or carers',
      answer_2: 'Passively wait for them to speak to you',
      answer_3: 'Introduce yourself and ask if you can help',
      answer_4: 'Ask them to tell you all about their disability',
      correct_answer: 3,
    },
    {
      id: 4,
      question: 'What can we try to provide for disabled customers?',
      answer_1: 'A map of the venue',
      answer_2: 'Personal escort',
      answer_3: 'Priority access',
      answer_4: 'Reasonable adjustments',
      correct_answer: 4,
    },
    {
      id: 5,
      question: 'Which of the following options is not a primary source of information about disability-related considerations?',
      answer_1: 'Scope UK',
      answer_2: 'Mind',
      answer_3: 'Your local MP’s website',
      answer_4: 'The World Health Organisation',
      correct_answer: 3,
    },
  ];
  