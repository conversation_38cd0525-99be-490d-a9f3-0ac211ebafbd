import { Button, Text, View } from 'reshaped';

import { useModalAction } from 'src/context/ModalContext';

const FilterButton = () => {
  const { openModal } = useModalAction();

  return (
    <Button
      color='black'
      rounded={true}
      className='!bg-[#323c58]'
      onClick={() => openModal('FILTERS_CLIENT')}
    >
      <View direction={'row'} justify={'center'} align={'center'} gap={1}>
        <span className='material-icons-outlined'>tune</span>

        <Text className='w-99 h-23 rubik font-medium text-base leading-6'>
          Filter
        </Text>
      </View>
    </Button>
  );
};

export default FilterButton;
