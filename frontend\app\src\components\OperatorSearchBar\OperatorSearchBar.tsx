// @ts-nocheck
import { useContext } from 'react';
import { TextField, View, Text, Button, useToggle, Badge } from 'reshaped';
import JobsFilter from '../JobsFilter/JobsFilter';
import { JobContext } from 'src/context/JobContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import { useNavigate } from 'react-router-dom';

const OperatorSearchBar: React.FC = () => {
  const navigate = useNavigate();
  const { handleSearch, toggleEmergencyHire, totalCount, favouritesCount, appliedJobsCount, search, resetOuterFilters, filters } =
    useContext(JobContext);

  const { active, activate, deactivate } = useToggle(false);
  const isMapView = window.location.pathname === '/search-jobs-map';
  const isFavoritePage = window.location.pathname === '/favorite-jobs';
  const isAppliedPage = window.location.pathname === '/applied-jobs';
  const isSearchPage = window.location.pathname === '/search-jobs';

  const resultsCount = isAppliedPage ? appliedJobsCount : isFavoritePage ? favouritesCount : totalCount;

  const innerFilters = { ...filters };

  if (isFavoritePage || isMapView || isAppliedPage || isSearchPage) {
    delete innerFilters.is_favorite;
    delete innerFilters.is_emergency_hire;
  }

  const outerFilters = { search, is_emergency_hire: innerFilters.is_emergency_hire };
  const innerFilterCount = Object.values(innerFilters).reduce((filterCount, currentFilter) => {
    if (!!currentFilter && currentFilter?.length !== 0) filterCount++;
    return filterCount;
  }, 0);

  const outerFilterCount = Object.values(outerFilters).reduce((filterCount, currentFilter) => {
    if (!!currentFilter && currentFilter?.length !== 0) filterCount++;
    return filterCount;
  }, 0);

  return (
    <>
      <View
        height={'220px'}
        width={'100%'}
        borderRadius={'circular'}
        className='bg-opacity-96 flex  flex-col gap-[16px] bg-white p-10  shadow-md lg:mx-auto lg:hidden  lg:w-[380px]'
      >
        <View
          justify={'center'}
          align={'center'}
          borderRadius='circular'
          padding={1}
          className='border-1 h-[56px] w-full border border-solid border-gray-300 lg:w-[380px] '
          maxWidth='100%'
        >
          <TextField
            defaultValue={search}
            size='medium'
            variant='headless'
            startSlot={<span className='material-icons-outlined'>search</span>}
            name='searchInput'
            placeholder='Search for example “CCTV from London”'
            className='w-10/12 !pl-2'
            onChange={({ value }) => handleSearch(value)}
          />
        </View>
        <View className='flex w-[auto] flex-row justify-between gap-[12px] lg:w-[380px] '>
          {!isMapView && (
            <Button
              onClick={() => {
                navigate('/search-jobs-map');
              }}
              variant='outline'
              rounded={true}
              className='self-strech flex h-[40px] w-[100%] lg:w-[160px]'
            >
              <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                <span className='material-icons-outlined'>map</span>
                <Text className=' rubik text-base font-medium leading-6'>Map search</Text>
              </View>
            </Button>
          )}
          {isMapView && (
            <Button
              onClick={() => {
                navigate('/search-jobs');
              }}
              variant='outline'
              rounded={true}
              className='self-strech flex h-[40px] w-[100%] sm:w-[160px]'
            >
              <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                <span className='material-icons-outlined'>grid_on</span>
                <Text className=' rubik text-base font-medium leading-6'>Grid search</Text>
              </View>
            </Button>
          )}
          <Badge.Container>
            {innerFilterCount !== 0 && (
              <Badge className='cursor-default border border-[#9C9999] bg-[#F4F5F7]' rounded>
                <Text className='rubik'>{innerFilterCount}</Text>
              </Badge>
            )}
            <Button color='black' rounded={true} className='!bg-[#323c58]' onClick={activate}>
              <div className='flex flex-row items-center gap-1'>
                <span className='material-icons-outlined'>tune</span>
                <Text className='w-99 h-23 rubik text-sm font-medium leading-6 lg:text-base'>Filter</Text>
              </div>
            </Button>
          </Badge.Container>
        </View>
        <View className='flex w-full flex-row items-center justify-between  gap-[12px] sm:mx-auto  lg:w-[360px]'>
          <Text className='rubik leading-16 text-left text-base font-medium tracking-normal'>{resultsCount} Results</Text>
          <Button
            onClick={() => toggleEmergencyHire(isFavoritePage)}
            variant='ghost'
            icon={() => (
              <span
                className={`${filters.is_emergency_hire ? 'material-icons' : 'material-icons-outlined'} -mt-0.5 mr-[3px] text-[20px] text-[#CB101D]`}
              >
                {filters?.is_emergency_hire ? 'notifications_active' : 'notifications'}
              </span>
            )}
            className='btn-no-hover !bg-transparent'
          >
            <Text className=' rubik text-base font-medium leading-6'>Emergency Hire</Text>
          </Button>
        </View>
      </View>
      <View
        height={'104px'}
        width={'100%'}
        borderRadius={'circular'}
        direction={'row'}
        align={'center'}
        className=' bg-opacity-96 hidden w-full max-w-[1320px]  justify-between bg-white px-6 shadow-md lg:flex'
      >
        <View direction={'row'} justify={'start'} align={'center'} gap={8}>
          <View
            direction={'row'}
            justify={'center'}
            align={'center'}
            height='56px'
            width='360px'
            borderRadius='circular'
            padding={1}
            className='border-1 border border-solid border-gray-300 bg-[#F8F8F8]'
            maxWidth='100%'
          >
            <TextField
              defaultValue={search}
              size='medium'
              variant='headless'
              startSlot={<span className='material-icons-outlined'>search</span>}
              name='searchInput'
              placeholder='Search for example “CCTV from London”'
              className='w-10/12 !pl-2'
              onChange={({ value }) => handleSearch(value)}
            />
          </View>
          <View height={'20px'} justify={'center'} width={'250px'}>
            <Text className='rubik text-left text-base font-medium leading-5 tracking-normal'>
              <span className='rubik text-[14px] font-medium leading-5 text-[#3C455D]'>{resultsCount}</span> results
            </Text>
          </View>
        </View>

        <View>
          <View className='sm:hidden lg:flex' direction={'row'} justify={'center'} align={'center'} gap={4}>
            <Button
              onClick={() => toggleEmergencyHire(isFavoritePage)}
              variant='ghost'
              icon={() => (
                <span
                  className={`${filters.is_emergency_hire ? 'material-icons' : 'material-icons-outlined'} -mt-0.5 mr-[3px] text-[20px] text-[#CB101D]`}
                >
                  {filters?.is_emergency_hire ? 'notifications_active' : 'notifications'}
                </span>
              )}
              className='btn-no-hover !bg-transparent'
            >
              <Text className='w-99 h-23 rubik text-base font-medium leading-6'>Emergency Hire</Text>
            </Button>
            {!isMapView && (
              <Button
                onClick={() => {
                  navigate('/search-jobs-map');
                }}
                variant='outline'
                rounded={true}
                className='self-strech flex h-[40px] w-[100%] sm:w-[160px]'
              >
                <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                  <span className='material-icons-outlined text-[20px]'>map</span>
                  <Text className=' rubik text-base font-medium leading-6 '>Map search</Text>
                </View>
              </Button>
            )}
            {isMapView && (
              <Button
                onClick={() => {
                  navigate('/search-jobs');
                }}
                variant='outline'
                rounded={true}
                className='self-strech flex h-[40px] w-[100%] sm:w-[160px]'
              >
                <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                  <span className='material-icons-outlined'>grid_on</span>
                  <Text className=' rubik text-base font-medium leading-6'>Grid search</Text>
                </View>
              </Button>
            )}

            <Badge.Container>
              {innerFilterCount !== 0 && (
                <Badge className='cursor-default border border-[#9C9999] bg-[#F4F5F7]' rounded>
                  <Text className='rubik'>{innerFilterCount}</Text>
                </Badge>
              )}
              <Button color='black' rounded={true} className='!bg-[#323c58]' onClick={activate}>
                <div className='flex flex-row items-center gap-1'>
                  <span className='material-icons-outlined text-[20px]'>tune</span>
                  <Text className='w-99 h-23 rubik text-sm font-medium leading-6 lg:text-base'>Filter</Text>
                </div>
              </Button>
            </Badge.Container>

            <Button
              onClick={() => {
                resetOuterFilters();
                Array.from(document.getElementsByTagName('input')).forEach((el) => (el.value = ''));
              }}
              variant='ghost'
              className='btn-no-hover !bg-transparent'
            >
              <Text className='w-99 h-23 rubik text-base font-medium leading-6 underline'>Reset</Text>
            </Button>
          </View>
        </View>
      </View>
      <JobsFilter active={active} deactivate={deactivate} />
    </>
  );
};

export default OperatorSearchBar;
