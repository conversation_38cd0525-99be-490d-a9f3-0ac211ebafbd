import { Button, Text, Image, View } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { headerLogo } from '../../../assets/images';
import '../HeaderMenu/HeaderMenu.css'
const SmallScreenHeaderMenu = () => {
  const navigate = useNavigate();

  return (
    <nav className='w-full h-full flex flex-col  items-center justify-center mt-[50px]'>
      <View className='flex flex-row justify-between w-full '>
        <Button
          
          onClick={() => navigate('/')}
          icon={() => (
            <span className='material-icons align-middle text-500 '>close</span>
          )}
          className='flex items-center justify-end ml-auto mr-[5%] btn-no-hover !bg-transparent'
        ></Button>
      </View>
      <View className=' flex flex-col items-center justify-center mt-[90px]'>
        <Image src={headerLogo} className='w-[200px] h-auto' />
        <Button
          onClick={() => navigate('/')}
          className='mt-[50px] btn-no-hover !bg-transparent'
        >
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 '>
            Home Page
          </Text>
        </Button>
        <Button  onClick={() => navigate('/security-operatives')} className='btn-no-hover !bg-transparent'>
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 '>
            Security Operatives
          </Text>
        </Button>
        <Button  onClick={() => navigate('/clients')} className='btn-no-hover !bg-transparent'>
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 '>
            Clients
          </Text>
        </Button>
        <Button
          onClick={() => navigate('/surely-pro?type=surleypro')}
          className='btn-no-hover !bg-transparent'
        >
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5'>
            Surely Pro
          </Text>
        </Button>
        <Button  onClick={() => navigate('/inclusivity-pledge')} className='btn-no-hover !bg-transparent'>
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5'>
            Inclusivity Pledge
          </Text>
        </Button>
        <Button  onClick={() => navigate('/about-us')} className='btn-no-hover !bg-transparent'>
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5'>
            About Us
          </Text>
        </Button>
        <Button  onClick={() => navigate('/security-jobs')} className='btn-no-hover !bg-transparent'>
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5'>
            Security Jobs
          </Text>
        </Button>
      </View>
    </nav>
  );
};

export default SmallScreenHeaderMenu;
