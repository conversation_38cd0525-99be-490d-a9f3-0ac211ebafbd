// @ts-nocheck
import { useState, useContext, useEffect } from 'react';
import { Button, DropdownMenu, Text, View, Avatar, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from 'src/context/AuthContext';
import { toast } from 'react-hot-toast';
import GuestUpgradeToast from 'src/components/Toast/GuestUpgradeToast';

// Import only the icons we need
import clientdropdownicon3 from '../../../assets/icons/clientdropdownicon/clientdropdownicon3.svg';
import clientdropdownicon7 from '../../../assets/icons/clientdropdownicon/clientdropdownicon7.svg';
import clientdropdownicon8 from '../../../assets/icons/clientdropdownicon/clientdropdownicon8.svg';

const GuestHeaderButton = () => {
  const navigate = useNavigate();
  const { active, activate, deactivate } = useToggle();
  const { user, unAuthenticateUser } = useContext(AuthContext);
  const toast = useToast();
  const name = user?.name ? user?.name : user?.profile?.name;
  const lastname = name?.split(' ').pop()?.charAt(0).toUpperCase() + name?.split(' ').pop()?.slice(1);
  const firstname = name?.split(' ').shift()?.charAt(0).toUpperCase() + name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;
  const initialNameFirstCharAt = (firstname?.[0] || '') + (lastname?.[0] || '');

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <DropdownMenu onOpen={activate} onClose={deactivate} contentGap={6} position='bottom'>
      <DropdownMenu.Trigger>
        {(attributes) => (
          <Button type='submit' rounded={true} className='!bg-[#323c58] h-[auto]' attributes={attributes}>
            <View className='flex flex-row justify-center items-center gap-2'>
              <Avatar
                size={8}
                initials={initialNameFirstCharAt}
                color='neutral'
                variant='faded'
                className='rubik font-medium text-[14px] leading-[20px] text-[#323C58]'
              />
              {!isSmallScreen && <Text className='rubik font-medium text-[15px] leading-[20px] text-[#FFFFFF]'>{initialName}</Text>}
              <span className='material-icons-outlined text-white'>{active ? 'close' : 'menu'}</span>
            </View>
          </Button>
        )}
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Section>
          <DropdownMenu.Item onClick={() => navigate('/search-operator')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium whitespace-nowrap'>
              <img src={clientdropdownicon3} />
              Find Security Operatives
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item 
            onClick={() => navigate('/client-settings-general')}
          >
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium text-[#0B80E7]'>
              <span className='material-icons-outlined'>upgrade</span>
              Become a Client
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/client-settings')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              <span className='material-icons-outlined text-base leading-[16px] text-[#323C58]'>settings</span>
              Account settings
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
        <DropdownMenu.Section>
          <DropdownMenu.Item
            onClick={() => {
              unAuthenticateUser();
              navigate('/');
            }}
          >
            <Text className='flex items-center gap-2 rubik font-[14px] leading-[20px] font-medium'>
              <img src={clientdropdownicon8} />
              Log out
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};

export default GuestHeaderButton;
