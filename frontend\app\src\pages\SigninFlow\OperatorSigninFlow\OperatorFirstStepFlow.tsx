// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Button, Text, View, TextField, Image, Actionable, Tooltip, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { headerLogo } from '../../../assets/images';

import { useRegistrationContext } from 'src/context/RegistrationContext';
import { getSiaLicenceValidation } from 'src/services/settings';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorFirstStepFlow: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [isCameraOpen, setIsCameraOpen] = useState(false);

  const [stream, setStream] = useState<MediaStream | null>(null);
  const { active, activate, deactivate } = useToggle(false);

  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [uploadedPhoto, setUploadedPhoto] = useState<string | null>(null);
  const [siaLicenceNumber, setSiaLicenceNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState({ dd: '', mm: '', yyyy: '' });

  const { setOperatorRegisterData } = useRegistrationContext();

  const [formValid, setFormValid] = useState({
    siaLicenceNumber: true,
    expiryDate: true,
    capturedImage: true,
  });

  const [siaLicenceTypes, setSiaLicenceTypes] = useState([]);
  const [getType, setGetType] = useState();
  const [getExpiryDate, setGetExpiryDate] = useState();

  useEffect(() => {
    if (siaLicenceNumber && siaLicenceNumber.length === 16) {
      const formattedSiaLicenceNumber = siaLicenceNumber.replace(/\s/g, '');
      getSiaLicenceValidation(formattedSiaLicenceNumber).then((result: any) => {
        if (result.error) {
          const id = toast.show({
            title: 'SIA licence not verified:',
            text: 'Your SIA licence verification has not been accepted. Please review the provided information and try again.',
            icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
            startSlot: (
              <Button variant='ghost' className='mb-[80px] h-[21px] w-[21px]' onClick={() => toast.hide(id)}>
                <Text className='text-[12px]'>X</Text>
              </Button>
            ),
          });
        } else {
          setGetExpiryDate(result.expiry_date);
          setGetType(result.licence_sector);
          const predefinedOrder = ['Close Protection', 'Public Space Surveillance', 'Door Supervision', 'Security Guard'];
          const index = predefinedOrder.indexOf(result.licence_sector);
          const orderedLicenceTypes: any = index !== -1 ? predefinedOrder.slice(index) : predefinedOrder;
          setSiaLicenceTypes(orderedLicenceTypes);
        }
      });
    }
  }, [siaLicenceNumber]);

  useEffect(() => {
    if (getExpiryDate) {
      const apiExpiryDate = new Date(getExpiryDate);
      const formattedExpiryDate = {
        dd: String(apiExpiryDate.getDate()).padStart(2, '0'),
        mm: String(apiExpiryDate.getMonth() + 1).padStart(2, '0'),
        yyyy: String(apiExpiryDate.getFullYear()),
      };
      setExpiryDate(formattedExpiryDate);
    }
  }, [getExpiryDate]);

  const handleDateChange = (value: string, fieldName: 'dd' | 'mm' | 'yyyy') => {
    const numericValue = value.replace(/\D/g, '');
    const numberValue = parseInt(numericValue, 10);

    if (fieldName === 'mm') {
      if (numberValue >= 1 && numberValue <= 12) {
        setExpiryDate((prevExpiryDate) => ({
          ...prevExpiryDate,
          [fieldName]: numericValue,
        }));
      }
    } else {
      if (
        numericValue.length === 0 ||
        (numericValue.length <= 2 && numberValue >= 0 && numberValue <= 31) ||
        (numericValue.length <= 4 && fieldName === 'yyyy')
      ) {
        setExpiryDate((prevExpiryDate) => ({
          ...prevExpiryDate,
          [fieldName]: numericValue,
        }));
      }
    }
  };

  const openCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
      });
      setStream(cameraStream);
      setIsCameraOpen(true);
    } catch (error) {
      console.error('Error accessing camera:', error);
    }
  };

  const closeCamera = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    setStream(null);
    setIsCameraOpen(false);
  };

  const takePicture = () => {
    if (stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      const canvas = document.createElement('canvas');
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const capturedDataURL = canvas.toDataURL('image/jpeg');
        setCapturedImage(capturedDataURL);
        const id1 = toast.show({
          title: 'SIA licence card captured successfully:',
          text: "Thank you! We're now processing the front image of your SIA licence for document verification.",
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: (
            <Button variant='ghost' className='mb-[80px] h-[21px] w-[21px]' onClick={() => toast.hide(id1)}>
              <Text className='text-[12px]'>x</Text>
            </Button>
          ),
        });
      }
    }
  };

  useEffect(() => {
    if (isCameraOpen && stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      videoElement.srcObject = stream;
    }
  }, [isCameraOpen, stream]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleUploadPhoto = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];
      processFile(file);
    }
  };

  const processFile = (file: File) => {
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > 5) {
      const id2 = toast.show({
        title: 'Error.',
        text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id2)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
    } else {
      const reader = new FileReader();
      reader.onloadend = () => {
        const id3 = toast.show({
          title: 'SIA licence card uploaded successfully:',
          text: "Thank you! We're now processing the front image of your SIA licence for document verification.",
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: (
            <Button variant='ghost' className='mb-[80px] h-[21px] w-[21px]' onClick={() => toast.hide(id3)}>
              <Text className='text-[12px]'>X</Text>
            </Button>
          ),
        });
        setUploadedPhoto(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleNextStep = () => {
    if (!siaLicenceNumber) {
      const id4 = toast.show({
        title: 'Enter SIA Licence Number:',
        text: 'This field is required.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id4)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
      return setFormValid((formValid) => ({
        ...formValid,
        siaLicenceNumber: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, siaLicenceNumber: true }));

    const currentDate = new Date();
    const inputDate = new Date(parseInt(expiryDate.yyyy, 10), parseInt(expiryDate.mm, 10) - 1, parseInt(expiryDate.dd, 10));

    if (inputDate < currentDate) {
      return setFormValid((formValid) => ({
        ...formValid,
        expiryDate: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, expiryDate: true }));

    if (expiryDate.dd === '' || expiryDate.mm === '' || (expiryDate.yyyy.length !== 4 && parseInt(expiryDate.yyyy, 10))) {
      return setFormValid((formValid) => ({
        ...formValid,
        expiryDate: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, expiryDate: true }));

    if (!capturedImage && !uploadedPhoto) {
      const id5 = toast.show({
        title: 'Enter a photo of your SIA licence card:',
        text: 'This field is required.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id5)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
      return setFormValid((formValid) => ({
        ...formValid,
        capturedImage: false,
      }));
    } else setFormValid((formValid) => ({ ...formValid, capturedImage: true }));

    const dualDigitExpiryDateDay = expiryDate.dd.length === 1 ? '0' + expiryDate.dd : expiryDate.dd;
    const dualDigitExpiryDateMonth = expiryDate.mm.length === 1 ? '0' + expiryDate.mm : expiryDate.mm;
    const dualDigitExpiryDateYear = expiryDate.yyyy.length === 1 ? '0' + parseInt(expiryDate.yyyy, 10) : expiryDate.yyyy;

    setOperatorRegisterData((prevState: any) => ({
      ...prevState,
      firstStep: {
        expiryDate: `${dualDigitExpiryDateYear}-${dualDigitExpiryDateMonth}-${dualDigitExpiryDateDay}`,
        siaLicenceNumber: siaLicenceNumber,
        capturedImage: capturedImage || uploadedPhoto,
        siaLicenceTypes: siaLicenceTypes,
      },
    }));
    closeCamera();

    navigate('/second-step-validation-operator');
  };

  return (
    <View className='mt-[20px] flex flex-col overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} className='btn-no-hover ' icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <CloseAccountCreatorModal active={active} deactivate={deactivate} />
      <View className='mx-auto flex flex-col  sm:w-[536px]'>
        <Text className='font-rufina-stencil leading-40  text-[32px] font-normal text-[#1A1A1A] lg:text-center'>Your SIA licence</Text>
        <Text className='rubik mt-[4px] text-base font-normal leading-[24px] text-[#323C58] lg:text-center'>
          You’ve got what it takes, get noticed. Your information will always be 100% secure.
        </Text>
        <View className='flex flex-row '>
          <Text className='rubik mt-[12px] text-[15px] font-medium leading-5 text-[#1A1A1A] '>SIA licence number</Text>

          <Tooltip text='On your physical licence card, your SIA licence number is printed on the top- left corner. Its a unique identifier specific to you.'>
            {(attributes) => (
              <Actionable attributes={attributes} as='div'>
                <div className='ml-[8px] mt-[10px] flex h-[22px] w-[22px] items-center justify-center rounded-full border bg-[#C7CDDB]'>
                  <span className='material-icons-outlined text-[12px]'>question_mark</span>
                </div>
              </Actionable>
            )}
          </Tooltip>
        </View>
        <TextField
          name='SIA'
          placeholder='XXXX XXXX XXXX XXXX'
          className='mt-[5px] h-[48px] w-[full]'
          value={siaLicenceNumber}
          // onChange={(event) => setSiaLicenceNumber(event.value)}
          onChange={(event) => {
            setSiaLicenceNumber(event.value);
            if (event.value !== '') {
              setFormValid((prevState) => ({ ...prevState, siaLicenceNumber: true }));
            }
          }}
        />
        {!formValid.siaLicenceNumber && (
          <Text className='rubik mt-[12px] text-[15px] font-normal leading-5  text-red-400'>SIA license number is required.</Text>
        )}
        <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-[#444B5F]'>
          Double check your licence number so that we can verify you
        </Text>
        <View className='flex flex-row'>
          <Text className='rubik mt-[16px] mt-[16px] text-[15px] font-medium leading-5 text-[#1A1A1A]'>SIA licence expiry date</Text>

          <Tooltip text='Renew your licence before it expires to accept client bookings. Youll receive reminders one month and one week prior to expiry.'>
            {(attributes) => (
              <Actionable attributes={attributes} as='div'>
                <div className='ml-[8px] mt-[12px] flex h-[22px] w-[22px] items-center justify-center rounded-full border bg-[#C7CDDB]'>
                  <span className='material-icons-outlined text-[12px]'>question_mark</span>
                </div>
              </Actionable>
            )}
          </Tooltip>
        </View>
        {/* <View className='flex flex-row'>
          <TextField
            name='DD'
            placeholder='DD'
            className='sm:w-[64px] h-[48px] mt-[5px]'
            value={expiryDate.dd}
            onChange={(event) => handleDateChange(event.value, 'dd')}
          />
          <TextField
            name='MM'
            placeholder='MM'
            className='ml-[12px] sm:w-[64px] h-[48px] mt-[5px]'
            value={expiryDate.mm}
            onChange={(event) => handleDateChange(event.value, 'mm')}
          />
          <TextField
            name='YY'
            placeholder='YYYY'
            className='ml-[12px] sm:w-[64px] h-[48px] mt-[5px]'
            value={expiryDate.yyyy}
            onChange={(event) => handleDateChange(event.value, 'yyyy')}
          />
        </View> */}
        <View className='flex flex-row'>
          <div className='mt-[5px] flex h-[48px] w-[64px] items-center justify-center rounded-[4px] border border-[#BBC1D3] bg-[#FFF]'>
            <div className='input-value'>{expiryDate.dd}</div>
          </div>
          <div className='ml-[12px] mt-[5px] flex h-[48px] w-[64px] items-center justify-center rounded-[4px] border border-[#BBC1D3] bg-[#FFF]'>
            <div className='input-value'>{expiryDate.mm}</div>
          </div>
          <div className='ml-[12px] mt-[5px] flex h-[48px] w-[64px] items-center justify-center rounded-[4px] border border-[#BBC1D3] bg-[#FFF]'>
            <div className='input-value'>{expiryDate.yyyy}</div>
          </div>
        </View>

        {!formValid.expiryDate && (
          <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>
            {getExpiryDate ? 'SIA license is expired.' : 'SIA license expiry is required.'}
          </Text>
        )}
        <Text className='rubik mt-[16px] mt-[16px] text-[15px] font-medium leading-5 text-[#1A1A1A]'>
          Take a photo of your SIA licence card and upload
        </Text>

        <View className='mt-[10px] flex flex-col items-center  justify-between sm:flex-row'>
          <View className='flex w-full flex-col sm:w-auto'>
            <button
              className='bg-background-base flex h-[48px] cursor-pointer items-center justify-center gap-2 self-stretch rounded  border border-[#BBC1D3] !bg-[white] px-4 py-2 text-[15px] !text-[#000000] sm:w-[180px]'
              onClick={() => {
                const uploadInput = document.getElementById('photo-upload');
                if (uploadInput) {
                  uploadInput.click();
                }
              }}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              disabled={!!capturedImage || !!uploadedPhoto}
            >
              <span className='material-icons-outlined mt-[-2px] align-middle text-[#0B80E7]'>file_download</span>
              <span className='align-middle'>Drop or browse</span>
              <input id='photo-upload' type='file' accept='image/*' onChange={handleUploadPhoto} style={{ display: 'none' }} />
            </button>

            <View className='mb-3 mt-[12px] flex items-center'>
              <hr className='bg-neutral-faded mr-2 flex-grow' />
              <span className='rubik text-black-white-black text-sm'>Or</span>
              <hr className='bg-neutral-faded ml-2 flex-grow' />
            </View>

            <View className='flex flex-row items-center'>
              <div className='flex w-full flex-col sm:mr-[20px] sm:w-auto'>
                <Button
                  variant='outline'
                  icon={() => <span className='material-icons-outlined mt-[-3px] text-[#323C58]'>{isCameraOpen ? 'close' : 'videocam'}</span>}
                  className='border-neutral bg-background-base flex h-[48px] cursor-pointer items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2  text-[15px] !text-[#000000] sm:w-[180px]'
                  onClick={isCameraOpen ? closeCamera : openCamera}
                >
                  <Text className='rubik text-[15px] font-medium leading-[24px] text-[#323C58]'>
                    {isCameraOpen ? 'Close Camera' : 'Take a picture'}
                  </Text>
                </Button>
                {isCameraOpen && (
                  <Button
                    variant='outline'
                    icon={() => <span className='material-icons-outlined mt-[-1px]'>photo</span>}
                    onClick={takePicture}
                    className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded  border !bg-[white] px-4 py-2'
                    disabled={!isCameraOpen || !!capturedImage || !!uploadedPhoto}
                  >
                    <Text className='rubik text-[15px] font-medium leading-[24px] text-[#323C58]'>Take Picture</Text>
                  </Button>
                )}

                {!formValid.capturedImage && (
                  <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Photo is required.</Text>
                )}
              </div>
            </View>
          </View>

          <View className='flex items-center'>
            {isCameraOpen && (
              <div className='relative flex items-center justify-center'>
                <video id='camera-feed' className='h-[230px] w-[230px]' autoPlay playsInline></video>
                {(capturedImage || uploadedPhoto) && (
                  <button
                    className='absolute right-0 top-0 flex h-[22px] w-[22px] cursor-pointer items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                    onClick={() => {
                      setCapturedImage(null);
                      setUploadedPhoto(null);
                    }}
                  >
                    <span className='material-icons text-[16px]'>close</span>
                  </button>
                )}
              </div>
            )}
            <div className='relative flex h-[230px] w-[230px] items-center justify-center' onDragOver={handleDragOver} onDrop={handleDrop}>
              {uploadedPhoto && (
                <>
                  <img src={uploadedPhoto} alt='Uploaded' />
                  <button
                    className='absolute right-0 top-0 flex h-[22px] w-[22px] cursor-pointer items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                    onClick={() => {
                      setUploadedPhoto(null);
                    }}
                  >
                    <span className='material-icons text-[16px]'>close</span>
                  </button>
                </>
              )}
            </div>

            {capturedImage && !isCameraOpen && (
              <div className='relative flex h-[230px] w-[230px] items-center justify-center'>
                <img src={capturedImage} alt='Captured' />
                <button
                  className='absolute right-0 top-0 flex h-[22px] w-[22px] cursor-pointer items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                  onClick={() => {
                    setCapturedImage(null);
                  }}
                >
                  <span className='material-icons text-[16px]'>close</span>
                </button>
              </div>
            )}
          </View>
        </View>

        <Text className='rubik font-weight-400 leading-20 mt-[8px] text-base font-normal text-[#3C455D]'>
          · Please make sure it’s bright and clear
        </Text>
        <Text className='rubik font-weight-400 leading-20 text-base font-normal text-[#3C455D]'>· All corners of the document should be visible</Text>
      </View>

      <View className='mt-[20px] flex  flex-col sm:mt-[123px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full bg-[#0B80E7] lg:w-[330px]' />
          <div className='h-full w-full bg-[#D7D4D4]' />
        </div>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            icon={() => <span className='material-icons-outlined text-[19px] text-[#14171F]'>close</span>}
            onClick={handleGoBack}
            className='bg-background-base flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[white]  px-4  py-2 sm:w-[103px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#14171F]'>Close</Text>
          </Button>

          <Button
            endIcon={() => <span className='material-icons-outlined text-[18px] text-[#FFF]'>arrow_forward_ios</span>}
            onClick={handleNextStep}
            className='border-neutral bg-background-base flex h-[48px] items-center justify-center self-stretch self-stretch rounded-[8px] border  !bg-[#0B80E7] sm:w-[135px] '
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#FFF]'> Next Step</Text>
          </Button>
        </View>
        <div className='mt-[30px] flex items-center justify-center sm:mt-[0px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default OperatorFirstStepFlow;
