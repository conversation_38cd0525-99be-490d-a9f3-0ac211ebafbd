export type QuestionDataInclusivityPledge = {
    id: number;
    question: string;
    answer_1: string;
    answer_2: string;
    answer_3: string;
    answer_4: string;
    correct_answer: number;
  };
  
  export const inclusivityPledgeQuestions: QuestionDataInclusivityPledge[] = [
    {
      id: 1,
      question:
        'How many LGBT+ people feel unable to disclose their sexual or gender identity in the workplace? ',
      answer_1: '1 in 10 ',
      answer_2: '1 in 5',
      answer_3: '1 in 8  ',
      answer_4: '1 in 20',
      correct_answer: 3,
    },
    {
      id: 2,
      question:
        'When hearing a discriminatory comment or phrase, how should you react? ',
      answer_1: 'Say “You can’t say that sort of thing anymore.”',
      answer_2: 'Ignore the comment to avoid conflict and move on.',
      answer_3: 'Don’t try to embarrass the person, just ask politely but firmly if they realised what they said was potentially offensive or discriminatory.  ',
      answer_4: 'Call 999 as soon as possible and ask for the police.',
      correct_answer: 3,
    },
    {
      id: 3,
      question:
        'How do we deal with unconscious biases? ',
      answer_1: 'Ignore them, it’s easier and avoids having to deal with the problem. ',
      answer_2: 'Surface, accept and reflect on them - and then discuss with colleagues to challenge, understand and overcome them.  ',
      answer_3: 'Work on your first instinct and don’t take colleagues’ opinions into consideration.',
      answer_4: 'Worry about surfacing your own unconscious biases and keep them well hidden.  ',
      correct_answer: 2,
    },
    {
      id: 4,
      question:
        'When talking to a disabled person, what should you do? ',
      answer_1: 'Speak loudly, regardless of whether you have been asked to do so or not.',
      answer_2: 'Ask them in a kind and compassionate voice what happened to them.',
      answer_3: 'Treat them with exactly the same level of respect as you offer to every other customer or colleague.',
      answer_4: 'Immediately talk to their carer, companion or interpreter to establish their needs. ',
      correct_answer: 3,
    },
    {
      id: 5,
      question:
        'When should you ask what someone’s disability is? ',
      answer_1: 'When you know that you’re going to have to know what it is to help them. ',
      answer_2: 'Never. Instead, explain that you are there to help in any way they need, and try to be unintrusive.',
      answer_3: 'When they tell you that they need to urgently use the disabled toilet.',
      answer_4: 'When they approach the door for the first time to gain access to the venue. ',
      correct_answer: 2,
    },
    {
        id: 6,
        question:
          'When is it okay to use the term “Queer”',
        answer_1: 'When you have been specifically invited or asked to use the term. ',
        answer_2: 'When you’re having a joke with colleagues and do not intend offence. ',
        answer_3: 'If you know the person identifies as LGBT+ and think it’s OK to do so.',
        answer_4: 'You’re ejecting someone from a venue and you think they deserve it. ',
        correct_answer: 1,
      },
      {
        id: 7,
        question:
          'A colleague asks you to refer to them in a different way through the use of pronouns. ‘They/them’ instead of ‘he/him’, for instance. What do you do?',
        answer_1: 'Refuse their demands and continue to use whatever pronouns you like. ',
        answer_2: 'Inform them that you’re really busy and you can’t be held responsible for using incorrect pronouns.',
        answer_3: 'Apologise genuinely, and make every reasonable effort to use the correct pronouns in the right spirit of intent.',
        answer_4: 'Repeatedly apologise out of extreme embarrassment in order to make sure your colleague knows you’re really sorry.  ',
        correct_answer: 3,
      },
      {
        id: 8,
        question:
          'When someone reports an incident of discrimination that has happened to them, what should you do? ',
        answer_1: 'Keep calm, listen intently and display understanding.',
        answer_2: 'Tell the person you believe them, and that they are safe and welcome. ',
        answer_3: 'Record everything, either by notepad or through Body-Worn CCTV. ',
        answer_4: 'All of the above. ',
        correct_answer: 4,
      },
      {
        id: 9,
        question:
          'What are the protected characteristics legally covered by the Equality Act 2010. ',
        answer_1: 'Age, gender identity, race and country of residence.',
        answer_2: 'Age; disability; gender reassignment; marriage and civil partnership; pregnancy and maternity; race, religion or belief; sex; and sexual orientation.',
        answer_3: 'Anything that someone think, say or does that other may find offensive.',
        answer_4: 'Race, religion or sexual identity. ',
        correct_answer: 2,
      },
      {
        id: 10,
        question:
          'As a Surely Pro Inclusivity Pledge badge holder, what else should we do to uphold the key principles outlined in the Equality Act 2010? ',
        answer_1: 'Actively promote equality, take positive action to redress inequality, build a culture that champions diversity and inclusion.',
        answer_2: 'Nothing really, the law states that we only need to do what is legally required of us with regards to the nine protected characteristics.',
        answer_3: 'Tell everyone to behave properly at all times and avoid mentioning diversity.',
        answer_4: 'Make a citizen’s arrest and call the police the moment you witness any form of discrimination.',
        correct_answer: 1,
      },
  ];
  