// @ts-nocheck
import React, { useState, useRef } from 'react';
import { Button, Text, View, Image, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { headerLogo } from '../../../assets/images';

import { useRegistrationContext } from 'src/context/RegistrationContext';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorThirdStepFlow: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [fileName, setFileName] = useState<any>();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { active, activate, deactivate } = useToggle(false);

  const [validFileName, setValidFileName] = useState(true);

  const { setOperatorRegisterData } = useRegistrationContext();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files: any = e.target.files;
    setFileName(files[0].name);
    if (files && files.length > 0) {
      const fileDataArray: string[] = [];
      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push(base64);
      }
      setSelectedFiles(fileDataArray);
      const id1 = toast.show({
        title: 'Photo uploaded successfully:',
        text: " Thank you! We're now processing the image for document verification.",
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='mb-[70px] h-[21px] w-[21px]' onClick={() => toast.hide(id1)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
    }
    setValidFileName(true);
  };

  const deleteFile = (indexToDelete: number) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToDelete);
    setSelectedFiles(updatedFiles);
  };

  const openFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const isButtonDisabled = selectedFiles.length > 0;

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleNextStep = () => {
    if (selectedFiles.length === 0) {
      const id2 = toast.show({
        title: 'Upload the proof of address.',
        text: 'Please upload the proof of address.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        startSlot: (
          <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id2)}>
            <Text className='text-[12px]'>X</Text>
          </Button>
        ),
      });
      setValidFileName(false);
      return;
    }

    setOperatorRegisterData((prevState) => ({
      ...prevState,
      thirdStep: {
        addressVerificationDocument: selectedFiles[0], 
      },
    }));

    navigate('/forth-step-validation-operator');
  };

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
          resolve(e.target.result);
        } else {
          reject(new Error('Failed to read file as base64.'));
        }
      };
      reader.readAsDataURL(file);
    });
  };
  const handleDragOver = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: any) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    handleFiles(files);
  };

  const handleFiles = async (files: FileList) => {
    const fileDataArray = [...selectedFiles];
    let newFileName = fileName; 

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const base64 = await readFileAsBase64(file);
      fileDataArray.push(base64);
      newFileName = file.name; 
    }

    setSelectedFiles(fileDataArray);
    setFileName(newFileName); 
  };

  return (
    <View className='mt-[20px] flex flex-col overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} className='btn-no-hover ' icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <CloseAccountCreatorModal active={active} deactivate={deactivate} />
      <View className='mx-auto flex  flex-col sm:w-[536px] '>
        <Text className='font-rufina-stencil text-[32px] font-normal leading-[40px] text-[#1A1A1A] lg:text-center'>Address verification</Text>
        <Text className='rubik mx-auto  text-base font-normal leading-[24px] text-[#323C58] lg:w-[458px] lg:text-center'>
          To verify your location, we kindly request a photo of any statement document such as water, electricity, gas, telephone, bank or similar.
        </Text>
        <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A] '>Upload proof of address</Text>

        <button
          onClick={openFileInput}
          className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border border  border-[#BBC1D3] !bg-[white] px-4 py-2 text-[15px] !text-[#000000]'
          disabled={isButtonDisabled}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {' '}
          <span className='material-icons-outlined mt-[-2px] text-[21px] text-[#323C58]'>upload</span>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#323C58]'>
            Drop or&nbsp;
            <span className='rubik text-[14px] font-medium leading-[20px] text-[#0B80E7]'>browse</span>
          </Text>
          <input type='file' ref={fileInputRef} accept='.pdf,.doc,.docx,.jpg,.jpeg,.png' style={{ display: 'none' }} onChange={handleFileChange} />
        </button>
        {!validFileName && <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Any proof of address is required.</Text>}
        <Text className='rubik font-weight-400 mt-[5px] text-base font-normal leading-[20px] text-[#3C455D]'>
          · Please make sure it’s bright and clear
        </Text>
        <Text className='rubik font-weight-400 text-base font-normal leading-[20px] text-[#3C455D]'>
          · All corners of the document should be visible
        </Text>

        <View className='mt-4 flex flex-col sm:w-[536px]'>
          {selectedFiles.length > 0 && <Text className='rubik text-base font-normal leading-5 text-[#1A1A1A]'>Selected Documents:</Text>}
          <ul>
            {selectedFiles.map((fileBase64, index) => (
              <li key={index}>
                <View className='flex flex-row items-center'>
                  <Text className='rubik text-base font-normal leading-5 text-[#1A1A1A] '>{fileName}</Text>
                  <Button
                    variant='outline'
                    icon={() => <span className='material-icons align-middle text-[17px] text-red-500 '>close</span>}
                    onClick={() => deleteFile(index)}
                    className='ml-[16px] flex h-[17px] w-[70px] items-center justify-center !bg-[#fff]'
                  >
                    <Text className='align-middle text-[13px] text-red-500'>Delete</Text>
                  </Button>
                </View>
              </li>
            ))}
          </ul>
        </View>
      </View>

      <View className='mt-[20px] flex  flex-col sm:mt-[123px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full bg-[#0B80E7] lg:w-[990px]' />
          <div className='h-full w-full bg-[#D7D4D4]' />
        </div>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            icon={() => <span className='material-icons-outlined text-[19px] text-[#14171F]'>arrow_back_ios</span>}
            onClick={handleGoBack}
            className='bg-background-base flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[white]  px-4  py-2 sm:w-[103px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#14171F]'>Back</Text>
          </Button>

          <Button
            endIcon={() => <span className='material-icons-outlined text-[18px] text-[#FFF]'>arrow_forward_ios</span>}
            onClick={handleNextStep}
            className='border-neutral bg-background-base flex h-[48px] items-center justify-center self-stretch self-stretch rounded-[8px] border  !bg-[#0B80E7] sm:w-[135px] '
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#FFF]'> Next Step</Text>
          </Button>
        </View>
        <div className='mt-[30px] flex items-center justify-center sm:mt-[0px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default OperatorThirdStepFlow;
