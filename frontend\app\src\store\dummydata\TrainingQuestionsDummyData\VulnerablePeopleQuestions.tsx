export type QuestionDataVulnerablePeople = {
    id: number;
    question: string;
    answer_1: string;
    answer_2: string;
    answer_3: string;
    answer_4: string;
    correct_answer: number;
  };
  
  export const vulnerablePeopleQuestions: QuestionDataVulnerablePeople[] = [
    {
      id: 1,
      question:
        'Which of the following is classed as vulnerable?',
      answer_1: 'Young person in the company of other young people',
      answer_2: 'Going out as a couple',
      answer_3: 'Designated driver',
      answer_4: 'An overly intoxicated individual',
      correct_answer: 4,
    },
    {
      id: 2,
      question:
        'If a person asks to speak to <PERSON><PERSON>, what does this mean?',
      answer_1: 'They feel vulnerable and require covert intervention',
      answer_2: 'Excessive alcohol consumption and require a soft drink',
      answer_3: 'Showing off that they know someone who works there ',
      answer_4: 'A bomb threat code word',
      correct_answer: 1,
    },
    {
      id: 3,
      question:
        'Which of the following is likely to make an ejected person become vulnerable?',
      answer_1: 'They are joined by a friend',
      answer_2: 'They are intoxicated and quiet',
      answer_3: 'They are intoxicated and aggressive',
      answer_4: 'They walk away after a brief outburst',
      correct_answer: 3,
    },
    {
      id: 4,
      question: 'Which of the following factors contribute directly to the vulnerability of a person on a night out in a busy city centre?',
      answer_1: 'Age',
      answer_2: 'Sobriety',
      answer_3: 'Weight',
      answer_4: 'Height',
      correct_answer: 2,
    },
    {
      id: 5,
      question: 'Which of the following is not a typical type of behaviour displayed by a vulnerable person?',
      answer_1: 'Becoming confused quickly',
      answer_2: 'Sounding distressed or flustered',
      answer_3: 'Being able to articulate their problem quickly',
      answer_4: 'Saying “YES” to every question even when not appropriate',
      correct_answer: 3,
    },
  ];
  