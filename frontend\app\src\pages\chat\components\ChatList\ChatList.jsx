import { useEffect, useState, useMemo } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { TextField, Loader } from 'reshaped';

import { useAuthContext } from 'src/context/AuthContext';
import { useChatContext } from 'src/context/ChatContext';

import ChatCard from './ChatCard';
import ChatMessages from '../ChatMessages/ChatMessages';

const ChatList = ({ navigateToMessages }) => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const fromOperatorProfile = location?.state?.operator_id;

  const [search, setSearch] = useState('');
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const { user } = useAuthContext();
  const { handleGetChats, chats, currentChat, setCurrentChat, setContract } = useChatContext();

  useEffect(() => {
    if (fromOperatorProfile) {
      if (chats?.length === 0) {
        handleGetChats().then((res) => {
          const contractlessChat = res?.find((chat) => chat.receiver_id === fromOperatorProfile && chat.contract === null && chat.job === null);
          setCurrentChat(contractlessChat);
          navigate('/chat/' + contractlessChat?.id);
          setIsLoadingChats(false);
          return;
        });
      }

      if (chats?.length > 0) {
        const contractlessChat = chats.find((chat) => chat.receiver_id === fromOperatorProfile && chat.contract === null && chat.job === null);
        if (!contractlessChat) {
          handleGetChats()?.then((res) => {
            const lastChat = res?.[0];
            if (!lastChat) {
              setIsLoadingChats(false);
              return null;
            }
            if (!currentChat?.id) {
              setCurrentChat(lastChat);
              navigate('/chat/' + lastChat?.id);
              setIsLoadingChats(false);
              return;
            }
          });
        }
        if (contractlessChat) {
          setCurrentChat(contractlessChat);
          navigate('/chat/' + contractlessChat?.id);
          setIsLoadingChats(false);
          return;
        }
      }
    }
    if (!params?.id && !chats?.[0]?.id) {
      setIsLoadingChats(true);
      handleGetChats()?.then((res) => {
        const lastChat = res?.[0];
        if (!lastChat) {
          setIsLoadingChats(false);
          return null;
        }
        if (!currentChat?.id) {
          setCurrentChat(lastChat);
          navigate('/chat/' + lastChat?.id);
          setIsLoadingChats(false);
          return;
        }
      });
    }
    if (!params.id && chats?.length > 0 && !!chats?.[0]?.id) {
      setIsLoadingChats(true);
      setCurrentChat(chats?.find((chat) => chat?.id === +params.id));
      setIsLoadingChats(false);
      return;
    }
    if (!params.id && chats?.length > 0) {
      setIsLoadingChats(true);
      setCurrentChat(chats?.find((chat) => chat?.id === +params.id));
      setIsLoadingChats(false);
      return;
    }
    if (params?.id && !chats?.[0]?.id) {
      setIsLoadingChats(true);
      handleGetChats()?.then((res) => {
        setCurrentChat(res?.find((chat) => chat?.id === +params.id));
        setIsLoadingChats(false);
      });
      return;
    }
  }, [params?.id, currentChat?.id]);

  // const findAndSetChat = async (condition) => {
  //   setIsLoadingChats(true);
  //   const res = await handleGetChats();
  //   const chat = res?.find(condition);
  //   if (chat) {
  //     setCurrentChat(chat);
  //     navigate('/chat/' + chat.id);
  //   }
  //   setIsLoadingChats(false);
  // };

  // useEffect(() => {
  //   const conditionForOperator = (chat) => chat.receiver_id == fromOperatorProfile && chat.contract === null && chat.job === null;
  //   const conditionForParams = (chat) => chat?.id == +params.id;

  //   if (fromOperatorProfile) {
  //     if (chats?.length === 0 || !chats.find(conditionForOperator)) {
  //       findAndSetChat(conditionForOperator);
  //     }
  //   } else if (!params?.id && !chats?.[0]?.id) {
  //     findAndSetChat(() => true);
  //   } else if (params?.id && !chats?.[0]?.id) {
  //     findAndSetChat(conditionForParams);
  //   } else if (!params.id && chats?.length > 0 && !!chats?.[0]?.id) {
  //     findAndSetChat(conditionForParams);
  //   }
  // }, [params?.id, currentChat?.id]);

  const searchedChats = useMemo(() => {
    return chats.filter((chat) => {
      const nameSearched = chat?.receiver_id === user?.profile?.id 
        ? chat?.sender?.name 
        : chat?.receiver?.name;
      return nameSearched?.toLowerCase().includes(search?.toLowerCase());
    });
  }, [chats, search, user?.profile?.id]);

  return (
    <div className='h-full w-full rounded-lg	rounded-b-none border border-[#DFE2EA] bg-white lg:h-[777px] lg:w-[424px]'>
      <div className='border-b border-[#DFE2EA] px-3 py-4 lg:h-[72px] lg:p-[24px]'>
        <p className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A] lg:text-left'>All chats</p>
      </div>
      <div className='flex flex-col gap-4 p-4 lg:h-[705px] lg:p-6'>
        <TextField
          icon={() => <span className='material-icons-outlined -mt-[2px] text-[#999999]'>search</span>}
          placeholder='Search...'
          className='h-[48px] rounded-[94px] border border-[#BBC1D3] bg-[#F8F8F8] px-3 py-[14px] text-[#999999]'
          onChange={(e) => setSearch(e.value)}
        />
        <div className='flex h-[calc(100vh-316px)] flex-col gap-3 overflow-auto lg:h-full'>
          {isLoadingChats ? (
            <div className='flex h-full w-full items-center justify-center'>
              <Loader size='medium' className='h-10 w-10' />
            </div>
          ) : (
            searchedChats?.map((chat, index) => {
              return (
                <div
                  onClick={() => {
                    setCurrentChat(chat);
                    navigate('/chat/' + chat.id);
                    navigateToMessages && navigateToMessages('2');
                  }}
                  key={chat?.id || index}
                  className='chat-list-item pr-2'
                >
                  <ChatCard chat={chat} />
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatList;
