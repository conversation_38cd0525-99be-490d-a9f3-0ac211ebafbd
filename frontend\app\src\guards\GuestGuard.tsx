// @ts-nocheck
import {FunctionComponent, ReactNode} from 'react'
import {Navigate, useLocation} from 'react-router-dom'
import {useAuthContext} from "src/context/AuthContext.tsx";

interface GuestGuardProps {
    children: ReactNode
}

const GuestGuard: FunctionComponent<GuestGuardProps> = ({children}) => {
    const location = useLocation()

    const {user} = useAuthContext();

  const isGuest = user?.profile ? user?.profile?.account_type == '5' :  user?.account_type == '5'

    if (!isGuest) {
        return <Navigate to="/" state={{from: location}}/>
    }

    return <>{children}</>
}

export default GuestGuard
