// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { View, Text, Button } from 'reshaped';
import Footer from '../Footer/Footer';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import surelyplusfilms1 from '../../assets/images/surelyplusfilms/surelyplusfilms1.png';
import surelyplusfilms2 from '../../assets/images/surelyplusfilms/surelyplusfilms2.svg';
import surelyPlusFilms, {
  SurelyPlusFilmsType,
} from '../../store/dummydata/SurelyPlusFilmsDummyData/SurelyPlusFilmsDummyData';
import SurelyPlusFilmModal from './SurelyPlusFilmModal/SurelyPlusFilmModal';
import homepage31 from '../../assets/images/homepage/homepage31.jpg';

const SurelyPlusFilms: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();
  const { openModal } = useModalAction();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  const truncateText = (text: any, maxLength: any) => {
    if (text.length > maxLength) {
      return text.substring(0, maxLength) + '...';
    }
    return text;
  };

  const [isFilmModalOpen, setIsFilmModalOpen] = useState(false);
  const [selectedFilmLink, setSelectedFilmLink] = useState<string | null>(null);
  const [selectedFilmDescription, setSelectedFilmDescription] = useState<string | null>(null);
  const [expandedText, setExpandedText] = useState({});

  const openFilmModal = (filmLink: string, description: string) => {
    setSelectedFilmLink(filmLink);
    setSelectedFilmDescription(description);
    setIsFilmModalOpen(true);
  };

  const closeFilmModal = () => {
    setIsFilmModalOpen(false);
    setSelectedFilmLink(null);
    setSelectedFilmDescription(null);
  };

  const handleReadMoreClick = (itemId: any) => {
    setExpandedText((prevExpandedText) => ({
      ...prevExpandedText,
      [itemId]: !prevExpandedText[itemId],
    }));
  };

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#FFFFFF]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <img src={surelyplusfilms1} className='mt-[67px]' />
          <View className='flex flex-col xl:w-[538px]  xl:px-0 px-[12px] justify-center sm:items-center mx-auto mt-[84px]'>
            <Text className='xl:text-center text-start font-rufina-stencil text-[48px] font-normal leading-[56px] text-[#323C58]'>
              SurelyPlus Films.
            </Text>
            <Text className='xl:text-center text-start rubik text-[16px] font-normal leading-[24px] text-[#323C58] mt-[12px]'>
              Browse through our growing collection of video blogs, in which we share the views and opinions of many of
              our Surely members – both clients and security operatives – as well as interested third party stakeholders
              from whom we can learn important things too. 
            </Text>
            <div className='items-center mx-auto bg-[#388DD8] w-[200px] h-[4px] my-4' />
          </View>
        </View>
        <View className='w-full max-w-[1320px] xl:px-0 px-3  mx-auto items-start text-center grid grid-cols-1 md:grid-cols-3 gap-8  mt-[48px]'>
          {surelyPlusFilms.map((item) => (
            <View
              key={item.id}
              className='flex flex-col items-start flex-shrink-0 xl:w-[424px] w-full xl:mb-[147px] mb-[50px] shadow-md rouded-[8px]'
            >
              <iframe width='100%' height='315' src={item.embed} title='YouTube video player' frameBorder='0'></iframe>
              <View className='flex flex-col items-start p-[24px]  self-stretch bg-[#FFFF] shadow-md rounded-tl-[0px] rounded-tr-[0px] rounded-br-[8px] rounded-bl-[8px]  bg-[#FFFF]'>
                <Text className='rubik text-[16px] leading-[20px] font-medium text-[#1A1A1A] mt-[12px] text-left'>
                  {item.title}
                </Text>
                <Text className='rubik text-[14px] leading-[20px] font-normal text-[#383838] mt-[12px] text-left  '>
                  {expandedText[item.id] ? item.description : truncateText(item.description, 200)}
                </Text>
                <button
                  className='btn-no-hover  flex items-center w-[147px] h-[48px] mt-[2px]'
                  onClick={() => handleReadMoreClick(item.id)}
                >
                  <Text className='!text-[#0B80E7]  rubik text-[17px] font-medium leading-5'>
                    {expandedText[item.id] ? 'Read less' : 'Read more'}
                  </Text>
                </button>
              </View>
            </View>
          ))}
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px] sm:mt-0 mt-10 md:mx-auto bg-cover bg-no-repeat bg-center '
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Footer />
      <SurelyPlusFilmModal
        active={isFilmModalOpen}
        deactivate={closeFilmModal}
        videoLink={selectedFilmLink}
        description={selectedFilmDescription}
      />
    </View>
  );
};

export default SurelyPlusFilms;
