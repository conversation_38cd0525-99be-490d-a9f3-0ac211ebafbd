import { useState } from 'react';
import { Text } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import './HeaderMenu.css';

interface DropdownOption {
  label: string;
  onClick: () => void;
}

const HeaderMenu = () => {
  const navigate = useNavigate();
  const [isOperatorDropdown, setIsOperatorDropdown] = useState(false);
  const [isClientDropdown, setIsClientDropdown] = useState(false);
  const [isSurelyProDropdown, setIsSurelyProDropdown] = useState(false);
  const [isSecurityJobsDropdown, setIsSecurityJobsDropdown] = useState(false);
  const [isAboutUsDropdown, setIsAboutUsDropdown] = useState(false);

  const clientFaq = () => {
    navigate('/clients');
    setTimeout(() => {
      const faqSection = document.getElementById('faq');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };
  const clientWork = () => {
    navigate('/clients');
    setTimeout(() => {
      const faqSection = document.getElementById('work');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };
  const operatorFaq = () => {
    navigate('/security-operatives');
    setTimeout(() => {
      const faqSection = document.getElementById('faq');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };
  const operatorWork = () => {
    navigate('/security-operatives');
    setTimeout(() => {
      const faqSection = document.getElementById('work');
      if (faqSection) {
        faqSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 0);
  };

  const operatorOptions: DropdownOption[] = [
    { label: 'How it works', onClick: () => operatorWork() },
    { label: 'FAQs', onClick: () => operatorFaq() },
  ];
  const clientOptions: DropdownOption[] = [
    { label: 'How it works', onClick: () => clientWork() },
    { label: 'FAQs', onClick: () => clientFaq() },
  ];

  const surelyProOptions: DropdownOption[] = [
    {
      label: 'Use of Equipment',
      onClick: () => navigate('/surely-pro?type=useofequipment'),
    },
    {
      label: 'Customer Service',
      onClick: () => navigate('/surely-pro?type=customerservice'),
    },
    {
      label: 'Substance Awareness',
      onClick: () => navigate('/surely-pro?type=substanceawareness'),
    },
    {
      label: 'Vulnerable People',
      onClick: () => navigate('/surely-pro?type=vulnerablepeople'),
    },
    {
      label: 'Disability Focus',
      onClick: () => navigate('/surely-pro?type=disabilityfocus'),
    },
    {
      label: 'Conflict Management',
      onClick: () => navigate('/surely-pro?type=conflictmanagement'),
    },
  ];

  const securityJobsOptions: DropdownOption[] = [
    {
      label: 'Bodyguard Jobs',
      onClick: () => navigate('/security-jobs/bodyguard'),
    },
    {
      label: 'CCTV Operator Jobs',
      onClick: () => navigate('/security-jobs/cctv-operator'),
    },
    {
      label: 'Door Supervisor Jobs',
      onClick: () => navigate('/security-jobs/door-supervisor'),
    },
    {
      label: 'Event Security Jobs',
      onClick: () => navigate('/security-jobs/event-security'),
    },
    {
      label: 'Private Security Jobs',
      onClick: () => navigate('/security-jobs/private-security'),
    },
    {
      label: 'Security Guard Jobs',
      onClick: () => navigate('/security-jobs/security-guard'),
    },
    {
      label: 'Close Protection Jobs',
      onClick: () => navigate('/security-jobs/close-protection'),
    },
  ];

  const aboutUsOptions: DropdownOption[] = [
    {
      label: 'Security Matters',
      onClick: () => navigate('/security-matters-archive'),
    },
    { label: 'Code of Conduct', onClick: () => navigate('/code-of-conduct') },
    { label: 'Useful Resources', onClick: () => navigate('/useful-resources') },
    { label: 'SurelyPlus Films', onClick: () => navigate('/surely-pro-films') },
  ];

  const handleOperatorClick = (onClick: () => void) => {
    setIsOperatorDropdown(false);
    onClick();
  };
  const handleClientClick = (onClick: () => void) => {
    setIsClientDropdown(false);
    onClick();
  };
  const handleSurelyProClick = (onClick: () => void) => {
    setIsSurelyProDropdown(false);
    onClick();
  };
  const handleSecurityJobsClick = (onClick: () => void) => {
    setIsSecurityJobsDropdown(false);
    onClick();
  };
  const handleAboutUsClick = (onClick: () => void) => {
    setIsAboutUsDropdown(false);
    onClick();
  };

  return (
    <nav
      className='w-full flex justify-center items-center gap-2 xl:gap-6'
    >
      <div
        className='relative mt-[3px] '
        onMouseEnter={() => setIsOperatorDropdown(true)}
        onMouseLeave={() => setIsOperatorDropdown(false)}
      >
        <button
          onClick={() => navigate('/security-operatives')}
          className='btn-no-hover btn-hover-line flex items-center '
        >
          <div className='flex items-center'>
            <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 mr-[1px] xl:mr-[2px]'>
              Security Operatives
            </Text>
            <span className='material-icons text-[22px] text-[#323C58] '>
              expand_more
            </span>
          </div>
        </button>
        {isOperatorDropdown && (
          <div className='origin-top-right absolute  w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5'>
            <div className='py-1'>
              {operatorOptions.map((option) => (
                <button
                  key={option.label}
                  className='block px-4 py-2 text-sm  btn-no-hover w-full'
                  onClick={() => handleOperatorClick(option.onClick)}
                >
                  <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 text-left'>
                    {option.label}
                  </Text>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      <div
        className='relative mt-[3px]  '
        onMouseEnter={() => setIsClientDropdown(true)}
        onMouseLeave={() => setIsClientDropdown(false)}
      >
        <button
          onClick={() => navigate('/clients')}
          className='inline-flex btn-no-hover btn-hover-line flex items-center '
        >
          <div className='flex items-center'>
            <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 mr-[1px] xl:mr-[2px]'>
              Clients
            </Text>
            <span className='material-icons text-[22px] text-[#323C58] '>
              expand_more
            </span>
          </div>
        </button>
        {isClientDropdown && (
          <div className='origin-top-right absolute   w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5'>
            <div className='py-1'>
              {clientOptions.map((option) => (
                <button
                  key={option.label}
                  className='block px-4 py-2 text-sm  btn-no-hover w-full'
                  onClick={() => handleClientClick(option.onClick)}
                >
                  <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 text-left'>
                    {option.label}
                  </Text>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      <div
        className='relative mt-[3px] '
        onMouseEnter={() => setIsSurelyProDropdown(true)}
        onMouseLeave={() => setIsSurelyProDropdown(false)}
      >
        <button
          onClick={() => navigate('/surely-pro?type=surleypro')}
          className='inline-flex btn-no-hover btn-hover-line flex items-center '
        >
          <div className='flex items-center'>
            <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 xl:mr-[2px]'>
              SurelyPro
            </Text>
            <span className='material-icons text-[22px] text-[#323C58] '>
              expand_more
            </span>
          </div>
        </button>
        {isSurelyProDropdown && (
          <div className='origin-top-right absolute  w-50 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5'>
            <div className='py-1'>
              {surelyProOptions.map((option) => (
                <button
                  key={option.label}
                  className='block px-4 py-2 text-sm  btn-no-hover w-full'
                  onClick={() => handleSurelyProClick(option.onClick)}
                >
                  <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 text-left whitespace-nowrap'>
                    {option.label}
                  </Text>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      <button
        onClick={() => navigate('/inclusivity-pledge')}
        className='inline-flex btn-no-hover btn-hover-line flex items-center '
      >
        <div className='flex items-center'>
          <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 mr-[1px] xl:mr-[2px] mt-1'>
            Inclusivity Pledge
          </Text>
        </div>
      </button>

      <div
        className='relative mt-[3px] '
        onMouseEnter={() => setIsAboutUsDropdown(true)}
        onMouseLeave={() => setIsAboutUsDropdown(false)}
      >
        <button
          onClick={() => navigate('/about-us')}
          className='inline-flex btn-no-hover btn-hover-line flex items-center '
        >
          <div className='flex items-center'>
            <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 mr-[1px] xl:mr-[2px]'>
              About Us
            </Text>
            <span className='material-icons text-[22px] text-[#323C58] '>
              expand_more
            </span>
          </div>
        </button>
        {isAboutUsDropdown && (
          <div className='origin-top-right absolute  w-50 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5'>
            <div className='py-1'>
              {aboutUsOptions.map((option) => (
                <button
                  key={option.label}
                  className='block px-4 py-2 text-sm  btn-no-hover w-full'
                  onClick={() => handleAboutUsClick(option.onClick)}
                >
                  <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 text-left whitespace-nowrap'>
                    {option.label}
                  </Text>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      <div
        className='relative mt-[3px] '
        onMouseEnter={() => setIsSecurityJobsDropdown(true)}
        onMouseLeave={() => setIsSecurityJobsDropdown(false)}
      >
        <button
          onClick={() => navigate('/security-jobs')}
          className='inline-flex btn-no-hover btn-hover-line flex items-center '
        >
          <div className='flex items-center'>
            <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 mr-[1px] xl:mr-[2px]'>
              Security Jobs
            </Text>
            <span className='material-icons text-[22px] text-[#323C58] '>
              expand_more
            </span>
          </div>
        </button>
        {isSecurityJobsDropdown && (
          <div className='origin-top-right absolute  w-70 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5'>
            <div className='py-1'>
              {securityJobsOptions.map((option) => (
                <button
                  key={option.label}
                  className='block px-4 py-2 text-sm  btn-no-hover w-full'
                  onClick={() => handleSecurityJobsClick(option.onClick)}
                >
                  <Text className='text-[#323C58] rubik text-[15px] font-medium leading-5 text-left whitespace-nowrap'>
                    {option.label}
                  </Text>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default HeaderMenu;
