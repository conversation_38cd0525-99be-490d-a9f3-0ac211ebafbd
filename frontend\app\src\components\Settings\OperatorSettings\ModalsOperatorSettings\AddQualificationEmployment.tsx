// @ts-nocheck
import React, { useRef, useState, useEffect } from 'react';
import { Text, View, Button, Divider, Modal, TextField } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';

interface AddQualificationEmploymentProps {
  active: boolean;
  deactivate: () => void;
  qualificationArray: any[];
  addQualification: (secondTabSettings: any) => void;
  selectedQualification: any;
  onEditQualification: (id: any) => void;
  isEditingQualification: boolean;
}

const AddQualificationEmployment: React.FC<AddQualificationEmploymentProps> = ({
  active,
  deactivate,
  qualificationArray,
  addQualification,
  selectedQualification,
  onEditQualification,
  isEditingQualification,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [referenceLetter1, setReferenceLetter1] = useState<string[]>([]);
  const referenceLetter = referenceLetter1[0];
  const [fileName, setFileName] = useState<any>();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [fieldsRequiredError, setFieldsRequiredError] = useState<any>();
  const [validFileName, setValidFileName] = useState(true);

  const [qualificationTitle, setQualificationTitle] = useState('');
  const [qualificationDescription, setQualificationDescription] = useState('');
  const [expiryDateMM, setexpiryDateMM] = useState('');
  const [expiryDateYYYY, setexpiryDateYYYY] = useState('');
  const [expiryDateDD, setexpiryDateDD] = useState('');
  const expiryDate = expiryDateYYYY + '-' + expiryDateMM.padStart(2, '0') + '-' + expiryDateDD.padStart(2, '0');
  const qualificationDocument = selectedFiles[0];

  const [isDragging, setIsDragging] = useState(false);

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
          resolve(e.target.result);
        } else {
          reject(new Error('Failed to read file as base64.'));
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files: any = e.target.files;
    setFileName(files[0].name);
    if (files && files?.length > 0) {
      const fileDataArray: string[] = [];
      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push(base64);
      }
      setSelectedFiles(fileDataArray);
    }
  };
  const deleteFile = (indexToDelete: number) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToDelete);
    setSelectedFiles(updatedFiles);
  };

  const openFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const isButtonDisabled = selectedFiles?.length > 0;

  const handleDateChange = (
    value: string,
    setStateFunction: React.Dispatch<React.SetStateAction<string>>,
    maxLength: number,
    minValue = 0,
    maxValue = Infinity,
  ) => {
    const numericValue = value.replace(/\D/g, '');

    const numberValue = parseInt(numericValue, 10);

    if (numericValue?.length === 0 || (numericValue?.length <= maxLength && numberValue >= minValue && numberValue <= maxValue)) {
      setStateFunction(numericValue.toString());
    }
  };

  const editQualification = () => {
    if (!qualificationTitle || !qualificationDescription || !expiryDate || !expiryDateMM || !expiryDateYYYY || !expiryDateDD) {
      setFieldsRequiredError('Fields are required');
    } else if (!validFileName) {
      setFieldsRequiredError('Invalid file name');
    } else {
      const editedPositionData = {
        id: selectedQualification.id,
        data: {
          qualificationTitle,
          qualificationDescription,
          expiryDateMM,
          expiryDateYYYY,
          expiryDateDD,
          qualificationDocument,
        },
      };

      onEditQualification(editedPositionData);
      deactivate();
    }
  };

  const saveQualification = () => {
    if (!qualificationTitle || !qualificationDescription || !expiryDate || !expiryDateMM || !expiryDateYYYY || !expiryDateDD) {
      setFieldsRequiredError('Fields are required');
    } else if (!validFileName) {
      setFieldsRequiredError('Invalid file name');
    } else if (expiryDateYYYY?.length != 4) {
      setFieldsRequiredError('Expiry date should be a 4-digit year (YYYY)');
    } else {
      const newQualificationData = {
        qualificationTitle,
        qualificationDescription,
        expiryDate,
        qualificationDocument,
      };
      addQualification(newQualificationData);
      setQualificationTitle('');
      setQualificationDescription('');
      setexpiryDateMM('');
      setexpiryDateYYYY('');
      setexpiryDateDD('');
      setFileName('');
      setSelectedFiles([]);
      setValidFileName(true);

      deactivate();
    }
  };

  useEffect(() => {
    if (active) {
      if (active && selectedQualification) {
        setQualificationTitle(selectedQualification.qualificationTitle || '');
        setQualificationDescription(selectedQualification.qualificationDescription || '');

        const [year, month, day] = (selectedQualification.expiryDate || '').split('-');
        // setexpiryDateMM(selectedQualification.expiryDate||'')
        // setexpiryDateYYYY(selectedQualification.expiryDate ||'')
        // setexpiryDateDD(selectedQualification.expiryDate ||'')
        setexpiryDateYYYY(year || '');
        setexpiryDateMM(month || '');
        setexpiryDateDD(day || '');
        // setSelectedFiles(selectedQualification.selectedFiles ||'')
      } else {
        setQualificationTitle('');
        setQualificationDescription('');
        setexpiryDateMM('');
        setexpiryDateYYYY('');
        setexpiryDateDD('');
        // setSelectedFiles([])
      }
    }
  }, [active, selectedQualification]);

  const handleFileDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const fileDataArray: string[] = [];
      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push(base64);
      }
      setSelectedFiles(fileDataArray);
      setFileName(files[0].name);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Add qualification</Text>
        </View>
        <View className='mt-[16px] flex flex-col'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Title</Text>
          <TextField
            value={qualificationTitle}
            onChange={(e) => setQualificationTitle(e.value)}
            name='text'
            className='mt-1 h-[48px] w-full sm:w-[376px]'
          />
        </View>
        <View className='mt-[16px] flex flex-col'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Qualification</Text>
          <TextField
            value={qualificationDescription}
            onChange={(e) => setQualificationDescription(e.value)}
            name='text'
            className='mt-1 h-[48px] w-full sm:w-[376px]'
          />
        </View>
        <View className='mt-[16px] flex flex-col'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Expiry date</Text>

          <View className='mt-1 flex flex-row gap-3'>
            <TextField
              name='text'
              className='h-[48px] w-[117px]'
              placeholder='DD'
              value={expiryDateDD}
              onChange={(e) => handleDateChange(e.value, setexpiryDateDD, 3, 1, 31)}
            />

            <TextField
              name='text'
              className='h-[48px] w-[117px]'
              placeholder='MM'
              value={expiryDateMM}
              onChange={(e) => handleDateChange(e.value, setexpiryDateMM, 3, 1, 12)}
            />

            <TextField
              name='text'
              className='h-[48px] w-[117px]'
              placeholder='YYYY'
              value={expiryDateYYYY}
              onChange={(e) => handleDateChange(e.value, setexpiryDateYYYY, 4)}
            />
          </View>
        </View>
        <View className='mt-[16px] flex flex-col'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Qualification file/document</Text>
          {/* <Button
            variant='outline'
            icon={() => <span className='material-icons-outlined mt-[-1px] text-[21px] text-[#323C58]'>upload</span>}
            onClick={openFileInput}
            className='border-neutral bg-background-base mt-1 flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#000000]'
            disabled={isButtonDisabled}
          >
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#323C58]'>
              Drop or&nbsp;
              <span className='rubik text-[14px] font-medium leading-[20px] text-[#0B80E7]'>browse</span>
            </Text>
            <input type='file' ref={fileInputRef} accept='.pdf,.doc,.docx,.jpg,.jpeg,.png' style={{ display: 'none' }} onChange={handleFileChange} />
          </Button> */}
          <div
            onDrop={handleFileDrop}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            className={`drop-zone ${isDragging ? 'dragging' : ''}`}
          >
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined mt-[-1px] text-[21px] text-[#323C58]'>upload</span>}
              onClick={openFileInput}
              className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#000000]'
              disabled={isButtonDisabled}
            >
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#323C58]'>
                Drop or&nbsp;
                <span className='rubik text-[14px] font-medium leading-[20px] text-[#0B80E7]'>browse</span>
              </Text>
              <input
                type='file'
                ref={fileInputRef}
                accept='.pdf,.doc,.docx,.jpg,.jpeg,.png'
                style={{ display: 'none' }}
                onChange={handleFileChange}
              />
            </Button>
          </div>
          <ul>
            {selectedFiles?.map((file, index) => (
              <li key={index}>
                <View className='mt-[18px] flex flex-row items-center'>
                  <Text className='rubik text-base font-normal leading-5 text-[#1A1A1A] '>{fileName}</Text>
                  <Button
                    variant='outline'
                    className='ml-[16px] mt-[10px] flex w-[78px] items-center justify-center !border-[#DFE2EA] !bg-[#fff] '
                    onClick={() => deleteFile(index)}
                  >
                    <Text className='rubik p-[4px 8px 4px 8px] flex items-center align-middle text-sm font-normal text-[#CB101D]'>
                      <span className='material-icons align-middle text-[20px]'>close</span>
                      Delete
                    </Text>
                  </Button>
                </View>
              </li>
            ))}
          </ul>
          <Text className='text-neutral rubik mt-[16px] text-[13px] leading-4 text-[#323C58]'>PDF, JPEG, PNG files only.</Text>
        </View>

        <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>{fieldsRequiredError}</Text>

        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            // onClick={saveQualification}
            onClick={() => {
              isEditingQualification ? editQualification() : saveQualification();
            }}
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>{isEditingQualification ? 'Edit' : 'Add'}</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default AddQualificationEmployment;
