// @ts-nocheck
import React from 'react';
import { View, Image, Text, Button, Divider } from 'reshaped';
import SurelyIcon from '../LandingPage/SurelyIcon';
import SurelyText from '../LandingPage/SurelyText';
import Facebook from './Facebook';
import Linkedin from './Linkedin';
import Twiter from './Twiter';
import Youtube from './Youtube';
import Instagram from './Instagram';
import Tiktok from './Tiktok';
import { useNavigate } from 'react-router-dom';
import '../../components/Header/HeaderMenu/HeaderMenu.css';

const Footer: React.FC = () => {
  const navigate = useNavigate();

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <View className='mx-auto flex w-full flex-col items-center gap-8 bg-[#444B5F] text-center'>
      <div className='mt-4 w-full max-w-screen-xl lg:mt-[60px]'>
        <View className='relative flex flex-col justify-between px-[12px] lg:px-0 xl:flex-row'>
          <View className='grid w-full grid-cols-2 justify-between gap-2 lg:flex lg:w-[835px] lg:flex-row '>
            <SurelyIcon />
            <View className='flex flex-col xl:ml-[107px]'>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
                onClick={() => navigate('/')}
              >
                Home
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
                onClick={() => navigate('/security-operatives')}
              >
                Security Operatives
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
                onClick={() => navigate('/clients')}
              >
                Clients
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
                onClick={() => navigate('/surely-pro?type=surleypro')}
              >
                SurelyPro
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/security-matters-archive')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Security Matters
              </button>
            </View>
            <View className='flex flex-col xl:ml-[69.5px]'>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/about-us')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                About Us
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
                onClick={() => navigate('/inclusivity-pledge')}
              >
                Inclusivity Pledge
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/code-of-conduct')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Code of Conduct
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/useful-resources')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Useful Resources
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/surely-pro-films')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                SurelyPlus Films
              </button>
            </View>
            <View className='flex flex-col xl:ml-[69.5px]'>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/terms-of-use')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Terms of Use
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/privacy-policy')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Privacy Policy
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/cookies-policy')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Cookies Policy
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/accessibility-policy')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Accessibility Policy
              </button>
              <button
                style={{
                  textAlign: 'left',
                  display: 'block',
                  width: 'fit-content',
                }}
                onClick={() => navigate('/contact-us')}
                className='btn-no-hover rubik p-0 text-left text-[16px] font-normal leading-6 text-[#fff] sm:mt-[10px]'
              >
                Contact Us
              </button>
            </View>
          </View>
          <div className='absolute bottom-8 right-2 z-[100] sm:right-10 md:right-14 lg:top-4'>
            <Button
              variant='outline'
              icon={() => (
                <svg xmlns='http://www.w3.org/2000/svg' width='19' height='11' viewBox='0 0 19 11' fill='none'>
                  <path
                    d='M16.6148 10.9961L18.2063 9.40461L9.30481 0.503124L0.40332 9.40461L1.9948 10.9961L9.30481 3.68608L16.6148 10.9961Z'
                    fill='#1A1A1A'
                  />
                </svg>
              )}
              onClick={scrollToTop}
              className='flex h-[60px] w-[60px] flex-shrink-0 items-center justify-center gap-2 rounded-lg !bg-[#7CDBEF] p-2 md:ml-[115px]'
            ></Button>
          </div>
          <View className='z-20 mt-14 flex flex-row-reverse items-end justify-center  sm:ml-[0px]  sm:mt-[0px] xl:flex-col xl:justify-end'>
            <View className='mt-5 flex flex-row sm:mt-[147px]'>
              <a href='https://www.facebook.com/SurelyUK/' target='_blank' rel='noopener noreferrer' className='mr-2 lg:mr-[5px]'>
                <Facebook />
              </a>
              <a href='https://twitter.com/SurelySecurity' target='_blank' rel='noopener noreferrer' className='mr-2 lg:mr-[5px]'>
                <Twiter />
              </a>
              <a href='https://www.linkedin.com/company/surely-security/' target='_blank' rel='noopener noreferrer' className='mr-2 lg:mr-[5px]'>
                <Linkedin />
              </a>
              <a href='https://www.youtube.com/@SurelySecurity' target='_blank' rel='noopener noreferrer' className='mr-2 lg:mr-[5px]'>
                <Youtube />
              </a>
              <a href='https://www.instagram.com/surelysecurity/' target='_blank' rel='noopener noreferrer' className='mr-2 lg:mr-[5px]'>
                <Instagram />
              </a>
              <a
                href='https://www.tiktok.com/@surelysecurity?_t=8h8Leo1kIvh&_r=1'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <Tiktok />
              </a>
            </View>
          </View>
        </View>
        <Divider className='mt-2 h-[1px] w-full bg-[#fff] sm:mt-[47.3px]'></Divider>
        <View className='mt-[32px] flex flex-col items-center justify-between px-[12px] pb-[10px] sm:mx-[0px] sm:mb-[34px] sm:mt-[32px] sm:flex-row sm:pb-0 lg:mt-[15px] lg:items-start xl:px-0'>
          <SurelyText />
          <Text className='rubik mt-2 text-left text-[16px] font-normal leading-6 text-[#fff] lg:mt-[5px]'>Surely Security © 2023</Text>
        </View>
      </div>
    </View>
  );
};

export default Footer;
