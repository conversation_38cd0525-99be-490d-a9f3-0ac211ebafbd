export type QuestionDataCustomerService = {
    id: number;
    question: string;
    answer_1: string;
    answer_2: string;
    answer_3: string;
    answer_4: string;
    correct_answer: number;
  };
  
  export const customerServiceQuestions: QuestionDataCustomerService[] = [
    {
      id: 1,
      question:
        'Which of the following options is a positive behaviour you should exhibit?',
      answer_1: 'Being aggressive',
      answer_2: 'Being assertive',
      answer_3: 'Being passive',
      answer_4: 'Being dismissive',
      correct_answer: 2,
    },
    {
      id: 2,
      question:
        'Which of the following is not one of the key factors affecting how well we communicate?',
      answer_1: 'The words you speak',
      answer_2: 'Your body language',
      answer_3: 'Your tone of voice',
      answer_4: 'Your native language',
      correct_answer: 4,
    },
    {
      id: 3,
      question:
        'Fill in the missing word. My “……” affects my behaviour more than anything else. ',
      answer_1: 'Attitude',
      answer_2: 'Spoken words',
      answer_3: 'Body language',
      answer_4: 'Tone of voice',
      correct_answer: 1,
    },
    {
      id: 4,
      question: 'Which of the following is not a proactive step in delivering good customer service?',
      answer_1: 'Learning how to recognise common flashpoints',
      answer_2: 'Holding regular team meetings to agree procedures',
      answer_3: 'Holding debriefs following incidents to discuss issues',
      answer_4: 'Expecting a new security operative to know workplace procedures without a proper briefing session',
      correct_answer: 4,
    },
    {
      id: 5,
      question: 'When a message you send is received and 	understood by a customer, what is this known as?',
      answer_1: 'Proactive service delivery',
      answer_2: 'Positive communication',
      answer_3: 'Proactive communication',
      answer_4: 'Customer expectation',
      correct_answer: 2,
    },
  ];
  