// @ts-nocheck
import { useState } from 'react';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import { View } from 'reshaped';
import NoJobPhoto from 'src/components/NoData/NoJobPhoto';

const JobPhotoCarousel = ({ allImagesEmpty, validImages, handleImageClick, selectedImageIndex, images }: any) => {
  const [selectedButtonIndex, setSelectedButtonIndex] = useState(0);

  return (
    <>
      <Carousel
        showArrows={images?.length === 1 ? false : true}
        showStatus={false}
        showThumbs={false}
        infiniteLoop={true}
        showIndicators={false}
        renderArrowPrev={() => {
          if (validImages?.length > 1)
            return (
              <button
                className='absolute top-1/2 z-10 mr-2  flex -translate-y-1/2 rounded-full bg-[#fff] p-2 focus:outline-none drop-shadow-lg'
                onClick={() => handleImageClick((selectedImageIndex - 1 + validImages?.length) % validImages?.length)}
              >
                <span className='material-icons-outlined'>arrow_back_ios</span>
              </button>
            );
        }}
        renderArrowNext={() => {
          if (validImages?.length > 1)
            return (
              <button
                className='absolute right-0 top-1/2 z-10  flex -translate-y-1/2 rounded-full bg-[#fff] p-2 focus:outline-none drop-shadow-lg'
                onClick={() => handleImageClick((selectedImageIndex + 1) % validImages?.length)}
              >
                <span className='material-icons-outlined'>arrow_forward_ios</span>
              </button>
            );
        }}
        selectedItem={selectedImageIndex}
        onChange={(index) => {
          setSelectedButtonIndex(index);
          handleImageClick(index);
        }}
      >
        {allImagesEmpty ? (
          <div className='relative h-[176px] w-full'>
            <NoJobPhoto />
          </div>
        ) : (
          (Array.isArray(images) ? images : [images]).map((image, index) => (
            <div key={index} className='relative'>
              <img className='mx-auto aspect-[16/9] rounded-xl object-contain' src={image} alt={`Job Image ${index + 1}`} />
            </div>
          ))
        )}
      </Carousel>
      {images?.length > 1 && (
        <View className='flex justify-center'>
          <div className='shrink-1  flex justify-center gap-[10px]'>
            {validImages.map((_: any, index: any) => (
              <div
                key={index}
                className={`shrink-1 flex h-[12px] cursor-pointer ${
                  selectedButtonIndex === index ? '!w-[22px]  rounded-[8px] bg-[#0B80E7]' : '!w-[12px] rounded-[100%] bg-[#C4BCBD]'
                }`}
                onClick={() => {
                  setSelectedButtonIndex(index);
                  handleImageClick(index);
                }}
                aria-label={`Select image ${index + 1}`}
              ></div>
            ))}
          </div>
        </View>
      )}
    </>
  );
};

export default JobPhotoCarousel;
