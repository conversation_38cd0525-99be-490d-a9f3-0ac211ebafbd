export interface BaseRegisterDataType {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  passwordConfirmation: string;
  loginType: number;
  platform: number;
  appVersion: string;
  firebaseToken: string;
  access_token: string;
  ref:any;
  referFriend:any

}

export interface CrucialRegisterDataType {
  accountType: number;
  phone: string;
  postCode: any;
  city: any;
  google_token: string;
  linkedin_token: string
}

export type SharedRegisterDataType = {
  baseData: BaseRegisterDataType;
  crucialData: CrucialRegisterDataType;
};

export type ClientRegisterFirstStepDataType = {
  clientRepresentBusinessAs: string;
  companyRegisteredNumber: string;
  industrySectors: string;
  selectedIndustry: string;
  accountName: any;
};

export type ClientRegisterDataType = {
  firstStep: ClientRegisterFirstStepDataType;
};

export type OperatorRegisterFirstStepDataType = {
  siaLicenceNumber: string;
  expiryDate: string;
  capturedImage: string;
  siaLicenceTypes:any;
};

export type OperatorRegisterSecondStepDataType = {
  documentType: string;
  frontImage: string;
  backImage: string;
};

export type OperatorRegisterThirdStepDataType = {
  addressVerificationDocument: string;
};

export type OperatorRegisterFourthStepDataType = {
  capturedImageSelfie: string;
};

export type OperatorRegisterDataType = {
  firstStep: OperatorRegisterFirstStepDataType;
  secondStep: OperatorRegisterSecondStepDataType;
  thirdStep: OperatorRegisterThirdStepDataType;
  fourthStep: OperatorRegisterFourthStepDataType;
};

export type ClientJoinedRegisterDataType = BaseRegisterDataType &
  CrucialRegisterDataType &
  ClientRegisterFirstStepDataType;

export type OperatorJoinedRegisterDataType = BaseRegisterDataType &
  CrucialRegisterDataType &
  OperatorRegisterFirstStepDataType &
  OperatorRegisterSecondStepDataType &
  OperatorRegisterThirdStepDataType &
  OperatorRegisterFourthStepDataType;

  export type OperatorValidateDataType = 
  OperatorRegisterFirstStepDataType &
  OperatorRegisterSecondStepDataType &
  OperatorRegisterThirdStepDataType &
  OperatorRegisterFourthStepDataType;

export type RegistrationContextType = {
  sharedRegisterData: SharedRegisterDataType;
  clientRegisterData: ClientRegisterDataType;
  operatorRegisterData: OperatorRegisterDataType;
  setSharedRegisterData: any;
  setClientRegisterData: any;
  setOperatorRegisterData: any;
};
