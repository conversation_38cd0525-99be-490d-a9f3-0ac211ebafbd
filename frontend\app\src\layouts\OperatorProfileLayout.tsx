import React from 'react';
import { Container } from 'reshaped';

import Header from 'src/components/Header/Header';
import OperatorIndustrySelectors from 'src/components/Profile/OperatorProfileComponents/OperatorIndustrySectors/OperatorIndustrySectors';
import OperatorLanguageCard from 'src/components/Profile/OperatorProfileComponents/OperatorLanguageCard/OperatorLanguageCard';
import OperatorProfileCard from 'src/components/Profile/OperatorProfileComponents/OperatorProfileCard/OperatorProfileCard';
import OperatorSurleyProBadges from 'src/components/Profile/OperatorProfileComponents/OperatorSurleyProBadges/OperatorSurleyProBadges';
import { EmailVerificationBanner } from 'src/components/EmailVerificationBanner';

interface Props {
  children?: React.ReactNode;
}

export const OperatorProfileLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container padding={0} className="w-[100vw] min-h-[100vh] bg-[url('src/assets/altBg.jpg')] bg-cover">
      <Header />
      <EmailVerificationBanner />
      <main className='w-full max-w-[1320px] flex flex-col justify-between mx-auto md:flex-row  mt-[60px]  px-[12px] xl:px-0 gap-6 md:gap-3  '>
        <aside className='flex flex-col gap-6 '>
          <OperatorProfileCard />
          <OperatorIndustrySelectors />
          <OperatorSurleyProBadges />
          <OperatorLanguageCard />
        </aside>
        <section className=''>{children}</section>
      </main>
    </Container>
  );
};

export default OperatorProfileLayout;
