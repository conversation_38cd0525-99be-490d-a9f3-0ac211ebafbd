// @ts-nocheck
import React from 'react';
import { Card, Text, View } from 'reshaped';

interface ClientOperatorReviewCardProps {
  review: any; 
  allReviewAndComent: any;
}

const ClientOperatorReviewCard: React.FC<ClientOperatorReviewCardProps> = ({ review, allReviewAndComent }: ClientOperatorReviewCardProps) => {
  return (
    <Card className='flex flex-col'>
      <View className='flex items-center justify-between'>
        {[...Array(5)].map((_, starIndex) => (
          <span
            key={starIndex}
            className={`material-icons ${
              starIndex < allReviewAndComent?.rating_avg
                ? 'text-yellow-400'
                : 'text-gray-400'
            }`}
          >
            star
          </span>
        ))}
        <Text className='text-neutral text-base font-medium leading-5 mt-[6px]'>
          {allReviewAndComent.rate?.toFixed(1)}{allReviewAndComent?.rating_avg}
        </Text>
        <Text className='text-neutral-faded text-xs font-normal leading-4 mt-[5px]'>
          {allReviewAndComent?.date}
        </Text>
      </View>
      <Text className='text-neutral text-base font-medium leading-5 mt-[8px]'>
        {allReviewAndComent.name}
      </Text>
      <Text className='text-gray-300 text-base italic font-normal leading-5 mt-[8px] !text-[#383838]'>
        {allReviewAndComent?.comment}
      </Text>
    </Card>
  );
};

export default ClientOperatorReviewCard;
