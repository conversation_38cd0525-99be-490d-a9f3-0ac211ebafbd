// @ts-nocheck
import React from 'react';
import { View, Image, Text } from 'reshaped';
import SurelyIcon from 'src/pages/LandingPage/SurelyIcon';
import SurelyIconNoData from './SurelyIconNoData';


const NoJobClient: React.FC = () => {
  return (
    <View className='flex flex-col justify-center items-center  mx-auto rounded-xl absolute inset-0 w-full h-full object-cover  rounded-xl'>

      <SurelyIconNoData />
      <Text className='text-[#444B5F] text-center font-normal leading-[24px] rubik !text-[#383838] mt-[10px]'>
      You don’t have job posts.
      </Text>
    </View>
  );
};

export default NoJobClient;
