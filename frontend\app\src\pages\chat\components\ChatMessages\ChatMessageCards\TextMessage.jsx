import { useAuthContext } from 'src/context/AuthContext';
import getNameInitials from '../../../../../utils/getNameInitials';
import MessageStatus from './MessageStatus';
import moment from 'moment';
import getValidImageUrl from 'src/utils/getValidImageUrl';

const TextMessage = ({ message }) => {
  const { user } = useAuthContext();

  const isRead = message?.read
  const isOpOnline = true;
  const invitationMessage = user?.profile?.id == message?.receiver_id ? 'has invited you to job' : 'was invited to job';
  const nameShown = message?.sender_id === user?.profile?.id ? user?.profile?.name : message.sender?.name;
  const imageShown =
    message?.sender_id === user?.profile?.id ? user?.profile?.profile_photo : message.sender?.profile_photo;

  return (
    <div className='relative flex items-start gap-4 rounded-lg px-2 py-3 pr-[60px] hover:bg-[#F4F5F7] lg:items-center'>
      <div className='h-10 w-10 shrink-0 lg:h-14 lg:w-14'>
        {imageShown ? (
          <img
            className={`h-full w-full rounded-full border-2 ${
              isOpOnline ? 'border-[rgba(10, 158, 44, 0.80))]' : 'border-transparent'
            }`}
            src={getValidImageUrl(imageShown)}
          />
        ) : (
          <div
            className={`h-full w-full rounded-full border-2 font-medium ${
              isOpOnline ? 'border-[rgba(10, 158, 44, 0.80))]' : 'border-transparent'
            } flex cursor-default items-center justify-center`}
          >
            {getNameInitials(nameShown)}
          </div>
        )}
      </div>
      <div className='flex w-full flex-col gap-[3px] text-left'>
        <div className='flex items-center gap-2'>
          <h2 className='rubik overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>{nameShown}</h2>
          <MessageStatus status={isRead} />
        </div>
        <p className='rubik overflow-ellipsis text-[14px] leading-5 text-[#383838]'>{message?.message}</p>
      </div>
    </div>
  );
};

export default TextMessage;
