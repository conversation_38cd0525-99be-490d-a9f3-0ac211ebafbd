import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Tabs, View } from 'reshaped';

import ChatList from './components/ChatList/ChatList';
import ChatMessages from './components/ChatMessages/ChatMessages';
import ChatContracts from './components/ChatContracts/ChatContracts';

const ChatPage = () => {
  const location = useLocation();
  const redirectedToActivePage = location?.state?.activeTab || '1';
  const [activeTab, setActiveTab] = useState(redirectedToActivePage || '1');

  const handleActiveTab = (tab) => tab !== activeTab && setActiveTab(tab);

  return (
    <div className='relative h-full lg:pb-20'>
      <div className='hidden gap-6 lg:flex'>
        <ChatList />
        <ChatMessages />
        <ChatContracts />
      </div>
      <div className='flex h-full flex-col gap-6 lg:hidden'>
        <Tabs value={activeTab} variant='borderless' itemWidth='equal'>
          <div className='sticky top-2 z-50 overflow-auto bg-[#FAFBFF] px-4 lg:px-0'>
            <Tabs.List>
              <Tabs.Item value='1'>
                <div onClick={() => handleActiveTab('1')}>Your chats</div>
              </Tabs.Item>
              <Tabs.Item value='2'>
                <div onClick={() => handleActiveTab('2')}>Chat</div>
              </Tabs.Item>
              <Tabs.Item value='3'>
                <div onClick={() => handleActiveTab('3')}>Contract</div>
              </Tabs.Item>
            </Tabs.List>
          </div>
          <View.Item>
            <Tabs.Panel value='1'>
              <ChatList navigateToMessages={handleActiveTab} />
            </Tabs.Panel>
            <Tabs.Panel value='2'>
              <ChatMessages />
            </Tabs.Panel>
            <Tabs.Panel value='3'>
              <ChatContracts />
            </Tabs.Panel>
          </View.Item>
        </Tabs>
      </div>
    </div>
  );
};

export default ChatPage;
