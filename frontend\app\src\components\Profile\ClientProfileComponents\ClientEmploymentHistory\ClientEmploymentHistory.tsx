// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, Button, View, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';
import moment from 'moment';
import { ClientType } from '../../../../store/dummydata/OperatorsDummyData';

interface ClientEmploymentHistoryProps {
  oper: any;
}

const ClientEmploymentHistory: React.FC<ClientEmploymentHistoryProps> = ({
  oper,
}) => {
  const { employments } = oper;
  return (
    <>
      <Card className='xl:w-full xl:mx-auto p-6 xl:max-w-[312px]'>
        <View className='flex items-center justify-between'>
          <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>
            Employment history
          </Text>
        </View>
        <View className='mt-[20px] flex flex-col gap-2'>
          {employments?.length === 0 ? (
            <NoDataClientProfile />
          ) : (
            employments?.map(
              (
                { start_date, job_title, job_description, end_date }: any,
                index: number,
              ) => {
                let formattedStartDate =
                  moment(start_date).format('DD.MM.YYYY');
                let formattedEndDate;
                end_date
                  ? (formattedEndDate = moment(end_date).format('DD.MM.YYYY'))
                  : (formattedEndDate = null);

                return (
                  <View key={index} className='flex  gap-3 px-2'>
                  <View className='flex flex-col py-1'>
                    <span className='material-icons text-xs text-[#323C58]'>fiber_manual_record</span>
                    <div className='flex grow w-[1px] bg-[#BBC1D3] gap-2 ml-[5px]' />
                  </View>
                  <View className='flex flex-col py-1 mt-1'>
                    <Text className='text-[15px] font-normal leading-5 rubik !text-[#1A1A1A]'>
                      {formattedStartDate} - {formattedEndDate || 'Now'}
                    </Text>
                    <Text className='text-[14px] font-normal leading-5 rubik !text-[#323C58]'>{job_title}</Text>
                    <Text className='font-normal text-[14px] leading-5 rubik !text-[#383838]'>{job_description}</Text>
                  </View>
                </View>
                );
              },
            )
          )}
        </View>
      </Card>
    </>
  );
};

export default ClientEmploymentHistory;
