import surelyMessageIcon from 'src/assets/icons/surelyMessageIcon.svg';
import { useChatContext } from 'src/context/ChatContext';

const EscrowPaidMessage = ({ message, outstanding }) => {
  const { contract } = useChatContext();

  const nameShown = message?.sender?.name;
  const escrowAmount = contract?.escrow_amount;
  const outstandingAmount = contract?.pay_amount

  if (outstanding) {
    return (
      <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
        <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
          <img className='w-full' src={surelyMessageIcon} />
        </div>
        <div className='rubik flex w-full flex-col gap-[3px] text-left'>
          <h2 className='overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Payment completed</h2>
          <p className='overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
            <span className='text-[14px] leading-5 text-[#323C58] font-medium'>{nameShown}</span> added{' '}
            <span className='text-[14px] leading-5 text-[#323C58] font-medium'>£{outstandingAmount}</span> to finalize the payment amount.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex items-start gap-4 rounded-lg border border-[#0DA934] bg-[#E6FEF3] p-4 pr-[60px] lg:items-center'>
      <div className='h-8 w-8 shrink-0 lg:h-12 lg:w-12'>
        <img className='w-full' src={surelyMessageIcon} />
      </div>
      <div className='rubik flex w-full flex-col gap-[3px] text-left'>
        <h2 className='overflow-ellipsis text-[16px] font-medium leading-5 text-[#1A1A1A]'>Money added to escrow</h2>
        <p className='overflow-ellipsis text-[14px] leading-5 text-[#383838]'>
          <span className='text-[14px] leading-5 text-[#323C58] font-medium'>{nameShown}</span> added{' '}
          <span className='text-[14px] leading-5 text-[#323C58] font-medium'>£{escrowAmount}</span> to contract escrow.
        </p>
      </div>
    </div>
  );
};

export default EscrowPaidMessage;
