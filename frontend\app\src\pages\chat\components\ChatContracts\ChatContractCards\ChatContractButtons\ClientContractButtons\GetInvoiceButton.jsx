import { Link } from 'react-router-dom';

const ClientGetInvoiceButton = ({ contractId, contract }) => {
  const isFullyPaid = contract?.escrow_status === 'paid' && contract?.payment_status === 'paid';

  if (!isFullyPaid) {
    return null;
  }

  return (
    <Link
      to={`/invoice/${contractId}`}
      target='_blank'
      className='flex cursor-pointer items-center justify-center gap-2 rounded-[8px] border border-[#DFE2EA] py-[10.5px]'
    >
      <p className='rubik flex items-center gap-2 text-[16px] font-medium leading-[24px] text-[#0B80E7]'>
        <span className='material-icons-outlined w-6'>receipt_long</span> Get invoice
      </p>
    </Link>
  );
};

export default ClientGetInvoiceButton;
