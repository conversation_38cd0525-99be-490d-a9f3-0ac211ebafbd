// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Button, View, Text, Card, useToggle } from 'reshaped';
import { getRating } from 'src/services/review';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import moment from 'moment';
import { AppContext } from 'src/context/AppContext';
import OperatorReviewModal from './OperatorReviewModal';

interface OperatorReviewSectionProps {
  oper: any;
}

const OperatorReviewSection: React.FC<any> = () => {
  const { active, activate, deactivate } = useToggle(false);
  const { operatorId } = useContext(AppContext);
  const [showAll, setShowAll] = useState(false);
  const [allReviewAndComent, setAllReviewAndComent] = useState<any>();

  const toggleLoadMore = () => {
    setShowAll(!showAll);
  };

  useEffect(() => {
    getRating(operatorId).then((data: any) => {
      // const reviewsArray = Array.isArray(data) ? data : data?.reviews || [];
      setAllReviewAndComent(data.data);
    });
  }, [operatorId]);

  const onlyForReviewAnComent = allReviewAndComent?.slice(0, 4);

  return (
    <View>
      <View className='mt-[24px] grid grid-cols-1 gap-4 md:grid-cols-2'>
        {onlyForReviewAnComent?.map((review: any, index: any) => {
          const makeRatingAvgNumber = +review?.rating_avg;
          return (
            <Card key={review.id} className='flex flex-col'>
              <View className='flex items-center justify-between'>
                {[...Array(5)].map((_, starIndex) => (
                  <span key={starIndex} className={`material-icons ${starIndex < makeRatingAvgNumber ? 'text-[#F4BF00]' : 'text-[#C7CDDB]'}`}>
                    star
                  </span>
                ))}
                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323C58] '>{makeRatingAvgNumber.toFixed(1)}</Text>
                <Text className='rubik text-[13px] font-normal leading-[16px] text-[#444B5F] '>{moment(review?.date).format('ddd D MMM')}</Text>
              </View>
              <Text className='rubik mt-[8px] text-[14px] font-medium leading-[20px] text-[#14171F]'>{review.name}</Text>
              <Text className='rubik mt-[4px] truncate text-[14px] font-normal italic leading-[20px] text-[#383838] '>"{review?.comment}"</Text>
            </Card>
          );
        })}
      </View>
      {/* <div className='mx-auto mt-3 flex w-full justify-center'>
        <button onClick={() => activate()} className='btn-no-hover bg-transparent'>
          Load More
        </button>
      </div> */}
      <Button className='border-5 mx-auto mt-3 flex h-[40px] w-[105px] rounded-[4px] border-[#000] !bg-[#fff]' onClick={() => activate()}>
        <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Load More</Text>
      </Button>
      <OperatorReviewModal active={active} deactivate={deactivate} allReviewAndComent={allReviewAndComent} />
    </View>
  );
};

export default OperatorReviewSection;
