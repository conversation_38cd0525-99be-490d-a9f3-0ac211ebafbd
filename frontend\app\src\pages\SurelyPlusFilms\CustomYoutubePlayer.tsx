// @ts-nocheck
import React, { useEffect, useRef } from 'react';

interface CustomYouTubePlayerProps {
  videoId: string;
}

const CustomYouTubePlayer: React.FC<CustomYouTubePlayerProps> = ({
  videoId,
}) => {
  const playerRef = useRef<YT.Player | null>(null);

  useEffect(() => {
    const onPlayerReady = (event: YT.PlayerEvent) => {
      // Get and log the video duration
      const videoDuration = event.target.getDuration();

      // Add a custom play icon using Tailwind CSS
      if (event.target.getIframe()) {
        event.target.getIframe().style.background =
          'url("path/to/your/custom-play-icon.png") center/cover no-repeat';
      }

      // Create an element to display video length
      const durationElement = document.createElement('div');
      durationElement.innerText = formatDuration(videoDuration);
      durationElement.className =
        'absolute bottom-0 right-0 p-2 bg-black text-white';
      if (event.target.getIframe() && event.target.getIframe().parentNode) {
        event.target.getIframe().parentNode.appendChild(durationElement);
      }
    };

    const formatDuration = (duration: number): string => {
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    };

    if (window.YT) {
      playerRef.current = new window.YT.Player('youtubePlayer', {
        videoId: videoId,
        events: {
          onReady: onPlayerReady,
        },
        playerVars: {
          origin: window.location.origin,
        },
      });
    }

    return () => {
      // Cleanup code if needed
      // For example, removing event listeners or clearing intervals
    };
  }, [videoId]);

  return (
    <div className='relative'>
      <div className='aspect-w-16 aspect-h-9'>
        {/* The iframe element */}
        <div
          className='h-full w-[424px]'
          id='youtubePlayer'
          ref={playerRef}
        ></div>
      </div>
    </div>
  );
};

export default CustomYouTubePlayer;
