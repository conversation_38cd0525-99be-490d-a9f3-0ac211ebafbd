// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, Button, View, Divider, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import { AppContext } from '../../../../context/AppContext';

interface LanguageData {
  language: any;
  level: any;
}

const OperatorLanguageCard: React.FC = () => {
  const navigate = useNavigate();
  const { testlang, languages } = useContext(AppContext);

  return (
    <Card className='xl:w-[424px] h-[auto] xl:mx-auto pr-[24px] pl-[24px] md:mt-[24px] md:mb-[100px] '>
      <View className='flex items-center justify-between'>
        <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>Languages spoken</Text>
        <Button
          icon={() => <span className='material-icons-outlined text-white text-base mt-[-2px]'>edit</span>}
          onClick={() =>
            navigate('/operator-settings-profile-details', {
              state: {
                activeTab: '3',
              },
            })
          }
          className='!w-[12px] !h-[12px] !rounded-full border border-[#323C58] !bg-[#323C58] p-0 pb-1'
        ></Button>
      </View>
      <View className='mt-[12px] '>
        {testlang?.length === 0 ? (
          <NoDataProfile />
        ) : (
          testlang
            ?.slice()
            ?.sort((a: any, b: any) => a.language.localeCompare(b.language))
            ?.map((data: any, index: any) => (
              <React.Fragment key={index}>
                <Text className='text-[15px] font-normal leading-[20px] rubik !text-[#1A1A1A] mt-[20px] '>
                  {data.language}
                </Text>
                <Text className='text-[14px] font-normal leading-[20px] rubik !text-[#383838] mt-[1px]'>
                  {data.level}
                </Text>
                {index !== testlang.length - 1 && <Divider className='h-[1px] w-full mt-[12px]' />}
              </React.Fragment>
            ))
        )}
      </View>
    </Card>
  );
};

export default OperatorLanguageCard;
