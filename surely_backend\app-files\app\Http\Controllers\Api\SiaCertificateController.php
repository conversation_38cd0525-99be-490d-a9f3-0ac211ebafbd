<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SiaCertificateResource;
use App\Models\SiaCertificate;
use Illuminate\Http\Request;

class SiaCertificateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $siaCertificate = SiaCertificate::where('user_id', auth()->id())->first();

        if (!$siaCertificate) {
            return response()->json([
                'error' => true,
                'message' => 'No data found for sia certificate!',
                'data' => [],
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => new SiaCertificateResource($siaCertificate),
        ]);
    }

    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $siaCertificate = SiaCertificate::where('user_id', auth()->id())->first();

        if (!$siaCertificate) {
            $request->merge([
                'user_id' => auth()->id()
            ]);

            $siaCertificate = SiaCertificate::create($request->all());
        }

        $siaCertificate->update($request->all());

        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => new SiaCertificateResource($siaCertificate),
        ]);
    }
}
