
			[data-rs-theme="productTheme"][data-rs-color-mode="light"], [data-rs-theme="productTheme"][data-rs-color-mode="dark"] {
					--rs-font-family-title: BlinkMacSystemFont, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
--rs-font-family-body: BlinkMacSystemFont, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
--rs-font-weight-regular: 400;
--rs-font-weight-medium: 500;
--rs-font-weight-bold: 700;
--rs-font-weight-heavy: 800;
--rs-font-weight-black: 900;
--rs-font-size-title-1: 96px;
--rs-line-height-title-1: 100px;
--rs-font-family-title-1: var(--rs-font-family-title);
--rs-font-weight-title-1: var(--rs-font-weight-heavy);
--rs-font-size-title-2: 80px;
--rs-line-height-title-2: 84px;
--rs-font-family-title-2: var(--rs-font-family-title);
--rs-font-weight-title-2: var(--rs-font-weight-heavy);
--rs-font-size-title-3: 64px;
--rs-line-height-title-3: 68px;
--rs-font-family-title-3: var(--rs-font-family-title);
--rs-font-weight-title-3: var(--rs-font-weight-heavy);
--rs-font-size-title-4: 56px;
--rs-line-height-title-4: 60px;
--rs-font-weight-title-4: var(--rs-font-weight-bold);
--rs-font-size-title-5: 48px;
--rs-line-height-title-5: 52px;
--rs-font-weight-title-5: var(--rs-font-weight-bold);
--rs-font-size-title-6: 36px;
--rs-line-height-title-6: 40px;
--rs-font-weight-title-6: var(--rs-font-weight-bold);
--rs-font-size-featured-1: 32px;
--rs-line-height-featured-1: 40px;
--rs-font-size-featured-2: 24px;
--rs-line-height-featured-2: 32px;
--rs-font-size-featured-3: 20px;
--rs-line-height-featured-3: 28px;
--rs-font-size-body-1: 18px;
--rs-line-height-body-1: 24px;
--rs-font-size-body-2: 16px;
--rs-line-height-body-2: 24px;
--rs-font-size-body-3: 14px;
--rs-line-height-body-3: 20px;
--rs-font-size-caption-1: 12px;
--rs-line-height-caption-1: 16px;
--rs-font-size-caption-2: 10px;
--rs-line-height-caption-2: 12px;
--rs-unit-base: 4px;
--rs-unit-radius-small: 4px;
--rs-unit-radius-medium: 8px;
--rs-unit-radius-large: 12px;
--rs-unit-x1: 4px;
--rs-unit-x2: 8px;
--rs-unit-x3: 12px;
--rs-unit-x4: 16px;
--rs-unit-x5: 20px;
--rs-unit-x6: 24px;
--rs-unit-x7: 28px;
--rs-unit-x8: 32px;
--rs-unit-x9: 36px;
--rs-unit-x10: 40px;
--rs-color-black: #000000;
--rs-color-white: #FFFFFF;
--rs-color-on-background-primary: #fff;
--rs-color-on-background-positive: #fff;
--rs-color-on-background-critical: #fff;
--rs-color-rgb-black: 0, 0, 0;
--rs-color-rgb-white: 255, 255, 255;
--rs-duration-fast: 200ms;
--rs-duration-medium: 300ms;
--rs-duration-slow: 400ms;
--rs-easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
--rs-easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
--rs-easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
--rs-shadow-raised: 0px 2px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px -1px rgba(0, 0, 0, 0.1);
--rs-shadow-overlay: 0px 5px 10px rgba(0, 0, 0, 0.05), 0px 15px 25px rgba(0, 0, 0, 0.07);
			}
		
			[data-rs-theme="productTheme"][data-rs-color-mode="light"] {
					--rs-color-foreground-neutral: #14171F;
--rs-color-foreground-neutral-faded: #4D5874;
--rs-color-foreground-disabled: #C7CDDB;
--rs-color-foreground-primary: #0B80E7;
--rs-color-foreground-positive: #05751F;
--rs-color-foreground-critical: #CB101D;
--rs-color-background-neutral: #DFE2EA;
--rs-color-background-neutral-faded: #F3F4F6;
--rs-color-background-neutral-highlighted: #D4D8E3;
--rs-color-background-disabled: #ECEEF3;
--rs-color-background-disabled-faded: #F4F5F7;
--rs-color-background-primary: #0B80E7;
--rs-color-background-primary-highlighted: #6d6bf5;
--rs-color-background-primary-faded: #EDECFD;
--rs-color-background-positive: #118850;
--rs-color-background-positive-faded: #EBFEF6;
--rs-color-background-positive-highlighted: #009950;
--rs-color-background-critical: #E22C2C;
--rs-color-background-critical-faded: #FEF1F2;
--rs-color-background-critical-highlighted: #eb4747;
--rs-color-border-neutral: #BBC1D3;
--rs-color-border-neutral-faded: #DFE2EA;
--rs-color-border-disabled: #DFE2EA;
--rs-color-border-primary: #0B80E7;
--rs-color-border-primary-faded: #D7D5FB;
--rs-color-border-positive: #05751F;
--rs-color-border-positive-faded: #CDEDD5;
--rs-color-border-critical: #CB101D;
--rs-color-border-critical-faded: #FBD5D8;
--rs-color-background-page: #FFFFFF;
--rs-color-background-page-faded: #FAFAFA;
--rs-color-background-elevation-base: #FFFFFF;
--rs-color-background-elevation-raised: #FFFFFF;
--rs-color-background-elevation-overlay: #FFFFFF;
--rs-color-on-background-neutral: #000;
--rs-color-rgb-background-neutral: 223, 226, 234;
--rs-color-rgb-background-neutral-faded: 243, 244, 246;
--rs-color-rgb-background-neutral-highlighted: 212, 216, 227;
--rs-color-rgb-background-disabled: 236, 238, 243;
--rs-color-rgb-background-disabled-faded: 244, 245, 247;
--rs-color-rgb-background-primary: 90, 88, 242;
--rs-color-rgb-background-primary-highlighted: 109, 107, 245;
--rs-color-rgb-background-primary-faded: 237, 236, 253;
--rs-color-rgb-background-positive: 17, 136, 80;
--rs-color-rgb-background-positive-faded: 235, 254, 246;
--rs-color-rgb-background-positive-highlighted: 0, 153, 80;
--rs-color-rgb-background-critical: 226, 44, 44;
--rs-color-rgb-background-critical-faded: 254, 241, 242;
--rs-color-rgb-background-critical-highlighted: 235, 71, 71;
--rs-color-rgb-background-page: 255, 255, 255;
--rs-color-rgb-background-page-faded: 250, 250, 250;
--rs-color-rgb-background-elevation-base: 255, 255, 255;
--rs-color-rgb-background-elevation-raised: 255, 255, 255;
--rs-color-rgb-background-elevation-overlay: 255, 255, 255;
			}
		
			[data-rs-theme="productTheme"][data-rs-color-mode="dark"] {
					--rs-color-foreground-neutral: #EFF0F1;
--rs-color-foreground-neutral-faded: #C2C8D6;
--rs-color-foreground-disabled: #404A63;
--rs-color-foreground-primary: #9D9CF7;
--rs-color-foreground-positive: #03AB5F;
--rs-color-foreground-critical: #EB6666;
--rs-color-background-neutral: #384056;
--rs-color-background-neutral-faded: #242838;
--rs-color-background-neutral-highlighted: #404A63;
--rs-color-background-disabled: #1C202B;
--rs-color-background-disabled-faded: #161922;
--rs-color-background-primary: #5250F2;
--rs-color-background-primary-highlighted: #5f5df4;
--rs-color-background-primary-faded: #24254C;
--rs-color-background-positive: #06743F;
--rs-color-background-positive-faded: #0F2921;
--rs-color-background-positive-highlighted: #008545;
--rs-color-background-critical: #AB1717;
--rs-color-background-critical-faded: #2F1E1F;
--rs-color-background-critical-highlighted: #c11515;
--rs-color-border-neutral: #49536F;
--rs-color-border-neutral-faded: #313649;
--rs-color-border-disabled: #242938;
--rs-color-border-primary: #0B80E7;
--rs-color-border-primary-faded: #2E3160;
--rs-color-border-positive: #03A059;
--rs-color-border-positive-faded: #163C30;
--rs-color-border-critical: #E95454;
--rs-color-border-critical-faded: #412A2B;
--rs-color-background-page: #0D1117;
--rs-color-background-page-faded: #0F131A;
--rs-color-background-elevation-base: #14181f;
--rs-color-background-elevation-raised: #181D25;
--rs-color-background-elevation-overlay: #1C212B;
--rs-color-on-background-neutral: #fff;
--rs-color-rgb-background-neutral: 56, 64, 86;
--rs-color-rgb-background-neutral-faded: 36, 40, 56;
--rs-color-rgb-background-neutral-highlighted: 64, 74, 99;
--rs-color-rgb-background-disabled: 28, 32, 43;
--rs-color-rgb-background-disabled-faded: 22, 25, 34;
--rs-color-rgb-background-primary: 82, 80, 242;
--rs-color-rgb-background-primary-highlighted: 95, 93, 244;
--rs-color-rgb-background-primary-faded: 36, 37, 76;
--rs-color-rgb-background-positive: 6, 116, 63;
--rs-color-rgb-background-positive-faded: 15, 41, 33;
--rs-color-rgb-background-positive-highlighted: 0, 133, 69;
--rs-color-rgb-background-critical: 171, 23, 23;
--rs-color-rgb-background-critical-faded: 47, 30, 31;
--rs-color-rgb-background-critical-highlighted: 193, 21, 21;
--rs-color-rgb-background-page: 13, 17, 23;
--rs-color-rgb-background-page-faded: 15, 19, 26;
--rs-color-rgb-background-elevation-base: 20, 24, 31;
--rs-color-rgb-background-elevation-raised: 24, 29, 37;
--rs-color-rgb-background-elevation-overlay: 28, 33, 43;
			}
		