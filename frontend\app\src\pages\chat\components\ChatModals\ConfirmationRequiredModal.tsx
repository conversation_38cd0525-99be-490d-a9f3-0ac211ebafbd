// @ts-nocheck
import React from 'react';
import { Text, View, Button, Modal } from 'reshaped';
import { useNavigate } from 'react-router-dom';

interface ConfirmationRequiredModalProps {
  active: boolean;
  deactivate: () => void;
}

const ConfirmationRequiredModal: React.FC<ConfirmationRequiredModalProps> = ({
  active,
  deactivate,
}) => {

  const navigate = useNavigate();

  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='!w-[424px] !h-[auto]'
    >
      <View className='flex flex-col'>
      <svg xmlns="http://www.w3.org/2000/svg" width="47" height="47" viewBox="0 0 47 47" fill="none">
  <path d="M23.5195 10.2098L39.2641 41.7077H7.77498L23.5195 10.2098ZM23.5195 0.549805L0.519531 46.5498H46.5195L23.5195 0.549805ZM25.6104 34.4445H21.4286V39.2866H25.6104V34.4445ZM25.6104 19.9182H21.4286V29.6024H25.6104V19.9182Z" fill="#CB101D"/>
</svg>
        <Text className='text-[#1A1A1A] rubik text-[20px] font-normal mt-[10px]'>Confirmation required
        </Text>
        <Text className='text-[#323C58] rubik text-[15px] font-normal leading-5 mt-[3px]'>
        If you do not agree with shift hours provided by security operative and you have valid reasons for refusal, promptly communicate them to the client via chat.
        </Text>

        <View className='flex flex-row justify-between mt-[20px]'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base w-[173px] h-[48px] mr-[10px]'
          >
            <Text className='rubik text-[16px] leading-[24px] font-medium text-[#323C58]'>No, go back</Text>
          </Button>
          <Button
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] bg-background-base !text-white !bg-[#CB101D] w-[173px] h-[48px]'
          >
           <Text className='rubik text-[16px] leading-[24px] font-medium text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmationRequiredModal;
