<?php

namespace App\DataTables;

use App\Models\Report;
use Carbon\Carbon;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class ReportsDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('action', 'reports.files_modal')
            ->editColumn('date', function ($row) {
                return "
                    <p class='h5'>" . Carbon::parse($row->date)->format('d-m-Y') . "</p>
                ";
            })
            ->editColumn('reporter', function ($row) {
                return "
                    <div>
                        <p class='m-0 h5'>$row->reporter</p>
                        <a href='mailTo:$row->reporter_email' class='text-primary'> $row->reporter_email </a>
                    </div>
                ";
            })
            ->editColumn('reported_user', function ($row) {
                return "
                    <div>
                        <p class='m-0 h5'>$row->reported_user</p>
                        <a href='mailTo:$row->reported_user_email' class='text-primary'> $row->reported_user_email </a>
                    </div>
                ";
            })
            ->editColumn('comment', function ($row) {
                return "<p class='cut-text' style=''>$row->comment</p>";
            })
            ->rawColumns(['reporter', 'reported_user', 'date', 'comment', 'action'])
            ->filter(function ($query) {
                $q = request()->get('search')['value'];
                $query->where('reporter.name', 'like', "%$q%")
                ->orWhere('reporter.email', 'like', "%$q%")
                ->orWhere('reported.name', 'like', "%$q%")
                ->orWhere('reported.email', 'like', "%$q%")
                ->orWhere('reports.created_at', 'like', "%$q%")
                ->orWhere('reports.comment', 'like', "%$q%");
            });
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Report $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Report $model)
    {
        return $model->join('mobile_users as reporter', 'reporter.id', '=', 'reports.author_id')
            ->join('mobile_users as reported', 'reported.id', '=', 'reports.reported_id')
            ->select([
                'reports.id as id',
                'reporter.name as reporter',
                'reporter.email as reporter_email',
                'reported.name as reported_user',
                'reported.email as reported_user_email',
                'reports.created_at as date',
                'reports.comment as comment',
                'reports.files as files',
            ]);
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('reports-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            // ->dom('Bfrtip')
            ->parameters([
                "dom" => "<'dt--top-section'<'row'<'col-sm-12 col-md-6 d-flex justify-content-md-start justify-content-center'B><'col-sm-12 col-md-6 d-flex justify-content-md-end justify-content-center mt-md-0 mt-3'f>>>" .
                    "<'table-responsive'tr>" .
                    "<'dt--bottom-section d-sm-flex justify-content-sm-between text-center'<'dt--pages-count  mb-sm-0 mb-3'i><'dt--pagination'p><'dt--page-length'l>>",
                "lengthMenu" => array(10, 20, 50, 100, 10000),
                'buttons' => [
                    'buttons' => [
                        ['extend' => 'copy', 'className' => 'btn'],
                        ['extend' => 'csv', 'className' => 'btn'],
                        ['extend' => 'excel', 'className' => 'btn'],
                        ['extend' => 'print', 'className' => 'btn'],
                    ]
                ],
                'oLanguage' => [
                    'oPaginate' => [
                        'sPrevious' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                        "sNext" => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                    ],
                    'sInfo' => 'Showing page _PAGE_ of _PAGES_ | Results: _TOTAL_',
                    'sSearch' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                    'sSearchPlaceholder' => 'Search...',
                    "sLengthMenu" => "Results :  _MENU_",
                ],
                'stripeClasses' => [],
            ])
            ->orderBy(1)
            ->ajax([
                'url' => '/reports',
                'type' => 'GET',
                'scheme' => 'https',
            ]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('col-1'),
            Column::make('reporter')->addClass('col-2'),
            Column::make('reported_user')->addClass('col-2'),
            Column::make('date')->addClass('col-2'),
            Column::make('comment')->addClass('col-2'),
            Column::computed('action')
                ->title('Report Files')
                ->exportable(false)
                ->printable(false)
                ->width(60)
                ->addClass('text-center'),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'Reports_' . date('YmdHis');
    }
}
