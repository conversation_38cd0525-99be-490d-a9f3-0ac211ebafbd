// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Button, DropdownMenu, Text, View, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { getGeneral, getProfileDetails } from 'src/services/settings';
import { AuthContext } from 'src/context/AuthContext';
import { AppContext } from 'src/context/AppContext';
import operatordropdown1 from '../../../assets/icons/operatordropdownicon/operatordropdown1.svg';
import operatordropdown2 from '../../../assets/icons/operatordropdownicon/operatordropdown2.svg';
import operatordropdown3 from '../../../assets/icons/operatordropdownicon/operatordropdown3.svg';
import operatordropdown4 from '../../../assets/icons/operatordropdownicon/operatordropdown4.svg';
import operatordropdown5 from '../../../assets/icons/operatordropdownicon/operatordropdown5.svg';
import operatordropdown6 from '../../../assets/icons/operatordropdownicon/operatordropdown6.svg';
import operatordropdown7 from '../../../assets/icons/operatordropdownicon/operatordropdown7.svg';

const OperatorHeaderButton = () => {
  const navigate = useNavigate();
  const { user, unAuthenticateUser } = useContext(AuthContext);
  // const { name, profilePhoto } = useContext(AppContext);

  const name = user?.name ? user?.name : user?.profile?.name
  const profilePhoto = user?.proiflePhoto ? user?.profilePhoto : user?.profile?.profilePhoto

  const [isOpened, setIsOpened] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;
  const lastname = name?.split(' ').pop()?.charAt(0).toUpperCase() + name?.split(' ').pop()?.slice(1);
  const firstname = name?.split(' ').shift()?.charAt(0).toUpperCase() + name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;

  const getImageSrc = (image: any) => {
    if (image && image.startsWith('https://app.surelysecurity.com/storage/')) {
      return image;
    } else if (image) {
      return 'https://app.surelysecurity.com/storage/' + image;
    } else {
      return null;
    }
  };

  const imageUrl = getImageSrc(profilePhoto);

  return (
    <DropdownMenu onOpen={() => setIsOpened(true)} onClose={() => setIsOpened(false)} contentGap={6} position='bottom'>
      <DropdownMenu.Trigger>
        {(attributes) => (
          <Button size='medium' type='submit' rounded={true} className='!bg-[#323c58]' attributes={attributes}>
            <View className='flex flex-row items-center justify-center gap-2 bg-[#323c58] '>
              {profilePhoto ? (
                <Image className='h-[32px] w-[32px] rounded-full bg-[#323c58]' src={imageUrl} />
              ) : (
                <View className='flex h-[32px]  w-[32px] items-center justify-center  rounded-full bg-[#F4F5F7]'>
                  <Text className='rubik mt-[1px] text-[20px] uppercase text-[#323c58]'>{name?.charAt(0)}</Text>
                </View>
              )}
              {!isSmallScreen && <Text className='rubik text-base font-medium leading-5 text-white'>{initialName}</Text>}
              <span className='material-icons-outlined text-white'>{isOpened ? 'close' : 'menu'}</span>
            </View>
          </Button>
        )}
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Section>
          <DropdownMenu.Item onClick={() => navigate('/my-profile')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>person_outline</span> */}
              <img src={operatordropdown1} />
              Profile
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/operator-settings-availability')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              <span className='material-icons-outlined ml-[-1px] text-[18px]'>notifications_active</span>
              Availability
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/chat')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>textsms</span> */}
              <img src={operatordropdown2} />
              Messages
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/search-jobs')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>security</span> */}
              <img src={operatordropdown3} />
              Find jobs
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/favorite-jobs')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>favorite</span> */}
              <img src={operatordropdown4} />
              Favourites list
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/applied-jobs')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <img src={operatordropdown3} /> */}
              <span className='material-icons-outlined text-[16.5px]'>work_outline</span>
              Submitted applications
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/operator-settings-notification')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>visibility</span> */}
              <img src={operatordropdown5} />
              Notifications
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/operator-settings')}>
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>settings</span> */}
              <img src={operatordropdown7} />
              Account settings
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
        <DropdownMenu.Section>
          <DropdownMenu.Item
            onClick={() => {
              unAuthenticateUser();
              navigate('/');
            }}
          >
            <Text className='rubik flex items-center gap-2 font-[14px] font-medium leading-[20px]'>
              {/* <span className='material-icons-outlined'>login</span> */}
              <img src={operatordropdown6} />
              Log out
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};

export default OperatorHeaderButton;
