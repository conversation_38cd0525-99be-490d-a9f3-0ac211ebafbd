// @ts-nocheck
import { <PERSON><PERSON>, <PERSON><PERSON>, Text, View } from 'reshaped';

const BadgeEmergencyHire: React.FC = () => {
  return (
      <button
        className='px-2 py-0 lg:h-[24px] !bg-[#FFDBDB] rounded-[4px] '
      >
        <Text className='rubik items-center text-[12px] text-[#AF0909] font-normal  leading-[16px]'>
        EMERGENCY HIRE
        </Text>
      </button>
  );
};
export default BadgeEmergencyHire;
