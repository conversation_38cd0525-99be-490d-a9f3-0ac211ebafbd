// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Text, Button } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Subscribe from '../Subscribe/Subscribe';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import FacebookContactUs from './ContactUsIcon/FacebookContactUs';
import InstagramContactUs from './ContactUsIcon/InstagramContactUs';
import LinkedinContactUs from './ContactUsIcon/LinkedinContactUs';
import TiktokContactUs from './ContactUsIcon/TiktokContactUs';
import TwiterContactUs from './ContactUsIcon/TwiterContactUs';
import YoutubeContactUs from './ContactUsIcon/YoutubeContactUs';

const ContactUs: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px] '>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-center mx-auto mt-[100px]'>
            <Text className='text-center font-rufina-stencil text-[48px] font-normal leading-[56px] text-[#323C58]'>
              CONTACT US
            </Text>
            <div className='items-center mx-auto bg-[#388DD8] w-[200px] h-[4px] my-4' />
          </View>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-start mx-auto mt-[20px] mb-[20px]'>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Surely Security
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Belle House,
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              1 Hudsons Place,
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Unit 2, Platform 1
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Victoria Mainline Station
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              London, SW1V 1JT
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              T: +44 (0) 20 3582 5523
            </Text>

            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              <a
                className='cursor-pointer text-[16px] leading-[24px] font-normal'
                href='mailto:<EMAIL>'
              >
                <EMAIL>
              </a>
            </Text>
            <View className='flex flex-row mt-5 sm:mt-[14px] rounded-[8px] w-auto '>
              <a
                href='https://www.facebook.com/SurelyUK/'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <FacebookContactUs />
              </a>
              <a
                href='https://twitter.com/SurelySecurity'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <TwiterContactUs />
              </a>
              <a
                href='https://www.linkedin.com/company/surely-security/'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <LinkedinContactUs />
              </a>
              <a
                href='https://www.youtube.com/@SurelySecurity'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <YoutubeContactUs />
              </a>
              <a
                href='https://www.instagram.com/surelysecurity/'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <InstagramContactUs />
              </a>
              <a
                href='https://www.tiktok.com/@surelysecurity?_t=8h8Leo1kIvh&_r=1'
                target='_blank'
                rel='noopener noreferrer'
                className='mr-2 lg:mr-[5px]'
              >
                <TiktokContactUs />
              </a>
            </View>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Surely is a breath of fresh air, an online company with people you
              can speak to. We’re here to transform the way security operatives
              are hired for the benefit of all. If you have any questions, or
              would like to find out more, please get in touch – we look forward
              to hearing from you.
            </Text>
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default ContactUs;
