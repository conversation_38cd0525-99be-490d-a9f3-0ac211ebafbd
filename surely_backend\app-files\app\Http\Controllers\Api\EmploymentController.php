<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserEmploymentResource;
use App\Models\Employment;
use App\Models\MobileUser;
use App\Models\Qualification;
use App\Models\UserResume;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class EmploymentController extends Controller
{
    use \App\Traits\Helpers;

    public function index(): JsonResponse
    {
        $user = MobileUser::with(['employments', 'qualifications', 'userResume'])->find(auth()->id());
        $data = new UserEmploymentResource($user);

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => $data
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $result = [];
        $employments = $request->get('employments');

        $existingEmployments = Employment::where('user_id', auth()->id())->get();
        $existingEmploymentIds = $existingEmployments->pluck('id')->toArray();

        foreach ($employments as $employment) {
            if (isset($employment['id'])) {
                $currentEmployment = Employment::find($employment['id']);
                if ($currentEmployment) {
                    if (!empty($currentEmployment->reference_letter)) {
                        Storage::delete('public/' . $currentEmployment->reference_letter);
                    }
                    $currentEmployment->update($employment);
                    $result[] = $currentEmployment;

                    unset($existingEmploymentIds[array_search($employment['id'], $existingEmploymentIds, true)]);
                }
            } else {
                $employment['user_id'] = auth()->id();
                $employment['reference_letter'] = (!empty($employment['reference_letter'])) ? $this->base64Upload('reference_letter', $employment['reference_letter']) : null;

                $result[] = Employment::create($employment);
            }
        }

        Employment::whereIn('id', $existingEmploymentIds)->delete();

        $qualifications = $request->get('qualifications');

        $existingQualifications = Qualification::where('user_id', auth()->id())->get();
        $existingQualificationIds = $existingQualifications->pluck('id')->toArray();

        foreach ($qualifications as $qualification) {
            if (isset($qualification['id'])) {
                $currentQualification = Qualification::find($qualification['id']);
                if ($currentQualification) {
                    if (!empty($currentQualification->qualification_document)) {
                        Storage::delete('public/' . $currentQualification->qualification_document);
                    }
                    $currentQualification->update($qualification);
                    $result[] = $currentQualification;

                    unset($existingQualificationIds[array_search($qualification['id'], $existingQualificationIds, true)]);
                }
            } else {
                $qualification['user_id'] = auth()->id();
                $qualification['qualification_document'] = (!empty($qualification['qualification_document'])) ? $this->base64Upload('qualification_document', $qualification['qualification_document']) : null;

                $result[] = Qualification::create($qualification);
            }
        }

        Qualification::whereIn('id', $existingQualificationIds)->delete();

        $cv = $request->get('cv');

        $existingCV = UserResume::where('user_id', auth()->id())->first();
        if ($existingCV) {
            if (empty($cv)) {
                Storage::delete('public/'.$existingCV->cv);
                $existingCV->delete();
                $result[] = null;
            }
        } elseif (!empty($cv)) {
            $cv = $this->base64Upload('user_cv', $cv);
            $result[] = UserResume::create(['user_id' => auth()->id(), 'cv' => $cv, 'cv_name' => $request->get('cv_name')]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => $result,
        ]);
    }
}
