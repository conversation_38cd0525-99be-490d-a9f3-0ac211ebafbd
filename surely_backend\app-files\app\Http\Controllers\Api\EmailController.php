<?php

namespace App\Http\Controllers\Api;

use App\Models\MobileUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class EmailController
{

    public function sendEmail(Request $request)
    {
        $user = MobileUser::where('email', $request->get('email'))->first();
        $email = new \SendGrid\Mail\Mail(); 
        $email->setFrom("<EMAIL>", "noreply");
        $email->setSubject("Surely Verification Code");
        $email->addTo($request->get('email'));
        $verificationCode = random_int(100000, 999999);
        $email->addContent("text/html", "<p>Your Surely verification code is: <strong>$verificationCode</strong></p>");
        $sendgrid = new \SendGrid(getenv('SENDGRID_API_KEY'));
        try {
            // $user->verification_code = $verificationCode;
            // $user->save();
            $response = $sendgrid->send($email);
            print $response->statusCode() . "\n";
            print_r($response->headers());
            print $response->body() . "\n";
        } catch (Exception $e) {
            echo 'Caught exception: '. $e->getMessage() ."\n";
        }
    }

    public function sendSMS(Request $request)
    {
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Authorization' => getEnv('MESSAGE_BIRD_ACCESS_KEY')
        ])
        ->post('https://nest.messagebird.com/workspaces/e9fe50bc-d4d6-496f-98b3-dfee520a2876/channels/ecadbefe-c8d8-4fff-ba26-53133156f35b/messages');

        return $response;
    }

}
