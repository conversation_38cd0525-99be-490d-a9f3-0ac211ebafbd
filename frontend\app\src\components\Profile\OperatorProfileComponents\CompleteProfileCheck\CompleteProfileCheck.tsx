// @ts-nocheck
import React, { useState, useContext } from 'react';
import { View, Text, Progress, Actionable, Tooltip, useToggle } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import CompleteProfileModal from '../CompleteProfileModal/CompleteProfileModal';

const CompleteProfileCheck: React.FC = () => {
  const navigate = useNavigate();
  const { active, activate, deactivate } = useToggle(false);
  const [completenessValue, setCompletenessValue] = useState<number>(20);

  const handleCompletenessChange = (value: number) => {
    setCompletenessValue(value);
  };

  return (
    <View className='flex flex-col mt-10 sm:mt-0'>
      <View className='flex flex-row gap-2'>
        <Text className='font-medium rubik text-[#lalala] mt-[0px] mr-[0px]'>
          Profile completeness: {completenessValue}%
        </Text>
        <Tooltip
          text='Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile. '
        >
          {(attributes) => (
            <Actionable attributes={attributes} as='div'>
              <div className='w-[22px] h-[22px] rounded-full border bg-[#C7CDDB]  flex items-center justify-center'>
                <span className='material-icons-outlined text-[12px]'>
                  question_mark
                </span>
              </div>
            </Actionable>
          )}
        </Tooltip>
      </View>
      <Progress value={completenessValue} color='positive' className='mt-2' />
      <button
        className='btn-no-hover text-[#078549] rubik text-[14px] font-medium leading-[20px] underline mt-[16px]'
        style={{ textAlign: 'left', display: 'block', width: 'fit-content' }}
        onClick={activate}
      >
        Complete your profile
      </button>

      <CompleteProfileModal
        active={active}
        deactivate={deactivate}
        onCompletenessChange={handleCompletenessChange}
      />
    </View>
  );
};

export default CompleteProfileCheck;
