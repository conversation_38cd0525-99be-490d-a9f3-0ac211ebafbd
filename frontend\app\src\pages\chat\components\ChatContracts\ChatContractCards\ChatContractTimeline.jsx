import { parse, format } from 'date-fns';

const ChatContractTimeline = ({ start, end }) => {
  const [startDate, startHour] = start?.split(' - ');
  const [endDate, endHour] = end?.split(' - ');

  return (
    <div className='w-full flex gap-3 items-center'>
      <div className='w-full rubik text-[#1A1A1A] font-medium p-4 bg-[#F4F5F7] rounded-lg text-left'>
        Starts:
        <span className='block text-[14px] text-[#323C58] '>{startDate}</span>
        <span className='block text-[14px] text-[#323C58] '>{startHour}</span>
      </div>
      <div className='w-full rubik text-[#1A1A1A] font-medium p-4 bg-[#F4F5F7] rounded-lg text-left'>
        Ends:
        <span className='block text-[14px] text-[#323C58]'>{endDate}</span>
        <span className='block text-[14px] text-[#323C58]'>{endHour}</span>
      </div>
    </div>
  );
};

export default ChatContractTimeline;
