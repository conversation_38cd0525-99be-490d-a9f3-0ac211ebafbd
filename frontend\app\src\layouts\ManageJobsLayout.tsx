import React from 'react';
import { Container } from 'reshaped';

import Header from '../components/Header/Header';

interface Props {
  children?: React.ReactNode;
}

export const ManageJobsLayout = ({ children }: Props): JSX.Element => {
  return (
    <Container padding={0} className="w-[100vw] h-[100vh] bg-[url('src/assets/altBg.jpg')] bg-cover overflow-y-auto">
      <Header />
      <main className='align-center lg:mt-[80px] w-full max-w-[1320px] mx-auto text-center '>
        <section className='w-full mt-[61px] '>
          <article className='flex gap-5 flex-wrap mt-[61px]'>{children}</article>
        </section>
      </main>
    </Container>
  );
};

export default ManageJobsLayout;
