// @ts-nocheck
import React, { useEffect, useMemo, useState } from 'react';
import { Card, View, Text, Button, TextField, Select, Switch, useToggle, Tooltip, Actionable, useToast, Image } from 'reshaped';
import { parseISO, isSameDay, format } from 'date-fns';
import { useNavigate, useLocation } from 'react-router-dom';
import limoreserveskews from '../../assets/images/postjob/limoreserveskews.svg';
import surelyproicon1 from '../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../assets/icons/surelyproicon/surelyproicon7.svg';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { editPostJob } from 'src/services/jobs';
import moment from 'moment';
import CalendarEditJob from 'src/components/Calendars/CalendarEditJob';
import CalendarEditJobNew from 'src/components/Calendars/CalendarEditJobNew';
import AcceptEmergencyHire from '../PostJob/AcceptEmergencyHire';
import ClosePostJobModal from '../PostJob/ClosePostJobModal';
import overrideDuplicates from 'src/utils/overrideDuplicateDates';

const EditPost: React.FC = () => {
  const navigate = useNavigate();
  const routeLocation = useLocation();
  const { job } = routeLocation.state;
  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const { active: active2, activate: activate2, deactivate: deactivate2 } = useToggle(false);
  const { active: active3, activate: activate3, deactivate: deactivate3 } = useToggle(false);
  const toast = useToast();
  const addImageHttps = (image: string | null) => {
    return image && !image.startsWith('https://app.surelysecurity.com/storage/') ? `https://app.surelysecurity.com/storage/${image}` : image;
  };
  const [images, setImages] = useState<(string | null)[]>([addImageHttps(job.images[0]), addImageHttps(job.images[1]), addImageHttps(job.images[2])]);
  const [jobPostName, setJobPostName] = useState<string>(job.post_name);
  const [selectedDates, setSelectedDates] = useState<any>(job.date_range);
  const [shiftData, setShiftData] = useState<any>(job.date_range);

  const [siaLicence, setSiaLicense] = useState<any>(job.sia_licence);
  const [industrySectors, setIndustrySectors] = useState<any>(job.industry_sector);
  const [qualifications, setQualifications] = useState(job.relevant_qualification);
  const [surelyProBadge, setSurelyProBadge] = useState<any>(job.surely_pro_badge);
  const [ratingLevel, setRatingLevel] = useState<number>(job.rating_level);
  const [hourlyRateMin, setHourlyRateMin] = useState(job.hourly_rate_min);
  const [hourlyRateMax, setHourlyRateMax] = useState(job.hourly_rate_max);
  const [jobTitle, setJobTitle] = useState<string>(job.title);
  const [numOperatives, setNumOperatives] = useState<any>(job.nr_of_operatives);
  const [location, setLocation] = useState(job.location);
  const [jobDescription, setJobDescription] = useState<string>(job.description);
  const [dutyOfCare, setDutyOfCare] = useState<string>(job.duty_of_care);
  const [jobBenefits, setJobBenefits] = useState<string>(job.benefits);
  const [isEmergencyHire, setIsEmergencyHire] = useState<boolean>(job.is_emergency_hire);
  const [isInclusivityPledge, setIsInclusivityPledge] = useState<boolean>(job.is_inclusivity_pledge);
  const [showEmergencyHireModal, setShowEmergencyHireModal] = useState(true);

  const [jobPostNameMissing, setJobPostNameMissing] = useState('');
  const [selectedDatesMissing, setSelectedDatesMissing] = useState('');
  const [shiftDataMissing, setShiftDataMissing] = useState('');
  const [siaLicenceMissing, setSiaLicenceMissing] = useState('');
  const [industrySectorsMissing, setIndustrySectorsMissing] = useState('');
  const [qualificationsMissing, setQualificationsMissing] = useState('');
  const [surelyProBadgeMissing, setSurelyProBadgeMissing] = useState('');
  const [ratingLevelMissing, setRatingLevelMissing] = useState('');
  const [hourlyRateMaxMissing, setHourlyRateMaxMissing] = useState('');
  const [jobTitleMissing, setJobTitleMissing] = useState('');
  const [numOperativesMissing, setNumOperativesMissing] = useState('');
  const [locationMissing, setLocationMissing] = useState('');
  const [jobDescriptionMissing, setJobDescriptionMissing] = useState('');
  const [imageSizeError, setImageSizeError] = useState<string | null>(null);
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [maxRateTouched, setMaxRateTouched] = useState(false);
  const [closePopovers, setClosePopovers] = useState(false);

  const priceCloseProtection = 25;
  const priceDoorSupervisor = 12.5;
  const priceSecurityGuard = 11.5; 
  const pricePublicSpaceSurveillance = 11.5;
  const priceCashValuablesTransit = 11.5;
  const priceVehicleImmobilisation = 11.5;

  const handleClosePopovers = async () => setClosePopovers((prevState) => !prevState);

  const handleRateClick = (index: any) => {
    setRatingLevel(index);
    if (ratingLevelMissing) {
      setRatingLevelMissing('');
    }
  };

  const handleHourlyRateMaxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;

    // Ensure inputValue is consistently a string, defaulting to empty if not.
    // This helps prevent errors if autofill provides a non-string value (e.g., null/undefined).
    const valueAsString = (typeof inputValue === 'string') ? inputValue : '';

    const numericValue = valueAsString.replace(/[^0-9.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
    if (!isNaN(parseFloat(numericValue)) || numericValue === '') {
      setHourlyRateMax(numericValue);
      setMaxRateTouched(true);
      setHourlyRateMaxMissing('');
      if (parseFloat(numericValue) >= parseFloat(hourlyRateMin)) {
        setMaxRateTouched(false);
      }
    } else {
      setHourlyRateMaxMissing('');
    }
  };

  const handleAcceptEmergencyHire = (accepted: boolean) => {
    setIsEmergencyHire(accepted);
    setShowEmergencyHireModal(false);
  };

  const handleJobPostNameChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setJobPostName(event.target.value);
    if (jobPostNameMissing && jobPostName.trim() !== '') {
      setJobPostNameMissing('');
    }
  };

  const filterUniqueDates = (array) => {
    const uniqueDatesMap = new Map();

    array.forEach((dateObj) => {
      const parsedStartDate = parseISO(dateObj.start);
      const dayKey = parsedStartDate.toISOString().substr(0, 10);

      if (uniqueDatesMap.has(dayKey)) {
        const existingStartDate = parseISO(uniqueDatesMap.get(dayKey).start);
        if (isSameDay(existingStartDate, parsedStartDate)) {
          uniqueDatesMap.set(dayKey, dateObj);
        }
      } else {
        uniqueDatesMap.set(dayKey, dateObj);
      }
    });

    return Array.from(uniqueDatesMap.values());
  };

  const totalDates = filterUniqueDates([...selectedDates]);

  const sortedDates = totalDates
    .map((item) => ({
      start: parseISO(item.start),
      end: parseISO(item.end),
    }))
    .sort((a, b) => a.start - b.start);

  const submitEditJob = async (emergencyHireDone: boolean) => {
    const requiredFields: { field: any; label: string }[] = [
      { field: jobPostName, label: 'Job Post Name' },
      // { field: shiftData, label: 'Selected Dates' },
      { field: selectedDates, label: 'Selected Dates' },
      // { field: shiftData, label: 'Selected Dates' },
      { field: selectedDates, label: 'Selected Dates' },
      { field: siaLicence, label: 'SIA Licence' },
      { field: jobTitle, label: 'Job Title' },
      { field: industrySectors, label: 'Industry Sectors' },
      { field: qualifications, label: 'Qualifications' },
      { field: surelyProBadge, label: 'Surely Pro Badge' },
      { field: ratingLevel, label: 'Rating Level' },
      { field: hourlyRateMin, label: 'Hourly Rate Min' },
      { field: hourlyRateMax, label: 'Hourly Rate Max' },
      { field: numOperatives, label: 'Number of Operatives' },
      { field: location, label: 'Location' },
      { field: jobDescription, label: 'Job Description' },
    ];

    const missingFields: { field: any; label: string }[] = requiredFields.filter(
      (field) => !field.field || (Array.isArray(field.field) && field.field.length === 0),
    );

    if (missingFields.length > 0) {
      missingFields.forEach((field) => {
        switch (field.label) {
          case 'Job Post Name':
            setJobPostNameMissing('Please enter a catchy job post name');
            break;
          case 'Selected Dates':
            setShiftDataMissing('Please select one or more suitable dates');
            break;
          case 'SIA Licence':
            setSiaLicenceMissing('Please provide your SIA licence information');
            break;
          case 'Job Title':
            setJobTitleMissing('Please add a descriptive job title');
            break;
          case 'Industry Sectors':
            setIndustrySectorsMissing('Please specify industry sectors');
            break;
          case 'Qualifications':
            setQualificationsMissing('Please list the necessary qualifications');
            break;
          case 'Surely Pro Badge':
            setSurelyProBadgeMissing('Please select your Surely Pro Badges');
            break;
          case 'Rating Level':
            setRatingLevelMissing('Please select an appropriate rating level');
            break;
          case 'Hourly Rate Max':
            setHourlyRateMaxMissing('Please provide the maximum hourly rate');
            break;
          case 'Number of Operatives':
            setNumOperativesMissing('Please specify the number of operatives needed');
            break;
          case 'Location':
            setLocationMissing('Please add the job location');
            break;
          case 'Job Description':
            setJobDescriptionMissing('Please provide a detailed job description');
            break;
          default:
            break;
        }
      });

      toast.show({
        title: '',
        text: 'Some fields are missing or incomplete. Please review and fill in the required information.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    } else {
      setJobPostNameMissing('');
      setShiftDataMissing('');
      setSiaLicenceMissing('');
      setIndustrySectorsMissing('');
      setQualificationsMissing('');
      setSurelyProBadgeMissing('');
      setRatingLevelMissing('');
      setHourlyRateMaxMissing('');
      setJobTitleMissing('');
      setNumOperativesMissing('');
      setLocationMissing('');
      setJobDescriptionMissing('');
      setJobPostNameMissing('');
      setShiftDataMissing('');
      setSiaLicenceMissing('');
      setIndustrySectorsMissing('');
      setQualificationsMissing('');
      setSurelyProBadgeMissing('');
      setRatingLevelMissing('');
      setHourlyRateMaxMissing('');
      setJobTitleMissing('');
      setNumOperativesMissing('');
      setLocationMissing('');
      setJobDescriptionMissing('');
      try {
        if (isEmergencyHire && !emergencyHireDone) {
          activate2();
        } else {
          const totalDates = [...selectedDates];
          const filteredDates = filterUniqueDates(totalDates);

          const postjob: any = {
            images: images || [],
            jobPostName,
            shiftData: filteredDates,
            siaLicence,
            industrySectors,
            qualifications,
            surelyProBadge,
            ratingLevel,
            hourlyRateMin,
            hourlyRateMax,
            jobTitle,
            numOperatives,
            location,
            jobDescription,
            dutyOfCare,
            jobBenefits,
            isEmergencyHire,
            isInclusivityPledge,
          };

          const response = await editPostJob({ id: job.id, input: postjob });

          if (!response.error) {
            toast.show({
              title: 'Done!',
              text: 'Job edit successfully',
              startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
            });
            navigate('/manage-jobs');
          } else {
            console.error('Error editing job:', response.error);
          }
        }
      } catch (error) {
        console.error('Error editing job:', error);
      }
    }
  };

  const handleNumOperativesChange = (event: any) => {
    const numericValue = parseInt(event.target.value.replace(/[^0-9]/g, ''), 10);
    setNumOperatives(isNaN(numericValue) ? '' : numericValue);
  };

  const placeholderOptionSia = {
    label: 'Select or type...',
    value: '',
  };

  const placeholderOptionIndustry = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionSurelyBadge = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionRelevant = {
    label: 'Select or type...',
    value: '',
  };

  const handleImageUpload = (index: number) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setImageSizeError('Image size exceeds the limit of 5MB. Please choose a smaller image.');
        toast.show({
          title: 'Error',
          text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }

      setImageSizeError('');

      const reader = new FileReader();
      reader.onloadend = () => {
        const newImages = [...images];
        newImages[index] = reader.result as string;
        setImages(newImages);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = (index: number) => () => {
    const newImages = [...images];
    newImages[index] = null;
    setImages(newImages);
  };

  const handleDecreaseOperatives = () => {
    const newValue = parseInt(numOperatives as string, 10) - 1;
    setNumOperatives(newValue === 0 ? '' : newValue);
    if (newValue !== '') {
      setNumOperativesMissing('');
    }
  };

  const handleIncreaseOperatives = () => {
    const newValue = parseInt(numOperatives as string, 10) + 1;
    setNumOperatives(newValue.toString());
    if (newValue !== '') {
      setNumOperativesMissing('');
    }
  };

  const generateStars = (value: number) => {
    const totalStars = 5;
    const filledStars = value;
    const emptyStars = totalStars - filledStars;

    const stars = [];
    for (let i = 0; i < filledStars; i++) {
      stars.push(
        <span key={i} className='material-icons-outlined text-yellow-400'>
          star
        </span>,
      );
    }
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <span key={i + filledStars} className='material-icons-outlined text-gray-400'>
          star
        </span>,
      );
    }
    return stars;
  };

  const handleSelectSiaLicence = (sia: string) => {
    setSiaLicense([sia]);
  };


  useEffect(() => {
    if (siaLicence.length > 0) {
      const selectedLicencesPrices = siaLicence.map((licence: any) => {
        switch (licence) {
          case 'Close Protection':
            return priceCloseProtection;
          case 'Door Supervisor':
            return priceDoorSupervisor;
          case 'Security Guard':
            return priceSecurityGuard;
          case 'Public Space Surveillance':
            return pricePublicSpaceSurveillance;
          case 'Cash & Valuables in Transit':
            return priceCashValuablesTransit;
          case 'Vehicle Immobilisation':
            return priceVehicleImmobilisation;
          default:
            return 0;
        }
      });

      const minPrice = Math.min(...selectedLicencesPrices);
      setHourlyRateMin(minPrice.toString());
    } else {
      setHourlyRateMin('0');
    }
  }, [siaLicence]);

  const handleRemoveSiaLicence = (sia: string) => {
    setSiaLicense((prevSia: any) => prevSia.filter((selectedSia: any) => selectedSia !== sia));
  };

  const clearAllSelectedSiaLicence = () => {
    setSiaLicense([]);
  };

  const handleSelectSurelyProBadge = (surelyBadge: string) => {
    if (!surelyProBadge.includes(surelyBadge)) {
      setSurelyProBadge((prevSurelyBadge: any) => [...prevSurelyBadge, surelyBadge]);
    }
  };

  const handleRemoveSurelyProBadge = (surelyBadge: string) => {
    setSurelyProBadge((prevSurelyBadge: any) => prevSurelyBadge.filter((selectedSurelyBadge: any) => selectedSurelyBadge !== surelyBadge));
  };

  const clearAllSelectedSurelyProBadge = () => {
    setSurelyProBadge([]);
  };

  const handleSelectIndustry = (industry: string) => {
    if (!industrySectors.includes(industry)) {
      setIndustrySectors((prevSectors: any) => [...prevSectors, industry]);
    }
  };

  const handleRemoveIndustry = (industry: string) => {
    setIndustrySectors((prevSectors: any) => prevSectors.filter((selectedIndustry: any) => selectedIndustry !== industry));
  };

  const clearAllSelectedIndustries = () => {
    setIndustrySectors([]);
  };

  const handleSelectRelavant = (relavant: string) => {
    if (!qualifications.includes(relavant)) {
      setQualifications((prevRelavant: any) => [...prevRelavant, relavant]);
    }
  };

  const handleRemoveRelavant = (relavant: string) => {
    setQualifications((prevRelavant: any) => prevRelavant.filter((selectedRelavant: any) => selectedRelavant !== relavant));
  };

  const clearAllSelectedRelavant = () => {
    setQualifications([]);
  };

  const getIconForSurelyProBadge = (badge: string): string => {
    switch (badge) {
      case 'Customer Service':
        return surelyproicon3;
      case 'Use Of Equipment':
        return surelyproicon2;
      case 'Disability Focus':
        return surelyproicon6;
      case 'Substance Awareness':
        return surelyproicon4;
      case 'Vulnerable People':
        return surelyproicon5;
      case 'Conflict Managament':
        return surelyproicon7;
      default:
        return surelyproicon1;
    }
  };

  const handleDateRangeSelect: any = (date: { start: string; end: string }) => {
    if (date?.start) {
      setSelectedDates([date]);
    } else if (date?.length > 0) {
      setSelectedDates(date);
    }
  };

  const onSubmit = () => {};

  return (
    <div className='grid w-[90%] grid-cols-1 gap-[4px] sm:w-auto'>
      <Text className='font-rufina-stencil text-[32px] font-normal leading-[40px] text-[#1A1A1A]'>Edit your job</Text>
      <Text className='rubik text-center text-[16px] font-normal leading-[24px] text-[#323C58]'>Find the right security operative for your job.</Text>

      <Card
        className={`mt-[32px] h-[auto] rounded-[8px] border p-[24px] shadow-md lg:w-[500px] ${imageSizeError ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
      >
        <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Upload a job image (optional)</Text>
        <View className='mt-2 flex flex-col justify-between gap-3 sm:flex-row sm:gap-0'>
          {images?.map((image, index) => (
            <div key={index} className='relative'>
              {image ? (
                <div className='relative '>
                  <img src={image} alt={`Uploaded Job ${index + 1}`} className='h-[136px] rounded-md object-cover md:w-[138px]' />
                  <button
                    className='absolute right-1 top-1 flex h-[22px] w-[22px] items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                    onClick={handleRemoveImage(index)}
                  >
                    <span className='material-icons text-[16px]'>close</span>
                  </button>
                </div>
              ) : (
                <div className='flex h-[136px] items-center justify-center rounded-[8px] border border-gray-300 bg-[#DBDFEA] md:w-[138px]'>
                  <label htmlFor={`imageInput${index}`}>
                    <span className='material-icons'>add</span>
                  </label>
                  <input id={`imageInput${index}`} type='file' accept='image/*' className='hidden' onChange={handleImageUpload(index)} />
                </div>
              )}
            </div>
          ))}
        </View>
        {imageSizeError && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{imageSizeError}</Text>}
        <Text className='rubik mt-4 text-[14px] font-normal leading-[20px] text-[#3C455D]'>
          It's useful for operatives to see where they'll be working.
        </Text>
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Job post name</Text>

        <div
          className={`mt-[4px] flex flex flex-col justify-between rounded-[4px] border ${jobPostNameMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'} border-solid bg-[#ffff] p-[8px] `}
        >
          <textarea
            name='text'
            className=' border-none  bg-transparent outline-none '
            placeholder='Enter your job post name'
            value={jobPostName || ''}
            // onChange={(event) => setJobPostName(event.target.value)}
            onChange={handleJobPostNameChange}
            maxLength={50}
            rows={Math.max(Math.ceil(jobPostName?.length / 50), 1)}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />

          <p className='mr-[5px] mt-[26px]  text-right text-gray-300'>{50 - (jobPostName?.length || 0)} characters left</p>
        </div>
        {jobPostNameMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{jobPostNameMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Job date range</Text>
        <div className='mt-[4px] flex flex-row'>
          <Button
            className={`flex h-[56px] w-[56px] items-center justify-center rounded-[8px] border !bg-[#F4F5F7] p-2 ${selectedDatesMissing ? 'border-[#CB101D]' : 'border-[#DFE2EA]'}`}
            icon={() => (
              <span className={`material-icons-outlined text-[20px] -mt-0.5 ${selectedDatesMissing ? 'text-[#CB101D]' : 'text-[#14171F]'}`}>
                calendar_today
              </span>
            )}
            onClick={activate1}
          />

          <Text className='rubik ml-[20px] mt-4 text-[14px] font-medium leading-[20px]'>
            {selectedDates.length > 0
              ? `From ${format(sortedDates?.[0]?.start, 'd MMM yyyy')} to ${format(sortedDates?.[sortedDates?.length - 1]?.end, 'd MMM yyyy')}`
              : 'Select a date range'}
          </Text>
        </div>
        {selectedDatesMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{selectedDatesMissing}</Text>}

        {/* <CalendarEditJob
          active={active1}
          deactivate={deactivate1}
          onDateRangeSelect={handleDateRangeSelect}
          shiftData={selectedDates}
          setShiftData={setShiftData}
          setSelectedDates={setSelectedDates}
          onSubmit={onSubmit}
        /> */}

        <CalendarEditJobNew
          handleClosePopovers={handleClosePopovers}
          closePopovers={closePopovers}
          active={active1}
          deactivate={() => handleClosePopovers().then(() => deactivate1())}
          selectedDates={selectedDates}
          handleParentDates={handleDateRangeSelect}
          readOnly={false}
          // onDateRangeSelect={handleDateRangeSelect}
          // shiftData={selectedDates}
          // setShiftData={setSelectedDates}
          // setSelectedDates={setSelectedDates}
          // onSubmit={() => {}}
        />

        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>SIA license</Text>
        <Select
          className={`mt-4 w-full rounded-[4px] ${siaLicenceMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'} border-solid bg-[#ffff] p-2`}
          name='sia_licence'
          placeholder={siaLicence?.length > 0 ? '' : placeholderOptionSia.label}
          options={[
            {
              label: 'Close Protection',
              value: 'Close Protection',
            },
            { label: 'Door Supervisor', value: 'Door Supervisor' },
            { label: 'Security Guard', value: 'Security Guard' },
            {
              label: 'Public Space Surveillance',
              value: 'Public Space Surveillance',
            },
            { label: 'Cash & Valuables in Transit', value: 'Cash & Valuables in Transit' },
            { label: 'Vehicle Immobilisation', value: 'Vehicle Immobilisation' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              setSiaLicense([...siaLicence, selectedOption.value]);
              if (siaLicenceMissing) {
                setSiaLicenceMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-wrap gap-2 sm:w-[250px] md:w-[400px] lg:w-[250px]'>
              {siaLicence.map((selectedSia: any) => (
                <Button
                  key={selectedSia}
                  size='small'
                  rounded={true}
                  elevated={false}
                  onClick={() => handleRemoveSiaLicence(selectedSia)}
                  className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs !text-[#323c58] '
                >
                  <Text color='positive' className='flex items-center gap-1'>
                    <span className='material-icons text-[14px]'>star</span>
                    {selectedSia}
                  </Text>
                </Button>
              ))}
            </div>
          }
        />
        {siaLicenceMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{siaLicenceMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Industry sector</Text>
        <Select
          className={`mt-[4px]  w-full rounded-[4px] p-2 ${industrySectorsMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          name='industry_sectors'
          placeholder={industrySectors?.length > 0 ? '' : placeholderOptionIndustry.label}
          options={[
            {
              label: 'Bars, Clubs & Restaurants',
              value: 'Bars, Clubs & Restaurants',
            },
            { label: 'Restaurants & Hotels', value: 'Restaurants & Hotels' },
            { label: 'Events & Festivals', value: 'Events & Festivals' },
            { label: 'Private Hire', value: 'Private Hire' },
            { label: 'Film, TV & Media', value: 'Film, TV & Media' },
            { label: 'Commercial Offices', value: 'Commercial Offices' },
            { label: 'Construction', value: 'Construction' },
            { label: 'Education', value: 'Education' },
            {
              label: 'Financial & Banking',
              value: 'Financial & Banking',
            },
            { label: 'Government', value: 'Government' },
            { label: 'Healthcare', value: 'Healthcare' },
            { label: 'High Street Retail', value: 'High Street Retail' },
            { label: 'Industrial & Manufacturing', value: 'Industrial & Manufacturing' },
            { label: 'Non-Profit Organisations', value: 'Non-Profit Organisations' },
            { label: 'Tourism', value: 'Tourism' },
            { label: 'Mobile Patrol', value: 'Mobile Patrol' },
            { label: 'Other', value: 'Other' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              setIndustrySectors([...industrySectors, selectedOption.value]);
              if (industrySectorsMissing) {
                setIndustrySectorsMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-col sm:flex-row'>
              <div className='mt-2 flex flex-col flex-wrap gap-2 sm:w-[250px] sm:flex-row md:w-[400px] lg:w-[250px]'>
                {industrySectors.map((selectedIndustry: any) => (
                  <Button
                    key={selectedIndustry}
                    size='small'
                    rounded={true}
                    elevated={false}
                    onClick={() => handleRemoveIndustry(selectedIndustry)}
                    className='max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs '
                  >
                    <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedIndustry}</Text>
                  </Button>
                ))}
              </div>
              {industrySectors?.length > 0 && (
                <Button variant='ghost' className='rubik py-0 font-medium text-[#3C455D] underline' onClick={clearAllSelectedIndustries}>
                  Clear all
                </Button>
              )}
            </div>
          }
        />
        {industrySectorsMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{industrySectorsMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Relavant qualification</Text>
        <Select
          className={`mt-[4px]  w-full rounded-[4px] border p-2 ${qualificationsMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          name='relevant'
          placeholder={qualifications?.length > 0 ? '' : placeholderOptionRelevant.label}
          options={[
            { label: 'Dog Handling Skills', value: 'Dog Handling Skills' },
            { label: 'Driving Skills', value: 'Driving Skills' },
            { label: 'Fire Safety', value: 'Fire Safety' },
            { label: 'First Aid', value: 'First Aid' },
            { label: 'Health & Safety', value: 'Health & Safety' },
            { label: 'Mechanical Restraints', value: 'Mechanical Restraints' },
            { label: 'Mental Health', value: 'Mental Health' },
            { label: 'Counter Terrorism', value: 'Counter Terrorism' },
            { label: 'Other', value: 'Other' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              setQualifications([...qualifications, selectedOption.value]);
              if (qualificationsMissing) {
                setQualificationsMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-col sm:flex-row'>
              <div className='mt-2 flex flex-col flex-wrap gap-2 sm:w-[250px] sm:flex-row md:w-[400px] lg:w-[250px]'>
                {qualifications.map((selectedRelavant: any) => (
                  <Button
                    key={selectedRelavant}
                    size='small'
                    rounded={true}
                    elevated={false}
                    onClick={() => handleRemoveIndustry(selectedRelavant)}
                    className='max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs '
                  >
                    <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedRelavant}</Text>
                  </Button>
                ))}
              </div>
              {qualifications?.length > 0 && (
                <Button variant='ghost' className='rubik py-0 font-medium text-[#3C455D] underline' onClick={clearAllSelectedRelavant}>
                  Clear all
                </Button>
              )}
            </div>
          }
        />
        {qualificationsMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{qualificationsMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>SurelyPro badge</Text>
        <Select
          className={`mt-[4px]  w-full rounded-[4px] border p-2 ${surelyProBadgeMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          name='industry_sectors'
          placeholder={surelyProBadge?.length > 0 ? '' : placeholderOptionSurelyBadge.label}
          options={[
            { label: 'Customer Service', value: 'Customer Service' },
            { label: 'Use of Equipment', value: 'Use Of Equipment' },
            { label: 'Disability Focus', value: 'Disability Focus' },
            { label: 'Substance Awareness', value: 'Substance Awareness' },
            { label: 'Vulnerable People', value: 'Vulnerable People' },
            { label: 'Conflict Managament', value: 'Conflict Managament' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              handleSelectSurelyProBadge(selectedOption.value);
              if (surelyProBadgeMissing) {
                setSurelyProBadgeMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-col sm:flex-row'>
              <div className='mt-2 flex flex-col  flex-wrap gap-2 sm:w-[250px] sm:flex-row md:w-[400px] lg:w-[250px]'>
                {surelyProBadge.map((selectedSurelyBadge: any) => (
                  <Button
                    key={selectedSurelyBadge}
                    size='small'
                    rounded={true}
                    elevated={false}
                    onClick={() => handleRemoveSurelyProBadge(selectedSurelyBadge)}
                    className='max-w-xs overflow-hidden  truncate !bg-[#DDEFFF] px-2 py-1 text-xs '
                  >
                    <div className='flex flex-row'>
                      <img src={getIconForSurelyProBadge(selectedSurelyBadge)} alt={`Icon for ${selectedSurelyBadge}`} className='mr-2 h-4 w-4' />
                      <Text className='rubik font-normal leading-4 text-[#053D6D]'>{selectedSurelyBadge}</Text>
                    </div>
                  </Button>
                ))}
              </div>
              {surelyProBadge?.length > 0 && (
                <Button variant='ghost' className='rubik py-0 font-medium text-[#3C455D] underline' onClick={clearAllSelectedSurelyProBadge}>
                  Clear all
                </Button>
              )}
            </div>
          }
        />
        {surelyProBadgeMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{surelyProBadgeMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Rating level</Text>
        <View className='mt-[5px] flex flex-row gap-[8px]'>
          {[1, 2, 3, 4, 5].map((index) => (
            <span
              key={index}
              onClick={() => handleRateClick(index)}
              className={` material-icons-outlined  ${ratingLevel >= index ? 'text-[#F4BF00]' : 'text-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
            >
              star
            </span>
          ))}
        </View>
        {ratingLevelMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{ratingLevelMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Hourly rate</Text>
        <View className='mt-[4px] flex flex-col  gap-3 sm:flex-row md:items-center'>
          <View className=' w-full'>
            <View className='flex flex-col items-start '>
              <Text className='rubik text-sm font-normal leading-5 text-[#1A1A1A]'>Min</Text>
              <View className='h-[48px] w-full flex-shrink-0 rounded-md border border-[#BBC1D3] px-[12px] py-[14px]'>
                <Text>£{hourlyRateMin}</Text>
              </View>
            </View>
          </View>
          <View className='w-full'>
            <View className='flex flex-col items-start '>
              <Text className='rubik text-sm font-normal leading-5 text-[#1A1A1A]'>Max</Text>
              <input
                name='text'
                placeholder='Max'
                className={`h-[48px] w-full rounded-[8px] rounded-md border bg-[#fff] px-[12px] py-[14px] ${hourlyRateMaxMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
                style={{ outline: 'none' }}
                value={hourlyRateMax !== '' ? `£${hourlyRateMax}` : ''}
                // onInput={(event) => {
                //   const numericValue = (event.target as HTMLInputElement).value.replace(/[^0-9]/g, '');
                //   setHourlyRateMax(numericValue);
                // }}
                onInput={handleHourlyRateMaxChange}
              />
            </View>
          </View>
        </View>
        {hourlyRateMaxMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{hourlyRateMaxMissing}</Text>}
        {maxRateTouched && (
          <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>Maximum hourly rate is less or equal than minimum hourly rate</Text>
        )}
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-5 leading-[20px] text-[#1A1A1A]'>Job Title</Text>
        <div
          className={`mt-[4px] flex flex flex-col justify-between rounded border  bg-[#ffff] p-[8px] ${jobTitleMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'} `}
        >
          <textarea
            name='text'
            className=' border-none  bg-transparent outline-none '
            placeholder='Enter job title - for example: Senior Door Supervisor'
            value={jobTitle || ''}
            // onChange={(event) => setJobTitle(event.target.value)}
            onChange={(event) => {
              const value = event.target.value;
              setJobTitle(value);
              if (value) {
                setJobTitleMissing('');
              }
            }}
            maxLength={50}
            rows={Math.max(Math.ceil(jobTitle?.length / 50), 1)}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />

          <p className='mr-[5px] mt-[26px]  text-right text-gray-300'>{50 - (jobTitle?.length || 0)} characters left</p>
        </div>
        {jobTitleMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{jobTitleMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Number of operatives needed</Text>
        <View className='mt-[4px] flex items-center gap-[12px]'>
          <Button
            className='flex h-[48px] w-[56px] items-center justify-center rounded-[8px] border border-[#DFE2EA] !bg-[#F4F5F7] p-[16px]'
            onClick={handleDecreaseOperatives}
          >
            <View className='flex items-center'>
              <span className='material-icons-outlined'>remove</span>
            </View>
          </Button>
          <input
            name='number operative'
            className={`h-[48px] w-[74.5px] rounded-[4px] rounded-[8px] border bg-[#fff] px-[12px] py-[14px] ${numOperativesMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
            style={{ outline: 'none', textAlign: 'center' }}
            value={numOperatives}
            // onChange={handleNumOperativesChange}
            onChange={(event) => {
              const value = event.target.value;
              setNumOperatives(value);
              if (value) {
                setNumOperativesMissing('');
              }
            }}
          />
          <Button
            className='flex h-[48px] w-[56px] items-center justify-center rounded-[8px] border border-[#DFE2EA] !bg-[#F4F5F7] p-4'
            onClick={handleIncreaseOperatives}
          >
            <View className='flex items-center'>
              <span className='material-icons-outlined'>add</span>
            </View>
          </Button>
        </View>
        {numOperativesMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{numOperativesMissing}</Text>}
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Location</Text>
        <TextField
          name='location'
          className={`mt-[4px] w-full rounded-[4px] border px-[12] py-[14px] ${locationMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          placeholder='E.g. CRO 3RL or 36 Factory Lane'
          // onChange={(event) => setLocation(event.value)}
          onChange={(event) => {
            const value = event.value;
            setLocation(value);
            if (value) {
              setLocationMissing('');
            }
          }}
          value={location}
        />
        {locationMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{locationMissing}</Text>}
        <Text className='rubik mt-2 text-[14px] text-[#3C455D] '>Type postcode to begin</Text>
        {/* <Text className='rubik mt-[16px] text-sm font-medium leading-5 text-[#1A1A1A]'>what3words (optional)</Text>
        <img src={limoreserveskews} className='mt-[8px]' /> */}
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-[20px] leading-[20px] text-[#14171F]'>Job description</Text>

        <div
          className={`mt-[4px] flex flex flex-col justify-between rounded border bg-[#ffff] p-[8px] ${jobDescriptionMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
        >
          <textarea
            name='job_description'
            placeholder='Provide more details about your job task'
            className=' border-none  bg-transparent outline-none'
            value={jobDescription || ''}
            // onChange={(event) => setJobDescription(event.target.value)}
            onChange={(event) => {
              const value = event.target.value;
              setJobDescription(value);
              if (value) {
                setJobDescriptionMissing('');
              }
            }}
            maxLength={500}
            rows={9}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />

          <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{500 - (jobDescription?.length || 0)} characters left</p>
        </div>
        {jobDescriptionMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{jobDescriptionMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Duty of care (optional)</Text>
        <div className='mt-[4px] flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px]'>
          <textarea
            name='duty_of_care'
            placeholder='What you do to look after operatives...'
            className=' border-none  bg-transparent outline-none '
            value={dutyOfCare || ''}
            onChange={(event) => setDutyOfCare(event.target.value)}
            maxLength={250}
            rows={9}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />
          <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{250 - (dutyOfCare?.length || 0)} characters left</p>
        </div>
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Job benefits (optional)</Text>
        <div className='mt-[4px] flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px] '>
          <textarea
            name='job_benefits'
            placeholder='Provide details about your job benefits like free entry, meals, discounts etc.'
            className=' border-none  bg-transparent outline-none '
            value={jobBenefits || ''}
            onChange={(event) => setJobBenefits(event.target.value)}
            maxLength={250}
            rows={9}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />
          <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{250 - (jobBenefits?.length || 0)} characters left</p>
        </div>
        <div className='flex flex-row justify-between'>
          <div className='flex flex-col'>
            <div className='mt-4 flex items-center gap-2'>
              <Text className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A] '>Emergency Hire</Text>
              <Tooltip text='The Emergency Hire feature enables businesses to quickly fill urgent security positions, ensuring the safety and protection of their premises. With this option, there is a 5% extra fee.'>
                {(attributes) => (
                  <Actionable attributes={attributes} as='div'>
                    <div className='ml-2 flex h-[22px] w-[22px] items-center justify-center rounded-full border bg-[#C7CDDB]'>
                      <span className='material-icons-outlined text-[12px]'>question_mark</span>
                    </div>
                  </Actionable>
                )}
              </Tooltip>
            </div>
            <Text className='rubik text-sm text-[#444B5F]'>Certified Security Operatives available immediately</Text>
          </div>
          <Switch name='emergency' checked={isEmergencyHire} onChange={() => activate2()} />
        </div>
        <AcceptEmergencyHire
          active={active2}
          deactivate={deactivate2}
          onAccept={handleAcceptEmergencyHire}
          onDecline={() => {
            setIsEmergencyHire(false);
            deactivate2();
          }}
        />
        <div className='flex flex-row justify-between'>
          <div className='flex flex-col'>
            <div className='mt-4 flex items-center gap-2'>
              <Text className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A]'>Inclusivity Pledge</Text>
              <Tooltip text='After watching our informative video about inclusivity and successfully completing the test Security Operatives will be awarded an Inclusivity Badge. This badge serves as a visible symbol of your commitment to promoting inclusivity and diversity.'>
                {(attributes) => (
                  <Actionable attributes={attributes} as='div'>
                    <div className='ml-2 flex h-[22px] w-[22px] items-center justify-center rounded-full border bg-[#C7CDDB]'>
                      <span className='material-icons-outlined text-[12px]'>question_mark</span>
                    </div>
                  </Actionable>
                )}
              </Tooltip>
            </div>
            <Text className='rubik text-sm text-[#444B5F]'>
              This badge serves as a visible symbol of your commitment to promoting inclusivity and diversity.
            </Text>
          </div>
          <Switch name='inclusivity' checked={isInclusivityPledge} onChange={() => setIsInclusivityPledge((prev) => !prev)} />
        </div>
      </Card>
      <div className='mb-[51.8px] mt-[24px] flex flex-row justify-between'>
        <Button
          variant='outline'
          icon={() => <span className='material-icons-outlined mt-[-2px] text-[22px]'>close</span>}
          onClick={activate3}
          className='border-neutral flex h-[48px] w-[103px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[white] px-4 py-2'
        >
          <Text className=' rubik text-[16px] font-medium leading-[24px] !text-[#14171F]'>Close</Text>
        </Button>

        <ClosePostJobModal active={active3} deactivate={deactivate3} />

        <div
          onClick={submitEditJob}
          className='submit-button border-neutral flex h-[48px] w-[140px] cursor-pointer items-center justify-center gap-2 self-stretch rounded-[8px] border  !bg-[#0B80E7] px-4 py-2 '
        >
          <Text className=' rubik text-[16px] font-medium leading-[24px] !text-[#ffff]'>Edit job</Text>
          <span className='material-icons-outlined mt-[2px] text-[15px] !text-[#ffff]'>arrow_forward_ios</span>
        </div>
      </div>
    </div>
  );
};

export default EditPost;
