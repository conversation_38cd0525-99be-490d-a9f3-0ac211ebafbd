// @ts-nocheck
import React, { useState } from 'react';
import { Card, Text, Button, View } from 'reshaped';
import { ClientType } from '../../../../store/dummydata/OperatorsDummyData';
import surelyproicon1 from '../../../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../../../assets/icons/surelyproicon/surelyproicon7.svg';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';

interface ClientSurleyProBadgesProps {
  oper: any;
}

const ClientSurleyProBadges: React.FC<ClientSurleyProBadgesProps> = ({
  oper,
}) => {
  const { surely_pro_badge  } = oper;
  const [type, setType] = useState();

  const geticonSrc = (type: any) => {
    switch (type) {
      case 'CustomerService':
        return surelyproicon3;
      case 'UseOfEquipment':
        return surelyproicon2;
      case 'DisabilityFocus':
        return surelyproicon6;
      case 'SubstanceAwareness':
        return surelyproicon4;
      case 'VulnerablePeople':
        return surelyproicon5;
      case 'ConflictManagament':
        return surelyproicon7;
      default:
        return surelyproicon1;
    }
  };
  const imageSrc = geticonSrc(type);

  const allBadgesAreInclusivityPledge = surely_pro_badge?.every(
    (badge: any) => badge.type === 'InclusivityPledge',
  );

  return (
    <Card className='xl:w-[424px] h-[auto] lg:mx-auto p-6'>
      <View className='flex items-center justify-between'>
        <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>
          SurelyPro badges
        </Text>
      </View>
      <View className='gap-2 mt-4'>
        {surely_pro_badge?.length === 0 || allBadgesAreInclusivityPledge ? (
          <NoDataClientProfile />
        ) : (
          <div className='gap-2 flex flex-wrap gap-2'>
            {surely_pro_badge
              ?.filter((badge: any) => badge.type !== 'InclusivityPledge')
              ?.map((badge: any, index: any) => (
                <Button
                  key={index}
                  size='small'
                  rounded={true}
                  elevated={false}
                  className='px-2 py-1 border  !bg-[#DDEFFF] text-xs max-w-xs overflow-hidden truncate'
                >
                  <View className='flex flex-row'>
                    {badge.type && (
                      <img
                        src={geticonSrc(badge.type)}
                        alt={badge.type}
                        className='mr-2'
                      />
                    )}
                    <Text className='text-[#053D6D] rubik font-normal leading-4'>
                      {badge.text}
                    </Text>
                  </View>
                </Button>
              ))}
          </div>
        )}
      </View>
    </Card>
  );
};

export default ClientSurleyProBadges;
