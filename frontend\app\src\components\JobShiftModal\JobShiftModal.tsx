import React from 'react';
import { Text, View, Modal, Divider } from 'reshaped';

interface JobShiftModalProps {
  active: boolean;
  deactivate: () => void;
  selectedJob: any;
}

const formatNumber = (number: number) => {
  return number < 10 ? `0${number}` : `${number}`;
};

const formatTime = (date: string) => {
  const dateObj = new Date(date);
  return {
    hours: formatNumber(dateObj.getHours()),
    minutes: formatNumber(dateObj.getMinutes()),
  };
};

const JobShiftModal: React.FC<JobShiftModalProps> = ({ active, deactivate, selectedJob }) => {
  return (
    <Modal active={active} onClose={deactivate} className='p-[24px] lg:w-[904px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Check job hours</Text>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[24px] grid grid-cols-1 justify-end md:grid-cols-2 lg:grid-cols-3'>
          {selectedJob.date_range.map((date: any, index: any) => (
            <View key={index} className='mt-[16px] flex flex-col gap-[8px] lg:w-[260px]'>
              <div className='flex flex-row gap-[8px]'>
                <span className='material-icons-outlined w-1/12 text-sm text-[#0B80E7]'>calendar_today</span>
                <span className='rubik text-[14px] font-medium leading-[20px] text-[#3C455D]'>
                  {new Date(date.start).toLocaleDateString('en-US', {
                    day: 'numeric',
                    month: 'short',
                  })}
                </span>
              </div>
              <View className='flex flex-col gap-[6px]  rounded-[8px] border border-[#DFE2EA] bg-[#fff] p-[16px]'>
                <View className='flex flex-col gap-[12px] rounded-[8px] bg-[#F4F5F7]  p-[16px]'>
                  <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Shift start time:</Text>
                  <View className='flex flex-row gap-[12px]'>
                    <View className='flex items-center justify-center gap-[8px] rounded-[4px] border border-[#DFE2EA] lg:h-[48px] lg:w-[82px]'>
                      <Text className='rubik text-[14px] font-normal leading-[20px] text-[#383838]'>
                        {formatTime(date.start).hours}:{formatTime(date.start).minutes}
                      </Text>
                    </View>
                    <span className='rubik my-auto text-[24px] font-normal leading-[20px] text-[#323C58]'>:</span>
                    <View className='flex items-center justify-center gap-[8px] rounded-[4px] border border-[#DFE2EA] lg:h-[48px] lg:w-[82px]'>
                      <Text className='rubik text-[14px] font-normal leading-[20px] text-[#383838]'>
                        {formatTime(date.start).hours}:{formatTime(date.start).minutes}
                      </Text>
                    </View>
                  </View>
                </View>
                <View className='flex flex-col gap-[12px] rounded-[8px] bg-[#F4F5F7]  p-[16px]'>
                  <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Shift end time:</Text>
                  <View className='flex flex-row gap-[12px]'>
                    <View className='flex items-center justify-center gap-[8px] rounded-[4px] border border-[#DFE2EA] lg:h-[48px] lg:w-[82px]'>
                      <Text className='rubik text-[14px] font-normal leading-[20px] text-[#383838]'>
                        {formatTime(date.end).hours}:{formatTime(date.end).minutes}
                      </Text>
                    </View>
                    <span className='rubik my-auto text-[24px] font-normal leading-[20px] text-[#323C58]'>:</span>
                    <View className='flex items-center justify-center gap-[8px] rounded-[4px] border border-[#DFE2EA] lg:h-[48px] lg:w-[82px]'>
                      <Text className='rubik text-[14px] font-normal leading-[20px] text-[#383838]'>
                        {formatTime(date.end).hours}:{formatTime(date.end).minutes}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    </Modal>
  );
};

export default JobShiftModal;
