// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Text, Button } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import { useNavigate } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Subscribe from '../Subscribe/Subscribe';

import '../../components/Header/HeaderMenu/HeaderMenu.css';

const PrivacyPolicy: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='flex flex-col w-full max-w-[1320px] mx-auto items-center text-center '>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-center mx-auto mt-[100px]'>
            <Text className='text-center font-rufina-stencil text-[48px] font-normal leading-[56px] text-[#323C58]'>
              PRIVACY POLICY
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              This policy (together with our terms of use and any other
              documents referred to on it) sets out the basis on which any
              personal data we collect from you, or that you provide to us, will
              be processed by us. Please read the following carefully to
              understand our views and practices regarding your personal data
              and how we will treat it.
            </Text>
            <div className='items-center mx-auto bg-[#388DD8] w-[200px] h-[4px] my-4' />
          </View>
          <View className='flex flex-col w-[90%] sm:w-[620px] justify-center items-start mx-auto mt-[15px] mb-[20px]'>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              By visiting&nbsp;
              <a
                className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='www.surelysecurity.com'
                target='_blank'
                rel='noopener noreferrer'
              >
                www.surelysecurity.com
              </a>
              &nbsp;you are accepting and consenting to the practices described in
              this policy. For the purpose of the Data Protection Act 1998, the
              data controller is Surely, a trading name of 5AT Limited,
              registered at 20-22 First Floor, Station Road, Knowle, West
              Midlands, United Kingdom, B93 0HT.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              1. Introduction & General Terms
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              This policy not only relates to any personal information you
              provide to our site, it also relates to our use of any personal
              information you provide to us via phone or text, by email, in
              letters, our social media or other correspondence. Surely is
              committed to safeguarding your personal information. Whenever you
              provide such information, we are legally obliged to use your
              information in line with all laws concerning the protection of
              personal information, including the Data Protection Act 1998
              (these laws are referred to collectively in this policy as the
              “data protection laws”). Our site may, from time to time, contain
              hyperlinks to and from websites owned and operated by third
              parties. These third-party websites have their own privacy
              policies and are also likely to use cookies, and we, therefore,
              urge you to review their website privacy policies too before you
              submit any personal data to these websites. They will govern the
              use of personal information you submit, which may also be
              collected by cookies whilst visiting these websites. We do not
              accept any responsibility or liability for the privacy practices
              of such third-party websites, and your use of such websites is
              entirely at your own risk.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              2. Who are we?
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Surely is the trading name of registered company 5AT Limited and
              is a marketplace platform for finding and hiring licenced security
              personnel. We are primarily located in the UK. When we refer to
              “we”’, “us” or “our”, we are referring to Surely, the trading name
              of 5AT.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              3. What information will Surely collect about me?
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              We may collect and process the following data about you:
              Information you give us. You may give us personal information when
              you participate in, access or sign up to any of our products,
              services, or activities, visit our site and/or complete any of our
              online forms, or by corresponding with us by phone, email or
              otherwise. This can consist of information such as your name,
              email address, postal address, landline telephone or mobile
              number, and date of birth, depending on the activity involved.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              Information we collect about you.
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              With regard to each of your visits to our site, we may
              automatically collect the following information: • technical
              information, including Internet Protocol (IP) addresses (an IP
              address is a number that can uniquely identify a specific computer
              or other network device on the internet and is used to connect
              your computer to the Internet) from visitors to our website,
              browser type and version, time zone setting, browser plug-in types
              and versions, operating system and platform. • information about
              your visit, including the full Uniform Resource Locators (URL)
              clickstream to, through and from our site (including date and
              time); information you viewed or searched for; page response
              times, download errors, length of visits to certain pages, page
              interaction information (such as scrolling, clicks, and
              mouse-overs), and methods used to browse away from the page.
              Information we receive from other sources. We may receive
              information about you if you use any of the other services we
              provide. We are also working closely with third parties
              (including, for example, business partners, sub-contractors,
              advertising networks, analytics providers, and search information
              providers) and may receive information about you from them.
            </Text>
            <Text className='rubik text-[24px] leading-[30px] font-normal text-[#383838] mt-[25px] text-left'>
              Cookies:
            </Text>
            <Text className='rubik text-[16px] leading-[24px] font-normal text-[#383838] mt-[12px] text-left'>
              Our site uses cookies to distinguish you from other users of our
              site. This helps us to provide you with a good experience when you
              browse our website and also allows us to improve our site. For
              detailed information about the cookies we use and the purposes for
              which we use them, please read our Cookie Policy.
            </Text>
          </View>
        </View>
      </View>
      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default PrivacyPolicy;
