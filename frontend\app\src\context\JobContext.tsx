// @ts-nocheck
import React, { createContext, useEffect, useState, useMemo, useRef, useContext } from 'react';
import Job, { addJobsFavorite, getAllJobs, removeJobsFavorite } from 'src/services/jobs';
import _ from 'lodash';
import { AuthContext } from './AuthContext';
import { is } from 'date-fns/locale';

const defaultPerPage = '&per_page=20';

export const JobContext = createContext<any>(null);

export const initialJobFilters = {
  postal_code: '',
  job_location_range: '',
  hourly_rate_min: '',
  hourly_rate_max: '',
  sia_licence: [],
  industry_sector: [],
  surely_pro_badge: [],
  is_emergency_hire: false,
  is_inclusivity_pledge: false,
  is_favorite: false,
};

const JobProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isClient } = useContext(AuthContext);
  const [jobs, setJobs] = useState<any>(null);
  const [allJobs, setAllJobs] = useState(null);
  const [totalCount, setTotalCount] = useState<any>(0);
  const [selectedJob, setSelectedJob] = useState();
  const [favourites, setFavourites] = useState(null);
  const [favouritesCount, setFavouritesCount] = useState<any>(0);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [isLastPage, setIsLastPage] = useState(false);
  const [from, setFrom] = useState(0);
  const [isLoadingJobs, setIsLoadingJobs] = useState(false);
  const [isLoadingMoreJobs, setIsLoadingMoreJobs] = useState(false);
  const [isLoadingApplications, setIsLoadingApplications] = useState(false);
  const [filters, setFilters] = useState<any>(initialJobFilters);
  const [appliedJobsCount, setAppliedJobsCount] = useState();

  const debouncedSearchRef = useRef(
    _.debounce((searchKey: string) => {
      setPage(1);
      setSearch(searchKey);
    }, 500),
  );

  const handleSearch = (searchKey: string) => debouncedSearchRef.current(searchKey);

  const toggleEmergencyHire = (isFavoritePage = false) => {
    setPage(1);
    setFilters((prevFilters: any) => {
      const currentEmergencyHire = !prevFilters.is_emergency_hire;

      return {
        ...prevFilters,
        is_favorite: isFavoritePage,
        is_emergency_hire: currentEmergencyHire,
      };
    });
  };

  const toggleInclusivityPledge = (isFavoritePage = false) => {
    setPage(1);
    setFilters((prevFilters: any) => {
      const currentInclusivityPledge = !prevFilters.is_inclusivity_pledge;

      return {
        ...prevFilters,
        is_favorite: isFavoritePage,
        is_inclusivity_pledge: currentInclusivityPledge,
      };
    });
  };

  const handleFilters = (filters: any) => {
    const { postCode, locationRange, minHour, maxHour, siaLicense, industrySectors, surelyProBadge, isEmergencyHire, isInclusivityPledge } = filters;
    const isFavoritePage = window.location.pathname === '/favorite-jobs';

    setFilters((prevFilters: any) => {
      return {
        ...prevFilters,
        postal_code: postCode !== undefined ? postCode : prevFilters?.postal_code,
        job_location_range: locationRange !== undefined ? locationRange : prevFilters?.job_location_range,
        hourly_rate_min: minHour !== undefined ? minHour : prevFilters?.hourly_rate_min,
        hourly_rate_max: maxHour !== undefined ? maxHour : prevFilters?.hourly_rate_max,
        sia_licence: siaLicense ?? [], //TODO: kjo duhej sepse ne nje moment te caktuar kur therrasim handleFilters, siaLicense eshte undefined
        industry_sector: industrySectors ?? [],
        surely_pro_badge: surelyProBadge ?? [],
        is_emergency_hire: isEmergencyHire !== undefined ? isEmergencyHire : prevFilters?.is_emergency_hire,
        is_inclusivity_pledge: isInclusivityPledge !== undefined ? isInclusivityPledge : prevFilters?.is_inclusivity_pledge,
        is_favorite: isFavoritePage,
      };
    });
    setPage(1);
  };

  const resetFilters = () => {
    setFilters({
      postal_code: '',
      job_location_range: '',
      hourly_rate_min: '',
      hourly_rate_max: '',
      sia_licence: [],
      industry_sector: [],
      surely_pro_badge: [],
      is_emergency_hire: false,
      is_inclusivity_pledge: false,
      is_favorite: filters?.is_favorite,
    });
    setPage(1);
  };

  const resetOuterFilters = () => {
    setSearch('');
    setFilters((prevFilters: any) => ({
      ...prevFilters,
      is_emergency_hire: false,
    }));
  };

  const handleSelectedJob = (id: any) => {
    setIsLoadingJobs(true);
    Job.get(id).then((res) => {
      setSelectedJob(res);
      setIsLoadingJobs(false);
    });
  };

  const handleLoadMore = () => {
    setPage((prevPage) => prevPage + 1);
  };

  const addFavorite = async (id: number) => {
    try {
      const response = await addJobsFavorite(id);
      if (!response.error) {
      }
    } catch (error) {
      console.error('error', error);
    }
  };

  const fetchAllJobs = async () => {
    try {
      const response = await getAllJobs();
      if (!response.error) setAllJobs(response.data);
    } catch (error) {
      console.error('Error fetching job', error);
    }
  };

  const loadJobs = () => {
    setIsLoadingJobs(true);
    const allFilters = `&page=${page}${memoizedFilters}`;
    Job.list(allFilters).then((res: any) => {
      setJobs(res?.data);
      setTotalCount(() => res?.meta?.total);
      setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
      setIsLoadingJobs(false);
    });
  };

  const memoizedFilters = useMemo(() => {
    const allFilters = { qs: search, ...filters };

    return Object.keys(allFilters).reduce((acc: any, key: any) => {
      if (allFilters[key]) {
        if (allFilters[key]?.length !== 0) {
          acc += `&${key}=${allFilters[key]}`;
        }
      }
      return acc;
    }, defaultPerPage);
  }, [search, filters, page]);

  const removeFavorite = async (id: number) => {
    try {
      removeJobsFavorite(id).then((res) => {
        setPage(1);
        Job.list(memoizedFilters).then((res: any) => {
          setFavourites(res?.data);
          setFavouritesCount(() => res?.meta?.total);
          setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
          setIsLoadingJobs(false);
        });
      });
    } catch (error) {
      console.error('error', error);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      return;
    }
    if (isClient === false) {
      const allFilters = `&page=${page}${memoizedFilters}`;
      if (page === 1) {
        setIsLoadingJobs(true);
        Job.list(allFilters).then((res: any) => {
          if (allFilters?.includes('is_favorite=true')) {
            setFavourites(res?.data);
            setFavouritesCount(() => res?.meta?.total);
            setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
            setIsLoadingJobs(false);
            return;
          }
          setJobs(res?.data);
          setTotalCount(() => res?.meta?.total);
          setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
          setIsLoadingJobs(false);
        });
      } else {
        setIsLoadingMoreJobs(true);
        Job.list(allFilters).then((res: any) => {
          if (allFilters?.includes('is_favorite=true')) {
            setFavourites(res?.data);
            setFrom(res?.meta?.from);
            setFavouritesCount(res?.meta?.total);
            setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
            setIsLoadingMoreJobs(false);
            return;
          }
          setJobs((prevJobs: any) => [...prevJobs, ...res?.data]);
          setFrom(res?.meta?.from);
          setTotalCount(res?.meta?.total);
          setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
          setIsLoadingMoreJobs(false);
        });
      }
    } else {
      setJobs(null);
      setFavourites([]);
      setTotalCount(0);
      setFavouritesCount(0);
      setIsLastPage(true);
      setIsLoadingJobs(false);
    }
  }, [page, memoizedFilters, isAuthenticated]);

  // useEffect(() => {
  //   if (!isAuthenticated) {
  //     return;
  //   }
  //   const allFilters = `&page=${page}${memoizedFilters}`;
  //   setIsLoadingJobs(true)
  //   Job.list(allFilters).then((res: any) => {
  //     setJobs(res?.data || []);
  //     setTotalCount(() => res?.meta?.total);
  //     setIsLastPage(res?.meta?.last_page == res?.meta?.current_page);
  //     setIsLoadingJobs(false)
  //   });
  // }, [memoizedFilters, isAuthenticated]);

  const contextValue = {
    jobs,
    setJobs,
    selectedJob,
    setSelectedJob,
    handleSelectedJob,
    handleLoadMore,
    filters,
    setFilters,
    toggleEmergencyHire,
    toggleInclusivityPledge,
    handleFilters,
    resetFilters,
    totalCount: totalCount,
    favourites,
    setFavourites,
    favouritesCount,
    addFavorite,
    removeFavorite,
    fetchAllJobs,
    allJobs,
    resetOuterFilters,
    handleSearch,
    search,
    setPage,
    page,
    isLastPage,
    setIsLastPage,
    from,
    loadJobs,
    isLoadingJobs,
    isLoadingMoreJobs,
    setIsLoadingApplications,
    isLoadingApplications,
    appliedJobsCount,
    setAppliedJobsCount,
  };

  return <JobContext.Provider value={contextValue}>{children}</JobContext.Provider>;
};

export default JobProvider;
