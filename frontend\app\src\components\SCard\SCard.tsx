import { Card, View, Divider, Image, Text, Icon } from 'reshaped';

import { IdVerified } from '../../assets/icons';
import BadgeCertified from './BadgeCertified/BadgeCertified';
import CertifiedButton from './CertifiedButton/CertifiedButton';


const SCard: React.FC = () => {
  return (
    <Card className='w-full p-6 border border-[#dfe2ea] drop-shadow-md rounded-lg md:w-[312px]'>
      <View className='flex flex-col items-center gap-5'>
        <View className='w-full flex items-center justify-between'>
          <BadgeCertified />
          <span className='material-icons-outlined border-[#BBC1D3] text-[#C7CDDB]'>
            favorite
          </span>
        </View>
        <View>
          <Image
            className='w-32 h-32 border-gradient'
            src='data:image/jpeg;base64,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'
          />
          <Icon
            className='absolute bottom-0 right-0 w-[50px] h-[51px]'
            svg={IdVerified}
          />
        </View>
        <View className='w-full flex flex-col justify-start gap-1'>
          <View className='flex items-center gap-2'>
            <span className='material-icons text-yellow-400 mr-2'>star</span>
            <Text className='font-medium text-[#323c58]'>4.9(5)</Text>
          </View>
          <View>
            <Text className='font-medium text-[#lalala]'>John Doe</Text>
          </View>
          <View className='flex'>
            <Text>Vlore</Text>
            <span className='material-icons-outlined text-blue-400'>place</span>
          </View>
        </View>
        <Divider className='w-full h-[1px]'></Divider>
        <View className='flex flex-wrap justify-start items-center gap-2'>
          <CertifiedButton />
          <CertifiedButton />
          <CertifiedButton />
          <CertifiedButton />
        </View>
      </View>
    </Card>
  );
};

export default SCard;
