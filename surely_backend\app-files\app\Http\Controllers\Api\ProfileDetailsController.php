<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProfileDetailsResource;
use App\Mail\ProfileVerification;
use App\Models\MobileUser;
use App\Models\UserLanguageLevel;
use App\Notifications\ProfileCompleteVerificationNotification;
use App\Notifications\ProfileIncompleteNotification;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Mail;

class ProfileDetailsController extends Controller
{
    use \App\Traits\Helpers;

    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index()
    {
        $user = MobileUser::find(auth()->id())->load('languages');
        $data = new ProfileDetailsResource($user);

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => $data
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();
        $data = $request->all();

        $data['sia_licence_types'] = $request->get('sia_licence_types');
        $data['industry_sectors'] = $request->get('industry_sectors');

        $existingProfilePhoto = $user->profile_photo;
        $newProfilePhotoData = $request->get('profile_photo');
        $newProfilePhoto = $this->base64Upload('profile_photo', $newProfilePhotoData);

        if ($newProfilePhoto) {
            if (Storage::disk('public')->exists($existingProfilePhoto)) {
                Storage::delete('public/' . $existingProfilePhoto);
            }
            $data['profile_photo'] = $newProfilePhoto;
        } elseif (empty($newProfilePhotoData) && Storage::disk('public')->exists($existingProfilePhoto)) {
            Storage::delete('public/' . $existingProfilePhoto);
            $data['profile_photo'] = '';
        }

        $additionalPictures = $request->get('additional_pictures');
        if (!empty($additionalPictures)) {
            $data['additional_pictures'] = $user->additional_pictures; // Laravel will automatically decode it as an array

            foreach ($additionalPictures as $key => $value) {
                if (empty($value)) {
                    if (isset($data['additional_pictures'][$key])) {
                        $existentPicture = $data['additional_pictures'][$key];
                        if (!empty($existentPicture)) {
                            // Delete the existing image from storage
                            Storage::delete('public/' . $existentPicture);
                            $data['additional_pictures'][$key] = '';
                        }
                    }
                } else {
                    $picture = $this->base64Upload('additional_pictures', $value);
                    if ($picture) {
                        if (isset($data['additional_pictures'][$key])) {
                            $existentPicture = $data['additional_pictures'][$key];
                            if (!empty($existentPicture)) {
                                // Delete the existing image from storage
                                Storage::delete('public/' . $existentPicture);
                            }
                        }

                        $data['additional_pictures'][$key] = $picture;
                    }
                }
            }
        }

        $existingProfileVideo = $user->profile_video;
        $newProfileVideoData = $request->get('profile_video');
        $newProfileVideo = $this->base64Upload('profile_video', $newProfileVideoData);

        if ($newProfileVideo) {
            if (Storage::disk('public')->exists($existingProfileVideo)) {
                Storage::delete('public/' . $existingProfileVideo);
            }
            $data['profile_video'] = $newProfileVideo;
        } elseif (empty($newProfileVideoData) && Storage::disk('public')->exists($existingProfileVideo)) {
            Storage::delete('public/' . $existingProfileVideo);
            $data['profile_video'] = '';
        }

        $languages = $request->get('languages');
        $existingLanguages = UserLanguageLevel::where('mobile_user_id', auth()->id())->get();
        $existingLanguageIds = $existingLanguages->pluck('id')->toArray();
        foreach ($languages as $language) {
            if (isset($language['id'])) {
                $currentLanguage = UserLanguageLevel::find($language['id']);
                if ($currentLanguage) {
                    $currentLanguage->update($language);
                    $result[] = $currentLanguage;

                    unset($existingLanguageIds[array_search($language['id'], $existingLanguageIds, true)]);
                }
            } else {
                $language['mobile_user_id'] = auth()->id();
                UserLanguageLevel::create($language);
            }
        }

        UserLanguageLevel::whereIn('id', $existingLanguageIds)->delete();
        $data['languages'] = $languages;

        try {
            if (
                $user->profile_title && $user->profile_description &&
                $user->industry_sectors && $user->profile_photo &&
                $user->profile_video && $user->additional_pictures &&
                $user->sia_licence_number && $user->languages &&
                $user->qualifications
            ) {
                $user->notify(new ProfileCompleteVerificationNotification($user->name));
            } else {
                $user->notify(new ProfileIncompleteNotification($user->name));
            }
        } catch (Exception $e) {
            //
        }

        $user->update($data);
        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => $data,
        ]);
    }
}
