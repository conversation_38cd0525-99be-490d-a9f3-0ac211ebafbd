interface PaymentData {
    id: number;
    title: string;
    date: string;
    value_type: string;
    value: string;
    countdown: number | string;
    button: string;
  }
  
  const ClientPaymentsDashboardDummyData: PaymentData[] = [
    {
      id: 1,
      title: '<PERSON> - #213245',
      date: 'Wed 8 June 2023',
      value_type: 'Outstanding',
      value: '£890.00',
      countdown: 10,
      button: 'Pay now',
    },
    {
      id: 2,
      title: 'Charlotte Baker - Goodwood Festival',
      date: 'Wed 7 June 2023',
      value_type: 'Payed',
      value: '£340.00',
      countdown: '',
      button: 'Get invoice',
    },
    {
      id: 3,
      title: '<PERSON> - #12345',
      date: 'Wed 18 May 2023',
      value_type: 'Outstanding',
      value: '£890.00',
      countdown: 10,
      button: 'Pay now',
    },
    {
      id: 4,
      title: 'Emily Smith - Goodwood Festival',
      date: 'Wed 7 June 2023',
      value_type: 'Payed',
      value: '£340.00',
      countdown: '',
      button: 'Get invoice',
    },
    {
      id: 5,
      title: 'John <PERSON> - <PERSON>wood Festival',
      date: 'Wed 7 June 2023',
      value_type: 'Payed',
      value: '£240.00',
      countdown: '',
      button: 'Get invoice',
    },
  ];
  
  export default ClientPaymentsDashboardDummyData;
  