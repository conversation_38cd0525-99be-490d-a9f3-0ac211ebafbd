// @ts-nocheck
import { TextField, View, Text, Button, Popover, useToggle, Badge } from 'reshaped';
import { useNavigate, useLocation } from 'react-router-dom';
import { useContext, useState } from 'react';
import OperativesFilter from '../filters/OperativesFilter/OperativesFilter';
import { OperativesContext } from 'src/context/OperativesContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import { useAuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';

const ClientSearchBarOne: React.FC = () => {
  const navigate = useNavigate();
  const { handleSearch, totalCount, favouritesCount, toggleInstantBook, resetOuterFilters, search, filters, toggleBS7858 } =
    useContext(OperativesContext);
  const { openModal } = useModalAction();
  const location = useLocation();
  const currentPath = location.pathname;
  const isFavoritePage = currentPath.includes('favorite-operator');
  const isMapView = window.location.pathname === '/search-operators-map';
  const isSearchView = window.location.pathname === '/search-operator';

  const resultsCount = isFavoritePage ? favouritesCount : totalCount;

  const { active, activate, deactivate } = useToggle(false);

  const innerFilters = { ...filters };

  if (isFavoritePage || isMapView || isSearchView) delete innerFilters.is_favorite && delete innerFilters.instant_book && delete innerFilters.bs7858;

  const outerFilters = { search, is_emergency_hire: filters.is_emergency_hire };

  const innerFilterCount = Object.values(innerFilters).reduce((filterCount, currentFilter) => {
    if (!!currentFilter && currentFilter?.length !== 0) filterCount++;
    return filterCount;
  }, 0);
  const outerFilterCount = Object.values(outerFilters).reduce((filterCount, currentFilter) => {
    if (!!currentFilter && currentFilter?.length !== 0) filterCount++;
    return filterCount;
  }, 0);

  const { user, isAuthenticated } = useAuthContext();
  const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
  const isGuest = accountType === '5';

  const handleSearchInput = (value: string) => {
    if (!isAuthenticated) {
      openModal('GUEST_VIEW');
      return;
    }

    if (isGuest) {
      openModal('GUEST_UPGRADE');
      return;
    }

    handleSearch(value);
  };

  return (
    <>
      <View className='mx-auto flex flex-col rounded-[55px] bg-[#F8F8F8] p-[12px] lg:hidden lg:w-[380px] lg:px-0'>
        <View
          justify={'center'}
          align={'center'}
          borderRadius='circular'
          padding={1}
          className='border-1 h-[56px] border border-solid border-gray-300 lg:w-[380px] '
          maxWidth='100%'
        >
          <TextField
            defaultValue={search}
            size='medium'
            variant='headless'
            startSlot={<span className='material-icons-outlined'>search</span>}
            name='searchInput'
            placeholder='Search for example "CCTV from London"'
            className='w-10/12 !pl-2'
            onChange={({ value }) => handleSearchInput(value)}
          />
        </View>
        <View className='mt-[20px] flex w-full flex-row justify-between gap-[8px] lg:w-[380px] lg:gap-0'>
          {!isMapView && (
            <Button onClick={() => navigate('/search-operators-map')} variant='outline' rounded={true} className='h-[40px] w-full lg:w-[160px]'>
              <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                <span className='material-icons-outlined'>map</span>
                <Text className=' rubik text-base font-medium leading-6'>Map search</Text>
              </View>
            </Button>
          )}
          {isMapView && (
            <Button onClick={() => navigate('/search-operator')} variant='outline' rounded={true} className='h-[40px] lg:w-[160px]'>
              <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                <span className='material-icons-outlined'>grid</span>
                <Text className=' rubik text-base font-medium leading-6'>Grid search</Text>
              </View>
            </Button>
          )}
          <Badge.Container>
            {innerFilterCount !== 0 && (
              <Badge className='cursor-default border border-[#9C9999] bg-[#F4F5F7]' rounded>
                <Text className='rubik'>{innerFilterCount}</Text>
              </Badge>
            )}
            <Button color='black' rounded={true} className='h-[40px] w-full !bg-[#323c58]' onClick={() => activate()}>
              <div className='flex flex-row items-center gap-1'>
                <span className='material-icons-outlined'>tune</span>
                <Text className='w-99 h-23 rubik text-sm font-medium leading-6 lg:text-base'>Filter</Text>
              </div>
            </Button>
          </Badge.Container>
        </View>
        <View className='mt-[20px] flex flex-row justify-between lg:mx-auto lg:w-[360px]'>
          <Text className='rubik leading-16 mt-[10px] text-left text-base font-medium tracking-normal'>{resultsCount} Results</Text>
          <Button variant='ghost' onClick={() => navigate('/post-job')} className='btn-no-hover !bg-transparent'>
            <View direction={'row'} justify={'center'} align={'center'}>
              <span className='material-icons-outlined text-[#0B80E7]'>add</span>

              <Text className='lg:w-99 h-23 rubik text-base font-medium leading-6'>Post a Job</Text>
            </View>
          </Button>
          <Button variant='ghost' onClick={() => toggleInstantBook()} className='btn-no-hover !bg-transparent'>
            <View direction={'row'} justify={'center'} align={'center'} gap={1}>
              <span className={`${filters?.instant_book ? 'material-icons' : 'material-icons-outlined'} -mt-0.5 mr-[3px] text-[20px] text-[#0B80E7]`}>
                {filters?.instant_book ? 'notifications_active' : 'notifications'}
              </span>
              <Text className='w-99 h-23 rubik text-base font-medium leading-6'>Instant Book</Text>
            </View>
          </Button>
          <Button variant='ghost' onClick={() => toggleBS7858()} className='btn-no-hover !bg-transparent'>
            <View direction={'row'} justify={'center'} align={'center'} gap={1}>
              <span className='material-icons-outlined -mt-0.5 mr-[3px] text-[20px] text-[#0B80E7]'>{filters.bs7858 ? 'security' : 'shield'}</span>
              <Text className='w-99 h-23 rubik text-base font-medium leading-6 text-[#14171F]'>BS7858</Text>
            </View>
          </Button>
        </View>
      </View>
      <View
        height={'104px'}
        width={'100%'}
        borderRadius={'circular'}
        direction={'row'}
        align={'center'}
        className='bg-opacity-96 hidden w-full max-w-[1320px] justify-between bg-white px-6 shadow-md lg:flex'
      >
        <View direction={'row'} justify={'start'} align={'center'} gap={3}>
          <View
            direction={'row'}
            justify={'center'}
            align={'center'}
            height='56px'
            borderRadius='circular'
            padding={1}
            className='border-1 border border-solid border-gray-300 bg-[#F8F8F8]  xl:w-[360px]'
            maxWidth='100%'
          >
            <TextField
              defaultValue={search}
              size='medium'
              variant='headless'
              startSlot={<span className='material-icons-outlined'>search</span>}
              name='searchInput'
              placeholder='Search for example "CCTV from London"'
              className='w-10/12 !pl-2'
              onChange={({ value }) => handleSearchInput(value)}
            />
          </View>
        </View>
        <View height={'20px'} justify={'center'} width={'250px'}>
          <Text className='rubik text-left text-[14px] font-normal leading-5 tracking-normal text-[#3C455D]'>
            <span className='rubik text-[14px] font-medium leading-5 text-[#3C455D]'>{resultsCount}</span> results
          </Text>
        </View>

        <View>
          <View className='sm:hidden lg:flex' direction={'row'} justify={'center'} align={'center'} gap={4}>
            <View className='flex gap-2'>
              <Button variant='ghost' onClick={() => navigate('/post-job')} className='btn-no-hover !bg-transparent '>
                <View direction={'row'} justify={'center'} align={'center'}>
                  <span className='material-icons-outlined text-[#0B80E7]'>add</span>
                  <Text className='w-99 h-23 rubik text-[16px] font-medium leading-6 text-[#323C58]'>Post a job</Text>
                </View>
              </Button>
              <Button variant='ghost' onClick={() => toggleInstantBook()} className='btn-no-hover !bg-transparent'>
                <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                  <span className={`${filters?.instant_book ? 'material-icons' : 'material-icons-outlined'} -mt-0.5 text-[20px]  text-[#0B80E7]`}>
                    {filters?.instant_book ? 'notifications_active' : 'notifications'}
                  </span>
                  <Text className='w-99 h-23 rubik text-[16px] font-medium leading-6 text-[#323C58]'>Instant Book</Text>
                </View>
              </Button>
              <Button variant='ghost' onClick={() => toggleBS7858()} className='btn-no-hover !bg-transparent'>
                <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                  <span className='material-icons-outlined -mt-0.5 mr-[3px] text-[20px] text-[#0B80E7]'>
                    {filters.bs7858 ? 'security' : 'shield'}
                  </span>
                  <Text className='w-99 h-23 rubik text-base font-medium leading-6 text-[#323C58]'>BS7858</Text>
                </View>
              </Button>
            </View>
            <View className='flex gap-4 '>
              {!isMapView && (
                <Button variant='outline' rounded={true} onClick={() => navigate('/search-operators-map')}>
                  <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                    <span className='material-icons-outlined text-[20px]'>map</span>
                    <Text className='w-99 h-23 rubik text-[16px] font-medium leading-6 text-[#323C58]'>Map search</Text>
                  </View>
                </Button>
              )}
              {isMapView && (
                <Button variant='outline' rounded={true} onClick={() => navigate('/search-operator')}>
                  <View direction={'row'} justify={'center'} align={'center'} gap={1}>
                    <span className='material-icons-outlined'>grid_on</span>
                    <Text className='w-99 h-23 rubik text-base font-medium leading-6'>Grid search</Text>
                  </View>
                </Button>
              )}
              <Badge.Container>
                {innerFilterCount !== 0 && (
                  <Badge className='cursor-default border border-[#9C9999] bg-[#F4F5F7]' rounded>
                    <Text className='rubik'>{innerFilterCount}</Text>
                  </Badge>
                )}
                <Button color='black' rounded={true} className='!bg-[#323c58]' onClick={() => activate()}>
                  <div className='flex flex-row items-center gap-1'>
                    <span className='material-icons-outlined text-[20px]'>tune</span>
                    <Text className='w-99 h-23 rubik text-sm font-medium leading-6 lg:text-base'>Filter</Text>
                  </div>
                </Button>
              </Badge.Container>
            </View>
            <Button
              onClick={() => {
                resetOuterFilters();
                Array.from(document.getElementsByTagName('input')).forEach((el) => (el.value = ''));
              }}
              variant='ghost'
              className='btn-no-hover !bg-transparent'
            >
              <Text className='w-99 h-23 rubik text-[16px] font-medium leading-6 text-[#323C58] underline '>Reset</Text>
            </Button>
          </View>
        </View>
      </View>
      <OperativesFilter active={active} deactivate={deactivate} innerFilters={innerFilters} />
    </>
  );
};
export default ClientSearchBarOne;
