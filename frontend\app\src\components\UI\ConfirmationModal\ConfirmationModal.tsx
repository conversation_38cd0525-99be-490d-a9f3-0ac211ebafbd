// @ts-nocheck
import React, { useContext } from 'react';
import { Text, View, Button, Modal } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { deleteUser } from 'src/services/settings';
import { AuthContext } from 'src/context/AuthContext';

interface ConfirmationModalProps {
  active: boolean;
  deactivate: () => void;
  successAction?: () => void;
  cancelLabel?: string;
  successLabel?: string;
  title?: string;
  description?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  active,
  deactivate,
  successAction,
  cancelLabel = 'Cancel',
  successLabel = 'Delete',
  title = 'Delete Account',
  description = 'Are you sure you want to delete your account?',
}) => {

  const handleAccount = () => {
    successAction && successAction();
  };
  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='!w-[424px] !h-[auto] p-[24px]'
    >
      <View className='flex flex-col'>
        <View className=''>
          <button
            onClick={deactivate}
            className='flex btn-no-hover items-center justify-end ml-auto p-0'
          >
            <span className='material-icons align-middle text-500'>close</span>
          </button>
          <span className='material-icons-outlined text-[#CB101D] text-[70px]'>
            warning
          </span>
          <Text className='text-[#1A1A1A] rubik text-[20px] font-normal mt-[10px]'>
            {title}
          </Text>
        </View>
        <Text className='text-[#323C58] rubik text-[15px] font-normal leading-5 mt-[3px]'>
          {description}
        </Text>

        <View className='flex flex-row justify-between mt-[20px]'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] w-[195px] h-[48px] mr-[10px]'
          >
            <Text className='rubik text-[#1A1A1A] font-medium leading-[24px] text-[16px]'>
            {cancelLabel}
            </Text>
          </Button>
          <Button
            onClick={handleAccount}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px]  !bg-[#CB101D] w-[195px] h-[48px]'
          >
            <Text className='rubik text-[16px] text-[#FFFFFF] leading-[24px] font-medium'>
            {successLabel}
            </Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmationModal;
