<?php

namespace App\Charts;

use ArielMejiaDev\LarapexCharts\LarapexChart;

class MobileUsersHorizontalBarChart
{
    protected $chart;

    public function __construct(LarapexChart $chart)
    {
        $this->chart = $chart;
    }

    public function build($users): \ArielMejiaDev\LarapexCharts\HorizontalBar
    {
        return $this->chart->horizontalBarChart()
            ->setTitle('Users registered this month')
            // ->setSubtitle('')
            ->setColors(['#323C58'])
            ->addData('Users', array_column($users, 'total'))
            ->setXAxis(array_column($users, 'month'))
            ->setHeight(290);
    }
}
