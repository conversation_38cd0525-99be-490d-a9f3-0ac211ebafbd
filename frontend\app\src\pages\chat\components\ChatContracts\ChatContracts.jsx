import { useContext, useEffect, useState } from 'react';
import { Loader, useToggle } from 'reshaped';
import moment from 'moment';
import { parseISO, format } from 'date-fns';

import { useChatContext } from 'src/context/ChatContext';

import ChatContractCTA from './ChatContractCards/ChatContractCTA';
import ChatContractPaymentStatus from './ChatContractCards/ChatContractPaymentStatus';
import ChatContractStatus from './ChatContractCards/ChatContractStatus';
import ChatContractTimeline from './ChatContractCards/ChatContractTimeline';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

import { contractStatuses } from './contractStatues';
import UpdateHourlyRateModal from '../ChatModals/UpdateHourlyRateModal';
import UpdatePaymentTermsModal from '../ChatModals/UpdatePaymentTermsModal';
import CalendarEditJob from 'src/components/Calendars/CalendarEditJob';
import CalendarEditJobNew from 'src/components/Calendars/CalendarEditJobNew';
import { useAuthContext } from 'src/context/AuthContext';

import { ClientNewContractButton } from './ChatContractCards/ChatContractButtons/ClientContractButtons';
import { useLocation } from 'react-router-dom';
import { AppContext } from 'src/context/AppContext';

const ChatContracts = () => {
  const screenWidth = window.innerWidth;
  const { state } = useLocation();
  const { active, activate, deactivate } = useToggle(false);
  const { handleGetChats, setCurrentChat, currentChat, loadContract, contract, setContract } = useChatContext();
  const { isAuthenticated, isClient, user } = useAuthContext();
  const { notificationSettings } = useContext(AppContext);
  const [showEditPayment, setShowEditPayment] = useState(false);
  const [showPaymentTerms, setShowPaymentTerms] = useState(false);
  const [operator, setOperator] = useState(null);
  const [loadingContracts, setLoadingContracts] = useState(false);
  const [selectedDates, setSelectedDates] = useState(contract?.date_range);
  const [ctaButtonsShown, setCtaButtonsShown] = useState(2);
  const [closePopovers, setClosePopovers] = useState(false);

  const handleClosePopovers = async () => setClosePopovers((prevState) => !prevState);

  const handleCtaButtonsNumber = (count) => {
    setCtaButtonsShown(count);
  };

  const fromOperatorProfile = state?.operator_id;
  const currentContract = contract?.id && {
    ...contract,
    status: contractStatuses.find((status) => status.value == contract?.status)?.label,
  };

  const userId = user?.profile?.id;
  const starts = `${currentContract?.date_range[0]?.start && format(parseISO(currentContract?.date_range[0]?.start), 'dd-MM-yyyy')} - ${
    currentContract?.date_range[0]?.start && format(parseISO(currentContract?.date_range[0]?.start), 'HH:mm')
  }`;
  const ends = `${
    currentContract?.date_range[currentContract?.date_range?.length - 1]?.end &&
    format(parseISO(currentContract?.date_range[currentContract?.date_range?.length - 1]?.end), 'dd-MM-yyyy')
  } - ${
    currentContract?.date_range[currentContract?.date_range?.length - 1]?.end &&
    format(parseISO(currentContract?.date_range[currentContract?.date_range?.length - 1]?.end), 'HH:mm')
  }`;

  const id = currentContract?.id;
  const location = currentContract?.location;

  const contractId = currentChat?.contract?.id;

  const handleDateRangeSelect = async (dates) => {
    if (dates?.length) {
      setSelectedDates([...selectedDates, ...dates]);
      // Add update to contract
      if (contractId) {
        await updateContractShifts([...selectedDates, ...dates]);
      }
    }
    if (dates?.start) {
      setSelectedDates([...selectedDates, dates]);
      // Add update to contract
      if (contractId) {
        await updateContractShifts([...selectedDates, dates]);
      }
    }
  };

  // Add this function to handle contract updates
  const updateContractShifts = async (dateRange) => {
    try {
      const response = await HttpClient.put(`contracts/${contractId}/shifts`, {
        date_range: dateRange,
        message: 'Shift time changed',
        type: 'shift_update_request',
      });

      if (!response.error) {
        setContract(response);
        // Refresh the dates display
        setSelectedDates(response.date_range);
      }
    } catch (error) {
      console.error('Error updating shifts:', error);
    }
  };

  useEffect(() => {}, []);

  useEffect(() => {
    if (currentChat?.id) {
      setOperator(currentChat?.receiver);
    }
  }, [currentChat?.id]);

  useEffect(() => {
    if (!contractId) {
      setLoadingContracts(false);
      setContract(null);
      return;
    }

    setLoadingContracts(true);
    loadContract(contractId).then((res) => {
      setSelectedDates(res?.date_range);
      setLoadingContracts(false);
    });
  }, [contractId]);

  useEffect(() => {
    if (!window.Echo) return;
    window.Echo.channel('surely-development').listen('NewMessageEvent', (e) => {
      const shoudlISeeThis = e.message.receiver_id === userId || e.message.sender_id === userId;
      const shouldThisChatSeeThis = e.message?.chat_id === currentChat?.id;
      if (shoudlISeeThis && shouldThisChatSeeThis) {
        const messageType = e?.message?.type;
        switch (messageType) {
          case 'individual_contract':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                handleGetChats().then(() => {});
                setLoadingContracts(false);
              });
            }
            break;
          case 'accept-shifts':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'shift_update_request':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setSelectedDates(res?.date_range);
                setLoadingContracts(false);
              });
            }
            break;
          case 'hourly_rate_changed':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'complete-shifts':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'confirm-shifts':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'reject':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'cancel':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'outstanding_payment':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'operator_accepted_contract':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                handleGetChats().then(() => {
                  if (e.message.receiver_id === userId) {
                    handleGetMessages();
                  }
                });
                setLoadingContracts(false);
              });
            }
            break;
          case 'escrow_payment':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'client_accepted_applicant':
            {
              setLoadingContracts(true);
              loadContract(e?.message?.contract_id)?.then((res) => {
                setContract(res);
                setLoadingContracts(false);
              });
            }
            break;
          case 'no_contract':
            handleGetChats().then((res) => {
              setCurrentChat(res?.[0]);
            });
            break;
          default:
            if (e.message.type !== 'text') break;
        }
      }
    });
  }, [isAuthenticated, userId, currentChat]);

  return (
    <div className='h-full w-full rounded-lg rounded-b-none border border-[#DFE2EA] bg-white lg:h-[777px] lg:w-[312px]'>
      <div className='border-b border-[#DFE2EA] px-3 py-4 lg:p-6 lg:px-6'>
        <p className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A] lg:text-left'>Contract details</p>
      </div>
      {loadingContracts ? (
        <div className='flex h-[calc(100dvh-200px)] w-full items-center justify-center lg:h-[545px]'>
          <Loader size='medium' className='h-10 w-10' />
        </div>
      ) : !currentContract?.id ? (
        <div className={`rubik flex h-[calc(100dvh-222px)] flex-col items-center justify-between pt-[44px] text-[16px] text-[#444B5F] lg:h-fit`}>
          <div className='flex w-full flex-col items-center lg:h-[545px]'>
            <SurelyIconNoData />
            <p className='mt-3'>No contract details</p>
          </div>
          {isClient && currentChat?.type === 'no_contract' && (
            <div className='mt-5 w-full px-3 py-6 lg:px-6'>
              <ClientNewContractButton chatId={currentChat?.id} />
            </div>
          )}
        </div>
      ) : (
        <>
          <div
            className={`flex ${ctaButtonsShown === 0 ? 'h-[calc(100dvh-210px)]' : ctaButtonsShown === 1 ? 'h-[calc(100dvh-318px)]' : 'h-[calc(100dvh-380px)]'} flex-col gap-3 overflow-auto p-3 ${ctaButtonsShown === 0 ? 'lg:h-[705px]' : ctaButtonsShown === 1 ? 'lg:h-[610px]' : 'lg:h-[545px]'} lg:p-6`}
          >
            <ChatContractStatus contract={currentContract} />
            {starts !== ' - ' && ends !== ' - ' && <ChatContractTimeline start={starts} end={ends} />}
            <span onClick={activate} className='rubik w-fit cursor-pointer text-left text-[14px] font-medium text-[#323C58] underline'>
              Edit shift details
            </span>
            <ChatContractPaymentStatus contract={currentContract} />
            <span
              onClick={() => {
                setShowEditPayment(true);
              }}
              className='rubik w-fit cursor-pointer text-left text-[14px] font-medium text-[#323C58] underline'
            >
              Edit payment details
            </span>
            <span
              onClick={() => {
                setShowPaymentTerms(true);
              }}
              className='rubik w-fit cursor-pointer text-left text-[14px] font-medium text-[#323C58] underline'
            >
              Payment terms
            </span>
            <div className='rubik w-full rounded-lg bg-[#F4F5F7] p-4 text-left font-medium text-[#1A1A1A]'>
              Detailed location
              <span className='block text-[14px] text-[#323C58]'>{location}</span>
            </div>
          </div>
          <ChatContractCTA contract={currentContract} chat={currentChat} operator={operator} handleCtaButtonsNumber={handleCtaButtonsNumber} />
        </>
      )}
      <UpdateHourlyRateModal
        contract={currentContract}
        active={showEditPayment}
        deactivate={() => {
          setShowEditPayment(false);
        }}
      />
      {selectedDates && (
        <CalendarEditJobNew
          handleClosePopovers={handleClosePopovers}
          closePopovers={closePopovers}
          contractId={currentContract?.id}
          active={active}
          deactivate={() => handleClosePopovers().then(() => deactivate())}
          selectedDates={selectedDates}
          readOnly={isClient === false || contract?.escrow_status !== 'pending'}
        />
      )}
      {showPaymentTerms && (
        <UpdatePaymentTermsModal
          contract={currentContract}
          active={showPaymentTerms}
          deactivate={() => {
            setShowPaymentTerms(false);
          }}
        />
      )}
    </div>
  );
};

export default ChatContracts;
