<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\CardRequest;
use App\Models\Card;
use App\Models\StripeAccount;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Customer;
use Stripe\PaymentMethod;

class CardController extends Controller
{
    public function add_card(CardRequest $request)
    {
        try {
            // Set Stripe API key
            $stripeKey = StripeAccount::first()->secret;
            Stripe::setApiKey($stripeKey);

            // Check for existing customer
            $existingCard = Card::where('mobile_user_id', Auth::guard('api')->id())->first();

            if ($existingCard) {
                $customerId = $existingCard->customer_id;
            } else {
                // Create new customer
                $customer = Customer::create([
                    'description' => auth()->guard('api')->user()->name,
                ]);
                $customerId = $customer->id;
            }

            // Attach payment method to customer
            $paymentMethod = PaymentMethod::retrieve($request->payment_method_id);
            $paymentMethod->attach(['customer' => $customerId]);

            // Save card details
            $card = new Card();
            $card->holder_name = $request->holder_name;
            $card->payment_method_id = $request->payment_method_id;
            $card->stripe_token = $request->payment_method_id;
            $card->type = $paymentMethod->card->brand;
            $card->last_numbers = $paymentMethod->card->last4;
            $card->mobile_user_id = Auth::guard('api')->id();
            $card->customer_id = $customerId;
            $card->country_code = $paymentMethod->card->country;
            $card->save();

            return response()->json([
                'error' => false,
                'message' => 'Card added successfully',
                'data' => $card
            ]);

        } catch (\Exception $e) {
            \Log::error('Card addition failed: ' . $e->getMessage());
            return response()->json([
                'error' => true,
                'message' => 'Failed to add card: ' . $e->getMessage()
            ], 500);
        }
    }

    public function my_cards()
    {

        $cards = Auth::guard('api')->user()->cards;
        $data = array();

        if (!$cards) {
            return response()->json([
                'error' => true,
                'message' => 'This user has no cards!',
            ]);
        }
        
        foreach ($cards as $card) {
            $obj = [
                'id' => $card->id,
                'type' => $card->type ?? '',
                'holder_name' => $card->holder_name,
                'last_numbers' => $card->last_numbers ?? '',
                'payment_method_id' => $card->payment_method_id ?? '',
            ];
            array_push($data, $obj);
        }
        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $data,
        ]);
    }


    public function destroy($id)
    {
        Card::find($id)->delete();

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);

    }
}
