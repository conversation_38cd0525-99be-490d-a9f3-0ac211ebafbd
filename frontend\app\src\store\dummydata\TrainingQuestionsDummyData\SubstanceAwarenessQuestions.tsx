export type QuestionDataSubstanceAwareness = {
    id: number;
    question: string;
    answer_1: string;
    answer_2: string;
    answer_3: string;
    answer_4: string;
    correct_answer: number;
  };
  
  export const substanceAwarenessQuestions: QuestionDataSubstanceAwareness[] = [
    {
      id: 1,
      question:
        'Which of the below could be a misleading sign or symptom of substance abuse?',
      answer_1: 'Hallucinations',
      answer_2: 'Powder under the nose',
      answer_3: 'Used needles/wraps',
      answer_4: 'Slurred words',
      correct_answer: 1,
    },
    {
      id: 2,
      question:
        'Using substances as an escape mechanism is most likely to be a sign of which of the following?',
      answer_1: 'Age',
      answer_2: 'Life events',
      answer_3: 'Family interaction',
      answer_4: 'Gender',
      correct_answer: 3,
    },
    {
      id: 3,
      question:
        'Which of the following is not a main category of substance abuse?',
      answer_1: 'Alcohol',
      answer_2: 'Hallucinogens',
      answer_3: 'Antidepressants',
      answer_4: 'Party drugs',
      correct_answer: 4,
    },
    {
      id: 4,
      question: 'Which of the following can we become reliant on?',
      answer_1: 'Medication',
      answer_2: 'Social media',
      answer_3: 'Dealers',
      answer_4: 'Club bar staff',
      correct_answer: 1,
    },
    {
      id: 5,
      question: 'Which of the following is not a factor why people use substances?',
      answer_1: 'Age',
      answer_2: 'Gender',
      answer_3: 'Social group',
      answer_4: 'Self esteem',
      correct_answer: 3,
    },
  ];
  