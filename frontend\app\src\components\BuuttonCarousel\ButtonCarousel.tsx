// @ts-nocheck
import { useContext, useState } from 'react';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import { Button, Text, Tabs, Image, View, Divider, Card, Icon } from 'reshaped';

const ButtonCarousel = ({ buttons }) => {
  const { openModal } = useModalAction();
  const { isAuthenticated } = useContext(AuthContext);
  const [selectedButtonIndex, setSelectedButtonIndex] = useState(0);

  return (
    <>
      <Carousel
        showArrows={false}
        showStatus={false}
        showThumbs={false}
        infiniteLoop={true}
        showIndicators={false}
        onChange={(index) => setSelectedButtonIndex(index)}
      >
        {buttons.map((button: any, index: any) => (
          <div key={index} className='mx-1'>
            <button
              onClick={() => {
                if (!isAuthenticated) {
                  openModal('REGISTER');
                }
              }}
              className='  mt-[15px] h-[174px] w-full items-start self-stretch rounded-[8px] border border-gray-300 !bg-[#fff] shadow-md sm:mt-[0px] xl:w-[244px]'
            >
              <div className='mx-auto flex w-fit flex-col items-center space-y-4'>
                <Button
                  size='small'
                  rounded={true}
                  elevated={false}
                  className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                >
                  <span className='material-icons text-[16px]   '>star</span>
                  {button.title}
                </Button>
                <hr className='w-full' />
                <div className='flex w-full justify-between '>
                  <p className='text-[17px] font-medium leading-[24px] text-[#323C58]'>{`+${button.available} available`}</p>
                  <span className='material-icons-outlined text-[24]'>arrow_forward_ios</span>
                </div>
              </div>
            </button>
          </div>
        ))}
      </Carousel>
      <View className='mt-4 flex justify-center'>
        {buttons.map((_: any, index: any) => (
          <div
            key={index}
            className={`mx-1 h-[12px] cursor-pointer ${
              selectedButtonIndex === index ? 'w-[22px] rounded-[8px] bg-[#0B80E7]' : 'w-[12px] rounded-[100%] bg-[#C4BCBD]'
            }`}
          />
        ))}
      </View>
    </>
  );
};

export default ButtonCarousel;
