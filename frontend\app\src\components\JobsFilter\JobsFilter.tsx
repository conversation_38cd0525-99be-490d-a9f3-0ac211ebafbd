// @ts-nocheck
import React, { useContext, useState } from 'react';
import { Text, View, Button, Modal, TextField, Select, Switch } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import surelyproicon1 from '../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../assets/icons/surelyproicon/surelyproicon7.svg';
import { JobContext } from 'src/context/JobContext';

const getIconForSurelyProBadge = (badge: string): string => {
  switch (badge) {
    case 'CustomerService':
      return surelyproicon3;
    case 'UseOfEquipment':
      return surelyproicon2;
    case 'DisabilityFocus':
      return surelyproicon6;
    case 'SubstanceAwareness':
      return surelyproicon4;
    case 'VulnerablePeople':
      return surelyproicon5;
    case 'ConflictManagament':
      return surelyproicon7;
    default:
      return surelyproicon1;
  }
};

interface JobsFilterProps {
  active: boolean;
  deactivate: () => void;
  onFilterCountChange: (count: number) => void;
}

const JobsFilter: React.FC<JobsFilterProps> = ({ active, deactivate, onFilterCountChange }) => {
  const { handleFilters, resetFilters, filters: contextFilters } = useContext(JobContext);

  const [postCode, setPostCode] = useState(contextFilters?.postal_code);
  const [locationRange, setLocationRange] = useState(contextFilters?.job_location_range);
  const [siaLicense, setSIALicense] = useState<string[]>(contextFilters?.sia_licence);
  const [industrySectors, setIndustrySectors] = useState<string[]>(contextFilters?.industry_sector);
  const [surelyProBadge, setSurelyProBadge] = useState<string[]>(contextFilters?.surely_pro_badge);
  const [isEmergencyHire, setIsEmergencyHire] = useState<boolean>(contextFilters?.is_emergency_hire);
  const [isInclusivityPledge, setIsInclusivityPledge] = useState<boolean>(contextFilters?.is_inclusivity_pledge);
  const [minHour, setMinHour] = useState(contextFilters?.hourly_rate_min);
  const [maxHour, setMaxHour] = useState(contextFilters?.hourly_rate_max);
  const sliderBackground = `linear-gradient(to right, #323C58 0%, #323C58 ${locationRange}%, #BBC1D3 ${locationRange}%, #BBC1D3 100%)`;
  const isFavoritePage = window.location.pathname === '/favorite-jobs';
  const[isFavorite, setIsFavorite]=useState(isFavoritePage)

  const filters = {
    postCode,
    locationRange,
    minHour,
    maxHour,
    siaLicense,
    industrySectors,
    surelyProBadge,
    isEmergencyHire,
    isInclusivityPledge,
    isFavorite
  };

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocationRange(Number(event.target.value));
  };
  const placeholderOptionSia = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionIndustry = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionSurelyBadge = {
    label: 'Select or type...',
    value: '',
  };
  const handleSelectSurelyProBadge = (surelyBadge: string) => {
    if (!surelyProBadge.includes(surelyBadge)) {
      setSurelyProBadge((prevSurelyBadge) => [...prevSurelyBadge, surelyBadge]);
    }
  };

  const handleRemoveSurelyProBadge = (surelyBadge: string) => {
    setSurelyProBadge((prevSurelyBadge) => prevSurelyBadge.filter((selectedSurelyBadge) => selectedSurelyBadge !== surelyBadge));
  };

  const clearAllSelectedSurelyProBadge = () => {
    setSurelyProBadge([]);
  };
  const handleSelectIndustry = (industry: string) => {
    if (!industrySectors.includes(industry)) {
      setIndustrySectors((prevSectors) => [...prevSectors, industry]);
    }
  };
  const handleRemoveIndustry = (industry: string) => {
    setIndustrySectors((prevSectors) => prevSectors.filter((selectedIndustry) => selectedIndustry !== industry));
  };
  const clearAllSelectedIndustries = () => {
    setIndustrySectors([]);
  };
  const handleSliderLocationChange = ({ value }: { value: number }) => {
    setLocationRange(value);
  };

  const handleSelectSiaLicense = (sia: string) => {
    if (!siaLicense.includes(sia)) {
      setSIALicense((prevSia) => [...prevSia, sia]);
    }
  };

  const handleRemoveSiaLicense = (sia: string) => {
    setSIALicense((prevSia) => prevSia.filter((selectedSia) => selectedSia !== sia));
  };

  const clearAllSelectedSiaLicense = () => {
    setSIALicense([]);
  };

  const handleSliderHourChange = (args: any) => {
    const newMinHour = Math.min(args.minValue, 12.5);

    setMinHour(newMinHour);
    setMaxHour(args.maxValue);
  };

  const handleEmergencyHire = () => {
    setIsEmergencyHire((prev) => !prev);
  };

  const handleInclusivityPledge = () => {
    setIsInclusivityPledge((prev) => !prev);
  };

  const handleReset = () => {
    setPostCode(''); //TODO: strangely only with this line the reset works not with null
    setLocationRange('');
    setSIALicense([]);
    setIndustrySectors([]);
    setSurelyProBadge([]);
    setIsEmergencyHire(false);
    setIsInclusivityPledge(false);
    setMinHour('');
    setMaxHour('');

    resetFilters();
  };

  const countNonEmptyStates = () => {
    let count = 0;
    count += postCode.trim() !== '' ? 1 : 0;
    count += locationRange !== '' ? 1 : 0;
    count += siaLicense.length > 0 ? 1 : 0;
    count += industrySectors.length > 0 ? 1 : 0;
    count += surelyProBadge.length > 0 ? 1 : 0;
    count += isEmergencyHire ? 1 : 0;
    count += isInclusivityPledge ? 1 : 0;
    count += minHour !== '' || maxHour !== '' ? 1 : 0;
    return count;
  };

  const handleSubmitFilters = () => {
    // const nonEmptyCount = countNonEmptyStates();
    handleFilters(filters);
    // onFilterCountChange(nonEmptyCount);
  };

  // const handleSubmitFilters = () => {
  //   handleFilters(filters);
  // };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] px-0 py-[24px]'>
      <View className='mt-[16px] flex items-center p-0 px-[24px]'>
        <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Filters</Text>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end'>
          <span className='material-icons text-500 align-middle '>close</span>
        </button>
      </View>
      <View>
        <View className='flex h-[500px] flex-col items-start gap-[16px] overflow-auto px-[24px] pt-[16px]'>
          <p className='rubik text-[16px] font-medium leading-[24px] text-[#323C58] underline' onClick={handleReset}>
            Reset
          </p>
          <View className='flex w-full flex-col items-start gap-[4px]'>
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Your postcode</Text>
            <TextField
              value={postCode}
              name='postcode'
              placeholder='E1 6AN'
              className='h-[48px] w-full rounded-[4px] lg:w-[376px]'
              onChange={(e) => setPostCode(e.value)}
            />
          </View>
          <View className='flex w-full flex-col items-start gap-[4px]'>
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Job location range</Text>
            <div className='mt-[8px] flex w-full flex-col items-center'>
              <div className='rubik text-center text-base font-normal text-[#14171F]'>
                <span id='sliderValue' className=' mt-[12px]'>
                  {locationRange}
                </span>
                miles
              </div>
              <input
                type='range'
                min='0'
                max='100'
                value={locationRange}
                className='mt-[10px] h-1 w-full'
                style={{ background: sliderBackground }}
                id='slider'
                onChange={handleSliderChange}
              />
              <div className='mt-[12px] flex w-full justify-between'>
                <span className='rubik text-base font-medium leading-[16px] text-[#323C58]'>0 miles</span>
                <span className='rubik text-base font-medium leading-[16px] text-[#323C58]'>+60 miles</span>
              </div>
            </div>
          </View>
          <View className='flex flex-col items-start gap-[4px] lg:w-full '>
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Hourly rate range</Text>
            <View className='flex items-start gap-[12px] lg:w-full lg:flex-row'>
              <View className='flex w-full flex-col'>
                <Text className='rubik text-[14px]'>Min</Text>
                <input
                  name='min'
                  placeholder='£12.50'
                  className=' h-[48px] w-full rounded-[4px] rounded-md border border-[#BBC1D3] bg-[#fff] px-[12px] py-[14px] lg:w-[182px]'
                  style={{ outline: 'none' }}
                  value={minHour !== '' ? `£${minHour}` : ''}
                  onInput={(event) => {
                    const numericValue = (event.target as HTMLInputElement).value.replace(/[^0-9]/g, '');
                    setMinHour(numericValue);
                  }}
                />
              </View>
              <View className='flex w-full flex-col'>
                <Text className='rubik text-[14px]'>Max</Text>
                <input
                  name='max'
                  placeholder='£39'
                  className=' h-[48px] w-full rounded-[4px] rounded-md border border-[#BBC1D3] bg-[#fff] px-[12px] py-[14px] lg:w-[182px]'
                  style={{ outline: 'none' }}
                  value={maxHour !== '' ? `£${maxHour}` : ''}
                  onInput={(event) => {
                    const numericValue = (event.target as HTMLInputElement).value.replace(/[^0-9]/g, '');
                    setMaxHour(numericValue);
                  }}
                />
              </View>
            </View>
          </View>
          <View className='flex flex-col items-start gap-[4px] lg:w-full '>
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>SIA license</Text>
            <Select
              className='mt-2 w-full rounded-md border p-2'
              name='sia_license'
              placeholder={siaLicense?.length > 0 ? '' : placeholderOptionSia.label}
              options={[
                {
                  label: 'Close Protection',
                  value: 'Close Protection',
                },
                { label: 'Door Supervisor', value: 'Door Supervisor' },
                { label: 'Security Guard', value: 'Security Guard' },
                {
                  label: 'Public Space Surveillance',
                  value: 'Public Space Surveillance',
                },
              ]}
              onChange={(selectedOption: any) => {
                if (selectedOption.value !== '') {
                  handleSelectSiaLicense(selectedOption.value);
                }
              }}
              startSlot={
                <>
                  <div className='w-[160px] gap-2'>
                    {siaLicense.map((selectedSia) => (
                      <Button
                        key={selectedSia}
                        size='small'
                        rounded={true}
                        elevated={false}
                        onClick={() => handleRemoveSiaLicense(selectedSia)}
                        className='mr-[10px] mt-[8px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs !text-[#323c58]'
                      >
                        <Text color='positive' className='flex items-center gap-1'>
                          <span className='material-icons text-[14px]'>star</span>
                          {selectedSia}
                        </Text>
                      </Button>
                    ))}
                  </div>
                  {siaLicense?.length > 0 && (
                    <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedSiaLicense}>
                      Clear all
                    </Button>
                  )}
                </>
              }
            />
          </View>
          <View className='flex flex-col items-start gap-[4px] lg:w-full'>
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Industry sector</Text>
            <Select
              className='mt-2 w-full rounded-md border p-2'
              name='industry_sectors'
              placeholder={industrySectors?.length > 0 ? '' : placeholderOptionIndustry.label}
              options={[
                {
                  label: 'Bars, Clubs & Restaurants',
                  value: 'Bars, Clubs & Restaurants',
                },
                { label: 'Events & Festivals', value: 'Events & Festivals' },
                { label: 'Private Hire', value: 'Private Hire' },
                { label: 'Film, TV & Media', value: 'Film, TV & Media' },
                { label: 'Rail, Air & Road', value: 'Rail, Air & Road' },
                { label: 'Commercial Offices', value: 'Commercial Offices' },
                { label: 'Construction', value: 'Construction' },
                { label: 'Education', value: 'Education' },
                {
                  label: 'Financial & Banking',
                  value: 'Financial & Banking',
                },
                { label: 'Government', value: 'Government' },
                { label: 'Healthcare', value: 'Healthcare' },
                { label: 'High Street Retail', value: 'High Street Retail' },
                { label: 'Other', value: 'Other' },
              ]}
              onChange={(selectedOption: any) => {
                if (selectedOption.value !== '') {
                  handleSelectIndustry(selectedOption.value);
                }
              }}
              startSlot={
                <>
                  <div className='w-[160px] gap-2'>
                    {industrySectors.map((selectedIndustry) => (
                      <Button
                        key={selectedIndustry}
                        size='small'
                        rounded={true}
                        elevated={false}
                        onClick={() => handleRemoveIndustry(selectedIndustry)}
                        className='mr-[5px] mt-[8px] max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs'
                      >
                        <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedIndustry}</Text>
                      </Button>
                    ))}
                  </div>
                  {industrySectors?.length > 0 && (
                    <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedIndustries}>
                      Clear all
                    </Button>
                  )}
                </>
              }
            />
          </View>
          <View className='flex flex-col items-start gap-[4px] lg:w-full'>
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>SurelyPro badge</Text>
            <Select
              className='mt-2 w-full rounded-md border p-2'
              name='industry_sectors'
              placeholder={surelyProBadge?.length > 0 ? '' : placeholderOptionSurelyBadge.label}
              options={[
                { label: 'Customer Service', value: 'CustomerService' },
                { label: 'Use of Equipment', value: 'UseOfEquipment' },
                { label: 'Disability Focus', value: 'DisabilityFocus' },
                { label: 'Substance Awareness', value: 'SubstanceAwareness' },
                { label: 'Vulnerable People', value: 'VulnerablePeople' },
                { label: 'Conflict Managament', value: 'ConflictManagament' },
              ]}
              onChange={(selectedOption: any) => {
                if (selectedOption.value !== '') {
                  handleSelectSurelyProBadge(selectedOption.value);
                }
              }}
              startSlot={
                <>
                  <div className='w-[160px] gap-2'>
                    {surelyProBadge.map((selectedSurelyBadge) => (
                      <Button
                        key={selectedSurelyBadge}
                        size='small'
                        rounded={true}
                        elevated={false}
                        onClick={() => handleRemoveSurelyProBadge(selectedSurelyBadge)}
                        className='mr-[5px] mt-[8px]  max-w-xs overflow-hidden truncate !bg-[#DDEFFF] px-2 py-1 text-xs'
                      >
                        <div className='flex flex-row'>
                          {' '}
                          <img src={getIconForSurelyProBadge(selectedSurelyBadge)} alt={`Icon for ${selectedSurelyBadge}`} className='mr-2 h-4 w-4' />
                          <Text className='rubik font-normal leading-4 text-[#053D6D]'>{selectedSurelyBadge}</Text>
                        </div>
                      </Button>
                    ))}
                  </div>
                  {surelyProBadge?.length > 0 && (
                    <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedSurelyProBadge}>
                      Clear all
                    </Button>
                  )}
                </>
              }
            />
          </View>
          <View className='flex flex-row justify-between gap-[4px] lg:w-full'>
            <div className='flex flex-col'>
              <Text className='rubik text-center text-start text-[16px] text-[#1A1A1A] xl:font-medium xl:leading-5'>Emergency Hire</Text>

              <Text className='rubik text-sm text-gray-600'>
                This feature enables you to instantly view job listings that require the immediate attention of a security operative.
              </Text>
            </div>
            <Switch name='emergency' checked={isEmergencyHire} onChange={handleEmergencyHire} />
          </View>
          <View className='flex flex-row justify-between gap-[4px] lg:w-full'>
            <div className='flex flex-col'>
              <Text className='rubik text-center text-start text-[16px] text-[#1A1A1A] xl:font-medium xl:leading-5'>Inclusivity Pledge</Text>

              <Text className='rubik text-sm text-gray-600'>
                This badge serves as a visible symbol of commitment to promoting inclusivity and diversity.
              </Text>
            </div>
            <Switch name='inclusivity' checked={isInclusivityPledge} onChange={handleInclusivityPledge} />
          </View>
        </View>
        <View className='mt-[30px] w-full px-[24px]'>
          <Button
            onClick={() => {
              handleSubmitFilters();
              deactivate();
            }}
            className='flex w-full items-center justify-center gap-2 self-stretch rounded-[8px] !bg-[#0B80E7] p-3'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFF]'>Show your results</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default JobsFilter;
