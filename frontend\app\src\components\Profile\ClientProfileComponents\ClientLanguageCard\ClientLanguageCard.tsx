// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, Button, View, Divider, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { ClientType } from '../../../../store/dummydata/OperatorsDummyData';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';

interface ClientLanguageCardProps {
  oper: any; 
}

const ClientLanguageCard: React.FC<ClientLanguageCardProps> = ({ oper }) => {
  const navigate = useNavigate();
  const { languages } = oper;

  return (
    <Card className='xl:w-[424px] h-[auto] xl:mx-auto pr-[24px] pl-[24px] md:mt-[24px] md:mb-[100px] '>
      <View className='flex items-center justify-between'>
        <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>
        Languages spoken
        </Text>

      </View>
      <View className='mt-[12px] '>
        {languages?.length === 0 ? (
          <NoDataClientProfile />
        ) : (
          languages
            ?.slice()
            ?.sort((a:any, b:any) => a.language.localeCompare(b.language))
            ?.map((language: any, index: any) => (
              <React.Fragment key={index}>
                <Text className='text-[15px] font-normal leading-[20px] rubik !text-[#1A1A1A] mt-[20px] '>
                {language.language}
                </Text>
                <Text className='text-[14px] font-normal leading-[20px] rubik !text-[#383838] mt-[1px]'>
                {language.level}
                </Text>
                {index !== languages.length - 1 && (
                  <Divider className='h-[1px] w-full mt-[12px]' />
                )}
              </React.Fragment>
            ))
        )}
      </View>
    </Card>
  );
};

export default ClientLanguageCard;
