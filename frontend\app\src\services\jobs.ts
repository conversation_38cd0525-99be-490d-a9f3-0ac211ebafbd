// @ts-nocheck
import { HttpClient } from 'src/client/http-client';
import client from '../client';
import { tr } from 'date-fns/locale';

export default class Job {
  // static async list(filters: any) {
  //   try {
  //     if (!filters) filters = {};
  //     let url =
  //       'jobs?' +
  //       Object.keys(filters).reduce((acc: any, key: any) => {
  //         acc += `&${key}=${filters[key]}`;
  //         return acc;
  //       });
  //     const response = await HttpClient.get<any>(url);
  //     if (!response.error) {
  //       return response;
  //     }
  //   } catch (error) {
  //     console.error(error);
  //   }
  // }

  static async list(queryParams: string) {
    const url = '/jobs?' + queryParams;
    try {
      const response = await HttpClient.get<any>(url);
      if (!response.error) {
        return response;
      }
    } catch (error) {
      console.error(error);
    }
  }
  create() {}

  static async get(id: any) {
    const response = await HttpClient.get<any>(`/jobs/${id}`);
    if (!response.error) {
      return response.data;
    }
  }

  static async applicationsOf(jobId: any) {
    try {
      const response = await HttpClient.get<any>(`/jobs/${jobId}/applications`);
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error(error);
    }
    return [];
  }

  static async declineApplicant(jobId: any, applicantId: any) {
    try {
      const response = await HttpClient.post<any>(`/jobs/${jobId}/applicants/${applicantId}/status`, {
        status: 'declined',
        type: 'job_application_declined',
      });
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error(error);
    }
  }

  static async acceptApplicant(jobId: any, applicantId: any) {
    try {
      const response = await HttpClient.post<any>(`/jobs/${jobId}/applicants/${applicantId}/status`, {
        status: 'accepted',
        type: 'client_accepted_applicant',
      });
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error(error);
    }
  }
}

export const addPostJob = async (jobsData: {
  images: any;
  jobPostName: any;
  selectedDates: any;
  siaLicence: any;
  industrySectors: any;
  qualifications: any;
  surelyProBadge: any;
  ratingLevel: any;
  hourlyRateMin: any;
  hourlyRateMax: any;
  jobTitle: any;
  numOperatives: any;
  location: any;
  jobDescription: any;
  dutyOfCare: any;
  jobBenefits: any;
  isEmergencyHire: any;
  isInclusivityPledge: any;
  payment: any;
}) => {
  const {
    images,
    jobPostName,
    selectedDates,
    siaLicence,
    industrySectors,
    qualifications,
    surelyProBadge,
    ratingLevel,
    hourlyRateMin,
    hourlyRateMax,
    jobTitle,
    numOperatives,
    location,
    jobDescription,
    dutyOfCare,
    jobBenefits,
    isEmergencyHire,
    isInclusivityPledge,
    payment
  } = jobsData;

  const finalData = {
    industry_sector: industrySectors,
    images: images,
    post_name: jobPostName,
    date_range: selectedDates,
    sia_licence: siaLicence,
    relevant_qualification: qualifications,
    surely_pro_badge: surelyProBadge,
    rating_level: ratingLevel,
    hourly_rate_min: hourlyRateMin,
    hourly_rate_max: hourlyRateMax,
    title: jobTitle,
    nr_of_operatives: numOperatives,
    location: location,
    description: jobDescription,
    duty_of_care: dutyOfCare,
    benefits: jobBenefits,
    is_emergency_hire: isEmergencyHire,
    is_inclusivity_pledge: isInclusivityPledge,
    payment_terms: payment,
  };

  try {
    const response = await client.jobs.addPostJob(finalData);
    return response;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};


export const getAllJobs = async () => {
  try {
    const response = await client.jobs.getJobs()
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const getAppliedJobs = async () => {
  try {
    const response = await client.jobs.getAppliedJobs()
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const removeJobsFavorite = async (id: any) => {
  try {
    const response = await client.favourites.removeJobsFavorite(id);
    if (!response.error) {
      return response;
    }
  } catch (error) {}
};

export const addJobsFavorite = async (id: any) => {
  try {
    const response = await client.favourites.addJobsFavorite(id);
    if (!response.error) {
      return response;
    }
  } catch (error) {}
};

export const getApplied = async (id: any) => {
  try {
    const response = await client.jobs.getApplied(id);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const editPostJob = async ({ id, input }: any) => {
  const {
    images,
    jobPostName,
    shiftData,
    siaLicence,
    industrySectors,
    qualifications,
    surelyProBadge,
    ratingLevel,
    hourlyRateMin,
    hourlyRateMax,
    jobTitle,
    numOperatives,
    location,
    jobDescription,
    dutyOfCare,
    jobBenefits,
    isEmergencyHire,
    isInclusivityPledge,
  } = input;

  const finalData = {
    id: id,
    industry_sector: industrySectors,
    images: images,
    post_name: jobPostName,
    date_range: shiftData,
    sia_licence: siaLicence,
    relevant_qualification: qualifications,
    surely_pro_badge: surelyProBadge,
    rating_level: ratingLevel,
    hourly_rate_min: hourlyRateMin,
    hourly_rate_max: hourlyRateMax,
    title: jobTitle,
    nr_of_operatives: numOperatives,
    location: location,
    description: jobDescription,
    duty_of_care: dutyOfCare,
    benefits: jobBenefits,
    is_emergency_hire: isEmergencyHire,
    is_inclusivity_pledge: isInclusivityPledge,
  };

  try {
    const response = await HttpClient.put<any>(`/jobs/${id}`, finalData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};
