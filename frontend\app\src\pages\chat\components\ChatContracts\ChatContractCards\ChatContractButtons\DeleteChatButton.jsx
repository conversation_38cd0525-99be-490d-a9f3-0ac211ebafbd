import { useState } from 'react';
import { useToggle } from 'reshaped';
import DeleteChatModal from 'src/pages/chat/components/ChatModals/DeleteChatModal';

const DeleteChatButton = ({ nameShown, handleDeleteChat }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div className='rubik height-[156px] flex flex-col gap-3 text-[16px] font-medium'>
        <div
          onClick={activate}
          className='flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg border border-[#DFE2EA] py-[10.5px] text-[#CB101D]'
        >
          <span className='material-icons'>close</span>Delete chat
        </div>
      </div>
      <DeleteChatModal nameShown={nameShown} active={active} deactivate={deactivate} handleDeleteChat={handleDeleteChat} />
    </>
  );
};

export default DeleteChatButton;
