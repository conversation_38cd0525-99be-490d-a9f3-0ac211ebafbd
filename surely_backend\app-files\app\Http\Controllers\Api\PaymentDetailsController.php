<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PaymentDetailsResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PaymentDetailsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index()
    {
        return new PaymentDetailsResource(auth()->user());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();
        $data['account_name'] = $request->get('account_name');
        $data['sort_code'] = $request->get('sort_code');
        $data['account_number'] = $request->get('account_number');
        $data['utr_number'] = $request->get('utr_number');
        $data['vat_number'] = $request->get('vat_number');
        $data['company_number'] = $request->get('company_number');

        if (! $user->update($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot saved!',
                'data' => $data,
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => $data,
        ]);
    }
}
