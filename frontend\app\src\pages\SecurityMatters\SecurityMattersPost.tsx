// @ts-nocheck
import React from 'react';
import { View, Text, Breadcrumbs, Divider } from 'reshaped';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import Footer from 'src/pages/Footer/Footer';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import Subscribe from '../Subscribe/Subscribe';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import homebreadcrumbsicon from '../../assets/icons/homebreadcrumbsicon/homebreadcrumbsicon.svg';
import moment from 'moment';

const SecurityMattersPost: React.FC = () => {
  const location = useLocation();
  const { itemData, allBlogs } = location.state || {};
  const { postId } = useParams<{ postId: any }>();
  const navigate = useNavigate();
  const selectedPost = allBlogs?.find((item: any) => item.id === Number(postId));
  const filteredData = allBlogs?.filter((item: any) => item.id !== selectedPost?.id);
  const firstThreeItems = filteredData?.slice(0, 3);

  const itemId = itemData ? itemData.id : null;
  const itemTitle = itemData ? itemData.title : null;
  const itemContent = itemData ? itemData.content : null;

  if (!selectedPost) {
    return (
      <View>
        <Text>Post not found!</Text>
      </View>
    );
  }

  const truncateText = (htmlContent: any, maxLength: any) => {
    const strippedContent = htmlContent.replace(/(<([^>]+)>)/gi, '');
    if (strippedContent.length > maxLength) {
      return `${strippedContent.substring(0, maxLength)}...`;
    } else {
      return strippedContent;
    }
  };

  const baseURL = 'https://app.surelysecurity.com/storage/';

  return (
    <View className='mt-[-90px] w-full overflow-x-hidden'>
      <View className='w-full bg-[#F8F8F8]'>
        <View className='mx-auto w-full max-w-[1320px] items-center '>
          <View className='mx-auto flex flex-shrink-0  flex-col '>
            <Breadcrumbs className='mt-[30px] sm:mt-[60px]'>
              <Breadcrumbs.Item onClick={() => navigate('/')}>
                <div className='flex flex-row gap-[4px]'>
                  <img src={homebreadcrumbsicon} />
                  <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Homepage</Text>
                </div>
              </Breadcrumbs.Item>
              <Breadcrumbs.Item onClick={() => navigate('/security-matters-archive')}>
                <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Security Matters</Text>
              </Breadcrumbs.Item>
              <Breadcrumbs.Item onClick={() => {}}>
                <Text className='rubik text-[16px] font-medium text-[#1A1A1A]'>
                  {itemData.title && itemData.title?.length > 40 ? `${itemData.title.substring(0, 40)}...` : itemData.title}
                </Text>
              </Breadcrumbs.Item>
            </Breadcrumbs>
            <img
              src={itemData?.img_path?.startsWith(baseURL) ? itemData?.img_path : baseURL + itemData?.img_path}
              className='rounded-br-0 rounded-bl-0  mt-[54px] h-[220px] w-full rounded-tl-[8px] rounded-tr-[8px]  sm:h-[440px]'
            />
            <View className='mx-auto mt-[57px] flex flex-col items-start  gap-[16px]  self-stretch px-[12px] sm:w-[647px] xl:p-[24px]'>
              <Text className='rubik text-left text-[14px] font-normal leading-[20px] text-[#383838]'>
                {moment(itemData.created_at).format('DD MMMM YYYY')}
              </Text>
              <Text className='font-rufina-stencil text-left text-[40px] font-medium leading-[40px] text-[#323C58] '>{itemData.title}</Text>
              {itemContent && <div dangerouslySetInnerHTML={{ __html: itemContent }} />}
            </View>
          </View>
        </View>
      </View>
      <Text className='font-rufina-stencil mt-[64px] items-center px-6  text-left text-[30px]  text-[32px] font-normal leading-[56px] !text-[#323C58] sm:ml-[55.7px] sm:px-0 sm:text-center  sm:leading-[40px]'>
        Check more interesting articles
      </Text>
      <View className='mx-auto mb-[20px] mt-[47px] grid w-full max-w-[1320px] grid-cols-1 items-center gap-7 text-center sm:mb-[77.3px] md:grid-cols-3'>
        {firstThreeItems?.slice(0, 3).map(
          (item: any) =>
            item.id !== selectedPost?.id && (
              <View
                key={item?.id}
                className='mx-auto mb-[35px] flex flex-shrink-0 flex-col items-start px-[12px] sm:mx-0 sm:mb-[0px] sm:w-[424px] xl:px-0'
              >
                <img
                  src={item?.img_path?.startsWith(baseURL) ? item?.img_path : baseURL + item?.img_path}
                  className='rounded-br-0 rounded-bl-0 w-full rounded-tl-[8px] rounded-tr-[8px]'
                />
                <View className='flex flex-col items-start gap-[16px] self-stretch rounded-bl-[8px] rounded-br-[8px] rounded-tl-[0px] rounded-tr-[0px] bg-[#FFFF] p-[24px] shadow-md'>
                  <Text className='rubik text-left text-[14px] font-normal leading-[20px] text-[#383838]'>{item?.date}</Text>
                  <Text className='rubik mt-[12px] h-[30px] text-left text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>{item?.title}</Text>
                  <Divider className='mt-[16px] h-[1px] w-full'></Divider>
                  <Text className='rubik mt-[12px] text-left text-[14px] font-normal leading-[20px] text-[#383838]'>
                    {item.content && <div dangerouslySetInnerHTML={{ __html: truncateText(item.content, 150) }} />}
                  </Text>
                  <button
                    className='btn-no-hover rubik text-[16px] font-[500] leading-[24px] text-[#0B80E7]'
                    onClick={() => navigate(`/security-matters-post/${item.id}`, { state: { itemData: item, allBlogs: allBlogs } })}
                  >
                    Read more
                  </button>
                </View>
              </View>
            ),
        )}
      </View>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default SecurityMattersPost;
