.btn-no-hover {
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding-left: 0; 
  padding-right: 0; 
}

.btn-no-hover:hover {
  background-color: transparent;
  box-shadow: none;
  border: none;
}

.btn-hover-line {
  position: relative;
  overflow: hidden;
}

.btn-hover-line::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: transparent;
  transition: width 0.3s, background-color 0.3s, transform 0.3s; }

.btn-hover-line:hover::before {
  width: 100%;
  background-color: #0B80E7;
  transform: translateX(0%);
}


.btn-no-hover_one {
  border: none;
  box-shadow: none;
  padding-left: 0; 
  padding-right: 0; 
}

.btn-no-hover_one:hover {
  box-shadow: none;
  border: none;
}

