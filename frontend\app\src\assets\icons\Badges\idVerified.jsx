const IdVerified = () => (
<svg
  viewBox="-5 -5 60 61"
  preserveAspectRatio="xMidYMid meet"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  className="block w-full h-full m-0 p-0"
>
  <g filter="url(#filter0_d_1229_19886)">
    <path
      d="M29.5 53.4111C43.3071 53.4111 54.5 41.9944 54.5 27.9111C54.5 13.8279 43.3071 2.41113 29.5 2.41113C15.6929 2.41113 4.5 13.8279 4.5 27.9111C4.5 41.9944 15.6929 53.4111 29.5 53.4111Z"
      fill="url(#paint0_linear_1229_19886)"
    />
    <path
      d="M28.0432 33.1681L35.5748 25.4859L34.1866 24.1121L28.1393 30.2803L24.7996 26.8737L23.3427 28.3597L28.0294 33.1681H28.0432ZM29.5 42.1261C26.4214 41.341 23.8925 39.5186 21.9134 36.6588C19.9343 33.799 18.9447 30.6588 18.9447 27.2522V19.2335L29.5137 15.1962L40.0827 19.2335V27.2522C40.0827 30.6588 39.0932 33.799 37.1141 36.6588C35.1349 39.5186 32.6061 41.341 29.5275 42.1261H29.5Z"
      fill="#7CDBEF"
    />
  </g>
  <defs>
    <filter
      id="filter0_d_1229_19886"
      x="0"
      y="0"
      width="100%"
      height="100%"
      filterUnits="userSpaceOnUse"
      colorInterpolationFilters="sRGB"
    >
      <feFlood floodOpacity="0" result="BackgroundImageFix" />
      <feColorMatrix
        in="SourceAlpha"
        type="matrix"
        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha"
      />
      <feOffset dy="2" />
      <feGaussianBlur stdDeviation="2" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix
        type="matrix"
        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
      />
      <feBlend
        mode="normal"
        in2="BackgroundImageFix"
        result="effect1_dropShadow_1229_19886"
      />
      <feBlend
        mode="normal"
        in="SourceGraphic"
        in2="effect1_dropShadow_1229_19886"
        result="shape"
      />
    </filter>
    <linearGradient
      id="paint0_linear_1229_19886"
      x1="29.5"
      y1="-17.4339"
      x2="29.5"
      y2="72.9561"
      gradientUnits="userSpaceOnUse"
    >
      <stop offset="0.536458" stopColor="#323C58" stopOpacity="0.95" />
      <stop offset="0.921875" stopColor="#6B789C" stopOpacity="0.96" />
    </linearGradient>
  </defs>
</svg>

);

export default IdVerified;