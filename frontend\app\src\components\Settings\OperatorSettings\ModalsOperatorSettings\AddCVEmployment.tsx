// @ts-nocheck
import React, { useRef, useState } from 'react';
import { Text, View, Button, Divider, Modal } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';

interface AddCVEmploymentProps {
  active: boolean;
  deactivate: () => void;
  curiculumVitae: any[];
  curiculumVitaeName: any[];
  addCuriculumVitae: (secondTabSettings: any) => void;
}

const AddCVEmployment: React.FC<AddCVEmploymentProps> = ({ active, deactivate, curiculumVitae, curiculumVitaeName, addCuriculumVitae }) => {
  const [selectedFiles1, setSelectedFiles1] = useState<string[]>([]);
  const [fileName, setFileName] = useState<any>();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const selectedFiles = selectedFiles1[0];
  const [validFileName, setValidFileName] = useState(true);
  const [isDragging, setIsDragging] = useState(false);

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
          resolve(e.target.result);
        } else {
          reject(new Error('Failed to read file as base64.'));
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files: any = e.target.files;
    setFileName(files[0].name);
    if (files && files.length > 0) {
      const fileDataArray: string[] = [];
      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push(base64);
      }
      setSelectedFiles1(fileDataArray);
    }
  };
  const deleteFile = (indexToDelete: number) => {
    const updatedFiles = selectedFiles1.filter((_, index) => index !== indexToDelete);
    setSelectedFiles1(updatedFiles);
  };

  const openFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const isButtonDisabled = selectedFiles1.length > 0;

  const saveCuriculumVitae = () => {
    const newCuriculumVitae = {
      selectedFiles,
      fileName,
    };
    addCuriculumVitae(newCuriculumVitae);

    deactivate();
  };

  const handleDragEnter = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = async (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    setFileName(files[0].name);
    const fileDataArray = [];

    for (const file of files) {
      const base64 = await readFileAsBase64(file);
      fileDataArray.push(base64);
    }

    setSelectedFiles1(fileDataArray);
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Add your CV</Text>
        </View>

        <View className='mt-[16px] flex flex-col'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Upload your CV</Text>

          <div
            onDragEnter={handleDragEnter}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`drop-zone ${isDragging ? 'dragging' : ''}`}
          >
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined mt-[-1px] text-[21px] text-[#323C58]'>upload</span>}
              onClick={openFileInput}
              className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded border !bg-[white] px-4 py-2 text-[15px] !text-[#000000]'
              disabled={isButtonDisabled}
            >
              <Text className='rubik text-[14px] font-medium leading-[20px] text-[#323C58]'>
                Drop or&nbsp;
                <span className='rubik text-[14px] font-medium leading-[20px] text-[#0B80E7]'>browse</span>
              </Text>
              <input
                type='file'
                ref={fileInputRef}
                accept='.pdf,.doc,.docx,.jpg,.jpeg,.png'
                style={{ display: 'none' }}
                onChange={handleFileChange}
              />
            </Button>
          </div>

          {!validFileName && <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Specified document is required.</Text>}
          <ul>
            {selectedFiles1.map((file: any, index: any) => (
              <li key={index}>
                <View className='mt-[18px] flex flex-row items-center'>
                  <Text className='rubik text-base font-normal leading-5 text-[#1A1A1A] '>{fileName}</Text>
                  <Button
                    variant='outline'
                    className='ml-[16px] mt-[10px] flex w-[78px] items-center justify-center !border-[#DFE2EA] !bg-[#fff] '
                    onClick={() => deleteFile(index)}
                  >
                    <Text className='rubik p-[4px 8px 4px 8px] flex items-center align-middle text-sm font-normal text-[#CB101D]'>
                      <span className='material-icons align-middle text-[20px]'>close</span>
                      Delete
                    </Text>
                  </Button>
                </View>
              </li>
            ))}
          </ul>
          <Text className=' rubik mt-[8px] text-[14px] leading-[20px] text-[#444B5F]'>PDF, JPEG, PNG files only.</Text>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
            onClick={saveCuriculumVitae}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Add</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default AddCVEmployment;
