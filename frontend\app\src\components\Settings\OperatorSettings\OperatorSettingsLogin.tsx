// @ts-nocheck
import React, { useState, useContext, useEffect } from 'react';
import {
  Text,
  View,
  Button,
  TextField,
  Tabs,
  Divider,
  useToast,
  Image,
  useToggle,
  Breadcrumbs,
} from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { editPassword, deleteUser } from 'src/services/settings';
import { AuthContext } from 'src/context/AuthContext';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import DeleteAccountModal from './ModalsOperatorSettings/DeleteAccountModal';
import { AppContext } from 'src/context/AppContext';
import google from '../../../assets/icons/socialicon/google.png';

const OperatorSettingsLogin: React.FC = () => {
  const navigate = useNavigate();
  const { unAuthenticateUser } = useContext(AuthContext);

  const [activeTab, setActiveTab] = useState('0');
  const { active, activate, deactivate } = useToggle(false);
  const [oldPassword, setOldPassword] = useState('');
  const [isPasswordVisible1, setPasswordVisible1] = useState(false);
  const [password, setPassword] = useState('');
  const [isPasswordVisible2, setPasswordVisible2] = useState(false);
  const [passwordConfirmation, setPasswordConfirmation] = useState('');
  const [isPasswordVisible3, setPasswordVisible3] = useState(false);
  const [error, setError] = useState('');
  const toast = useToast();
  const { fetchAppData } = useContext(AppContext);
  const [isSaving, setIsSaving] = useState(false);

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const togglePasswordVisibility1 = () => {
    setPasswordVisible1(!isPasswordVisible1);
  };
  const togglePasswordVisibility2 = () => {
    setPasswordVisible2(!isPasswordVisible2);
  };
  const togglePasswordVisibility3 = () => {
    setPasswordVisible3(!isPasswordVisible3);
  };

  const submitFirstTab = async () => {
    try {
      if (password !== passwordConfirmation) {
        setError('Passwords do not match');
      } else if (password.length < 8) {
        setError('Password must be at least 8 characters long');
      } else {
        setError('');
        const firstTabSettings: any = {
          oldPassword,
          password,
          passwordConfirmation,
        };
  
        const response = await editPassword(firstTabSettings);
        
        if (response.error) {
          console.error(response);
          setError(response.message);
        } else {
          toast.show({
            title: 'Password Successfully Changed!',
            text: 'Congratulations, your password has been changed successfully.',
            startSlot: <Image src={surleyicon} className='w-[30px] h-[30px]' />,
          });
        }
      }
    } catch (error) {
      console.error(error);
      setError('An unexpected error occurred');
    } finally {
      setIsSaving(false);
    }
  };
  

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  const handleDeleteAccount = () => {
    deleteUser().then((data) => {
      unAuthenticateUser(), navigate('/');
    });
  };

  return (
    <View className=' w-full sm:w-auto mx-auto px-[12px] md:px-0 overflow-hidden'>
      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='text-[#3C455D] rubik text-[16px]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='text-[#3C455D] rubik text-[16px]'>
            Account settings
          </span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='font-medium rubik text-[#1A1A1A] text-[16px]'>
            Login Details
          </span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='flex items-center p-0 mb-[16px] lg:mb-5'>
        <Text className=' lg:text-[32px] font-rufina-stencil font-normal xl:leading-10 text-[#323C58]'>
          Login details
        </Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          <Tabs.Item value='0'>
            <span className='rubik text-[14px] text-[#14171F] leading-[20px] font-normal '>
              Password
            </span>
          </Tabs.Item>
          {/* <Tabs.Item value='1'>
            <span className='rubik text-[14px] text-[#14171F] leading-[20px] font-normal'>
              Connected accounts
            </span>
          </Tabs.Item> */}
          <Tabs.Item value='2'>
            <span className='rubik text-[14px] text-[#14171F] leading-[20px] font-normal'>
              Delete your account
            </span>
          </Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col lg:flex-row justify-between'>
          <View className='flex flex-col gap-[16px] '>
            <View className='flex flex-col mt-[16px] gap-[4px]'>
              <Text className='text-[#1A1A1A] text-[14px] rubik font-medium leading-5'>
                Your current password
              </Text>
              <TextField
                name='password'
                value={oldPassword}
                onChange={(e) => setOldPassword(e.value)}
                inputAttributes={{
                  type: isPasswordVisible1 ? 'text' : 'password',
                }}
                className='px-[12px] py-[14px] gap-[8px] rounded-md bg-white focus:outline-none  sm:w-[536px] h-[48px] border border-gray-300 rounded-md '
                endSlot={
                  <Button
                    variant='ghost'
                    className='material-icons-outlined  text-gray-500 cursor-pointer -mt-1'
                    onClick={togglePasswordVisibility1}
                    icon={() => (
                      <span className='material-icons-outlined'>
                        {isPasswordVisible1 ? 'visibility' : 'visibility_off'}
                      </span>
                    )}
                  />
                }
              />
            </View>
            <View className='flex flex-col gap-[4px]'>
              <Text className='text-[#1A1A1A] text-neutral rubik font-medium leading-4'>
                Your new password
              </Text>
              <TextField
                name='password'
                value={password}
                onChange={(e) => setPassword(e.value)}
                inputAttributes={{
                  type: isPasswordVisible2 ? 'text' : 'password',
                }}
                className='px-[12px] py-[14px] gap-[8px] rounded-md bg-white focus:outline-none  sm:w-[536px] h-[48px] border border-gray-300 rounded-md'
                endSlot={
                  <Button
                    variant='ghost'
                    className='material-icons-outlined ml-2 text-gray-500 cursor-pointer -mt-1'
                    onClick={togglePasswordVisibility2}
                    icon={() => (
                      <span className='material-icons-outlined'>
                        {isPasswordVisible2 ? 'visibility' : 'visibility_off'}
                      </span>
                    )}
                  />
                }
              />
              <Text className='text-[#444] text-[14px] rubik font-normal leading-5  sm:w-[536px]'>
                Use more than 12 characters and a combination of uppercase and
                lowercase letters, numbers, and symbols.
              </Text>
            </View>

            <View className='flex flex-col gap-[4px] '>
              <Text className='text-[#1A1A1A] text-neutral rubik font-medium leading-4'>
                Confirm your new password
              </Text>
              <View className='relative flex align-center justify-space-between mt-[5px] flex-col'>
                <TextField
                  name='password'
                  value={passwordConfirmation}
                  onChange={(e) => setPasswordConfirmation(e.value)}
                  inputAttributes={{
                    type: isPasswordVisible3 ? 'text' : 'password',
                  }}
                  className='px-[12px] py-[14px] gap-[8px] rounded-md bg-white focus:outline-none  sm:w-[536px] h-[48px] border border-gray-300 rounded-md'
                  endSlot={
                    <Button
                      variant='ghost'
                      className='material-icons-outlined ml-2 text-gray-500 cursor-pointer -mt-1'
                      onClick={togglePasswordVisibility3}
                      icon={() => (
                        <span className='material-icons-outlined'>
                          {isPasswordVisible3 ? 'visibility' : 'visibility_off'}
                        </span>
                      )}
                    />
                  }
                />
                <Text className='text-red-400 rubik text-[15px] font-normal leading-5  mt-[16px]'>
                  {error}
                </Text>
              </View>
            </View>
            <Divider className='w-full h-[1px] '></Divider>

            <View className='flex flex-row justify-between '>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] !bg-[#fff] rubik text-[16px] font-medium !border-[#DFE2EA] sm:w-[260px] h-[48px]'
              >
                Cancel
              </Button>
              <Button
                onClick={submitFirstTab}
                className={`flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] rubik text-[16px] font-medium ${
                  isSaving ? '!text-[#0B80E7] !bg-[#fff] !border-[#0B80E7]' : '!text-[#ffff] !bg-[#0B80E7]'
                } sm:w-[260px] h-[48px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='flex flex-col w-[313px] ml-[0px] sm:ml-[135px] mt-[15px] sm:mt-[0px]'></View>
        </View>
      )}
      {/* {activeTab === '1' && (
        <View className='flex flex-col lg:flex-row justify-between'>
          <View className='flex flex-col mt-[16px] '>
            <Button
              variant='outline'
              icon={() => <img src={google} />}
              className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] !bg-[#fff] rubik font-medium  sm:w-[536px] h-[48px] !bg-[white]'
            >
              Unlink Google
            </Button>
            <Divider className='w-full h-[1px] mt-[16px]'></Divider>

            <View className='flex flex-row justify-between mt-[16px]'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] !bg-[#fff] rubik font-medium w-[120px] sm:w-[260px] h-[48px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => ''}
                className={`flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] rubik text-[16px] font-medium ${
                  isSaving ? '!text-[#0B80E7] !bg-[#fff] !border-[#0B80E7]' : '!text-[#ffff] !bg-[#0B80E7]'
                } sm:w-[260px] h-[48px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='flex flex-col w-[313px] ml-[0px] sm:ml-[135px] mt-[15px] sm:mt-[0px]'></View>
        </View>
      )} */}
      {activeTab === '2' && (
        <View className='flex flex-col lg:flex-row justify-between'>
          <View className='flex flex-col mt-[16px]  sm:w-[536px]'>
            <Text className='  rubik font-normal leading-5 text-[#444B5F] text-[14px]'>
              By taking this action, your account will be permanently deleted,
              along with all associated data, history, and connected accounts.
            </Text>
            <Button
              icon={() => (
                <span className='material-icons-outlined text-[#CB101D]  mt-[-3px]'>
                  close
                </span>
              )}
              onClick={activate}
              className='flex justify-center items-center px-4 py-3 gap-1 border !border-[#CB101D] rounded-lg !bg-[#fff]  sm:w-[536px] h-[48px] mt-[16px]'
            >
              <Text className='rubik font-medium leading-[24px] text-[#CB101D] text-[14px] '>
                Delete your account
              </Text>
            </Button>
            <DeleteAccountModal active={active} deactivate={deactivate} />
            <Divider className='w-full h-[1px] mt-[16px]'></Divider>

            <View className='flex flex-row justify-between mt-[16px]'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='flex justify-center items-center px-4 py-2 gap-1 border border-neutral text-[16px] rounded-[8px] !bg-[#fff] rubik font-medium w-[120px] sm:w-[260px] h-[48px] leading-[24px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => ''}
                className={`flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] rubik text-[16px] font-medium ${
                  isSaving ? '!text-[#0B80E7] !bg-[#fff] !border-[#0B80E7]' : '!text-[#ffff] !bg-[#0B80E7]'
                } sm:w-[260px] h-[48px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='flex flex-col w-[313px] ml-[0px] sm:ml-[135px] mt-[15px] sm:mt-[0px]'></View>
        </View>
      )}
    </View>
  );
};

export default OperatorSettingsLogin;
