import { useToggle } from 'reshaped';
// import AskForSupportOperatorModal from '../../../../ChatModals/AskForSupportOperatorModal';

const ReviewShiftHours = ({ handleConfirmShiftsContract }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div onClick={handleConfirmShiftsContract} className='flex cursor-pointer items-center justify-center gap-2 rounded-[8px] border border-[#DFE2EA] py-3 '>
        <p className='rubik text-[16px] font-medium text-[#0B80E7] flex items-center gap-2'>
        <span className='material-icons text-[#0B80E7]'>schedule</span>
        Review shift hours
        </p>
      </div>
      {/* <AskForSupportOperatorModal active={active} deactivate={deactivate} chat={chat} /> */}
    </>
  );
};

export default ReviewShiftHours;
