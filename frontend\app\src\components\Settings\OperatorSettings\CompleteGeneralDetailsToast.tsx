import { View, Image, Text, Button } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const CompleteGeneralDetailsToast = () => {
  const navigate = useNavigate();

  return (
    <View className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
      <View className='w-[370px] rounded-[8px] bg-[#1C212BF7] p-6 text-white sm:w-[420px]'>
        <View className='flex items-start gap-4'>
          <Image src={surleyicon} alt='Surely Icon' className='h-[64px] w-[64px]' />
          <View className='w-[308px] text-left'>
            <Text className='rubik text-[14px] font-bold leading-[20px] text-[#EFF0F1]'>
              Complete General Details First
            </Text>
            <Text className='rubik text-[14px] font-normal leading-[20px] text-[#EFF0F1]'>
              Please complete your general details including name, contact information, and location before adding bank account information.
            </Text>
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={() => navigate('/operator-settings-general')}
            >
              Go to General Settings
            </Button>
          </View>
        </View>
      </View>
    </View>
  );
};

export default CompleteGeneralDetailsToast;