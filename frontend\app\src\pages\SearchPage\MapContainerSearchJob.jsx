import React, { useContext, useEffect, useState, useMemo } from 'react';
import { useToggle } from 'reshaped';
import { Map, GoogleApiWrapper, <PERSON><PERSON>, IProvidedProps } from 'google-maps-react';
import { JobContext } from 'src/context/JobContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import markericon2 from '../../assets/icons/markericon/markericon2.png';
import JobsModalMapSearch from './JobsModalMapSearch';
import { AppContext } from 'src/context/AppContext';

const MapContainerSearchJob = ({ google }) => {
  const { latMap, lngMap } = useContext(AppContext);
  const { active: activeJob, activate: activateJob, deactivate: deactivateJob } = useToggle(false);
  const { fetchAllJobs, allJobs, filters, search } = useContext(JobContext);
  const [selectedJob, setSelectedJob] = useState(null);

  // Create a dependency array that will trigger refetch
  const searchDependencies = useMemo(() => {
    return JSON.stringify({ filters, search });
  }, [filters, search]);

  useEffect(() => {
    fetchAllJobs(filters, search);
  }, [searchDependencies]);

  return (
    <div style={{ height: '100vh', width: '100%', position: 'fixed', top: 75, left: 0, bottom: 0, right: 0 }}>
      <Map google={google} zoom={14} initialCenter={{ lat: latMap, lng: lngMap }}>
        {allJobs?.map((job, index) => (
          <Marker
            key={index}
            position={{ lat: job.lat, lng: job.lng }}
            onClick={() => {
              setSelectedJob(job);
              activateJob();
            }}
            icon={{
              url: markericon2,
              anchor: new google.maps.Point(16, 16),
              scaledSize: new google.maps.Size(32, 32),
            }}
          />
        ))}
      </Map>

      <JobsModalMapSearch active={activeJob} deactivate={deactivateJob} selectedJob={selectedJob} />
    </div>
  );
};

export default GoogleApiWrapper({
  apiKey: import.meta.env.VITE_REACT_APP_GMAP_API_KEY,
})(MapContainerSearchJob);
