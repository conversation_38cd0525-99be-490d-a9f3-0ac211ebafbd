// @ts-nocheck
import React, { useState, useContext } from 'react';
import { Button, Text, View, Select, Image, TextField, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import { useRegistrationContext } from 'src/context/RegistrationContext';
import { useAuthContext } from 'src/context/AuthContext';
import { AppContext } from 'src/context/AppContext';

import { headerLogo } from '../../../assets/images';
import { validateClientProfile } from 'src/services/user';

import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const ClientFirstStepFlow: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();

  const { setClientRegisterData } = useRegistrationContext();
  const { user: userInfo } = useAuthContext();
  const { fetchAppData } = useContext(AppContext);
  const user = userInfo?.user;

  const accountType = user?.account_type;

  const [formData, setFormData] = useState({
    client_represent_business_as: '',
    company_registered_number: '',
    industry_sectors: '',
  });

  const [industrySectors, setIndustrySectors] = useState<string[]>([]);

  const placeholderOption = {
    label: 'Select or type...',
    value: '',
  };

  const [businessValid, setBusinessValid] = useState(true);
  const [companyNumberValid, setCompanyNumberValid] = useState(true);
  const [industryValid, setIndustryValid] = useState(true);

  const handleSelectIndustry = (industry: string) => {
    if (!industrySectors.includes(industry)) {
      setIndustrySectors((prevSectors) => [...prevSectors, industry]);
    }
  };

  const handleRemoveIndustry = (industry: string) => {
    setIndustrySectors((prevSectors) => prevSectors.filter((selectedIndustry) => selectedIndustry !== industry));
  };

  const clearAllSelectedIndustries = () => {
    setIndustrySectors([]);
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleSubmit = () => {
    if (formData.client_represent_business_as === '') {
      setBusinessValid(false);
      toast.show({
        title: 'Choose one of the options:',
        text: 'What to do represent?',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
      return;
    } else {
      setBusinessValid(true);
    }

    if (formData.company_registered_number === '' && !['Agent', 'Private Individual'].includes(formData.client_represent_business_as)) {
      setCompanyNumberValid(false);
      toast.show({
        title: 'Action Required:',
        text: 'Add your company number',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
      return;
    } else {
      setCompanyNumberValid(true);
    }

    if (industrySectors.length === 0 && !['Agent', 'Private Individual'].includes(formData.client_represent_business_as)) {
      setIndustryValid(false);
      toast.show({
        title: 'Action Required:',
        text: 'Select at least one option of Industry Sectors',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
      return;
    } else {
      setIndustryValid(true);
    }

    const clientValidationData: any = {
      client_represent_business_as: formData.client_represent_business_as,
      company_registered_number: formData.company_registered_number,
      industry_sectors: industrySectors,
      account_type: accountType,
    };

    setClientRegisterData((prevState: any) => ({
      ...prevState,
      firstStep: formData,
    }));
    validateClientProfile(clientValidationData).then(() => {
      fetchAppData();
    });
    navigate('/client-dashboard');
  };

  return (
    <View className='mt-[20px] overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0 '>
      <View className='mx-auto flex flex-col lg:w-[536px]'>
        <Text className='font-rufina-stencil leading-40 text-center text-[32px] font-normal text-[#1A1A1A]'>We need more details</Text>
        <Text className='rubik mt-[16px] text-center text-base font-normal leading-6 text-[#323C58]'>Please provide the following details</Text>
        <Text className='rubik mt-[16px] text-[15px] font-medium leading-5 text-[#1A1A1A]'>I represent</Text>
        <Select
          name='client_represent_business_as'
          className='mt-[5px] h-[48px] w-[full]'
          placeholder='Select your field of business'
          options={[
            { label: 'Company', value: 'Company' },
            { label: 'ACS-accredited security company', value: 'ACS' },
            { label: 'Non-accredited security company', value: 'Non' },
            { label: 'Agent (Individual)', value: 'Agent' },
            { label: 'Private Individual', value: 'Private Individual' },
          ]}
          onChange={(event) =>
            setFormData({
              ...formData,
              ['client_represent_business_as']: event.value,
            })
          }
        />
        {!businessValid && (
          <Text className='rubik mt-[4px] text-[15px] font-normal leading-5  text-red-400'>Choose one of the options: What do you represent?</Text>
        )}
        <Text className='rubik mt-[4px] text-[15px] font-normal leading-5  text-[#444B5F]'>We need this to find the most suitable jobs.</Text>

        {['Agent', 'Private Individual'].includes(formData.client_represent_business_as) ? null : (
          <>
            <Text className='rubik mt-[12px] text-[15px] font-medium leading-5 text-[#1A1A1A]'>Company registered number</Text>
            <TextField
              name='company_registered_number'
              placeholder='XXXXXXXX'
              className='mt-[5px] h-[48px] w-[full]'
              onChange={(event) =>
                setFormData({
                  ...formData,
                  ['company_registered_number']: event.value,
                })
              }
            />

            {['Agent', 'Private Individual'].includes(formData.client_represent_business_as) ? (
              <></>
            ) : (
              <>
                {!companyNumberValid && (
                  <Text className='rubik mt-[4px] text-[15px] font-normal leading-5  text-red-400'>
                    Company number not correct: Please make sure that your number is correct so that we can verify.
                  </Text>
                )}
              </>
            )}

            <Text className='rubik mt-[12px] text-[15px] font-medium leading-5 text-[#1A1A1A]'>Industry sectors you’re looking for</Text>

            <View className='mt-[4px]'>
              <Select
                className='h-[auto]  rounded-md border  p-2 sm:w-[536px]'
                name='industry_sectors'
                placeholder={industrySectors?.length > 0 ? '' : placeholderOption.label}
                options={[
                  {
                    label: 'Bars, Clubs & Restaurants',
                    value: 'Bars, Clubs & Restaurants',
                  },
                  { label: 'Events & Festivals', value: 'Events & Festivals' },
                  { label: 'Private Hire', value: 'Private Hire' },
                  { label: 'Film, TV & Media', value: 'Film, TV & Media' },
                  { label: 'Rail, Air & Road', value: 'Rail, Air & Road' },
                  { label: 'Commercial Offices', value: 'Commercial Offices' },
                  { label: 'Construction', value: 'Construction' },
                  { label: 'Education', value: 'Education' },
                  {
                    label: 'Financial & Banking',
                    value: 'Financial & Banking',
                  },
                  { label: 'Government', value: 'Government' },
                  { label: 'Healthcare', value: 'Healthcare' },
                  { label: 'High Street Retail', value: 'High Street Retail' },
                  { label: 'Other', value: 'Other' },
                ]}
                onChange={(selectedOption: any) => {
                  if (selectedOption.value !== '') {
                    handleSelectIndustry(selectedOption.value);
                  }
                }}
                startSlot={
                  <>
                    <div className='w-[250px] gap-2'>
                      {industrySectors.map((selectedIndustry) => (
                        <Button
                          key={selectedIndustry}
                          size='small'
                          rounded={true}
                          elevated={false}
                          onClick={() => handleRemoveIndustry(selectedIndustry)}
                          className='mr-[5px] mt-2 max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs'
                        >
                          <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedIndustry}</Text>
                        </Button>
                      ))}
                    </div>
                    {industrySectors?.length > 0 && (
                      <Button variant='ghost' className='rubik ml-2 font-medium text-[#3C455D] underline' onClick={clearAllSelectedIndustries}>
                        Clear all
                      </Button>
                    )}
                  </>
                }
              />
              {!industryValid && (
                <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Action Required: Select at least one option.</Text>
              )}
            </View>
          </>
        )}
      </View>
      <View className='mt-[20px] flex flex-col sm:mt-[184px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full '>
          <div className='h-full w-1/2 bg-[#0B80E7]' />
          <div className='h-full w-1/2 bg-[#D7D4D4]' />
        </div>
        <View className='mt-[10px] flex flex-row justify-between sm:mt-[70px] '>
          <Button
            variant='outline'
            icon={() => <span className='material-icons-outlined p-0 pb-2 text-[15px] text-[#14171F]'>close</span>}
            onClick={handleGoBack}
            className='bg-background-base!bg-[white] flex h-[48px] w-[103px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#14171F]'>Close</Text>
          </Button>
          <Button
            endIcon={() => <span className='material-icons-outlined p-0 pb-1 text-[15px] !text-white'>arrow_forward_ios</span>}
            onClick={handleSubmit}
            className='border-neutral bg-background_base flex h-[48px] w-[116px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border  !bg-[#0B80E7] px-4 py-2 '
          >
            <Text className='rubik text-[15px] leading-[24px] !text-white'>Submit</Text>
          </Button>
        </View>
        <div className='mt-[10px] flex items-center justify-center sm:mt-[55.2px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default ClientFirstStepFlow;
