// @ts-nocheck
import { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToggle, useToast, Image } from 'reshaped';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import ForgotPassword from '../ForgotPassword/ForgotPassword';
import { loginUser, loginVerifyEmail } from 'src/services/user';
import { LoginUserInput } from 'src/types/user';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import moment from 'moment';


const LoginForm = ({ toOtpLogin, handleGoToRegister }: { toOtpLogin: Function; handleGoToRegister: Function }) => {
  const { closeModal } = useModalAction();
  const { authenticateUser } = useContext(AuthContext);

  const { active, activate, deactivate } = useToggle(false);
  const navigate = useNavigate();

  const toast = useToast();

  const isDataUnavailable = (userData: any) => {
    return (
      userData?.user?.profile_title === null ||
      userData?.user?.profile_description === null ||
      userData?.user?.profile_description === undefined ||
      userData?.user?.profile_photo === null ||
      userData?.user?.profile_video === null ||
      userData?.languages?.length === 0 ||
      userData?.user?.profile_title === undefined ||
      userData?.user?.profile_photo === undefined ||
      userData?.user?.profile_video === undefined ||
      userData?.employments?.length === 0 ||
      userData?.qualifications?.length === 0
    );
  };

  const schema: any = yup.object().shape({
    email: yup.string().email().required('Please enter your email.'),
    password: yup.string().required('Please enter your password.'),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<LoginUserInput>({
    resolver: yupResolver<LoginUserInput>(schema),
    defaultValues: {
      email: '',
      password: '',
      loginType: 1,
      platform: 3,
      appVersion: 'web 1.0',
      firebaseToken: 'test',
      ref:'login'
    },
  });
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isLoginError, setIsLoginError] = useState(false);
  const [loginStatus, setLoginStatus] = useState('');

  const email = watch('email');

  const onSubmitHandler = async (data: LoginUserInput) => {
    const loginResponse: any = await loginUser(data);
    const checkSiaLicenceExpiryDate = loginResponse?.data?.user.sia_licence_expiry_date;
    if (loginResponse?.token) {
      authenticateUser(loginResponse);
      if (checkSiaLicenceExpiryDate) {
        const currentDate = moment();
        const expiryDate = moment(checkSiaLicenceExpiryDate);
        const oneMonthBeforeExpiry = expiryDate.clone().subtract(1, 'months');
        if (currentDate.isAfter(expiryDate)) {
          toast.show({
            title: 'SIA Licence Expired',
            text: 'Attention! Your SIA licence has expired. Renew your licence immediately to avoid any disruptions.',
            startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
          });
        } else {
          if (currentDate.isAfter(oneMonthBeforeExpiry) && currentDate.isBefore(expiryDate)) {
            const daysLeft = expiryDate.diff(currentDate, 'days');
            if (daysLeft <= 29) {
              toast.show({
                title: 'SIA Licence Expiry Alert',
                text: `Attention! Your SIA licence is expiring in ${daysLeft} days. Renew your licence now to avoid any disruptions.`,
                startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
              });
            } else {
              toast.show({
                title: 'SIA Licence Expiry Alert',
                text: 'Attention! Your SIA licence is expiring in 1 month. Renew your licence now to avoid any disruptions.',
                startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
              });
            }
          }
        }
      }
      
      if (+loginResponse?.data?.user.account_type === 2) {
        navigate('/client-dashboard');
      } else {
        if (isDataUnavailable(loginResponse?.data)) {
          navigate('/my-profile');
        } else {
          navigate('/search-jobs');
        }
      }

      reset();
      closeModal(true);
    } else {
      setIsLoginError(true);
      setLoginStatus(loginResponse.status)
      if (loginResponse.status == 'closed') {
        toast.show({
          title: 'Account Closed:',
          text: 'For further details, please reach out to our support team via <NAME_EMAIL>',
          startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }if (loginResponse.status == 'suspended') {
        toast.show({
          title: 'Account Suspended:',
          text: "Your Surely account has been suspended for violating our terms of service. We value our community's integrity and expect all users to adhere to our guidelines. If you have any questions or would like to appeal this decision, please contact our support team.",
          startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
        });} 
        if (loginResponse.status == 'verify') {
          toast.show({
            title: 'Verify email:',
            text: 'For further details, please reach out to our support team via <NAME_EMAIL>',
            startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
          });} 
      else {
        toast.show({
          title: 'An error occurred during login.',
          text: 'Check email or password.',
          startSlot: <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }
    }
  };


  const submitverify = async () => {
    const verifyEmailPost: any = {
      email,
      ref:'login'
    };
    await loginVerifyEmail(verifyEmailPost)
    navigate('/verify-email')
  
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const sessionParam = queryParams.get('session');
    
    if (sessionParam === 'deployment') {
      toast.show({
        title: 'Session Expired',
        text: 'A new version has been deployed. Please log in again.',
        type: 'info'
      });
    }
  }, []);

  return (
    <form onSubmit={handleSubmit(onSubmitHandler)} className='flex items-center justify-center '>
      <article className='max-w-lg pl-3 pr-4 '>
        <section className='rubik mt-[10px] text-[14px] font-normal text-[#1A1A1A]'>
          Access your existing account using your email address. Don't have an account?
          <a href='#' className='rubik ml-1 text-[14px] font-normal text-[#0B80E7]' onClick={() => handleGoToRegister()}>
            Sign up now
          </a>
        </section>
        <section className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Email</section>
        <input
          {...register('email')}
          type='email'
          name='email'
          placeholder='Your email address'
          className={`rubik mt-[4px] h-[48px] w-full  rounded border border-[#BBC1D3] px-2 text-[14px] text-[#1A1A1A] sm:w-[368px] ${errors?.email?.message && 'border-[#CB101D]'}`}
        />
        <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.email?.message}</p>
        <a href='#' onClick={() => toOtpLogin()} className='font-normal text-black underline'>
          Use phone number instead
        </a>
        <section className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Password</section>

        <div className='relative flex items-center'>
          <input
            {...register('password')}
            type={isPasswordVisible ? 'text' : 'password'}
            name='password'
            placeholder='.......'
            className={`rubik mt-[4px] h-[48px] w-full rounded border border-[#BBC1D3] px-2 pr-[40px] text-[14px] text-[#1A1A1A] sm:w-[368px] ${errors?.email?.message && 'border-[#CB101D]'}`}
          />
          <span className='material-icons-outlined absolute right-4 mt-[3px] cursor-pointer' onClick={() => setIsPasswordVisible(!isPasswordVisible)}>
            {isPasswordVisible ? 'visibility' : 'visibility_off'}
          </span>
        </div>

        <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.password?.message}</p>
        <section className='rubik mt-[4px] text-[14px]  text-[#444B5F]'>
          Use more than 12 characters and a combination of uppercase and lowercase letters, numbers, and symbols.
        </section>
        <a className=' rubik font-normal text-[#323C58] underline' onClick={activate}>
          Forgot your password?
        </a>
        <ForgotPassword active={active} deactivate={deactivate} />
        {loginStatus !== 'verify' && (
        <button className='border-neutral bg-background-base rubik mt-[16px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border bg-[#0B80E7] px-4 py-2 text-[16px] text-white sm:w-[368px]'>
          Log in
        </button>)}
        {loginStatus === 'verify' && (
        <button type="button" onClick={() => submitverify()} className='border-neutral bg-background-base rubik mt-[16px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border bg-[#0B80E7] px-4 py-2 text-[16px] text-white sm:w-[368px]'>
          Verify Email
        </button>
      )}
      </article>
    </form>
  );
};

export default LoginForm;
