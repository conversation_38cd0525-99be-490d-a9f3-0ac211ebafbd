import axios from 'axios';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import { API_ENDPOINTS } from 'src/client/api-endpoints';
import { useNavigate } from 'react-router-dom';
import { checkTakenEmail } from 'src/services/user';

interface GuestLoginFormData {
  email: string;
}

const GuestLoginForm = () => {
  const [loading, setLoading] = useState(false);
  const [emailTaken, setEmailTaken] = useState(false);
  const { register, handleSubmit, formState: { errors } } = useForm<GuestLoginFormData>();
  const { authenticateUser } = useAuthContext();
  const { closeModal } = useModalAction();
  const navigate = useNavigate();

  const onSubmit = async (data: GuestLoginFormData) => {
    try {
      setLoading(true);
      setEmailTaken(false);

      // First check if email is already taken
      const emailCheckResponse = await checkTakenEmail(data);
      
      if (emailCheckResponse.error === true) {
        setEmailTaken(true);
        return;
      }

      const response = await axios.post(
        `${import.meta.env.VITE_REACT_APP_BASE_URL}${API_ENDPOINTS.GUEST_LOGIN}`, 
        data
      );
      
      if (response.data) {
        authenticateUser(response.data);
        closeModal();
        navigate('/search-operator');
      }
    } catch (error: any) {
      console.error('Guest login failed:', error);
      if (error.response?.data?.message) {
        setEmailTaken(true);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <input
          type="email"
          placeholder="Enter your email"
          {...register('email', { 
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Invalid email address'
            }
          })}
          className={`w-full px-4 py-2 border rounded outline-none focus:outline-none ${errors.email || emailTaken ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
        />
        {errors.email && (
          <span className="text-[#CB101D] text-sm">{errors.email.message}</span>
        )}
        {emailTaken && (
          <span className="text-[#CB101D] text-sm">This email is already registered. Please use a different email or login to your account.</span>
        )}
      </div>
      
      <button
        type="submit"
        disabled={loading}
        className="w-full border-neutral bg-background-base rubik mt-4 flex items-center justify-center text-white px-4 py-2 rounded-[8px] border bg-[#0B80E7] text-[16px] font-medium leading-[24px] disabled:opacity-50" 
      >
        {loading ? 'Please wait...' : 'Continue as Guest'}
      </button>
    </form>
  );
};

export default GuestLoginForm; 
