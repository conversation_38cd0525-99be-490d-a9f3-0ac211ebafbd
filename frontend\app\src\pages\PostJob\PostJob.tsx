// @ts-nocheck
import React, { ChangeEvent, useState, useRef, useEffect, useMemo } from 'react';
import { Card, View, Text, Button, TextField, Select, Switch, useToggle, Tooltip, Actionable, useToast, Image } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import limoreserveskews from '../../assets/images/postjob/limoreserveskews.svg';
import surelyproicon1 from '../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../assets/icons/surelyproicon/surelyproicon7.svg';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { addPostJob } from 'src/services/jobs';
import moment from 'moment';
import CalendarPostJob from 'src/components/Calendars/CalendarPostJob';
import { format, parseISO, isSameDay } from 'date-fns';
import AcceptEmergencyHire from '../../pages/PostJob/AcceptEmergencyHire';
import ClosePostJobModal from './ClosePostJobModal';
import CalendarPostJobNew from 'src/components/Calendars/CalendarPostJobNew';

const PostJob: React.FC = () => {
  const navigate = useNavigate();
  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const { active: active2, activate: activate2, deactivate: deactivate2 } = useToggle(false);
  const { active: active3, activate: activate3, deactivate: deactivate3 } = useToggle(false);
  const toast = useToast();
  const [images, setImages] = useState<(string | null)[]>([null, null, null]);
  const [jobPostName, setJobPostName] = useState<string>('');
  const [selectedDates, setSelectedDates] = useState<
    Array<{
      start: string;
      end: string;
    }>
  >([]);
  const [shiftData, setShiftData] = useState<{
    [key: string]: {
      startTimeHour: string;
      startTimeMinute: string;
      endTimeHour: string;
      endTimeMinute: string;
    };
  }>({});

  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [siaLicence, setSiaLicence] = useState<any>();
  const [industrySectors, setIndustrySectors] = useState<string[]>([]);
  const [qualifications, setQualifications] = useState<string[]>([]);
  const [surelyProBadge, setSurelyProBadge] = useState<string[]>([]);
  const [ratingLevel, setRatingLevel] = useState<number>(0);
  const [hourlyRateMin, setHourlyRateMin] = useState('0');
  const [hourlyRateMax, setHourlyRateMax] = useState('');
  const [jobTitle, setJobTitle] = useState<string>('');
  const [numOperatives, setNumOperatives] = useState<any>(0);
  const [location, setLocation] = useState('');
  const [jobDescription, setJobDescription] = useState<string>('');
  const [dutyOfCare, setDutyOfCare] = useState<string>('');
  const [jobBenefits, setJobBenefits] = useState<string>('');
  const [isEmergencyHire, setIsEmergencyHire] = useState<boolean>(false);
  const [isInclusivityPledge, setIsInclusivityPledge] = useState<boolean>(false);
  const [showEmergencyHireModal, setShowEmergencyHireModal] = useState(true);
  const [payment, setPayment] = useState(10);

  const [jobPostNameMissing, setJobPostNameMissing] = useState('');
  const [selectedDatesMissing, setSelectedDatesMissing] = useState('');
  const [siaLicenceMissing, setSiaLicenceMissing] = useState('');
  const [industrySectorsMissing, setIndustrySectorsMissing] = useState('');
  const [qualificationsMissing, setQualificationsMissing] = useState('');
  const [surelyProBadgeMissing, setSurelyProBadgeMissing] = useState('');
  const [ratingLevelMissing, setRatingLevelMissing] = useState('');
  const [hourlyRateMaxMissing, setHourlyRateMaxMissing] = useState('');
  const [jobTitleMissing, setJobTitleMissing] = useState('');
  const [numOperativesMissing, setNumOperativesMissing] = useState('');
  const [locationMissing, setLocationMissing] = useState('');
  const [jobDescriptionMissing, setJobDescriptionMissing] = useState('');
  const [imageSizeError, setImageSizeError] = useState<string | null>(null);
  const [maxRateTouched, setMaxRateTouched] = useState(false);
  const [closePopovers, setClosePopovers] = useState(false);

  const priceCloseProtection = 25;
  const priceDoorSupervisor = 12.5;
  const priceSecurityGuard = 11.5;
  const pricePublicSpaceSurveillance = 11.5;
  const priceCashValuablesTransit = 11.5;
  const priceVehicleImmobilisation = 11.5;

  const handleClosePopovers = async () => setClosePopovers((prevState) => !prevState);

  const handleRateClick = (index: any) => {
    setRatingLevel(index);
    if (ratingLevelMissing) {
      setRatingLevelMissing('');
    }
  };

  const handleAcceptEmergencyHire = () => {
    setIsEmergencyHire(true);
    setShowEmergencyHireModal(false);
  };

  const postJob = () => {
    const range = selectedDates?.length > 0 ? selectedDates : shiftData;

    const postjob: any = {
      images,
      jobPostName,
      selectedDates: range,
      siaLicence,
      industrySectors,
      qualifications,
      surelyProBadge,
      ratingLevel,
      hourlyRateMin,
      hourlyRateMax,
      jobTitle,
      numOperatives,
      location,
      jobDescription,
      dutyOfCare,
      jobBenefits,
      isEmergencyHire,
      isInclusivityPledge,
      payment,
    };

    addPostJob(postjob)
      .then((data: any) => {
        if (data.error) {
          toast.show({
            title: 'Error',
            text: data.message || 'An error occurred',
            startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
          });
        } else {
          toast.show({
            title: 'Congratulations!',
            text: 'Your Job Post is Now Live',
            startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
          });
          navigate('/manage-jobs/' + data?.id);
        }
      })
      .catch((error) => {
        toast.show({
          title: 'Error',
          text: error.message,
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      });
  };

  const filterUniqueDates = (array: any) => {
    const uniqueDatesMap = new Map();

    array.forEach((dateObj: any) => {
      const parsedStartDate = parseISO(dateObj.start);
      const dayKey = parsedStartDate.toISOString().substr(0, 10);

      if (uniqueDatesMap.has(dayKey)) {
        const existingStartDate = parseISO(uniqueDatesMap.get(dayKey).start);
        if (isSameDay(existingStartDate, parsedStartDate)) {
          uniqueDatesMap.set(dayKey, dateObj);
        }
      } else {
        uniqueDatesMap.set(dayKey, dateObj);
      }
    });

    return Array.from(uniqueDatesMap.values());
  };

  const totalDates = useMemo(() => filterUniqueDates(selectedDates), [selectedDates]);
  const sortedDates = useMemo(
    () =>
      totalDates
        ?.map((item) => ({
          start: parseISO(item.start),
          end: parseISO(item.end),
        }))
        .sort((a, b) => a.start - b.start),
    [totalDates],
  );

  const submitPostJob = () => {
    const range = totalDates?.length > 0 ? totalDates : shiftData?.length > 0 ? shiftData : [];

    const requiredFields: { field: any; label: string }[] = [
      { field: jobPostName, label: 'Job Post Name' },
      { field: range, label: 'Selected Dates' },
      { field: siaLicence, label: 'SIA Licence' },
      { field: jobTitle, label: 'Job Title' },
      { field: industrySectors, label: 'Industry Sectors' },
      { field: qualifications, label: 'Qualifications' },
      { field: surelyProBadge, label: 'Surely Pro Badge' },
      { field: ratingLevel, label: 'Rating Level' },
      { field: hourlyRateMin, label: 'Hourly Rate Min' },
      { field: hourlyRateMax, label: 'Hourly Rate Max' },
      { field: numOperatives, label: 'Number of Operatives' },
      { field: location, label: 'Location' },
      { field: jobDescription, label: 'Job Description' },
    ];

    const missingFields: { field: any; label: string }[] = requiredFields.filter(
      (field) => !field.field || (Array.isArray(field.field) && field.field.length === 0),
    );

    if (missingFields.length > 0) {
      missingFields.forEach((field) => {
        switch (field.label) {
          case 'Job Post Name':
            setJobPostNameMissing('Please enter a job post name.');
            break;
          case 'Selected Dates':
            setSelectedDatesMissing('Please select one or more suitable dates.');
            break;
          case 'SIA Licence':
            setSiaLicenceMissing('Please provide your SIA licence information.');
            break;
          case 'Job Title':
            setJobTitleMissing('Please add a descriptive job title.');
            break;
          case 'Industry Sectors':
            setIndustrySectorsMissing('Please specify industry sectors.');
            break;
          case 'Qualifications':
            setQualificationsMissing('Please list the necessary qualifications.');
            break;
          case 'Surely Pro Badge':
            setSurelyProBadgeMissing('Please select one or more SurelyPro badge.');
            break;
          case 'Rating Level':
            setRatingLevelMissing('Please select an appropriate rating level.');
            break;
          case 'Hourly Rate Max':
            setHourlyRateMaxMissing('Please provide the maximum hourly rate.');
            break;
          case 'Number of Operatives':
            setNumOperativesMissing('Please specify the number of operatives needed.');
            break;
          case 'Location':
            setLocationMissing('Please add the job location.');
            break;
          case 'Job Description':
            setJobDescriptionMissing('Please provide a detailed job description.');
            break;
          default:
            break;
        }
      });

      toast.show({
        title: '',
        text: 'Complete required fields to publish your job post.',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    } else {
      setJobPostNameMissing('');
      setSelectedDatesMissing('');
      setSiaLicenceMissing('');
      setIndustrySectorsMissing('');
      setQualificationsMissing('');
      setSurelyProBadgeMissing('');
      setRatingLevelMissing('');
      setHourlyRateMaxMissing('');
      setJobTitleMissing('');
      setNumOperativesMissing('');
      setLocationMissing('');
      setJobDescriptionMissing('');

      postJob();
    }
  };

  const placeholderOptionSia = {
    label: 'Select or type...',
    value: '',
  };

  const placeholderOptionIndustry = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionSurelyBadge = {
    label: 'Select or type...',
    value: '',
  };
  const placeholderOptionRelevant = {
    label: 'Select or type...',
    value: '',
  };

  const handleHourlyRateMaxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const numericValue = value.replace(/[^0-9.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
    if (!isNaN(parseFloat(numericValue)) || numericValue === '') {
      setHourlyRateMax(numericValue);
      setMaxRateTouched(true);
      setHourlyRateMaxMissing('');
      if (parseFloat(numericValue) >= parseFloat(hourlyRateMin)) {
        setMaxRateTouched(false);
      }
    } else {
      setHourlyRateMaxMissing('');
    }
  };

  const handleImageUpload = (index: number) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setImageSizeError('Image size exceeds the limit of 5MB. Please choose a smaller image.');
        toast.show({
          title: 'Error',
          text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return;
      }
      setImageSizeError('');

      const reader = new FileReader();
      reader.onloadend = () => {
        const newImages = [...images];
        newImages[index] = reader.result as string;
        setImages(newImages);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = (index: number) => () => {
    const newImages = [...images];
    newImages[index] = null;
    setImages(newImages);
  };

  const handleDecreaseOperatives = () => {
    const newValue = parseInt(numOperatives as string, 10) - 1;
    setNumOperatives(newValue === 0 ? '' : newValue);
    if (newValue !== '') {
      setNumOperativesMissing('');
    }
  };

  const handleIncreaseOperatives = () => {
    const newValue = parseInt(numOperatives as string, 10) + 1;
    setNumOperatives(newValue.toString());
    if (newValue !== '') {
      setNumOperativesMissing('');
    }
  };

  const handleNumOperativesChange = (event: any) => {
    const numericValue = parseInt(event.target.value.replace(/[^0-9]/g, ''), 10);
    setNumOperatives(isNaN(numericValue) ? '' : numericValue);
  };

  const generateStars = (value: number) => {
    const totalStars = 5;
    const filledStars = value;
    const emptyStars = totalStars - filledStars;

    const stars = [];
    for (let i = 0; i < filledStars; i++) {
      stars.push(
        <span key={i} className='material-icons-outlined text-yellow-400'>
          star
        </span>,
      );
    }
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <span key={i + filledStars} className='material-icons-outlined text-gray-400'>
          star
        </span>,
      );
    }
    return stars;
  };

  const handleSelectSiaLicence = (sia: string) => {
    setSiaLicence(sia);
  };

  useEffect(() => {
    if (siaLicence?.length > 0) {
      const selectedLicencesPrices = siaLicence?.map((licence: any) => {
        switch (licence) {
          case 'Close Protection':
            return priceCloseProtection;
          case 'Door Supervisor':
            return priceDoorSupervisor;
          case 'Security Guard':
            return priceSecurityGuard;
          case 'Public Space Surveillance':
            return pricePublicSpaceSurveillance;
          case 'Cash & Valuables in Transit':
            return priceCashValuablesTransit;
          case 'Vehicle Immobilisation':
            return priceVehicleImmobilisation;
          default:
            return 0;
        }
      });

      const minPrice = Math.min(...selectedLicencesPrices);
      setHourlyRateMin(minPrice.toString());
    } else {
      setHourlyRateMin('0');
    }
  }, [siaLicence]);

  const handleRemoveSiaLicence = (sia: string) => {
    setSiaLicence(null);
  };

  const handleSelectSurelyProBadge = (surelyBadge: string) => {
    if (!surelyProBadge.includes(surelyBadge)) {
      setSurelyProBadge((prevSurelyBadge) => [...prevSurelyBadge, surelyBadge]);
    }
  };

  const handleRemoveSurelyProBadge = (surelyBadge: string) => {
    setSurelyProBadge((prevSurelyBadge) => prevSurelyBadge.filter((selectedSurelyBadge) => selectedSurelyBadge !== surelyBadge));
  };

  const clearAllSelectedSurelyProBadge = () => {
    setSurelyProBadge([]);
  };

  const handleSelectIndustry = (industry: string) => {
    if (!industrySectors.includes(industry)) {
      setIndustrySectors((prevSectors) => [...prevSectors, industry]);
    }
  };

  const handleRemoveIndustry = (industry: string) => {
    setIndustrySectors((prevSectors) => prevSectors.filter((selectedIndustry) => selectedIndustry !== industry));
  };

  const clearAllSelectedIndustries = () => {
    setIndustrySectors([]);
  };

  const handleSelectRelavant = (relavant: string) => {
    if (!qualifications.includes(relavant)) {
      setQualifications((prevRelavant) => [...prevRelavant, relavant]);
    }
  };

  const clearAllSelectedRelavant = () => {
    setQualifications([]);
  };

  const handleJobPostNameChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setJobPostName(event.target.value);
    if (jobPostNameMissing && jobPostName.trim() !== '') {
      setJobPostNameMissing('');
    }
  };
  const handleJobTitleChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setJobTitle(event.target.value);
  };
  const handleJobDescriptionChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setJobDescription(event.target.value);
  };
  const handleDutyOfCareChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setDutyOfCare(event.target.value);
  };
  const handleJobBenefitsChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setJobBenefits(event.target.value);
  };
  const getIconForSurelyProBadge = (badge: string): string => {
    switch (badge) {
      case 'Customer Service':
        return surelyproicon3;
      case 'Use Of Equipment':
        return surelyproicon2;
      case 'Disability Focus':
        return surelyproicon6;
      case 'Substance Awareness':
        return surelyproicon4;
      case 'Vulnerable People':
        return surelyproicon5;
      case 'Conflict Managament':
        return surelyproicon7;
      default:
        return surelyproicon1;
    }
  };
  const handleDateRangeSelect: any = (date: { start: string; end: string }) => {
    if (date?.start) {
      setSelectedDates([date]);
    } else if (date?.length > 0) {
      setSelectedDates(date);
    }
  };

  return (
    <div className='grid w-[90%] grid-cols-1 gap-[4px] sm:w-auto'>
      <Text className='font-rufina-stencil text-[32px] font-normal leading-[40px] text-[#1A1A1A]'>Post your job</Text>
      <Text className='rubik text-center text-[16px] font-normal leading-[24px] text-[#323C58]'>Find the right security operative for your job.</Text>

      <Card
        className={`mt-[32px] h-[auto] rounded-[8px] border p-[24px] shadow-md lg:w-[500px] ${imageSizeError ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
      >
        <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Upload a job image (optional)</Text>
        <View className='mt-2 flex flex-col justify-between gap-3 sm:flex-row sm:gap-0'>
          {[...Array(3)].map((_, index) => (
            <div key={index} className='relative'>
              {images[index] ? (
                <div className='relative '>
                  <img src={images[index] || ''} alt={`Uploaded Job ${index + 1}`} className='h-[136px] rounded-md object-cover md:w-[138px]' />
                  <button
                    className='absolute right-1 top-1 flex h-[22px] w-[22px] items-center justify-center rounded-full bg-white p-1 text-gray-600 shadow-md'
                    onClick={handleRemoveImage(index)}
                  >
                    <span className='material-icons text-[16px]'>close</span>
                  </button>
                </div>
              ) : (
                <div className='flex h-[136px] items-center justify-center rounded-[8px] border border-gray-300 bg-[#DBDFEA] md:w-[138px]'>
                  <label htmlFor={`imageInput${index}`}>
                    <span className='material-icons'>add</span>
                  </label>
                  <input id={`imageInput${index}`} type='file' accept='image/*' className='hidden' onChange={handleImageUpload(index)} />
                </div>
              )}
            </div>
          ))}
        </View>
        {imageSizeError && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{imageSizeError}</Text>}
        <Text className='rubik mt-2 text-[14px] font-normal leading-[20px] text-[#3C455D]'>
          It's useful for operatives to see where they'll be working.
        </Text>
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Job post name</Text>

        <div
          className={`mt-[4px] flex flex flex-col justify-between rounded-[4px] border ${jobPostNameMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'} border-solid bg-[#ffff] p-[8px] `}
        >
          <textarea
            name='text'
            className=' border-none  bg-transparent outline-none '
            placeholder='Enter your job post name'
            value={jobPostName}
            onChange={handleJobPostNameChange}
            maxLength={50}
            rows={Math.max(Math.ceil(jobPostName?.length / 50), 1)}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />

          <p className='mr-[5px] mt-[26px]  text-right text-gray-300'>{50 - (jobPostName?.length || 0)} characters left</p>
        </div>
        {jobPostNameMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{jobPostNameMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Job date range</Text>
        <div className='mt-[4px] flex flex-row'>
          <Button
            className={`flex h-[56px] w-[56px] items-center justify-center rounded-[8px] border !bg-[#F4F5F7]  ${selectedDatesMissing ? 'border-[#CB101D]' : 'border-[#DFE2EA]'}`}
            icon={() => (
              <span className={`material-icons-outlined mt-[-3px] text-[20px] ${selectedDatesMissing ? 'text-[#CB101D]' : 'text-[#14171F]'}`}>
                calendar_today
              </span>
            )}
            onClick={activate1}
          />
          <Text className='rubik ml-[20px] mt-4 text-[14px] font-medium leading-[20px]'>
            {shiftData && shiftData.length > 1
              ? `From ${format(sortedDates?.[0]?.start, 'd MMM yyyy')} to ${format(sortedDates?.[sortedDates?.length - 1]?.end, 'd MMM yyyy')}`
              : selectedDates.length > 0
                ? `From ${format(sortedDates?.[0]?.start, 'd MMM yyyy')} to ${format(sortedDates?.[sortedDates?.length - 1]?.end, 'd MMM yyyy')}`
                : 'Select a date range'}
          </Text>
        </div>
        {selectedDatesMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{selectedDatesMissing}</Text>}
        <CalendarPostJobNew
          handleClosePopovers={handleClosePopovers}
          closePopovers={closePopovers}
          active={active1}
          deactivate={() => handleClosePopovers().then(() => deactivate1())}
          handleParentDates={handleDateRangeSelect}
        />
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>SIA license</Text>
        <Select
          className={`mt-[4px] w-full rounded-[4px] ${siaLicenceMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'} border-solid bg-[#ffff] p-2`}
          name='sia_licence'
          placeholder={!siaLicence ? 'Select an option' : ''}
          //value={siaLicence ? { label: siaLicence, value: siaLicence } : null}
          options={[
            { label: 'Close Protection', value: 'Close Protection' },
            { label: 'Door Supervisor', value: 'Door Supervisor' },
            { label: 'Security Guard', value: 'Security Guard' },
            { label: 'Public Space Surveillance', value: 'Public Space Surveillance' },
            { label: 'Cash & Valuables in Transit', value: 'Cash & Valuables in Transit' },
            { label: 'Vehicle Immobilisation', value: 'Vehicle Immobilisation' },
          ]}
          onChange={(selectedOption) => {
            setSiaLicence([selectedOption.value]);
            if (siaLicenceMissing) {
              setSiaLicenceMissing('');
            }
          }}
          startSlot={
            siaLicence && (
              <Button
                size='small'
                rounded={true}
                elevated={false}
                className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
              >
                <Text color='positive' className='rubik flex items-center gap-1'>
                  <span className='material-icons text-[16px]'>star</span>
                  {siaLicence}
                </Text>
              </Button>
            )
          }
        />
        {siaLicenceMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{siaLicenceMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Industry sector</Text>
        <Select
          className={`mt-[4px]  w-full rounded-[4px] p-2 ${industrySectorsMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          name='industry_sectors'
          placeholder={industrySectors?.length > 0 ? '' : placeholderOptionIndustry.label}
          options={[
            {
              label: 'Bars, Clubs & Restaurants',
              value: 'Bars, Clubs & Restaurants',
            },
            { label: 'Restaurants & Hotels', value: 'Restaurants & Hotels' },
            { label: 'Events & Festivals', value: 'Events & Festivals' },
            { label: 'Private Hire', value: 'Private Hire' },
            { label: 'Film, TV & Media', value: 'Film, TV & Media' },
            { label: 'Commercial Offices', value: 'Commercial Offices' },
            { label: 'Construction', value: 'Construction' },
            { label: 'Education', value: 'Education' },
            {
              label: 'Financial & Banking',
              value: 'Financial & Banking',
            },
            { label: 'Government', value: 'Government' },
            { label: 'Healthcare', value: 'Healthcare' },
            { label: 'High Street Retail', value: 'High Street Retail' },
            { label: 'Industrial & Manufacturing', value: 'Industrial & Manufacturing' },
            { label: 'Non-Profit Organisations', value: 'Non-Profit Organisations' },
            { label: 'Tourism', value: 'Tourism' },
            { label: 'Mobile Patrol', value: 'Mobile Patrol' },
            { label: 'Other', value: 'Other' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              setIndustrySectors([...industrySectors, selectedOption.value]);
              if (industrySectorsMissing) {
                setIndustrySectorsMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-col sm:flex-row'>
              <div className=' flex flex-col flex-wrap gap-2 sm:w-[250px] sm:flex-row md:w-[400px] lg:w-[250px]'>
                {industrySectors?.map((selectedIndustry) => (
                  <Button
                    key={selectedIndustry}
                    size='small'
                    rounded={true}
                    elevated={false}
                    onClick={() => handleRemoveIndustry(selectedIndustry)}
                    className='max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs '
                  >
                    <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedIndustry}</Text>
                  </Button>
                ))}
              </div>
              {industrySectors?.length > 0 && (
                <Button variant='ghost' className='rubik py-0 font-medium text-[#3C455D] underline' onClick={clearAllSelectedIndustries}>
                  Clear all
                </Button>
              )}
            </div>
          }
        />
        {industrySectorsMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{industrySectorsMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Relavant qualification</Text>
        <Select
          className={`mt-[4px]  w-full rounded-[4px] border p-2 ${qualificationsMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          name='relevant'
          placeholder={qualifications?.length > 0 ? '' : placeholderOptionRelevant.label}
          options={[
            { label: 'Dog Handling Skills', value: 'Dog Handling Skills' },
            { label: 'Driving Skills', value: 'Driving Skills' },
            { label: 'Fire Safety', value: 'Fire Safety' },
            { label: 'First Aid', value: 'First Aid' },
            { label: 'Health & Safety', value: 'Health & Safety' },
            { label: 'Mechanical Restraints', value: 'Mechanical Restraints' },
            { label: 'Mental Health', value: 'Mental Health' },
            { label: 'Counter Terrorism', value: 'Counter Terrorism' },
            { label: 'Other', value: 'Other' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              setQualifications([...qualifications, selectedOption.value]);
              if (qualificationsMissing) {
                setQualificationsMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-col sm:flex-row'>
              <div className=' flex flex-col flex-wrap gap-2 sm:w-[250px] sm:flex-row md:w-[400px] lg:w-[250px] '>
                {qualifications?.map((selectedRelavant) => (
                  <Button
                    key={selectedRelavant}
                    size='small'
                    rounded={true}
                    elevated={false}
                    onClick={() => handleRemoveIndustry(selectedRelavant)}
                    className='max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs '
                  >
                    <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedRelavant}</Text>
                  </Button>
                ))}
              </div>
              {qualifications?.length > 0 && (
                <Button variant='ghost' className='rubik py-0 font-medium text-[#3C455D] underline' onClick={clearAllSelectedRelavant}>
                  Clear all
                </Button>
              )}
            </div>
          }
        />
        {qualificationsMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{qualificationsMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>SurelyPro badge</Text>
        <Select
          className={`mt-[4px]  w-full rounded-[4px] border p-2 ${surelyProBadgeMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          name='industry_sectors'
          placeholder={surelyProBadge?.length > 0 ? '' : placeholderOptionSurelyBadge.label}
          options={[
            { label: 'Customer Service', value: 'Customer Service' },
            { label: 'Use of Equipment', value: 'Use Of Equipment' },
            { label: 'Disability Focus', value: 'Disability Focus' },
            { label: 'Substance Awareness', value: 'Substance Awareness' },
            { label: 'Vulnerable People', value: 'Vulnerable People' },
            { label: 'Conflict Managament', value: 'Conflict Managament' },
          ]}
          onChange={(selectedOption: any) => {
            if (selectedOption.value !== '') {
              handleSelectSurelyProBadge(selectedOption.value);
              if (surelyProBadgeMissing) {
                setSurelyProBadgeMissing('');
              }
            }
          }}
          startSlot={
            <div className='flex flex-col sm:flex-row'>
              <div className=' flex flex-col  flex-wrap gap-2 sm:w-[250px] sm:flex-row md:w-[400px] lg:w-[250px]'>
                {surelyProBadge?.map((selectedSurelyBadge) => (
                  <Button
                    key={selectedSurelyBadge}
                    size='small'
                    rounded={true}
                    elevated={false}
                    onClick={() => handleRemoveSurelyProBadge(selectedSurelyBadge)}
                    className='max-w-xs overflow-hidden  truncate !bg-[#DDEFFF] px-2 py-1 text-xs '
                  >
                    <div className='flex flex-row'>
                      <img src={getIconForSurelyProBadge(selectedSurelyBadge)} alt={`Icon for ${selectedSurelyBadge}`} className='mr-2 h-4 w-4' />
                      <Text className='rubik font-normal leading-4 text-[#053D6D]'>{selectedSurelyBadge}</Text>
                    </div>
                  </Button>
                ))}
              </div>
              {surelyProBadge?.length > 0 && (
                <Button variant='ghost' className='rubik py-0 font-medium text-[#3C455D] underline' onClick={clearAllSelectedSurelyProBadge}>
                  Clear all
                </Button>
              )}
            </div>
          }
        />
        {surelyProBadgeMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{surelyProBadgeMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Rating level</Text>
        <View className='mt-[5px] flex flex-row gap-[8px]'>
          {[1, 2, 3, 4, 5].map((index) => (
            <span
              key={index}
              onClick={() => handleRateClick(index)}
              className={`material-icons-outlined ${ratingLevel >= index ? 'text-[#F4BF00]' : 'text-[#DBDFEA]'} cursor-pointer rounded-[8px]`}
            >
              star
            </span>
          ))}
        </View>
        {ratingLevelMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{ratingLevelMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Hourly rate</Text>
        <View className='mt-[4px] flex flex-col gap-3 sm:flex-row md:items-center'>
          <View className='w-full'>
            <View className='flex flex-col items-start'>
              <Text className='rubik text-sm font-normal leading-5 text-[#1A1A1A]'>Min</Text>
              <View className='h-[48px] w-full flex-shrink-0 rounded-md border border-[#BBC1D3] px-[12px] py-[14px]'>
                <Text>£{hourlyRateMin}</Text>
              </View>
            </View>
          </View>
          <View className='w-full'>
            <View className='flex flex-col items-start'>
              <Text className='rubik text-sm font-normal leading-5 text-[#1A1A1A]'>Max</Text>
              <input
                name='text'
                placeholder='Max'
                className={`h-[48px] w-full rounded-[8px] rounded-md border bg-[#fff] px-[12px] py-[14px] ${hourlyRateMaxMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
                style={{ outline: 'none' }}
                value={hourlyRateMax !== '' ? `£${hourlyRateMax}` : ''}
                onInput={handleHourlyRateMaxChange}
              />
            </View>
          </View>
        </View>
        {hourlyRateMaxMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{hourlyRateMaxMissing}</Text>}
        {maxRateTouched && (
          <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>Maximum hourly rate is less or equal than minimum hourly rate</Text>
        )}
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-5 leading-[20px] text-[#1A1A1A]'>Job Title</Text>
        <div
          className={`mt-[4px] flex flex flex-col justify-between rounded border bg-[#ffff] p-[8px] ${jobTitleMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'} `}
        >
          <textarea
            name='text'
            className='border-none bg-transparent outline-none'
            placeholder='Enter job title - for example: Senior Door Supervisor'
            value={jobTitle}
            onChange={(event) => {
              const value = event.target.value;
              setJobTitle(value);
              if (value) {
                setJobTitleMissing('');
              }
            }}
            maxLength={50}
            rows={Math.max(Math.ceil(jobTitle?.length / 50), 1)}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />

          <p className='mr-[5px] mt-[26px]  text-right text-gray-300'>{50 - (jobTitle?.length || 0)} characters left</p>
        </div>
        {jobTitleMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{jobTitleMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Number of operatives needed</Text>
        <View className='mt-[4px] flex items-center gap-[12px]'>
          <Button
            className='flex h-[48px] w-[56px] items-center justify-center rounded-[8px] border border-[#DFE2EA] !bg-[#F4F5F7] p-[16px]'
            onClick={handleDecreaseOperatives}
          >
            <View className='flex items-center'>
              <span className='material-icons-outlined'>remove</span>
            </View>
          </Button>
          <input
            name='number operative'
            className={`h-[48px] w-[74.5px] rounded-[4px] rounded-[8px] border bg-[#fff] px-[12px] py-[14px] ${numOperativesMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
            style={{ outline: 'none', textAlign: 'center' }}
            value={numOperatives}
            onChange={(event) => {
              const value = event.target.value;
              setNumOperatives(value);
              if (value) {
                setNumOperativesMissing('');
              }
            }}
          />
          <Button
            className='flex h-[48px] w-[56px] items-center justify-center rounded-[8px] border border-[#DFE2EA] !bg-[#F4F5F7] p-4'
            onClick={handleIncreaseOperatives}
          >
            <View className='flex items-center'>
              <span className='material-icons-outlined'>add</span>
            </View>
          </Button>
        </View>
        {numOperativesMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{numOperativesMissing}</Text>}
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Location</Text>
        <TextField
          name='location'
          className={`mt-[4px] w-full rounded-[4px] border px-[12] py-[14px] ${locationMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
          placeholder='E.g. CRO 3RL or 36 Factory Lane'
          onChange={(event) => {
            const value = event.value;
            setLocation(value);
            if (value) {
              setLocationMissing('');
            }
          }}
        />
        {locationMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{locationMissing}</Text>}
        <Text className='rubik mt-2 text-[14px] text-[#3C455D] '>Type postcode to begin</Text>
        {/* <Text className='rubik mt-[16px] text-sm font-medium leading-5 text-[#1A1A1A]'>what3words (optional)</Text>
        <img src={limoreserveskews} className='mt-[8px]' /> */}
      </Card>

      <Card className='mt-[20px] h-[auto] gap-[12px] rounded-md border p-[24px] shadow-md lg:w-[500px]'>
        <Text className='rubik text-[14px] font-medium leading-[20px] leading-[20px] text-[#14171F]'>Job description</Text>

        <div
          className={`mt-[4px] flex flex flex-col justify-between rounded border bg-[#ffff] p-[8px] ${jobDescriptionMissing ? 'border-[#CB101D]' : 'border-[#BBC1D3]'}`}
        >
          <textarea
            name='job_description'
            placeholder='Provide more details about your job task'
            className='border-none bg-transparent outline-none'
            value={jobDescription}
            onChange={(event) => {
              const value = event.target.value;
              setJobDescription(value);
              if (value) {
                setJobDescriptionMissing('');
              }
            }}
            maxLength={500}
            rows={9}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />
          <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{500 - (jobDescription?.length || 0)} characters left</p>
        </div>
        {jobDescriptionMissing && <Text className='rubik mt-1 text-[14px] font-normal text-[#CB101D]'>{jobDescriptionMissing}</Text>}
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Duty of care (optional)</Text>
        <div className='mt-[4px] flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px]'>
          <textarea
            name='duty_of_care'
            placeholder='What you do to look after operatives...'
            className=' border-none  bg-transparent outline-none '
            value={dutyOfCare}
            onChange={handleDutyOfCareChange}
            maxLength={250}
            rows={9}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />
          <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{250 - (dutyOfCare?.length || 0)} characters left</p>
        </div>
        <Text className='rubik mt-[12px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Job benefits (optional)</Text>
        <div className='mt-[4px] flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px] '>
          <textarea
            name='job_benefits'
            placeholder='Provide details about your job benefits like free entry, meals, discounts etc.'
            className=' border-none  bg-transparent outline-none '
            value={jobBenefits}
            onChange={handleJobBenefitsChange}
            maxLength={250}
            rows={9}
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              resize: 'none',
            }}
          />
          <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{250 - (jobBenefits?.length || 0)} characters left</p>
        </div>
        <div className='flex flex-row justify-between'>
          <div className='flex flex-col'>
            <div className='mt-4 flex items-center gap-2'>
              <Text className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A] '>Emergency Hire</Text>
              <Tooltip text='The Emergency Hire feature enables businesses to quickly fill urgent security positions, ensuring the safety and protection of their premises. With this option, there is a 5% extra fee.'>
                {(attributes) => (
                  <Actionable attributes={attributes} as='div'>
                    <div className='ml-2 flex h-[22px] w-[22px] items-center justify-center rounded-full border bg-[#C7CDDB]'>
                      <span className='material-icons-outlined text-[12px]'>question_mark</span>
                    </div>
                  </Actionable>
                )}
              </Tooltip>
            </div>
            <Text className='rubik text-sm text-[#444B5F]'>Certified Security Operatives available immediately</Text>
          </div>
          <Switch name='emergency' checked={isEmergencyHire} onChange={() => activate2()} />
        </div>
        <AcceptEmergencyHire
          active={active2}
          deactivate={deactivate2}
          onAccept={handleAcceptEmergencyHire}
          onDecline={() => {
            setIsEmergencyHire(false);
            deactivate2();
          }}
        />
        <div className='flex flex-row justify-between'>
          <div className='flex flex-col'>
            <div className='mt-4 flex items-center gap-2'>
              <Text className='rubik text-center text-[16px] font-medium leading-5 text-[#1A1A1A]'>Inclusivity Pledge</Text>
              <Tooltip text='After watching our informative video about inclusivity and successfully completing the test Security Operatives will be awarded an Inclusivity Badge. This badge serves as a visible symbol of your commitment to promoting inclusivity and diversity.'>
                {(attributes) => (
                  <Actionable attributes={attributes} as='div'>
                    <div className='ml-2 flex h-[22px] w-[22px] items-center justify-center rounded-full border bg-[#C7CDDB]'>
                      <span className='material-icons-outlined text-[12px]'>question_mark</span>
                    </div>
                  </Actionable>
                )}
              </Tooltip>
            </div>
            <Text className='rubik text-sm text-[#444B5F]'>
              This badge serves as a visible symbol of your commitment to promoting inclusivity and diversity.
            </Text>
          </div>
          <Switch name='inclusivity' checked={isInclusivityPledge} onChange={() => setIsInclusivityPledge((prev) => !prev)} />
        </div>
      </Card>
      <div className='mb-[51.8px] mt-[24px] flex flex-row justify-between'>
        <Button
          variant='outline'
          icon={() => <span className='material-icons-outlined mt-[-2px] text-[22px]'>close</span>}
          onClick={activate3}
          className='border-neutral flex h-[48px] w-[103px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[white] px-4 py-2'
        >
          <Text className=' rubik text-[16px] font-medium leading-[24px] !text-[#14171F]'>Close</Text>
        </Button>

        <ClosePostJobModal active={active3} deactivate={deactivate3} />

        <Button
          onClick={() => {
            if (!isButtonDisabled) {
              submitPostJob();
              setTimeout(() => {
                setIsButtonDisabled(false);
              }, 1000);
              setIsButtonDisabled(true);
            }
          }}
          className='submit-button border-neutral h-[48px] self-stretch rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
        >
          <View className='flex flex-row items-center justify-center gap-2 w-full h-full'>
            <Text className='rubik text-[16px] font-medium leading-[24px] !text-[#ffff]'>Post a job</Text>
            <span className='material-icons-outlined mt-[2px] text-[15px] !text-[#ffff]'>arrow_forward_ios</span>
          </View>
        </Button>
      </div>
    </div>
  );
};

export default PostJob;
