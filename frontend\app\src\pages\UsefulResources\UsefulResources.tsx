// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { View, Image, Text, Button, Accordion, Card } from 'reshaped';
import Footer from '../Footer/Footer';
import { AuthContext } from 'src/context/AuthContext';
import { useModalAction } from 'src/context/ModalContext';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';
import usefulresources1 from '../../assets/images/usefulresources/usefulresources1.svg';
import usefulresources2 from '../../assets/images/usefulresources/usefulresources2.svg';
import usefulresources3 from '../../assets/images/usefulresources/usefulresources3.svg';
import usefulresources4 from '../../assets/images/usefulresources/usefulresources4.svg';
import usefulresources5 from '../../assets/images/usefulresources/usefulresources5.svg';
import usefulresources6 from '../../assets/images/usefulresources/usefulresources6.svg';
import usefulresources7 from '../../assets/images/usefulresources/usefulresources7.svg';
import usefulresources8 from '../../assets/images/usefulresources/usefulresources8.svg';
import usefulresources9 from '../../assets/images/usefulresources/usefulresources9.svg';

const UsefulResources: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();
  const { openModal } = useModalAction();

  const siaList = [
    'close protection',
    'door supervision',
    'security guarding',
    'public space surveillance (CCTV)',
    'cash and valuables in transit',
    'key holding',
    'vehicle immobilisation (in Northern Ireland only)',
  ];
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full bg-[#FFFFFF]'>
        <View className='flex flex-col w-full gap-[20px] max-w-[1320px] mx-auto items-center !font-normal text-center '>
          <View className='flex flex-col gap-[10px] lg:w-[648px]  p-[50px] bg-[#FFFF] mt-[49px] mb-[12px] items-center text-center mx-auto '>
            <Text className=' font-rufina-stencil text-[48px] !font-normal leading-[56px] text-[#323C58]'>
              Useful Resources
            </Text>
            <Text className='text-left rubik text-[16px] font-normal text-[#383838] leading-[24px] lg:w-[648px] mt-[12px]'>
              This section is designed to provide helpful information to
              security operatives. We will be evolving the content on a
              continual basis. And recommendations for additional content
              <Text className='text-left sm:text-center rubik text-[16px] text-[#383838] leading-[24px]'>
              should be sent to&nbsp;
              <a
                className='cursor-pointer !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='mailto:<EMAIL>'
              >
                <EMAIL>.
              </a>
            </Text>
            </Text>
            
            <div className='items-start bg-[#388DD8] w-[200px] h-[4px] my-4' />
          </View>

          <View className='flex flex-col sm:w-[1100px]  bg-[#FFFF]   items-start text-center mx-auto '>
            <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start '>
              <img
                src={usefulresources1}
                className='w-[300px] sm:w-[388px]  ml-[3%] sm:ml-[0px] shrink-1 '
              />
              <View className='flex flex-col justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px] '>
                <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                  Security Industry Authority (SIA)
                </Text>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                  The SIA is the national regulator of the UK’s private security
                  industry and was established under the Private Security
                  Industry Act 2001. Its primary aim is to contribute to public
                  protection by setting and improving standards in the regulated
                  private security industry. The Act covers England, Wales,
                  Scotland and Northern Ireland and relates to the following
                  activities:
                </Text>
                <div className='flex flex-col'>
                  {siaList.map((item, index) => (
                    <Text
                      key={index}
                      className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'
                    >
                      <li className='mr-[10px] !font-normal'>{item} </li>
                    </Text>
                  ))}
                </div>
                <a
                  className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                  href='https://www.gov.uk/government/organisations/security-industry-authority'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  https://www.gov.uk/government/organisations/security-industry-authority
                </a>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px] mt-[10px] sm:mt-[25px]'>
                  You can visit the SIA website to apply for an SIA licence...
                </Text>
                <a
                  className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                  href='https://www.gov.uk/guidance/apply-for-an-sia-licence'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  https://www.gov.uk/guidance/apply-for-an-sia-licence
                </a>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                  …or to renew an existing one.
                </Text>
                <a
                  className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                  href='https://www.gov.uk/guidance/renew-your-sia-licence'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  https://www.gov.uk/guidance/renew-your-sia-licence
                </a>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px] mt-[10px] sm:mt-[25px]'>
                  You can check the register of licence holders at any time to
                  ensure your licence is up to date.
                </Text>
                <a
                  className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                  href='https://services.sia.homeoffice.gov.uk/rolh'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  https://services.sia.homeoffice.gov.uk/rolh
                </a>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px] mt-[10px] sm:mt-[25px]'>
                  You can also search their database for details of all
                  registered training providers in the UK, which may assist you
                  with your CPD (continuing professional development)
                  activities.
                </Text>
                <a
                  className='cursor-pointer w-[500px] text-left text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                  href='https://www.services.sia.homeoffice.gov.uk/Pages/training-ApprovedTrainingProviders.aspx'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  https://www.services.sia.homeoffice.gov.uk/Pages/training-ApprovedTrainingProviders.aspx
                </a>
                <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px] mt-[10px] sm:mt-[25px]'>
                  You can also search the Approved Contractor Scheme (ACS)
                  register if you are interested in joining a reputable security
                  company. We recommend you also join Surely, as some of these
                  companies may use Surely to find accredited, professional
                  freelance security operatives from time to time.
                </Text>
                <a
                  className='cursor-pointer text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                  href='https://www.services.sia.homeoffice.gov.uk/Pages/acs-roac.aspx'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  https://www.services.sia.homeoffice.gov.uk/Pages/acs-roac.aspx
                </a>
              </View>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources2}
              className='w-[300px] h-[219.65px] sm:w-[388px]  ml-[3%] sm:ml-[0px] shrink-1 mt-[16px] '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                NHS
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                The National Health Service (NHS) provides information to assist
                with the assessment of mental health concerns, and what to do if
                you are experiencing mental health conditions. They help you to
                understand your feelings, symptoms and behaviours, and provide
                advice for dealing with life situations and events. They provide
                self-help tips and information to access mental health services.
              </Text>

              <a
                className='cursor-pointer mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://www.nhs.uk/mental-health/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://www.nhs.uk/mental-health/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources3}
              className='w-[300px] h-[219.65px] mt-[6px] sm:w-[388px]  ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                Mind
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                Mind provides advice and support to empower anyone experiencing
                a mental health problem. They campaign to improve services,
                raise awareness and promote understanding. Their corporate
                purpose is to make sure that everyone experiencing a mental
                health problem gets the support and respect they need. If you
                need help, they offer a variety of options, depending upon your
                personal situation.
              </Text>

              <a
                className='cursor-pointer mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://www.mind.org.uk/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://www.mind.org.uk/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources4}
              className='w-[300px] sm:w-[388px] sm:mt-[15px] ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                Samaritans
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                Mind provides advice and support to empower anyone experiencing
                a mental health problem. They campaign to improve services,
                raise awareness and promote understanding. Their corporate
                purpose is to make sure that everyone experiencing a mental
                health problem gets the support and respect they need. If you
                need help, they offer a variety of options, depending upon your
                personal situation.
              </Text>

              <a
                className='cursor-pointer mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://www.mind.org.uk/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://www.mind.org.uk/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources5}
              className='w-[300px] sm:w-[388px]  ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px] mt-[10px] sm:mt-[70.35px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                Combat Stress
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                Combat Stress is the UK’s leading charity for veterans’ mental
                health. For over a century, they’ve helped former servicemen and
                women with mental health problems, such as post-traumatic stress
                disorder (PTSD), anxiety and depression. They provide specialist
                treatment and support for veterans from every service and
                conflict, focusing primarily on those with complex mental health
                issues.
              </Text>
              <a
                className='cursor-pointer mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://combatstress.org.uk/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://combatstress.org.uk/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources6}
              className='w-[300px] h-[326.73px] sm:w-[388px]  ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px] sm:mt-[35px] '>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                Shout 85258
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                Shout 85258 is a free, confidential and anonymous text support
                service. You can text them from anywhere in the UK. If you’re
                struggling and need to talk, trained Shout Volunteers are there
                for you, day or night. If your life is at imminent risk, call
                999 for immediate help. They chat with people who are anxious,
                stressed, depressed, suicidal or overwhelmed and who need
                in-the-moment support.
              </Text>
              <a
                className='cursor-pointer mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://giveusashout.org/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://giveusashout.org/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources7}
              className='w-[300px] sm:w-[388px]  ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px] mt-[10px] sm:mt-[75px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                Magnum Boots
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                In 1982, the first Magnum boot was born through a request from
                the FBI training centre for lightweight, athletic and
                comfortable tactical boots. Today, Magnum boots are sold in over
                100 countries around the world. They are the first choice in
                footwear for Law Enforcement, Ambulance, Fire, Security,
                Healthcare and Service, Construction and Industrial
                professionals.
              </Text>
              <a
                className=' mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://www.magnumboots.co.uk/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://www.magnumboots.co.uk/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources8}
              className='w-[300px] sm:w-[388px] sm:mt-[84px]  ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px] mt-[10px] sm:mt-[85px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                Ledlenser Torches
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                As one of the world’s market leaders in LED torches and head
                torches, Ledlenser is obsessed with making the best lights in
                the world. They aim to become the number one worldwide producer
                of portable light. With quality German-engineered products, that
                means setting the highest standards possible in terms of
                innovation, materials, technology, workmanship, design and
                sustainability.
              </Text>
              <a
                className=' mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href='https://ledlenser.co.uk/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://ledlenser.co.uk/
              </a>
            </View>
          </View>

          <View className='flex flex-col sm:flex-row max-w-[1320px] justify-between gap-[62px] items-start mt-[30px] sm:mt-[110px] '>
            <img
              src={usefulresources9}
              className='w-[300px] sm:w-[388px] sm:mt-[60px] ml-[3%] sm:ml-[0px] shrink-1 '
            />
            <View className='flex flex-col gap-[12px] justify-center w-[370px] sm:w-[641px] items-start ml-[2%] sm:ml-[0px] mb-[116px]'>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[32px] font-normal leading-[56px] w-[370px] sm:w-[641px]'>
                PatrolStore
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px] w-[370px] sm:w-[641px]'>
                PatrolStore was launched in May 2007, offering kit, clothing and
                equipment to individuals operating within the Police, Military
                and Security Industries. As former Police Officers, Military
                Personnel and Private Security Operators, the management team at
                PatrolStore has developed a specialist range of products
                designed to provide the ultimate in functionality and
                performance for any tactical situation.
              </Text>
              <a
                className=' mt-[5px] sm:mt-[16px] text-[16px] rubik !font-normal underline underline-offset-1 text-[#0B80E7] hover:underline underline-offset-1 hover:text-[#0B80E7]'
                href=' https://patrolstore.com/'
                target='_blank'
                rel='noopener noreferrer'
              >
                https://patrolstore.com/
              </a>
            </View>
          </View>
        </View>
      </View>

      <div
        className='bg-left-top flex justify-between w-full h-[480px] md:w-full md:h-[312px]  md:mx-auto bg-cover bg-no-repeat bg-center '
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center w-full sm:w-[271px] h-[56px] items-center gap-2 rounded-[8px]  !bg-[#fff] text-[17px] rubik mr-[24px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER', { userType: 'operative' });
                  }
                }}
              >
                Sign up - Security Operative
              </Button>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px] text-[17px] !bg-[#0B80E7] mt-[10px] sm:mt-0  rubik !text-[#ffff] border-[0px]'
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                Sign up - Client
              </Button>
            </View>
          )}
        </View>
      </div>

      <Footer />
    </View>
  );
};

export default UsefulResources;
