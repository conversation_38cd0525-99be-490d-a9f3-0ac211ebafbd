import { useAuthContext } from 'src/context/AuthContext';
import { useNavigate } from 'react-router-dom';

export const GuestUpgradeBanner = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();

  const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
  const isGuest = accountType === '5';

  if (!isGuest) {
    return null;
  }

  const handleUpgradeClick = () => {
    navigate('/client-settings-general');
  };

  return (
    <div className="bg-[#323C58] border-l-4 border-[#0B80E7] p-4">
      <div className="flex justify-between items-center">
        <p className="text-white flex items-center gap-2">
        Complete your profile details to become a client and get full access to all client features.
          <div className="relative group">
            <span className="material-icons-outlined text-white cursor-help">help_outline</span>
            <div className="absolute top-full hidden group-hover:block w-64 p-3 bg-white text-gray-800 border border-gray-200 text-xs rounded-lg shadow-lg z-50">
              As a Client, you'll be able to post jobs, communicate directly with operatives, 
              and access advanced features to manage your security needs effectively.
            </div>
          </div>
        </p>
        <div className="flex gap-2">
          <button 
            className="bg-[#0B80E7] text-white px-4 py-2 rounded hover:bg-[#0960AA] flex items-center gap-2"
            onClick={handleUpgradeClick}
          >
            <span className="material-icons-outlined">upgrade</span>
            Become a Client
          </button>
          <button 
            className="text-white hover:text-gray-200 bg-transparent border border-white rounded-md px-4 py-2"
            onClick={() => document.querySelector('.bg-\\[\\#323C58\\]')?.remove()}
          >
            Later
          </button>
        </div>
      </div>
    </div>
  );
};