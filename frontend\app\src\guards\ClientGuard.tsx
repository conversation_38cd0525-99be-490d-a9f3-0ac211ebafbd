// @ts-nocheck
import {FunctionComponent, ReactNode} from 'react'
import {Navigate, useLocation} from 'react-router-dom'
import {useAuthContext} from "src/context/AuthContext.tsx";

interface ClientGuardProps {
    children: ReactNode
}

const ClientGuard: FunctionComponent<ClientGuardProps> = ({children}) => {
    const location = useLocation()

    const {user} = useAuthContext();

  const isClient = user?.profile ? user?.profile?.account_type == '2' :  user?.account_type == '2'

    if (!isClient) {
        return <Navigate to="/" state={{from: location}}/>
    }

    return <>{children}</>
}

export default ClientGuard
