// @ts-nocheck
import React, { useContext, useState, useEffect } from 'react';
import { View, Image, Text, Button, Accordion } from 'reshaped';
import clientpage1 from '../../assets/images/clientpage/clientpage1.png';
import clientpage2 from '../../assets/images/clientpage/clientpage2.svg';
import clientpage3 from '../../assets/images/clientpage/clientpage3.svg';
import clientpage4 from '../../assets/images/clientpage/clientpaage4.svg';
import clientpage5 from '../../assets/images/clientpage/clientpage5.svg';
import clientpage6 from '../../assets/images/clientpage/clientpage6.svg';
import clientpage7 from '../../assets/images/clientpage/clientpage7.svg';
import clientpage8 from '../../assets/images/clientpage/clientpage8.svg';
import { useModalAction } from 'src/context/ModalContext';
import Footer from '../Footer/Footer';
import clientQuestions, {
  ClientQuestionsType,
} from '../../store/dummydata/ClientQuestionsHomePage';
import Subscribe from '../Subscribe/Subscribe';
import { AuthContext } from 'src/context/AuthContext';
import homepage17 from '../../assets/images/homepage/homepage17.jpg';

const ClientPage: React.FC = () => {
  const [activeId, setActiveId] = useState<number | null>(null);
  const [showAllClients, setShowAllClients] = useState<boolean>(false);
  const { isAuthenticated } = useContext(AuthContext);
  const { openModal } = useModalAction();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  const toggleShowAllClients = () => {
    setShowAllClients(!showAllClients);
  };
  return (
    <View className='w-full overflow-x-hidden mt-[-90px]'>
      <View className='w-full max-w-[1320px] mx-auto flex flex-col items-center  text-center mt-[66px]'>
        <View className='flex flex-col !bg-[#F4F5F7] '>
          <Image
            src={clientpage1}
            className='xl:w-[1320px] h-[auto] sm:h-[580px]'
          />
          <View className='flex flex-col lg:flex-row justify-between max-w-[1320px] xl:mx-auto sm:gap-[24px] sm:mt-[70px]  px-[12px] xl:px-0 '>
            <View className='flex flex-col items-start mt-[52px] sm:mt-0 lg:w-[420px] xl:w-[520px]'>
              <Text className='text-left rubik text-[16px] font-medium text-[#388DD8] leading-[20px] ml-[0px] sm:ml-0'>
                Clients
              </Text>
              <Text className='text-left font-rufina-stencil text-[35px] sm:text-[48px] ml-[0px] sm:ml-0 mt-[10px] sm:mt-[10px] font-normal text-[#323C58] leading-[46px] sm:leading-[56px]'>
                Find the perfect security operatives.
              </Text>
            </View>
            <View className='flex flex-col mt-[52px] sm:mt-0  lg:w-[529px] items-start  gap-[24px]'>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] sm:mr-[0px] '>
                Are you frustrated by the quality of security operatives you’re
                sent? Do you feel they’re not representing value for money for
                what you’re paying? Are you worried they’re going to cause
                problems on the job? Is it a concern they might reflect badly on
                your brand? Do you get let down at the last moment by “no
                shows”?
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] sm:mr-[0px] mt-[10px] sm:mt-0'>
                If you’ve answered yes to one or more of the above questions,
                then perhaps you need to take a closer look at Surely. We want
                to transform the way the security operative marketplace works,
                by providing an online environment that highlights the skills,
                experience and knowledge of professional security operatives for
                you to have direct contact with.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] sm:mr-[0px] mt-[10px] sm:mt-0'>
                Over the last six months, we’ve spoken to lots of clients like
                you to ask them what they think – they told us that the amount
                they pay is not always reflected in the quality of the people
                they receive. They told us that they rarely if ever have the
                chance to choose security operatives based upon their
                personality and character. They told us that too often they are
                let down at the last minute by unreliable security operatives.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] sm:mr-[0px] mt-[10px] sm:mt-0'>
                That’s where Surely comes in. Our app addresses all of these
                issues - and many, many more. It is by far the most
                comprehensive marketplace app for security jobs in the UK,
                perhaps even in the world. It safeguards the best interests of
                both security operatives and clients at the same, and to the
                mutual benefit of both. We have taken incredible care to create
                the best solution to meet your needs.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] sm:mr-[0px] mt-[10px] sm:mt-0'>
                But don’t just take our word for it – it’s free to sign up and
                takes seconds to do so. You can check out our security
                operatives and post a job today.
              </Text>
              <Text className='text-left rubik text-[16px] font-normal leading-[24px] text-[#383838] mr-[0px] sm:mr-[0px] mt-[10px] sm:mt-0'>
                It’s simple, it’s secure, it’s Surely.
              </Text>
              {isAuthenticated ? (
                <div></div>
              ) : (
                <Button
                  onClick={() => {
                    openModal('REGISTER');
                  }}
                  className='w-[140px] h-[56px] !text-[#ffff] !bg-[#0B80E7] mt-[7px] mb-[40px] rounded-[8px]'
                >
                  <Text className='rubik text-[17px] leading-[24px] font-mendium '>
                    Sign up now
                  </Text>
                </Button>
              )}
            </View>
          </View>
        </View>

        <View className=' flex flex-col max-w-[1100px] mx-auto mb-[20px] sm:mb-0 sm:mt-[82px] mt-10'>
          <div id='work'>
            <Text className='!text-[#323C58] font-rufina-stencil text-[45px] lg:text-[48px] leading-[56px] text-center sm:text-center mt-5 sm:mt-0 sm:mb-0 mb-10'>
              How it works?
            </Text>
          </div>
          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[89px] gap-12 mt-[20px] sm:mt-[82px]'>
            <View className='flex flex-col gap-3   mx-auto lg:w-[517px] items-start justify-center sm:ml-[0px]  px-[12px] xl:px-0 '>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-5'>
                Professionalising the marketplace
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[45px] lg:text-[48px] font-normal leading-[56px]'>
                Sign up today.
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                There’s never been an easier, better way to find the right
                security operatives for your next job, whether you’re looking
                for individuals or teams. Apart from confirming they have a
                valid SIA licence, we also verify they’re compliant with BS5878
                requirements – you can ask them to share their documents before
                booking them if you wish to do so.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={clientpage2}
              className='w-full  lg:w-[482.28px] lg:h-[360.77px]  sm:ml-[0px] shrink-1'
            />
          </View>

          <View className='flex flex-col-reverse lg:flex-row max-w-[1100px] justify-between sm:gap-[89px] gap-12 mt-[40px] sm:mt-[81.3px]'>
            <img
              src={clientpage3}
              className=' w-full lg:w-[460.28px] lg:h-[356.77px]  sm:ml-[0px] shrink-1'
            />
            <View className='flex flex-col gap-3 justify-center  mx-auto lg:w-[100%] items-start  px-[12px] xl:px-0   sm:ml-[0px]'>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-5'>
                Better qualified security operatives
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[40px] lg:text-[48px] font-normal leading-[56px]'>
                Find your perfect match.
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                We all know that an SIA licence is the bare minimum when it
                comes to evaluating the quality of an individual security
                operative. That’s why Surely security operatives upload any
                relevant qualifications that demonstrate their knowledge and
                experience. Their personal profile includes photos, video and
                bio. You can also review their CV and any letters of
                recommendation too.
              </Text>
              <div className='items-start bg-[#388DD8] my-4 w-[160px] h-[4px]' />
            </View>
          </View>

          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[89px] gap-12 mt-[40px] sm:mt-[81.5px]'>
            <View className='flex flex-col gap-3  justify-center  mx-auto xl:w-[517px] items-start  sm:ml-[0px]  px-[12px] xl:px-0 '>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-5'>
                Post a task today
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[48px] font-normal leading-[56px]'>
                Attract the best talent.
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Create and post a task whenever you need a security operative –
                you can communicate directly via in-app chat, audio and video to
                discuss details. You can also search our community and contact
                security operatives directly. You can even find security
                operatives for last minute work through our Emergency Hire
                function.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={clientpage4}
              className='w-full lg:w-[490px] lg:h-[344.77px]  sm:ml-[0px] shrink-1 '
            />
          </View>
        </View>
      </View>

      <div
        className='bg-left-top flex sm:mt-[102px] mt-[20px] sm:mt-0 justify-between w-full h-[480px] md:w-full md:h-[315px] mx-0 md:mx-auto bg-cover bg-no-repeat bg-center'
        style={{ backgroundImage: `url(${homepage17})` }}
      >
        <View className='flex flex-col mx-auto my-auto'>
          <Text className='text-[#FFF] text-center font-rufina-stencil text-[48px] font-normal leading-[56px]'>
            Don’t miss the opportunity.
          </Text>
          {isAuthenticated ? (
            <div></div>
          ) : (
            <View className='flex flex-col mt-[32px] sm:flex-row mx-auto my-auto'>
              <Button
                className='flex p-4 justify-center items-center w-full sm:w-[166px] h-[56px] gap-2 rounded-[8px]  !bg-[#FFF] mt-[10px] sm:mt-0  '
                onClick={() => {
                  if (!isAuthenticated) {
                    openModal('REGISTER');
                  }
                }}
              >
                <Text className='text-[17px] font-medium leading-6	 rubik !text-[#1A1A1A]'>
                  Sign up - Client
                </Text>
              </Button>
            </View>
          )}
        </View>
      </div>
      <View className='w-full mx-auto max-w-[1320px] mx-auto flex flex-col items-center  text-center '>
        <View className=' flex flex-col max-w-[1100px] mx-auto mb-[20px] sm:mb-[0px]'>
          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between xl:gap-[89px] gap-12 mt-[40px] sm:mt-[99.3px]'>
            <img
              src={clientpage5}
              className='w-full lg:w-[496px] lg:h-[414.77px] h-auto sm:ml-[-20px] shrink-1'
            />
            <View className='flex flex-col gap-3 justify-center  mx-auto sm:mx-0 xl:w-[506px] items-start  px-[12px] xl:px-0   sm:ml-[0px]'>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-5'>
                All your financials in one place
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[48px] font-normal leading-[56px]'>
                A better way to be paid.
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                We’ve built a sophisticated payment system, so you’ll always
                know exactly where you stand for every task you create. Place a
                deposit in escrow to book a security operative, which is
                released when they have completed the job to your satisfaction
                and the total fee is confirmed. The balance is then paid within
                the agreed payment terms.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
          </View>

          <View className='flex flex-col-reverse lg:flex-row max-w-[1100px] justify-between sm:gap-[89px] gap-12 mt-[40px] sm:mt-[83.5px]'>
            <View className='flex flex-col gap-3 justify-center  mx-auto sm:mx-0  xl:w-[510px] items-start  px-[12px] xl:px-0   sm:ml-[0px]'>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-5'>
                Commitment to excellence
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[48px] font-normal leading-[56px]'>
                Developing the key skills we know you value.
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                We’ve created six SurelyPro badges that focus on the most
                important things we believe that a security operative needs to
                know to do their job better – dealing with vulnerable people,
                use of equipment, customer service, conflict management,
                disability focus and substance awareness. They need to pass an
                online exam to be awarded each badge.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
            <img
              src={clientpage6}
              className='w-full xl:w-[572px] lg:w-[472px] h-[auto]'
            />
          </View>

          <View className='flex flex-col lg:flex-row max-w-[1100px] justify-between sm:gap-[89px] gap-12 mt-[40px] sm:mt-[55.3px]'>
            <img
              src={clientpage7}
              className=' sm:mr-0  w-full lg:w-[504.28px] lg:h-[350.77px]   sm:ml-[0] shrink-1'
            />
            <View className='flex flex-col gap-3 justify-center  mx-auto sm:mx-0 xl:w-[510px] items-start  px-[12px] xl:px-0   sm:ml-[0px]'>
              <Text className='text-left text-[#388DD8] rubik text-[16px] font-medium leading-5'>
                Reputations have to be earned
              </Text>
              <Text className='text-left text-[#323C58] font-rufina-stencil text-[48px] font-normal leading-[56px]'>
                Check out the reviews left by other clients.
              </Text>
              <Text className='text-left text-[#383838] rubik text-[16px] font-normal leading-[24px]'>
                Clients have the option to rate security operatives for every
                single job they deliver, according to a number of important
                factors – punctuality, communication, helpfulness,
                professionalism, positivity, and dress code. Also, any same-day
                cancellations are listed on their profile, so you can determine
                which security operatives are reliable before you get in touch.
              </Text>
              <div className='items-start bg-[#388DD8] w-[160px] h-[4px] my-4' />
            </View>
          </View>
        </View>

        <div id='faq'></div>
        <Text className='!text-[#323C58] font-rufina-stencil text-[48px] leading-[56px] sm:mt-[110px] mt-10'>
          Frequently asked questions.
        </Text>
        <div className='w-full xl:w-auto'>
          <div className='flex sm:flex-row flex-col gap-[20px] w-full sm:w-auto px-[12px] xl:px-0 mt-[44.3px]'>
            <div className='flex flex-col w-full gap-4 mt-[15px]'>
              {clientQuestions
                .slice(0, showAllClients ? undefined : 8)
                .map((item: ClientQuestionsType) => {
                  if (item.id % 2 === 1) {
                    return (
                      <Accordion
                        key={item.id}
                        defaultActive={item.id === activeId}
                      >
                        <Accordion.Trigger>
                          {(attributes, { active }) => (
                            <Button
                              attributes={attributes}
                              highlighted={active}
                              variant='outline'
                              className='flex w-full  xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                              endIcon={() => (
                                <svg
                                  xmlns='http://www.w3.org/2000/svg'
                                  width='22'
                                  height='22'
                                  viewBox='0 0 22 22'
                                  fill='none'
                                >
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                    fill='#323C58'
                                  />
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                    fill='#323C58'
                                  />
                                </svg>
                              )}
                            >
                              <div className='flex flex-row '>
                                <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                  {item.question}
                                </p>
                              </div>
                            </Button>
                          )}
                        </Accordion.Trigger>
                        <Accordion.Content>
                          <div className='xl:w-[600px] flex flex-row '>
                            <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]  '>
                              {item.answer}
                            </p>
                          </div>
                        </Accordion.Content>
                      </Accordion>
                    );
                  }

                  return null;
                })}
            </div>
            <div className='flex flex-col w-full gap-4 mt-[15px]'>
              {clientQuestions
                .slice(0, showAllClients ? undefined : 8)
                .map((item: ClientQuestionsType) => {
                  if (item.id % 2 === 0) {
                    return (
                      <Accordion
                        key={item.id}
                        defaultActive={item.id === activeId}
                      >
                        <Accordion.Trigger>
                          {(attributes, { active }) => (
                            <Button
                              attributes={attributes}
                              highlighted={active}
                              variant='outline'
                              className='flex w-full xl:w-[648px] h-[104px] p-[24px] border-[#DFE2EA] text-left rounded-lg !bg-[#ffff] justify-between'
                              endIcon={() => (
                                <svg
                                  xmlns='http://www.w3.org/2000/svg'
                                  width='22'
                                  height='22'
                                  viewBox='0 0 22 22'
                                  fill='none'
                                >
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M20 9.80488L2.00015 9.80486C1.25456 9.80486 0.650147 10.4093 0.650146 11.1549C0.650146 11.9004 1.25456 12.5049 2.00015 12.5049L20 12.5049C20.7456 12.5049 21.35 11.9005 21.35 11.1549C21.35 10.4093 20.7456 9.80488 20 9.80488Z'
                                    fill='#323C58'
                                  />
                                  <path
                                    fillRule='evenodd'
                                    clipRule='evenodd'
                                    d='M11 21.5049C11.7456 21.5049 12.35 20.9005 12.35 20.1549L12.3499 2.15487C12.3499 1.40929 11.7455 0.804876 10.9999 0.804882C10.2543 0.804888 9.6499 1.40931 9.6499 2.15489L9.65002 20.1549C9.65003 20.9005 10.2544 21.5049 11 21.5049Z'
                                    fill='#323C58'
                                  />
                                </svg>
                              )}
                            >
                              <div className='flex flex-row '>
                                <p className='text-left text-[#323C58] rubik text-[14px] sm:text-[17px] font-medium leading-[24px]'>
                                  {item.question}
                                </p>
                              </div>
                            </Button>
                          )}
                        </Accordion.Trigger>
                        <Accordion.Content>
                          <div className=' xl:w-[600px] '>
                            <p className='mt-[5px] text-left rubik text-[#323C58] p-[8px]'>
                              {item.answer}
                            </p>
                          </div>
                        </Accordion.Content>
                      </Accordion>
                    );
                  }

                  return null;
                })}
            </div>
          </div>

          <div className='flex justify-center mt-[24px] sm:mb-[101px] mb-[45px]'>
            <Button
              className='w-[140px] h-[40px] border-5 border-[#000] !bg-[#fff] rubik '
              onClick={toggleShowAllClients}
            >
              {showAllClients ? 'Load Less' : 'Load More'}
            </Button>
          </div>
        </div>
      </View>
      <Subscribe />
      <Footer />
    </View>
  );
};

export default ClientPage;
