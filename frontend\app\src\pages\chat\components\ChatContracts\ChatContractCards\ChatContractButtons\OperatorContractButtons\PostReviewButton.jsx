import { useToggle } from 'reshaped';
import JobReviewModal from '../../../../ChatModals/JobReviewModal';

const OperatorPostReviewButton = ({ chat }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div onClick={activate} className='flex cursor-pointer items-center justify-center gap-2 rounded-[8px] border border-[#DFE2EA] py-[10.5px]'>
        <p className='rubik flex items-center gap-2 text-[16px] font-medium leading-[24px] text-[#05751F]'>
          <span className='w-6 material-icons-outlined'>work_outlined</span> Post a review
        </p>
      </div>
      <JobReviewModal active={active} deactivate={deactivate} chat={chat} />
    </>
  );
};

export default OperatorPostReviewButton;
