// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Button, Text, View, Image, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import { useRegistrationContext } from 'src/context/RegistrationContext';

import { validateOperatorProfile } from 'src/services/user';
import { headerLogo } from '../../../assets/images';
import { useModalAction } from 'src/context/ModalContext';
import { AppContext } from 'src/context/AppContext';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorFourthStepFlow: React.FC = () => {
  const { openModal } = useModalAction();
  const { fetchAppData } = useContext(AppContext);
  const { active, activate, deactivate } = useToggle(false);
  const toast = useToast();

  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImageSelfie, setCapturedImageSelfie] = useState<string | null>(null);
  const navigate = useNavigate();

  const [validSelfie, setValidSelfie] = useState(true);

  const { sharedRegisterData, operatorRegisterData, setOperatorRegisterData } = useRegistrationContext();

  const openCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
      });
      setStream(cameraStream);
      setIsCameraOpen(true);
    } catch (error) {
      // console.error('Error accessing camera:', error);
    }
  };

  const closeCamera = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    setStream(null);
    setIsCameraOpen(false);
  };

  const takePicture = () => {
    if (stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      const canvas = document.createElement('canvas');
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const capturedDataURL = canvas.toDataURL('image/jpeg');
        setCapturedImageSelfie(capturedDataURL);
        const id1 = toast.show({
          title: 'Picture Saved Successfully:',
          text: 'Your picture has been saved successfully.',
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id1)}><Text className='text-[12px]'>X</Text></Button>,        });
      }
      setValidSelfie(true);
    }
  };

  useEffect(() => {
    if (isCameraOpen && stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      videoElement.srcObject = stream;
    }
  }, [isCameraOpen, stream]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleSubmit = async () => {
    setOperatorRegisterData((prevState: any) => ({
      ...prevState,
      fourthStep: {
        capturedImageSelfie: capturedImageSelfie,
      },
    }));

    if (!capturedImageSelfie) {
      return setValidSelfie(false);
    } else setValidSelfie(true);

    const baseData = sharedRegisterData.baseData;
    const crucialData = sharedRegisterData.crucialData;

    const operatorRegisterFinalData = {
      ...baseData,
      ...crucialData,
      ...operatorRegisterData.firstStep,
      ...operatorRegisterData.secondStep,
      ...operatorRegisterData.thirdStep,
      capturedImageSelfie,
    };

    let response = await validateOperatorProfile(operatorRegisterFinalData);

    if (response) {
      closeCamera();
      fetchAppData();
      navigate('/my-profile');
      // openModal('LOGIN');
    } else {
    }
  };

  return (
    <View className='mt-[20px] flex flex-col overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} className='btn-no-hover ' icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <CloseAccountCreatorModal active={active} deactivate={deactivate} />

      <View className='mx-auto flex  flex-col sm:w-[536px]'>
        <Text className='font-rufina-stencil leading-40 text-[32px] font-normal text-[#1A1A1A] sm:text-center'>Take a selfie for verification</Text>
        <Text className='rubik mx-auto mt-[16px] text-base font-normal leading-6 text-[#323C58] sm:text-center lg:w-[488px]'>
          To confirm your identity, use your camera to capture a photograph of yourself. This image will not appear on your profile.
        </Text>
        <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A] '>Use your camera to take a picture of yourself.</Text>
        <View className='mt-[16px] flex flex-row'>
          <Button className='btn-no-hover_one mr-[12px] mt-[10px] flex h-8 w-8 items-center justify-center rounded-[100px] !bg-[#DFE2EA] sm:mt-[0px] sm:h-16 sm:w-16'>
            <span className='material-icons-outlined mt-[-4px] align-middle text-black'>person</span>
          </Button>
          <View className='w-full sm:w-auto'>
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined mr-[8px] mt-[-1px] text-[20px]'>photo_camera</span>}
              onClick={isCameraOpen ? closeCamera : openCamera}
              className='flex h-[60px] w-full rounded-[8px] !border-[#DFE2EA] !bg-[#FFFF] sm:w-[412px]'
            >
              <Text className='rubik text-[15px] font-medium leading-[20px]'> {isCameraOpen ? 'Close Camera' : 'Take a picture'}</Text>
            </Button>
            {isCameraOpen && (
              <Button
                variant='outline'
                icon={() => <span className='material-icons-outlined mr-[8px] mt-[-2px] text-[20px]'>photo</span>}
                onClick={takePicture}
                className='mt-[10px] flex h-[60px] rounded-[8px] !border-[#DFE2EA] !bg-[#FFFF] sm:w-[412px]'
              >
                <Text className='rubik text-[15px] font-medium leading-[20px]'>Take Picture</Text>
              </Button>
            )}
            <View className='flex flex-row items-center justify-between'>
              {isCameraOpen && (
                <div className='flex items-center justify-center'>
                  <video id='camera-feed' className='h-[230px] w-[230px]' autoPlay playsInline></video>
                </div>
              )}
              {capturedImageSelfie && (
                <div className='flex h-[230px] w-[230px] items-center justify-center'>
                  <img src={capturedImageSelfie} alt='Captured' />
                </div>
              )}
            </View>
          </View>
        </View>
        {!validSelfie && <Text className='rubik mx-auto mt-[16px] text-[15px] font-normal  leading-5 text-red-400'>Selfie picture is required.</Text>}
      </View>

      <View className='mt-[20px] flex  flex-col sm:mt-[123px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full w-full bg-[#0B80E7]' />
        </div>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            icon={() => <span className='material-icons-outlined text-[19px] text-[#14171F]'>arrow_back_ios</span>}
            onClick={handleGoBack}
            className='bg-background-base flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[white]  px-4  py-2 sm:w-[103px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#14171F]'>Back</Text>
          </Button>

          <Button
            endIcon={() => <span className='material-icons-outlined text-[18px] text-[#FFF]'>arrow_forward_ios</span>}
            onClick={handleSubmit}
            className='border-neutral bg-background-base flex h-[48px] items-center justify-center self-stretch self-stretch rounded-[8px] border  !bg-[#0B80E7] sm:w-[135px] '
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#FFF]'>Submit</Text>
          </Button>
        </View>
        <div className='mt-[30px] flex items-center justify-center sm:mt-[0px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default OperatorFourthStepFlow;
